import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3,
  TrendingUp,
  Users,
  School,
  Target,
  Clock,
  MapPin,
  Calendar,
  Download,
  Filter,
  PieChart,
  Activity
} from 'lucide-react';
import { useFieldStaffTimesheets, useFieldReports } from '@/hooks/field-staff/useFieldStaffAttendance';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { format, subDays, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from 'date-fns';

interface AnalyticsMetrics {
  totalHours: number;
  totalSchools: number;
  totalStudents: number;
  totalSessions: number;
  averageHoursPerDay: number;
  averageStudentsPerSession: number;
  schoolVisitFrequency: Record<string, number>;
  activityTypes: Record<string, number>;
  productivityTrend: Array<{ date: string; hours: number; students: number }>;
}

const FieldReportingAnalytics: React.FC = () => {
  const [dateRange, setDateRange] = useState('week');
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [startDate, setStartDate] = useState(format(startOfWeek(new Date()), 'yyyy-MM-dd'));
  const [endDate, setEndDate] = useState(format(endOfWeek(new Date()), 'yyyy-MM-dd'));

  // Update date range based on selection
  const updateDateRange = (range: string) => {
    setDateRange(range);
    const now = new Date();
    
    switch (range) {
      case 'week':
        setStartDate(format(startOfWeek(now), 'yyyy-MM-dd'));
        setEndDate(format(endOfWeek(now), 'yyyy-MM-dd'));
        break;
      case 'month':
        setStartDate(format(startOfMonth(now), 'yyyy-MM-dd'));
        setEndDate(format(endOfMonth(now), 'yyyy-MM-dd'));
        break;
      case 'quarter':
        setStartDate(format(subDays(now, 90), 'yyyy-MM-dd'));
        setEndDate(format(now, 'yyyy-MM-dd'));
        break;
      case 'custom':
        // Keep current dates for custom range
        break;
    }
  };

  const { data: timesheets, isLoading: timesheetsLoading } = useFieldStaffTimesheets(undefined, selectedStaff === 'all' ? undefined : selectedStaff);
  const { data: fieldReports, isLoading: reportsLoading } = useFieldReports(selectedStaff === 'all' ? undefined : selectedStaff, startDate, endDate);

  // Calculate analytics metrics
  const calculateMetrics = (): AnalyticsMetrics => {
    if (!timesheets || !fieldReports) {
      return {
        totalHours: 0,
        totalSchools: 0,
        totalStudents: 0,
        totalSessions: 0,
        averageHoursPerDay: 0,
        averageStudentsPerSession: 0,
        schoolVisitFrequency: {},
        activityTypes: {},
        productivityTrend: [],
      };
    }

    const filteredTimesheets = timesheets.filter(ts => 
      ts.timesheet_date >= startDate && ts.timesheet_date <= endDate
    );

    const totalHours = filteredTimesheets.reduce((sum, ts) => sum + (ts.total_work_hours || 0), 0);
    const totalSchools = filteredTimesheets.reduce((sum, ts) => sum + (ts.total_schools_visited || 0), 0);
    const totalStudents = filteredTimesheets.reduce((sum, ts) => sum + (ts.total_students_reached || 0), 0);
    const totalSessions = filteredTimesheets.reduce((sum, ts) => sum + (ts.total_sessions_conducted || 0), 0);

    const workingDays = filteredTimesheets.length;
    const averageHoursPerDay = workingDays > 0 ? totalHours / workingDays : 0;
    const averageStudentsPerSession = totalSessions > 0 ? totalStudents / totalSessions : 0;

    // School visit frequency
    const schoolVisitFrequency: Record<string, number> = {};
    filteredTimesheets.forEach(ts => {
      if (ts.schools_visited && Array.isArray(ts.schools_visited)) {
        ts.schools_visited.forEach((school: unknown) => {
          const schoolName = (school as { school_name?: string }).school_name || 'Unknown School';
          schoolVisitFrequency[schoolName] = (schoolVisitFrequency[schoolName] || 0) + 1;
        });
      }
    });

    // Activity types from field reports
    const activityTypes: Record<string, number> = {};
    fieldReports.forEach(report => {
      const activityType = report.activity_type || 'unknown';
      activityTypes[activityType] = (activityTypes[activityType] || 0) + 1;
    });

    // Productivity trend (simplified)
    const productivityTrend = filteredTimesheets.map(ts => ({
      date: ts.timesheet_date,
      hours: ts.total_work_hours || 0,
      students: ts.total_students_reached || 0,
    }));

    return {
      totalHours,
      totalSchools,
      totalStudents,
      totalSessions,
      averageHoursPerDay,
      averageStudentsPerSession,
      schoolVisitFrequency,
      activityTypes,
      productivityTrend,
    };
  };

  const metrics = calculateMetrics();

  const formatActivityType = (type: string) => {
    return type.split('_').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <PageLayout>
      <PageHeader
        title="Field Reporting Analytics"
        description="Track field staff productivity, school visit patterns, and impact measurement data"
      />

      {/* Filters */}
      <ContentCard>
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <Select value={dateRange} onValueChange={updateDateRange}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="week">This Week</SelectItem>
                <SelectItem value="month">This Month</SelectItem>
                <SelectItem value="quarter">Last 3 Months</SelectItem>
                <SelectItem value="custom">Custom Range</SelectItem>
              </SelectContent>
            </Select>

            {dateRange === 'custom' && (
              <div className="flex gap-2 items-center">
                <Input
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  className="w-auto"
                />
                <span className="text-gray-500">to</span>
                <Input
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  className="w-auto"
                />
              </div>
            )}

            <Select value={selectedStaff} onValueChange={setSelectedStaff}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Staff" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Staff</SelectItem>
                {/* Add staff options here */}
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Advanced Filters
            </Button>
          </div>
        </div>
      </ContentCard>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{metrics.totalHours.toFixed(1)}</div>
                <div className="text-sm text-gray-600">Total Hours</div>
                <div className="text-xs text-gray-500">
                  Avg: {metrics.averageHoursPerDay.toFixed(1)}h/day
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <School className="h-5 w-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{metrics.totalSchools}</div>
                <div className="text-sm text-gray-600">School Visits</div>
                <div className="text-xs text-gray-500">
                  {Object.keys(metrics.schoolVisitFrequency).length} unique schools
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{metrics.totalStudents}</div>
                <div className="text-sm text-gray-600">Students Reached</div>
                <div className="text-xs text-gray-500">
                  Avg: {metrics.averageStudentsPerSession.toFixed(1)}/session
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{metrics.totalSessions}</div>
                <div className="text-sm text-gray-600">Sessions Conducted</div>
                <div className="text-xs text-gray-500">
                  Leadership training focus
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts and Analysis */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* School Visit Frequency */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              School Visit Frequency
            </CardTitle>
            <CardDescription>Most visited schools in the selected period</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(metrics.schoolVisitFrequency)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 10)
                .map(([school, visits]) => (
                  <div key={school} className="flex items-center justify-between">
                    <div className="text-sm font-medium truncate flex-1 mr-2">
                      {school}
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="text-sm text-gray-600">{visits} visits</div>
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ 
                            width: `${(visits / Math.max(...Object.values(metrics.schoolVisitFrequency))) * 100}%` 
                          }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        {/* Activity Types */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Activity Types
            </CardTitle>
            <CardDescription>Distribution of field activities</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(metrics.activityTypes)
                .sort(([,a], [,b]) => b - a)
                .map(([type, count]) => (
                  <div key={type} className="flex items-center justify-between">
                    <div className="text-sm font-medium">
                      {formatActivityType(type)}
                    </div>
                    <Badge variant="outline">
                      {count} activities
                    </Badge>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Productivity Trend */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Productivity Trend
          </CardTitle>
          <CardDescription>Daily productivity metrics over time</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {metrics.productivityTrend.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {metrics.productivityTrend.slice(-7).map((day, index) => (
                  <div key={day.date} className="border rounded-lg p-3">
                    <div className="text-sm font-medium">
                      {format(new Date(day.date), 'MMM d')}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">
                      {day.hours.toFixed(1)}h • {day.students} students
                    </div>
                    <div className="mt-2 flex gap-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${Math.min((day.hours / 8) * 100, 100)}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No productivity data available for the selected period
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Impact Summary */}
      <Card>
        <CardHeader>
          <CardTitle>Impact Summary</CardTitle>
          <CardDescription>
            Key insights for NGO funder reporting ({format(new Date(startDate), 'MMM d')} - {format(new Date(endDate), 'MMM d, yyyy')})
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium mb-3">Key Achievements</h4>
              <ul className="space-y-2 text-sm">
                <li>• Reached {metrics.totalStudents} students across {Object.keys(metrics.schoolVisitFrequency).length} schools</li>
                <li>• Conducted {metrics.totalSessions} leadership training sessions</li>
                <li>• Maintained average of {metrics.averageStudentsPerSession.toFixed(1)} students per session</li>
                <li>• Field staff worked {metrics.totalHours.toFixed(1)} total hours</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-3">Program Reach</h4>
              <ul className="space-y-2 text-sm">
                <li>• Most active school: {Object.entries(metrics.schoolVisitFrequency).sort(([,a], [,b]) => b - a)[0]?.[0] || 'N/A'}</li>
                <li>• Primary activity: {Object.entries(metrics.activityTypes).sort(([,a], [,b]) => b - a)[0]?.[0] ? formatActivityType(Object.entries(metrics.activityTypes).sort(([,a], [,b]) => b - a)[0][0]) : 'N/A'}</li>
                <li>• Average daily engagement: {metrics.averageHoursPerDay.toFixed(1)} hours</li>
                <li>• Geographic coverage: {Object.keys(metrics.schoolVisitFrequency).length} school locations</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </PageLayout>
  );
};

export default FieldReportingAnalytics;
