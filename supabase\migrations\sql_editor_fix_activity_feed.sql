-- QUICK FIX FOR ACTIVITY FEED ERRORS
-- Copy and paste this entire script into Supabase SQL Editor and run it
-- This fixes both JSON/JSONB conversion and VARCHAR/TEXT mismatch errors

-- Fix the get_recent_activities function
CREATE OR REPLACE FUNCTION get_recent_activities(
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    activity_type activity_type,
    user_id UUID,
    user_name TEXT,  -- Changed from VARCHAR to TEXT to match profiles.name
    entity_type entity_type,
    entity_id UUID,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    entity_details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        a.activity_type,
        a.user_id,
        p.name as user_name,
        a.entity_type,
        a.entity_id,
        a.description,
        a.metadata,
        a.created_at,
        CASE 
            WHEN a.entity_type = 'task' THEN
                (SELECT jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                    'title', t.title,
                    'status', t.status,
                    'priority', t.priority
                ) FROM tasks t WHERE t.id = a.entity_id)
            WHEN a.entity_type = 'school' THEN
                (SELECT jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                    'name', s.name,
                    'school_type', s.school_type
                ) FROM schools s WHERE s.id = a.entity_id)
            WHEN a.entity_type = 'distribution' THEN
                (SELECT jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                    'school_name', s.name,
                    'quantity', bd.quantity
                ) FROM book_distributions bd
                JOIN schools s ON bd.school_id = s.id
                WHERE bd.id = a.entity_id)
            ELSE '{}'::jsonb
        END as entity_details
    FROM activities a
    JOIN profiles p ON a.user_id = p.id
    WHERE 
        -- Apply RLS: users can see activities they have access to
        (a.user_id = auth.uid() OR
         (a.entity_type = 'task' AND EXISTS (
             SELECT 1 FROM tasks t_check
             WHERE t_check.id = a.entity_id AND (
                 t_check.assigned_to = auth.uid() OR 
                 t_check.created_by = auth.uid()
             )
         )) OR
         (a.entity_type = 'school' AND EXISTS (
             SELECT 1 FROM profiles pr
             JOIN schools s ON s.division_id = pr.division_id
             WHERE pr.id = auth.uid() AND s.id = a.entity_id
         )) OR
         EXISTS (
             SELECT 1 FROM profiles p_check
             WHERE p_check.id = auth.uid() AND p_check.role IN ('admin', 'program_officer')
         ))
    ORDER BY a.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$;

-- Fix the get_user_activities function
CREATE OR REPLACE FUNCTION get_user_activities(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    activity_type activity_type,
    user_id UUID,
    user_name TEXT,  -- Changed from VARCHAR to TEXT to match profiles.name
    entity_type entity_type,
    entity_id UUID,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    entity_details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission to view activities for this user
    IF p_user_id != auth.uid() AND NOT EXISTS (
        SELECT 1 FROM profiles p_check
        WHERE p_check.id = auth.uid() AND p_check.role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to view user activities';
    END IF;

    RETURN QUERY
    SELECT 
        a.id,
        a.activity_type,
        a.user_id,
        p.name as user_name,
        a.entity_type,
        a.entity_id,
        a.description,
        a.metadata,
        a.created_at,
        CASE 
            WHEN a.entity_type = 'task' THEN
                (SELECT jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                    'title', t.title,
                    'status', t.status,
                    'priority', t.priority
                ) FROM tasks t WHERE t.id = a.entity_id)
            WHEN a.entity_type = 'school' THEN
                (SELECT jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                    'name', s.name,
                    'school_type', s.school_type
                ) FROM schools s WHERE s.id = a.entity_id)
            WHEN a.entity_type = 'distribution' THEN
                (SELECT jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                    'school_name', s.name,
                    'quantity', bd.quantity
                ) FROM book_distributions bd
                JOIN schools s ON bd.school_id = s.id
                WHERE bd.id = a.entity_id)
            ELSE '{}'::jsonb
        END as entity_details
    FROM activities a
    JOIN profiles p ON a.user_id = p.id
    WHERE a.user_id = p_user_id
    ORDER BY a.created_at DESC
    LIMIT p_limit;
END;
$$;

-- Fix the activity logging trigger to use jsonb_build_object
CREATE OR REPLACE FUNCTION log_activity_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Log task creation
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'task_created',
            NEW.created_by,
            'task',
            NEW.id,
            'Created task: ' || NEW.title,
            jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                'task_id', NEW.id,
                'task_title', NEW.title,
                'priority', NEW.priority,
                'assigned_to', NEW.assigned_to
            )
        );
        RETURN NEW;
    END IF;

    -- Log task updates
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' THEN
        -- Only log if status changed
        IF OLD.status != NEW.status THEN
            INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
            VALUES (
                CASE 
                    WHEN NEW.status = 'completed' THEN 'task_completed'
                    ELSE 'task_updated'
                END,
                auth.uid(),
                'task',
                NEW.id,
                CASE 
                    WHEN NEW.status = 'completed' THEN 'Completed task: ' || NEW.title
                    ELSE 'Updated task: ' || NEW.title
                END,
                jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                    'task_id', NEW.id,
                    'task_title', NEW.title,
                    'old_status', OLD.status,
                    'new_status', NEW.status,
                    'priority', NEW.priority
                )
            );
        END IF;
        RETURN NEW;
    END IF;

    -- Log book distribution
    IF TG_TABLE_NAME = 'book_distributions' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'distribution_logged',
            NEW.supervisor_id,
            'distribution',
            NEW.id,
            'Logged book distribution: ' || NEW.quantity || ' books',
            jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                'distribution_id', NEW.id,
                'school_id', NEW.school_id,
                'quantity', NEW.quantity,
                'book_title', NEW.book_title
            )
        );
        RETURN NEW;
    END IF;

    -- Log school addition
    IF TG_TABLE_NAME = 'schools' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'school_added',
            COALESCE(NEW.created_by, auth.uid()),
            'school',
            NEW.id,
            'Added new school: ' || NEW.name,
            jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                'school_id', NEW.id,
                'school_name', NEW.name,
                'school_type', NEW.school_type
            )
        );
        RETURN NEW;
    END IF;

    -- Log task comments
    IF TG_TABLE_NAME = 'task_comments' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'comment_added',
            NEW.user_id,
            'comment',
            NEW.id,
            'Added comment to task',
            jsonb_build_object(  -- Changed from json_build_object to jsonb_build_object
                'comment_id', NEW.id,
                'task_id', NEW.task_id,
                'comment_preview', LEFT(NEW.comment, 100)
            )
        );
        RETURN NEW;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
