import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type UserRole = Database['public']['Enums']['user_role'];

interface StaffMember {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  division_id: string | null;
  division_name: string | null;
  phone: string | null;
  country: string;
  is_active: boolean;
  created_at: string;
  schools_allocated: number;
}

interface CreateUserData {
  email: string;
  name: string;
  role: UserRole;
  division_id?: string;
  phone?: string;
  password?: string;
}

interface UpdateUserData {
  id: string;
  name?: string;
  role?: UserRole;
  division_id?: string;
  phone?: string;
  is_active?: boolean;
}

export const useStaffManagement = () => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch all staff members using RPC function to get emails
  const {
    data: staffMembers,
    isLoading: isLoadingStaff,
    error: staffError,
    refetch: refetchStaff
  } = useQuery({
    queryKey: ['staff-members'],
    queryFn: async (): Promise<StaffMember[]> => {
      console.log('Fetching all staff members with emails...');

      // Use RPC function to get staff with emails and school counts
      const { data, error } = await supabase.rpc('get_staff_with_school_counts');

      if (error) {
        console.error('Error fetching staff with emails:', error);
        throw new Error(`Failed to fetch staff: ${error.message}`);
      }

      // Transform the data to match our interface
      const staffWithEmails: StaffMember[] = (data || []).map(row => ({
        id: row.id,
        name: row.name,
        email: row.email || 'No email',
        role: row.role,
        division_id: row.division_id,
        division_name: row.division_name,
        phone: row.phone,
        country: row.country,
        is_active: row.is_active ?? true,
        created_at: row.created_at || new Date().toISOString(),
        schools_allocated: row.schools_allocated || 0,
      }));

      console.log('✅ Staff members fetched successfully:', staffWithEmails.length, 'members');
      return staffWithEmails;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 2,
  });

  // Create new user with auth and profile
  const createUser = useMutation({
    mutationFn: async (userData: CreateUserData) => {
      console.log('Creating new user:', userData.email);
      
      // Generate a temporary password if not provided
      const tempPassword = userData.password || `TempPass${Math.random().toString(36).slice(-8)}!`;
      
      // Create auth user
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: tempPassword,
        email_confirm: true,
      });

      if (authError) {
        console.error('Error creating auth user:', authError);
        throw new Error(`Failed to create user: ${authError.message}`);
      }

      if (!authData.user) {
        throw new Error('No user data returned from auth creation');
      }

      // Create profile
      const { error: profileError } = await supabase
        .from('profiles')
        .insert({
          id: authData.user.id,
          name: userData.name,
          role: userData.role,
          division_id: userData.division_id || null,
          phone: userData.phone || null,
          country: 'Uganda',
          is_active: true,
        });

      if (profileError) {
        // Clean up auth user if profile creation fails
        await supabase.auth.admin.deleteUser(authData.user.id);
        console.error('Error creating profile:', profileError);
        throw new Error(`Failed to create user profile: ${profileError.message}`);
      }

      return { user: authData.user, tempPassword };
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: `User created successfully. Temporary password: ${data.tempPassword}`,
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update user profile
  const updateUser = useMutation({
    mutationFn: async (userData: UpdateUserData) => {
      console.log('Updating user:', userData.id);
      
      const { error } = await supabase
        .from('profiles')
        .update({
          name: userData.name,
          role: userData.role,
          division_id: userData.division_id,
          phone: userData.phone,
          is_active: userData.is_active,
        })
        .eq('id', userData.id);

      if (error) {
        console.error('Error updating user:', error);
        throw new Error(`Failed to update user: ${error.message}`);
      }

      return userData;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Reset user password
  const resetPassword = useMutation({
    mutationFn: async ({ userId, newPassword }: { userId: string; newPassword?: string }) => {
      console.log('Resetting password for user:', userId);

      // Use provided password or generate a new temporary password
      const passwordToSet = newPassword || `TempPass${Math.random().toString(36).slice(-8)}!`;

      // Use the PostgreSQL function we created for password reset
      const { data, error } = await supabase.rpc('reset_user_password', {
        user_id: userId,
        new_password: passwordToSet
      });

      if (error) {
        console.error('Error resetting password:', error);
        throw new Error(`Failed to reset password: ${error.message}`);
      }

      if (!data) {
        throw new Error('Password reset failed - user not found');
      }

      return { newPassword: passwordToSet };
    },
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: "Password reset successfully. The user can now log in with the new password.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Update user role
  const updateUserRole = useMutation({
    mutationFn: async ({ userId, newRole }: { userId: string; newRole: UserRole }) => {
      console.log('Updating user role:', userId, newRole);

      const { error } = await supabase.rpc('update_user_role', {
        p_user_id: userId,
        p_new_role: newRole
      });

      if (error) {
        console.error('Error updating user role:', error);
        throw new Error(`Failed to update user role: ${error.message}`);
      }

      return { userId, newRole };
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User role updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Toggle user status (activate/deactivate)
  const toggleUserStatus = useMutation({
    mutationFn: async ({ userId, isActive }: { userId: string; isActive: boolean }) => {
      console.log('Toggling user status:', userId, isActive);

      const { error } = await supabase.rpc('toggle_user_status', {
        p_user_id: userId,
        p_is_active: isActive
      });

      if (error) {
        console.error('Error toggling user status:', error);
        throw new Error(`Failed to update user status: ${error.message}`);
      }

      return { userId, isActive };
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User status updated successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  // Delete user (remove from auth and profiles)
  const deleteUser = useMutation({
    mutationFn: async (userId: string) => {
      console.log('Deleting user:', userId);
      
      // Delete from auth (this will cascade to profile due to foreign key)
      const { error: authError } = await supabase.auth.admin.deleteUser(userId);

      if (authError) {
        console.error('Error deleting user from auth:', authError);
        throw new Error(`Failed to delete user: ${authError.message}`);
      }

      // Also delete from profiles table to be sure
      const { error: profileError } = await supabase
        .from('profiles')
        .delete()
        .eq('id', userId);

      if (profileError) {
        console.warn('Error deleting profile (may already be deleted):', profileError);
      }

      return userId;
    },
    onSuccess: () => {
      toast({
        title: "Success",
        description: "User deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['staff-members'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    // Data
    staffMembers: staffMembers || [],

    // Loading states
    isLoadingStaff,
    isUpdatingRole: updateUserRole.isPending,
    isTogglingStatus: toggleUserStatus.isPending,
    isDeletingUser: deleteUser.isPending,

    // Mutations
    createUser,
    updateUser,
    updateUserRole: updateUserRole.mutate,
    toggleUserStatus: toggleUserStatus.mutate,
    resetPassword: resetPassword.mutate,
    deleteUser: deleteUser.mutate,

    // Refetch functions
    refetchStaff,
  };
};
