import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  Calendar, 
  Clock, 
  Target,
  TrendingUp,
  CheckCircle,
  XCircle,
  Timer,
  Download,
  Printer,
  MapPin
} from 'lucide-react';
import { useClassSummaryReport } from '@/hooks/attendance/useAttendanceReports';

interface ClassSummaryReportProps {
  sessionId: string;
}

const ClassSummaryReport: React.FC<ClassSummaryReportProps> = ({
  sessionId,
}) => {
  const { data: report, isLoading, error } = useClassSummaryReport(sessionId);

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // TODO: Implement PDF export
    console.log('Exporting class report...');
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </CardContent>
      </Card>
    );
  }

  if (error || !report) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <XCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Report</h3>
          <p className="text-gray-600">
            {error?.message || 'Failed to load class report. Please try again.'}
          </p>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'late':
        return <Timer className="h-4 w-4 text-yellow-600" />;
      case 'excused':
        return <Clock className="h-4 w-4 text-blue-600" />;
      default:
        return <Users className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'excused':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getParticipationColor = (score: number) => {
    if (score >= 4) return 'text-green-600';
    if (score >= 3) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6 print:space-y-4">
      {/* Header */}
      <Card className="print:shadow-none">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl flex items-center gap-2">
                <Users className="h-6 w-6" />
                Class Attendance Summary
              </CardTitle>
              <CardDescription>
                Detailed attendance report for {report.class_info.session_name}
              </CardDescription>
            </div>
            <div className="flex gap-2 print:hidden">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <div className="text-sm font-medium text-gray-600">Session</div>
              <div className="text-lg font-semibold">{report.class_info.session_name}</div>
              <div className="text-sm text-gray-600 capitalize">
                {report.class_info.session_type.replace('_', ' ')}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600">Date & Time</div>
              <div className="text-lg font-semibold">
                {new Date(report.class_info.session_date).toLocaleDateString()}
              </div>
              <div className="text-sm text-gray-600">{report.class_info.school}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600">Facilitator</div>
              <div className="text-lg font-semibold">{report.class_info.facilitator}</div>
              {report.class_info.grade_level && (
                <div className="text-sm text-gray-600">Grade {report.class_info.grade_level}</div>
              )}
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600">Attendance Rate</div>
              <div className="text-2xl font-bold text-green-600">
                {report.attendance_summary.attendance_rate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">
                {report.attendance_summary.present + report.attendance_summary.late} of {report.attendance_summary.total_enrolled} attended
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Summary */}
      <Card className="print:shadow-none">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Attendance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">
                {report.attendance_summary.total_enrolled}
              </div>
              <div className="text-sm text-gray-600">Total Enrolled</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {report.attendance_summary.present}
              </div>
              <div className="text-sm text-gray-600">Present</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">
                {report.attendance_summary.absent}
              </div>
              <div className="text-sm text-gray-600">Absent</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600">
                {report.attendance_summary.late}
              </div>
              <div className="text-sm text-gray-600">Late</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {report.attendance_summary.excused}
              </div>
              <div className="text-sm text-gray-600">Excused</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Participation Analysis */}
      <Card className="print:shadow-none">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Participation Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">
                {report.participation_analysis.average_participation.toFixed(1)}
              </div>
              <div className="text-sm text-gray-600">Average Participation Score</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600 mb-2">High Performers</div>
              {report.participation_analysis.high_performers.length > 0 ? (
                <div className="space-y-1">
                  {report.participation_analysis.high_performers.map((student, index) => (
                    <Badge key={index} className="bg-green-100 text-green-800 mr-1 mb-1">
                      {student}
                    </Badge>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-500">No high performers identified</div>
              )}
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600 mb-2">Needs Attention</div>
              {report.participation_analysis.needs_attention.length > 0 ? (
                <div className="space-y-1">
                  {report.participation_analysis.needs_attention.map((student, index) => (
                    <Badge key={index} className="bg-red-100 text-red-800 mr-1 mb-1">
                      {student}
                    </Badge>
                  ))}
                </div>
              ) : (
                <div className="text-sm text-gray-500">All students participating well</div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table Assignments (if applicable) */}
      {report.table_assignments && report.table_assignments.length > 0 && (
        <Card className="print:shadow-none">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Round Table Assignments
            </CardTitle>
            <CardDescription>
              Leadership program table organization and participation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {report.table_assignments.map((table) => (
                <div key={table.table_number} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="font-medium">Table {table.table_number}</div>
                    <Badge variant="outline">
                      Avg: {table.participation_average.toFixed(1)}
                    </Badge>
                  </div>
                  <div className="space-y-1">
                    {table.students.map((student, index) => (
                      <div key={index} className="text-sm text-gray-600">
                        {student}
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Student Details */}
      <Card className="print:shadow-none">
        <CardHeader>
          <CardTitle>Student Attendance Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-2">Student</th>
                  <th className="text-left py-2">Student Number</th>
                  <th className="text-center py-2">Status</th>
                  <th className="text-center py-2">Check-in Time</th>
                  <th className="text-center py-2">Participation</th>
                  {report.table_assignments && <th className="text-center py-2">Table</th>}
                  <th className="text-left py-2">Notes</th>
                </tr>
              </thead>
              <tbody>
                {report.student_details.map((student, index) => (
                  <tr key={index} className="border-b">
                    <td className="py-2 font-medium">{student.student_name}</td>
                    <td className="py-2 text-gray-600">{student.student_number}</td>
                    <td className="py-2 text-center">
                      <div className="flex items-center justify-center gap-1">
                        {getStatusIcon(student.status)}
                        <Badge className={getStatusColor(student.status)}>
                          {student.status}
                        </Badge>
                      </div>
                    </td>
                    <td className="py-2 text-center text-gray-600">
                      {student.check_in_time 
                        ? new Date(student.check_in_time).toLocaleTimeString()
                        : '-'
                      }
                    </td>
                    <td className="py-2 text-center">
                      {student.participation_score ? (
                        <span className={`font-medium ${getParticipationColor(student.participation_score)}`}>
                          {student.participation_score}/5
                        </span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </td>
                    {report.table_assignments && (
                      <td className="py-2 text-center">
                        {student.table_number ? (
                          <Badge variant="outline">
                            Table {student.table_number}
                          </Badge>
                        ) : (
                          <span className="text-gray-400">-</span>
                        )}
                      </td>
                    )}
                    <td className="py-2 text-gray-600 max-w-xs truncate">
                      {student.notes || '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Report Footer */}
      <Card className="print:shadow-none">
        <CardContent className="text-center py-4">
          <div className="text-sm text-gray-500">
            Report generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
          </div>
          <div className="text-xs text-gray-400 mt-1">
            iLEAD Field Tracker - Class Attendance Summary
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ClassSummaryReport;
