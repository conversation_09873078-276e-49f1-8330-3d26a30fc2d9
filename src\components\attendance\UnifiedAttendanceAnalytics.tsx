import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart3, 
  MapPin, 
  TrendingUp, 
  Users, 
  Calendar,
  Download,
  Filter,
  AlertTriangle,
  CheckCircle,
  Clock,
  Navigation,
  Target
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useSchools } from '@/hooks/useSchools';
import { 
  useSchoolAttendanceSummary, 
  useStudentsAtRisk,
  useAttendanceTrends 
} from '@/hooks/attendance/useAttendanceAnalytics';
import { useStaffLocationLogs } from '@/hooks/attendance/useGPSTracking';
import { useFieldStaffTimesheets } from '@/hooks/field-staff/useFieldStaffAttendance';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import AttendanceAnalyticsDashboard from './AttendanceAnalyticsDashboard';
import LocationLogs from './LocationLogs';
import FieldReportingAnalytics from '../field-staff/FieldReportingAnalytics';

const UnifiedAttendanceAnalytics = () => {
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedSchoolId, setSelectedSchoolId] = useState('all');
  const [dateRange, setDateRange] = useState({
    start: new Date(new Date().getFullYear(), new Date().getMonth(), 1),
    end: new Date()
  });

  // Data fetching
  const { data: schools } = useSchools();
  const { data: attendanceSummary } = useSchoolAttendanceSummary(
    selectedSchoolId === 'all' ? undefined : selectedSchoolId,
    dateRange
  );
  const { data: studentsAtRisk } = useStudentsAtRisk(
    selectedSchoolId === 'all' ? undefined : selectedSchoolId,
    'high'
  );
  const { data: attendanceTrends } = useAttendanceTrends({
    schoolId: selectedSchoolId === 'all' ? undefined : selectedSchoolId,
    period: 'weekly',
    startDate: dateRange.start,
    endDate: dateRange.end
  });
  const { data: locationLogs } = useStaffLocationLogs();
  const { data: timesheets } = useFieldStaffTimesheets();

  // Role-based permissions
  const canViewAllAnalytics = profile?.role === 'admin' || profile?.role === 'program_officer';

  // Calculate summary metrics
  const getSummaryMetrics = () => {
    const totalSchools = schools?.length || 0;
    const activeSchools = schools?.filter(s => s.registration_status === 'registered').length || 0;
    const overallAttendanceRate = attendanceSummary?.attendance_rate || 0;
    const studentsAtRiskCount = studentsAtRisk?.length || 0;
    const activeStaff = timesheets?.filter(t => t.total_work_hours > 0).length || 0;
    const totalLocationLogs = locationLogs?.length || 0;

    return {
      totalSchools,
      activeSchools,
      overallAttendanceRate,
      studentsAtRiskCount,
      activeStaff,
      totalLocationLogs
    };
  };

  const metrics = getSummaryMetrics();

  // Tab configuration
  const availableTabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: BarChart3,
      description: 'Comprehensive attendance analytics dashboard'
    },
    {
      id: 'field-analytics',
      label: 'Field Analytics',
      icon: TrendingUp,
      description: 'Field staff productivity and performance metrics'
    },
    {
      id: 'location-tracking',
      label: 'Location Tracking',
      icon: MapPin,
      description: 'GPS location logs and verification data'
    }
  ];

  const formatDateRange = () => {
    return `${dateRange.start.toLocaleDateString()} - ${dateRange.end.toLocaleDateString()}`;
  };

  return (
    <PageLayout>
      <PageHeader
        title="Attendance Analytics"
        description={`Comprehensive analytics dashboard for attendance tracking and field operations (${formatDateRange()})`}
        icon={BarChart3}
      >
        <div className="flex items-center gap-2">
          <Badge variant={metrics.overallAttendanceRate >= 80 ? "default" : "destructive"}>
            {metrics.overallAttendanceRate.toFixed(1)}% Attendance
          </Badge>
          {metrics.studentsAtRiskCount > 0 && (
            <Badge variant="destructive">
              {metrics.studentsAtRiskCount} At Risk
            </Badge>
          )}
        </div>
      </PageHeader>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Schools Active
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.activeSchools}/{metrics.totalSchools}</div>
            <p className="text-xs text-muted-foreground">
              Currently registered
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <CheckCircle className="h-4 w-4" />
              Attendance Rate
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.overallAttendanceRate.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Overall attendance
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <AlertTriangle className="h-4 w-4" />
              Students at Risk
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.studentsAtRiskCount}</div>
            <p className="text-xs text-muted-foreground">
              Need intervention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Navigation className="h-4 w-4" />
              Location Logs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics.totalLocationLogs}</div>
            <p className="text-xs text-muted-foreground">
              GPS verifications
            </p>
          </CardContent>
        </Card>
      </div>

      <ContentCard>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Tab Navigation */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <TabsList className="grid w-full grid-cols-3 sm:w-auto">
              {availableTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {/* Common Filters */}
            <div className="flex items-center gap-2">
              {canViewAllAnalytics && (
                <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Select School" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Schools</SelectItem>
                    {schools?.filter(school => school.id && school.id.trim() !== '').map((school) => (
                      <SelectItem key={school.id} value={school.id}>
                        {school.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              <Input
                type="date"
                value={dateRange.start.toISOString().split('T')[0]}
                onChange={(e) => setDateRange(prev => ({ ...prev, start: new Date(e.target.value) }))}
                className="w-40"
              />
              
              <Input
                type="date"
                value={dateRange.end.toISOString().split('T')[0]}
                onChange={(e) => setDateRange(prev => ({ ...prev, end: new Date(e.target.value) }))}
                className="w-40"
              />

              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>

          {/* Tab Content */}
          <TabsContent value="overview" className="mt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Attendance Analytics Dashboard</h3>
                  <p className="text-sm text-muted-foreground">
                    Comprehensive view of attendance patterns, trends, and student performance metrics
                  </p>
                </div>
              </div>
              
              {/* Show risk alert if students need intervention */}
              {metrics.studentsAtRiskCount > 0 && (
                <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center text-red-800">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    <span className="font-medium">
                      {metrics.studentsAtRiskCount} student(s) at high risk require immediate intervention
                    </span>
                  </div>
                </div>
              )}
              
              {/* Embed the existing AttendanceAnalyticsDashboard component */}
              <div className="border rounded-lg">
                <AttendanceAnalyticsDashboard />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="field-analytics" className="mt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">Field Staff Analytics</h3>
                  <p className="text-sm text-muted-foreground">
                    Track field staff productivity, school visit patterns, and impact measurement data
                  </p>
                </div>
              </div>
              
              {/* Embed the existing FieldReportingAnalytics component */}
              <div className="border rounded-lg">
                <FieldReportingAnalytics />
              </div>
            </div>
          </TabsContent>

          <TabsContent value="location-tracking" className="mt-0">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">GPS Location Tracking</h3>
                  <p className="text-sm text-muted-foreground">
                    Monitor field staff locations, verify school visits, and track movement patterns
                  </p>
                </div>
              </div>
              
              {/* Location tracking summary */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Today's Check-ins</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {locationLogs?.filter(log => 
                        new Date(log.check_in_time).toDateString() === new Date().toDateString()
                      ).length || 0}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Verified Locations</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {locationLogs?.filter(log => log.location_verified).length || 0}
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Average Accuracy</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">
                      {locationLogs?.length ? 
                        (locationLogs.reduce((sum, log) => sum + log.location_accuracy, 0) / locationLogs.length).toFixed(0) + 'm'
                        : '0m'
                      }
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Embed the existing LocationLogs component */}
              <div className="border rounded-lg">
                <LocationLogs showFilters={true} />
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </ContentCard>
    </PageLayout>
  );
};

export default UnifiedAttendanceAnalytics;
