import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type AttendanceSession = Database['public']['Tables']['attendance_sessions']['Row'];
type AttendanceSessionInsert = Database['public']['Tables']['attendance_sessions']['Insert'];
type SessionType = Database['public']['Enums']['session_type'];

export interface CreateSessionData {
  session_name: string;
  session_type: SessionType;
  school_id: string;
  grade_level?: number;
  subject?: string;
  teacher_name?: string;
  session_date: string;
  start_time: string;
  end_time?: string;
  planned_duration_minutes?: number;
  location?: string;
  max_capacity?: number;
  round_tables_count?: number;
  session_description?: string;
  learning_objectives?: string[];
  materials_needed?: string[];
}

// Hook to fetch attendance sessions
export const useAttendanceSessions = (
  schoolId?: string,
  dateRange?: { start: Date; end: Date },
  options?: { enabled?: boolean }
) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['attendance-sessions', schoolId, dateRange, profile?.id],
    queryFn: async () => {
      console.log('Fetching attendance sessions...', { schoolId, dateRange });

      let query = supabase
        .from('attendance_sessions')
        .select(`
          *,
          school:schools(name, location_coordinates),
          facilitator:profiles!attendance_sessions_facilitator_id_fkey(name),
          student_attendance!student_attendance_session_id_fkey(
            id,
            student_id,
            attendance_status,
            student:students(first_name, last_name)
          )
        `)
        .order('session_date', { ascending: false })
        .order('start_time', { ascending: false });

      if (schoolId && schoolId !== 'all') {
        query = query.eq('school_id', schoolId);
      }

      if (dateRange) {
        query = query
          .gte('session_date', dateRange.start.toISOString().split('T')[0])
          .lte('session_date', dateRange.end.toISOString().split('T')[0]);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching attendance sessions:', error);
        throw error;
      }

      console.log('✅ Attendance sessions fetched successfully:', data?.length || 0, 'sessions');
      return data || [];
    },
    enabled: (options?.enabled !== false) && !!profile?.id,
    staleTime: 30 * 1000, // 30 seconds
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};

// Hook to fetch a single attendance session
export const useAttendanceSession = (sessionId: string) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['attendance-session', sessionId, profile?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('attendance_sessions')
        .select(`
          *,
          school:schools(name, location_coordinates),
          facilitator:profiles!attendance_sessions_facilitator_id_fkey(name),
          student_attendance(
            *,
            student:students(
              id,
              first_name,
              last_name,
              student_number,
              grade_level,
              gender
            )
          )
        `)
        .eq('id', sessionId)
        .single();

      if (error) {
        console.error('Error fetching attendance session:', error);
        throw error;
      }

      return data;
    },
    enabled: !!profile?.id && !!sessionId,
  });
};

// Hook to create attendance session
export const useCreateAttendanceSession = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (sessionData: CreateSessionData) => {
      console.log('Creating attendance session:', sessionData);

      // Try RPC function first
      try {
        const { data, error } = await supabase.rpc('create_attendance_session', {
          p_session_name: sessionData.session_name,
          p_session_type: sessionData.session_type,
          p_school_id: sessionData.school_id,
          p_grade_level: sessionData.grade_level,
          p_subject: sessionData.subject,
          p_teacher_name: sessionData.teacher_name,
          p_session_date: sessionData.session_date,
          p_start_time: sessionData.start_time,
          p_end_time: sessionData.end_time,
          p_planned_duration_minutes: sessionData.planned_duration_minutes,
          p_location: sessionData.location,
          p_max_capacity: sessionData.max_capacity,
          p_round_tables_count: sessionData.round_tables_count || 0,
          p_session_description: sessionData.session_description,
          p_learning_objectives: sessionData.learning_objectives,
          p_materials_needed: sessionData.materials_needed,
        });

        if (!error && data) {
          console.log('✅ Session created via RPC successfully:', data);
          return data;
        }
      } catch (rpcError) {
        console.warn('RPC function failed, falling back to direct insert:', rpcError);
      }

      // Fallback to direct insert
      const { data, error } = await supabase
        .from('attendance_sessions')
        .insert({
          session_name: sessionData.session_name,
          session_type: sessionData.session_type,
          school_id: sessionData.school_id,
          grade_level: sessionData.grade_level,
          subject: sessionData.subject,
          teacher_name: sessionData.teacher_name,
          session_date: sessionData.session_date,
          start_time: sessionData.start_time,
          end_time: sessionData.end_time,
          planned_duration_minutes: sessionData.planned_duration_minutes,
          location: sessionData.location,
          max_capacity: sessionData.max_capacity,
          round_tables_count: sessionData.round_tables_count || 0,
          session_description: sessionData.session_description,
          learning_objectives: sessionData.learning_objectives,
          materials_needed: sessionData.materials_needed,
          facilitator_id: (await supabase.auth.getUser()).data.user?.id,
          created_by: (await supabase.auth.getUser()).data.user?.id,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating attendance session:', error);
        throw error;
      }

      console.log('✅ Session created via direct insert successfully:', data);
      return data.id;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
      toast({
        title: 'Success',
        description: 'Attendance session created successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to create attendance session:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to create attendance session. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to delete attendance session
export const useDeleteAttendanceSession = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (sessionId: string) => {
      console.log('Deleting attendance session:', sessionId);

      const { error } = await supabase
        .from('attendance_sessions')
        .delete()
        .eq('id', sessionId);

      if (error) {
        console.error('Error deleting attendance session:', error);
        throw error;
      }

      console.log('✅ Session deleted successfully');
      return sessionId;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
      toast({
        title: 'Success',
        description: 'Session deleted successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to delete attendance session:', error);
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete session. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to update attendance session
export const useUpdateAttendanceSession = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ sessionId, updates }: { sessionId: string; updates: Partial<AttendanceSessionInsert> }) => {
      const { data, error } = await supabase
        .from('attendance_sessions')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', sessionId)
        .select()
        .single();

      if (error) {
        console.error('Error updating attendance session:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
      queryClient.invalidateQueries({ queryKey: ['attendance-session', data.id] });
      toast({
        title: 'Success',
        description: 'Attendance session updated successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to update attendance session:', error);
      toast({
        title: 'Error',
        description: 'Failed to update attendance session. Please try again.',
        variant: 'destructive',
      });
    },
  });
};
