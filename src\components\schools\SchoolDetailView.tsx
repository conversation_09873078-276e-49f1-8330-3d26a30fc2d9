
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { School, Users, MapPin, Phone, Mail, User, Calendar, Building, Edit } from 'lucide-react';
import { School as SchoolType } from '@/types/school';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface SchoolDetailViewProps {
  school: SchoolType;
  currentUser: Profile | null;
  onEdit?: () => void;
}

const SchoolDetailView = ({ school, currentUser, onEdit }: SchoolDetailViewProps) => {
  const canEdit = currentUser?.role === 'admin' || currentUser?.role === 'program_officer';

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div className="flex items-center space-x-3">
          <School className="h-8 w-8 text-purple-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">{school.name}</h1>
            {school.code && <p className="text-gray-600">{school.code}</p>}
          </div>
        </div>
        {canEdit && (
          <Button onClick={onEdit} variant="outline">
            <Edit className="h-4 w-4 mr-2" />
            Edit School
          </Button>
        )}
      </div>

      <div className="space-y-6">
        {/* Basic Information */}
        <Card>
          <CardHeader>
            <CardTitle>School Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Status and Type */}
            <div className="flex space-x-4">
              <Badge variant={
                school.school_type === 'primary' ? 'default' :
                school.school_type === 'secondary' ? 'secondary' :
                'outline'
              }>
                {school.school_type}
              </Badge>
              {school.ownership_type && (
                <Badge variant="outline">
                  {school.ownership_type}
                </Badge>
              )}
            </div>

            <Separator />

            {/* Academic Structure */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Academic Structure</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <Users className="h-6 w-6 text-blue-600 mx-auto mb-1" />
                  <div className="text-2xl font-bold text-gray-900">{school.student_count ?? 'N/A'}</div>
                  <div className="text-sm text-gray-600">Students</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <User className="h-6 w-6 text-green-600 mx-auto mb-1" />
                  <div className="text-2xl font-bold text-gray-900">{school.teacher_count ?? 'N/A'}</div>
                  <div className="text-sm text-gray-600">Teachers</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <Building className="h-6 w-6 text-purple-600 mx-auto mb-1" />
                  <div className="text-2xl font-bold text-gray-900">{school.classes_count || 'N/A'}</div>
                  <div className="text-sm text-gray-600">Classes</div>
                </div>
                <div className="text-center p-3 bg-gray-50 rounded-lg">
                  <School className="h-6 w-6 text-orange-600 mx-auto mb-1" />
                  <div className="text-2xl font-bold text-gray-900">{school.streams_per_class || 'N/A'}</div>
                  <div className="text-sm text-gray-600">Streams</div>
                </div>
              </div>
            </div>

            <Separator />

            {/* Leadership */}
            {(school.head_teacher_name || school.deputy_head_teacher_name) && (
              <>
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">School Leadership</h3>
                  <div className="space-y-2">
                    {school.head_teacher_name && (
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-gray-500 mr-2" />
                        <span className="text-sm text-gray-600">Head Teacher:</span>
                        <span className="ml-2 font-medium">{school.head_teacher_name}</span>
                      </div>
                    )}
                    {school.deputy_head_teacher_name && (
                      <div className="flex items-center">
                        <User className="h-4 w-4 text-gray-500 mr-2" />
                        <span className="text-sm text-gray-600">Deputy Head Teacher:</span>
                        <span className="ml-2 font-medium">{school.deputy_head_teacher_name}</span>
                      </div>
                    )}
                  </div>
                </div>
                <Separator />
              </>
            )}

            {/* Location Details */}
            <div>
              <h3 className="font-semibold text-gray-900 mb-3">Location & Contact</h3>
              <div className="space-y-3">
                <div className="flex items-start">
                  <MapPin className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                  <div>
                    <div className="font-medium">{[school.district, school.sub_county].filter(Boolean).join(', ') || 'Location not specified'}</div>
                    {school.location_description && (
                      <div className="text-sm text-gray-600 mt-1">{school.location_description}</div>
                    )}
                  </div>
                </div>
                
                {school.contact_phone && (
                  <div className="flex items-center">
                    <Phone className="h-4 w-4 text-gray-500 mr-2" />
                    <span>{school.contact_phone}</span>
                  </div>
                )}
                
                {school.email && (
                  <div className="flex items-center">
                    <Mail className="h-4 w-4 text-gray-500 mr-2" />
                    <span>{school.email}</span>
                  </div>
                )}

                {school.year_established && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    <span>Established: {school.year_established}</span>
                  </div>
                )}
                {school.date_joined_ilead && (
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    <span>Joined iLead: {new Date(school.date_joined_ilead).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Infrastructure Notes */}
            {school.infrastructure_notes && (
              <>
                <Separator />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Infrastructure</h3>
                  <p className="text-gray-700">{school.infrastructure_notes}</p>
                </div>
              </>
            )}

            {/* Additional Location Info */}
            {(school.nearest_health_center || school.distance_to_main_road) && (
              <>
                <Separator />
                <div>
                  <h3 className="font-semibold text-gray-900 mb-3">Accessibility</h3>
                  <div className="space-y-2">
                    {school.nearest_health_center && (
                      <div className="text-sm">
                        <span className="text-gray-600">Nearest Health Center:</span>
                        <span className="ml-2">{school.nearest_health_center}</span>
                      </div>
                    )}
                    {school.distance_to_main_road && (
                      <div className="text-sm">
                        <span className="text-gray-600">Distance to Main Road:</span>
                        <span className="ml-2">{school.distance_to_main_road}</span>
                      </div>
                    )}
                  </div>
                </div>
              </>
            )}
          </CardContent>
        </Card>


      </div>
    </div>
  );
};

export default SchoolDetailView;
