-- Update field_activity_type enum to only include approved activity types
-- Migration 028: Remove unused activity types and keep only 5 approved types

-- First, update any existing records that use the deprecated activity types
-- Map leadership_training to round_table_session (most similar)
UPDATE field_reports 
SET activity_type = 'round_table_session' 
WHERE activity_type = 'leadership_training';

-- Map community_engagement to other
UPDATE field_reports 
SET activity_type = 'other' 
WHERE activity_type = 'community_engagement';

-- Create a new enum with only the approved activity types
CREATE TYPE field_activity_type_new AS ENUM (
    'round_table_session',
    'school_visit', 
    'meeting',
    'assessment',
    'other'
);

-- Update the field_reports table to use the new enum
ALTER TABLE field_reports 
ALTER COLUMN activity_type TYPE field_activity_type_new 
USING activity_type::text::field_activity_type_new;

-- Update the field_staff_checkout function parameter type
-- First drop the function
DROP FUNCTION IF EXISTS field_staff_checkout(
    p_attendance_id UUID,
    p_activity_type field_activity_type,
    p_latitude DOUBLE PRECISION,
    p_longitude DOUBLE PRECISION,
    p_accuracy DOUBLE PRECISION,
    p_address TEXT,
    p_notes TEXT,
    p_round_table_sessions INTEGER,
    p_total_students INTEGER,
    p_students_per_session INTEGER,
    p_activities_conducted TEXT[],
    p_topics_covered TEXT[],
    p_challenges TEXT,
    p_wins TEXT,
    p_observations TEXT,
    p_lessons_learned TEXT,
    p_follow_up_required BOOLEAN,
    p_follow_up_actions TEXT,
    p_photos TEXT[],
    p_offline_sync BOOLEAN
);

-- Drop the old enum
DROP TYPE field_activity_type;

-- Rename the new enum to the original name
ALTER TYPE field_activity_type_new RENAME TO field_activity_type;

-- Recreate the field_staff_checkout function with the updated enum
CREATE OR REPLACE FUNCTION field_staff_checkout(
    p_attendance_id UUID,
    p_activity_type field_activity_type,
    p_latitude DOUBLE PRECISION DEFAULT NULL,
    p_longitude DOUBLE PRECISION DEFAULT NULL,
    p_accuracy DOUBLE PRECISION DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    p_round_table_sessions INTEGER DEFAULT 0,
    p_total_students INTEGER DEFAULT 0,
    p_students_per_session INTEGER DEFAULT 8,
    p_activities_conducted TEXT[] DEFAULT '{}',
    p_topics_covered TEXT[] DEFAULT '{}',
    p_challenges TEXT DEFAULT NULL,
    p_wins TEXT DEFAULT NULL,
    p_observations TEXT DEFAULT NULL,
    p_lessons_learned TEXT DEFAULT NULL,
    p_follow_up_required BOOLEAN DEFAULT FALSE,
    p_follow_up_actions TEXT DEFAULT NULL,
    p_photos TEXT[] DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT FALSE
)
RETURNS TEXT
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_record RECORD;
    report_title TEXT;
    result_message TEXT;
BEGIN
    -- Get the attendance record
    SELECT * INTO attendance_record
    FROM field_staff_attendance
    WHERE id = p_attendance_id AND staff_id = auth.uid();

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Attendance record not found or access denied';
    END IF;

    -- Check if already checked out
    IF attendance_record.check_out_time IS NOT NULL THEN
        RAISE EXCEPTION 'Already checked out at %', attendance_record.check_out_time;
    END IF;

    -- Generate report title
    report_title := CONCAT(
        'Field Report - ',
        CASE p_activity_type
            WHEN 'round_table_session' THEN 'Round Table Session'
            WHEN 'school_visit' THEN 'School Visit'
            WHEN 'meeting' THEN 'Meeting'
            WHEN 'assessment' THEN 'Assessment'
            WHEN 'other' THEN 'Other Activity'
            ELSE 'Field Activity'
        END,
        ' - ',
        TO_CHAR(CURRENT_DATE, 'YYYY-MM-DD')
    );

    -- Update attendance record with check-out information
    UPDATE field_staff_attendance
    SET 
        check_out_time = NOW(),
        check_out_latitude = p_latitude,
        check_out_longitude = p_longitude,
        check_out_accuracy = p_accuracy,
        check_out_address = p_address,
        notes = COALESCE(notes, '') || CASE WHEN notes IS NOT NULL AND notes != '' THEN E'\n' ELSE '' END || COALESCE(p_notes, ''),
        updated_at = NOW()
    WHERE id = p_attendance_id;

    -- Create field report
    INSERT INTO field_reports (
        attendance_id, staff_id, school_id, report_date, activity_type,
        round_table_sessions, total_students, students_per_session,
        activities_conducted, topics_covered, challenges,
        wins, observations, lessons_learned,
        follow_up_required, follow_up_actions, photos,
        notes, offline_sync,
        -- Legacy columns for compatibility
        title, description, report_type, reported_by,
        gps_coordinates, created_at, updated_at
    )
    VALUES (
        p_attendance_id, auth.uid(), attendance_record.school_id, CURRENT_DATE, p_activity_type,
        p_round_table_sessions, p_total_students, p_students_per_session,
        p_activities_conducted, p_topics_covered, p_challenges,
        p_wins, p_observations, p_lessons_learned,
        p_follow_up_required, p_follow_up_actions, p_photos,
        p_notes, p_offline_sync,
        -- Legacy columns
        report_title, 
        CONCAT('Field report for ', p_activity_type, ' activity on ', CURRENT_DATE),
        'departure',
        auth.uid(),
        CASE 
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL 
            THEN POINT(p_longitude, p_latitude) 
            ELSE NULL 
        END,
        NOW(), NOW()
    );

    result_message := CONCAT('Successfully checked out and created field report: ', report_title);
    
    RETURN result_message;
END;
$$;

-- Update comment for documentation
COMMENT ON TYPE field_activity_type IS 'Enum for field activity types: round_table_session, school_visit, meeting, assessment, other';

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION field_staff_checkout TO authenticated;
