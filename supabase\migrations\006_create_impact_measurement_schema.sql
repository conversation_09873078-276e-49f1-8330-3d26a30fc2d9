-- Impact Measurement & Outcomes Tracking Database Schema
-- This migration creates tables for comprehensive impact tracking

-- Create enums for impact measurement
CREATE TYPE assessment_type AS ENUM (
    'baseline', 
    'midterm', 
    'endline', 
    'quarterly', 
    'annual'
);

CREATE TYPE subject_type AS ENUM (
    'literacy', 
    'numeracy', 
    'english', 
    'mathematics', 
    'science', 
    'social_studies',
    'life_skills'
);

CREATE TYPE improvement_type AS ENUM (
    'infrastructure', 
    'equipment', 
    'resources', 
    'facilities', 
    'technology',
    'furniture',
    'utilities'
);

CREATE TYPE training_type AS ENUM (
    'pedagogy', 
    'subject_specific', 
    'technology', 
    'classroom_management', 
    'assessment',
    'inclusive_education',
    'leadership'
);

CREATE TYPE feedback_type AS ENUM (
    'student', 
    'parent', 
    'teacher', 
    'community_member',
    'school_administrator'
);

CREATE TYPE satisfaction_rating AS ENUM (
    'very_dissatisfied',
    'dissatisfied', 
    'neutral', 
    'satisfied', 
    'very_satisfied'
);

-- Student Learning Outcomes Tables

-- Students table for tracking individual students
CREATE TABLE students (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_number VARCHAR(50) UNIQUE,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    date_of_birth DATE,
    gender VARCHAR(10),
    grade_level INTEGER NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    enrollment_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active',
    guardian_name VARCHAR(200),
    guardian_contact VARCHAR(50),
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Student assessments for tracking learning outcomes
CREATE TABLE student_assessments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    assessment_type assessment_type NOT NULL,
    subject subject_type NOT NULL,
    grade_level INTEGER NOT NULL,
    pre_assessment_score DECIMAL(5,2),
    post_assessment_score DECIMAL(5,2),
    improvement_percentage DECIMAL(5,2),
    assessment_date DATE NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    assessor_name VARCHAR(100),
    notes TEXT,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Grade progression tracking
CREATE TABLE grade_progressions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    from_grade INTEGER NOT NULL,
    to_grade INTEGER NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    progression_date DATE NOT NULL,
    retention_status BOOLEAN DEFAULT false,
    dropout_status BOOLEAN DEFAULT false,
    dropout_reason TEXT,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- School Performance Improvement Tables

-- School infrastructure improvements tracking
CREATE TABLE school_infrastructure_improvements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_id UUID REFERENCES schools(id) NOT NULL,
    improvement_type improvement_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    before_condition TEXT,
    after_condition TEXT,
    before_photos JSONB DEFAULT '[]',
    after_photos JSONB DEFAULT '[]',
    improvement_date DATE NOT NULL,
    completion_date DATE,
    cost_estimate DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    funding_source VARCHAR(255),
    contractor_details TEXT,
    impact_description TEXT,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- School attendance tracking
CREATE TABLE school_attendance_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_id UUID REFERENCES schools(id) NOT NULL,
    record_date DATE NOT NULL,
    total_enrolled INTEGER NOT NULL,
    total_present INTEGER NOT NULL,
    attendance_rate DECIMAL(5,2) NOT NULL,
    grade_level INTEGER,
    gender VARCHAR(10),
    weather_condition VARCHAR(50),
    special_events TEXT,
    recorded_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teacher-to-student ratio tracking
CREATE TABLE teacher_student_ratios (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_id UUID REFERENCES schools(id) NOT NULL,
    record_date DATE NOT NULL,
    total_teachers INTEGER NOT NULL,
    total_students INTEGER NOT NULL,
    ratio_value DECIMAL(5,2) NOT NULL,
    grade_level INTEGER,
    subject subject_type,
    qualified_teachers INTEGER,
    notes TEXT,
    recorded_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teacher Training Effectiveness Tables

-- Teacher training programs
CREATE TABLE teacher_training_programs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    program_name VARCHAR(255) NOT NULL,
    training_type training_type NOT NULL,
    description TEXT,
    duration_hours INTEGER,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    facilitator_name VARCHAR(100),
    facilitator_organization VARCHAR(200),
    target_participants INTEGER,
    actual_participants INTEGER,
    training_materials JSONB DEFAULT '[]',
    venue VARCHAR(255),
    cost DECIMAL(10,2),
    funding_source VARCHAR(255),
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Teacher training participation
CREATE TABLE teacher_training_participation (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    training_program_id UUID REFERENCES teacher_training_programs(id) NOT NULL,
    teacher_name VARCHAR(100) NOT NULL,
    teacher_id VARCHAR(50),
    school_id UUID REFERENCES schools(id) NOT NULL,
    attendance_percentage DECIMAL(5,2),
    pre_training_score DECIMAL(5,2),
    post_training_score DECIMAL(5,2),
    improvement_score DECIMAL(5,2),
    certification_received BOOLEAN DEFAULT false,
    certification_date DATE,
    feedback_rating satisfaction_rating,
    feedback_comments TEXT,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Classroom observation records
CREATE TABLE classroom_observations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    teacher_name VARCHAR(100) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    observation_date DATE NOT NULL,
    observer_name VARCHAR(100) NOT NULL,
    subject subject_type NOT NULL,
    grade_level INTEGER NOT NULL,
    lesson_preparation_score INTEGER CHECK (lesson_preparation_score >= 1 AND lesson_preparation_score <= 5),
    teaching_methods_score INTEGER CHECK (teaching_methods_score >= 1 AND teaching_methods_score <= 5),
    student_engagement_score INTEGER CHECK (student_engagement_score >= 1 AND student_engagement_score <= 5),
    classroom_management_score INTEGER CHECK (classroom_management_score >= 1 AND classroom_management_score <= 5),
    assessment_methods_score INTEGER CHECK (assessment_methods_score >= 1 AND assessment_methods_score <= 5),
    overall_score DECIMAL(3,1),
    strengths TEXT,
    areas_for_improvement TEXT,
    recommendations TEXT,
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Beneficiary Feedback Tables

-- Feedback surveys
CREATE TABLE beneficiary_feedback_surveys (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    survey_title VARCHAR(255) NOT NULL,
    survey_description TEXT,
    target_audience feedback_type NOT NULL,
    school_id UUID REFERENCES schools(id),
    survey_questions JSONB NOT NULL,
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    is_active BOOLEAN DEFAULT true,
    response_count INTEGER DEFAULT 0,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Survey responses
CREATE TABLE survey_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    survey_id UUID REFERENCES beneficiary_feedback_surveys(id) NOT NULL,
    respondent_type feedback_type NOT NULL,
    school_id UUID REFERENCES schools(id),
    respondent_name VARCHAR(100),
    respondent_contact VARCHAR(50),
    responses JSONB NOT NULL,
    overall_satisfaction satisfaction_rating,
    additional_comments TEXT,
    response_date DATE NOT NULL,
    location_coordinates POINT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Longitudinal Tracking Tables

-- Student cohorts for longitudinal analysis
CREATE TABLE student_cohorts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    cohort_name VARCHAR(255) NOT NULL,
    cohort_year INTEGER NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    starting_grade INTEGER NOT NULL,
    initial_student_count INTEGER NOT NULL,
    current_student_count INTEGER NOT NULL,
    description TEXT,
    tracking_start_date DATE NOT NULL,
    tracking_end_date DATE,
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Longitudinal student tracking
CREATE TABLE longitudinal_student_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    student_id UUID REFERENCES students(id) NOT NULL,
    cohort_id UUID REFERENCES student_cohorts(id) NOT NULL,
    tracking_year INTEGER NOT NULL,
    grade_level INTEGER NOT NULL,
    attendance_rate DECIMAL(5,2),
    academic_performance_score DECIMAL(5,2),
    literacy_level VARCHAR(50),
    numeracy_level VARCHAR(50),
    behavioral_assessment TEXT,
    health_status VARCHAR(100),
    family_situation TEXT,
    risk_factors JSONB DEFAULT '[]',
    interventions_received JSONB DEFAULT '[]',
    tracking_date DATE NOT NULL,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Impact indicators for comprehensive tracking
CREATE TABLE impact_indicators (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    indicator_name VARCHAR(255) NOT NULL,
    indicator_category VARCHAR(100) NOT NULL,
    description TEXT,
    measurement_unit VARCHAR(50),
    target_value DECIMAL(10,2),
    baseline_value DECIMAL(10,2),
    current_value DECIMAL(10,2),
    school_id UUID REFERENCES schools(id),
    measurement_frequency VARCHAR(50),
    last_measured_date DATE,
    next_measurement_date DATE,
    data_source VARCHAR(255),
    responsible_person VARCHAR(100),
    is_active BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Impact measurements for tracking progress
CREATE TABLE impact_measurements (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    indicator_id UUID REFERENCES impact_indicators(id) NOT NULL,
    measurement_value DECIMAL(10,2) NOT NULL,
    measurement_date DATE NOT NULL,
    measurement_period VARCHAR(50),
    data_quality_score INTEGER CHECK (data_quality_score >= 1 AND data_quality_score <= 5),
    verification_status BOOLEAN DEFAULT false,
    verified_by UUID REFERENCES profiles(id),
    verification_date DATE,
    notes TEXT,
    supporting_documents JSONB DEFAULT '[]',
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_students_school_id ON students(school_id);
CREATE INDEX idx_students_grade_level ON students(grade_level);
CREATE INDEX idx_student_assessments_student_id ON student_assessments(student_id);
CREATE INDEX idx_student_assessments_school_id ON student_assessments(school_id);
CREATE INDEX idx_student_assessments_date ON student_assessments(assessment_date);
CREATE INDEX idx_grade_progressions_student_id ON grade_progressions(student_id);
CREATE INDEX idx_infrastructure_improvements_school_id ON school_infrastructure_improvements(school_id);
CREATE INDEX idx_attendance_records_school_id ON school_attendance_records(school_id);
CREATE INDEX idx_attendance_records_date ON school_attendance_records(record_date);
CREATE INDEX idx_teacher_ratios_school_id ON teacher_student_ratios(school_id);
CREATE INDEX idx_training_participation_school_id ON teacher_training_participation(school_id);
CREATE INDEX idx_classroom_observations_school_id ON classroom_observations(school_id);
CREATE INDEX idx_survey_responses_survey_id ON survey_responses(survey_id);
CREATE INDEX idx_longitudinal_tracking_student_id ON longitudinal_student_tracking(student_id);
CREATE INDEX idx_impact_measurements_indicator_id ON impact_measurements(indicator_id);
CREATE INDEX idx_impact_measurements_date ON impact_measurements(measurement_date);

-- Create triggers for updated_at columns
CREATE TRIGGER update_students_updated_at
    BEFORE UPDATE ON students
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_assessments_updated_at
    BEFORE UPDATE ON student_assessments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_infrastructure_improvements_updated_at
    BEFORE UPDATE ON school_infrastructure_improvements
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_programs_updated_at
    BEFORE UPDATE ON teacher_training_programs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_training_participation_updated_at
    BEFORE UPDATE ON teacher_training_participation
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_feedback_surveys_updated_at
    BEFORE UPDATE ON beneficiary_feedback_surveys
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_cohorts_updated_at
    BEFORE UPDATE ON student_cohorts
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_impact_indicators_updated_at
    BEFORE UPDATE ON impact_indicators
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE students ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE grade_progressions ENABLE ROW LEVEL SECURITY;
ALTER TABLE school_infrastructure_improvements ENABLE ROW LEVEL SECURITY;
ALTER TABLE school_attendance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_student_ratios ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_training_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE teacher_training_participation ENABLE ROW LEVEL SECURITY;
ALTER TABLE classroom_observations ENABLE ROW LEVEL SECURITY;
ALTER TABLE beneficiary_feedback_surveys ENABLE ROW LEVEL SECURITY;
ALTER TABLE survey_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_cohorts ENABLE ROW LEVEL SECURITY;
ALTER TABLE longitudinal_student_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE impact_indicators ENABLE ROW LEVEL SECURITY;
ALTER TABLE impact_measurements ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Students table
CREATE POLICY "Users can view students from their assigned schools" ON students
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "Users can insert students" ON students
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Users can update students" ON students
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        created_by = auth.uid()
    );

-- RLS Policies for Student Assessments
CREATE POLICY "Users can view assessments from their schools" ON student_assessments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        created_by = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "Users can insert assessments" ON student_assessments
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Users can update their assessments" ON student_assessments
    FOR UPDATE USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- RLS Policies for School Infrastructure Improvements
CREATE POLICY "Users can view infrastructure improvements" ON school_infrastructure_improvements
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        created_by = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "Users can insert infrastructure improvements" ON school_infrastructure_improvements
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

-- RLS Policies for Teacher Training Programs
CREATE POLICY "Users can view training programs" ON teacher_training_programs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Program officers and admins can manage training programs" ON teacher_training_programs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- RLS Policies for Survey Responses
CREATE POLICY "Users can view survey responses" ON survey_responses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "Anyone can insert survey responses" ON survey_responses
    FOR INSERT WITH CHECK (true);

-- Generic policies for other tables (can be refined based on specific needs)
CREATE POLICY "Users can view their data" ON grade_progressions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR created_by = auth.uid()
    );

CREATE POLICY "Users can insert data" ON grade_progressions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );
