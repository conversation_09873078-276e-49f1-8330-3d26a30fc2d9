import { useQuery, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

interface ReportParams {
  schoolId?: string;
  studentId?: string;
  startDate: Date;
  endDate: Date;
  reportType: 'individual' | 'class' | 'school' | 'funder';
  gradeLevel?: number;
  sessionType?: string;
}

interface IndividualStudentReport {
  student: {
    id: string;
    first_name: string;
    last_name: string;
    student_number: string;
    grade_level: number;
    gender: string;
    guardian_name?: string;
    guardian_contact?: string;
  };
  school: {
    name: string;
    location: string;
  };
  period: {
    start_date: string;
    end_date: string;
    total_days: number;
  };
  attendance_summary: {
    total_sessions: number;
    sessions_attended: number;
    sessions_absent: number;
    sessions_late: number;
    sessions_excused: number;
    attendance_rate: number;
    punctuality_rate: number;
    consecutive_absences: number;
    longest_absence_streak: number;
  };
  session_breakdown: {
    [sessionType: string]: {
      total: number;
      attended: number;
      rate: number;
    };
  };
  participation_scores: {
    average_score: number;
    sessions_with_scores: number;
    score_trend: 'improving' | 'declining' | 'stable';
  };
  attendance_pattern: Array<{
    date: string;
    session_name: string;
    status: string;
    participation_score?: number;
    notes?: string;
  }>;
  recommendations: string[];
  risk_assessment: {
    level: 'low' | 'medium' | 'high' | 'critical';
    factors: string[];
    intervention_needed: boolean;
  };
}

interface ClassSummaryReport {
  class_info: {
    session_name: string;
    session_type: string;
    session_date: string;
    facilitator: string;
    school: string;
    grade_level?: number;
  };
  attendance_summary: {
    total_enrolled: number;
    present: number;
    absent: number;
    late: number;
    excused: number;
    attendance_rate: number;
  };
  student_details: Array<{
    student_name: string;
    student_number: string;
    status: string;
    check_in_time?: string;
    participation_score?: number;
    table_number?: number;
    notes?: string;
  }>;
  participation_analysis: {
    average_participation: number;
    high_performers: string[];
    needs_attention: string[];
  };
  table_assignments?: Array<{
    table_number: number;
    students: string[];
    participation_average: number;
  }>;
}

interface SchoolLevelReport {
  school: {
    name: string;
    location: string;
    total_students: number;
  };
  period: {
    start_date: string;
    end_date: string;
    total_sessions: number;
  };
  overall_statistics: {
    total_attendance_records: number;
    overall_attendance_rate: number;
    punctuality_rate: number;
    students_at_risk: number;
  };
  grade_breakdown: Array<{
    grade: number;
    total_students: number;
    attendance_rate: number;
    sessions_conducted: number;
  }>;
  session_type_breakdown: Array<{
    session_type: string;
    total_sessions: number;
    average_attendance: number;
    total_participants: number;
  }>;
  trends: Array<{
    month: string;
    attendance_rate: number;
    total_sessions: number;
  }>;
  top_performers: Array<{
    student_name: string;
    grade: number;
    attendance_rate: number;
  }>;
  students_needing_intervention: Array<{
    student_name: string;
    grade: number;
    attendance_rate: number;
    risk_level: string;
    consecutive_absences: number;
  }>;
}

interface FunderReport {
  organization: {
    name: string;
    reporting_period: string;
    schools_covered: number;
    total_beneficiaries: number;
  };
  program_impact: {
    total_sessions_conducted: number;
    total_attendance_records: number;
    overall_attendance_rate: number;
    student_retention_rate: number;
    program_completion_rate: number;
  };
  demographic_breakdown: {
    by_gender: Array<{
      gender: string;
      count: number;
      attendance_rate: number;
    }>;
    by_grade: Array<{
      grade: number;
      count: number;
      attendance_rate: number;
    }>;
    by_school: Array<{
      school_name: string;
      students: number;
      attendance_rate: number;
      sessions_conducted: number;
    }>;
  };
  leadership_program_impact: {
    total_leadership_sessions: number;
    students_participated: number;
    average_participation_score: number;
    leadership_skills_improvement: number;
    round_table_effectiveness: number;
  };
  attendance_correlation: {
    attendance_vs_participation: number;
    attendance_vs_completion: number;
    high_attendance_outcomes: string[];
    low_attendance_challenges: string[];
  };
  success_stories: Array<{
    student_profile: string;
    improvement_metrics: string;
    impact_description: string;
  }>;
  challenges_and_recommendations: {
    key_challenges: string[];
    mitigation_strategies: string[];
    recommendations: string[];
  };
}

// Hook to generate individual student report
export const useIndividualStudentReport = (params: ReportParams) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['individual-student-report', params],
    queryFn: async (): Promise<IndividualStudentReport> => {
      if (!params.studentId) throw new Error('Student ID is required for individual report');

      // Fetch student basic info
      const { data: student, error: studentError } = await supabase
        .from('students')
        .select(`
          *,
          school:schools(name, location)
        `)
        .eq('id', params.studentId)
        .maybeSingle();

      if (studentError) throw studentError;
      if (!student) throw new Error('Student not found');

      // Fetch attendance records for the period
      const { data: attendanceRecords, error: attendanceError } = await supabase
        .from('student_attendance')
        .select(`
          *,
          session:attendance_sessions(
            session_name,
            session_type,
            session_date,
            start_time
          )
        `)
        .eq('student_id', params.studentId)
        .gte('recorded_at', params.startDate.toISOString())
        .lte('recorded_at', params.endDate.toISOString())
        .order('recorded_at', { ascending: true });

      if (attendanceError) throw attendanceError;

      // Calculate statistics
      const totalSessions = attendanceRecords?.length || 0;
      const sessionsAttended = attendanceRecords?.filter(r => ['present', 'late'].includes(r.attendance_status)).length || 0;
      const sessionsAbsent = attendanceRecords?.filter(r => r.attendance_status === 'absent').length || 0;
      const sessionsLate = attendanceRecords?.filter(r => r.attendance_status === 'late').length || 0;
      const sessionsExcused = attendanceRecords?.filter(r => r.attendance_status === 'excused').length || 0;

      const attendanceRate = totalSessions > 0 ? (sessionsAttended / totalSessions) * 100 : 0;
      const punctualityRate = sessionsAttended > 0 ? ((sessionsAttended - sessionsLate) / sessionsAttended) * 100 : 0;

      // Calculate consecutive absences
      let consecutiveAbsences = 0;
      let longestAbsenceStreak = 0;
      let currentStreak = 0;

      attendanceRecords?.forEach(record => {
        if (record.attendance_status === 'absent') {
          currentStreak++;
          longestAbsenceStreak = Math.max(longestAbsenceStreak, currentStreak);
        } else {
          currentStreak = 0;
        }
      });

      // Get current consecutive absences (from the end)
      for (let i = (attendanceRecords?.length || 0) - 1; i >= 0; i--) {
        if (attendanceRecords?.[i].attendance_status === 'absent') {
          consecutiveAbsences++;
        } else {
          break;
        }
      }

      // Session type breakdown
      const sessionBreakdown: { [key: string]: { total: number; attended: number; rate: number } } = {};
      attendanceRecords?.forEach(record => {
        const sessionType = record.session?.session_type || 'unknown';
        if (!sessionBreakdown[sessionType]) {
          sessionBreakdown[sessionType] = { total: 0, attended: 0, rate: 0 };
        }
        sessionBreakdown[sessionType].total++;
        if (['present', 'late'].includes(record.attendance_status)) {
          sessionBreakdown[sessionType].attended++;
        }
      });

      Object.keys(sessionBreakdown).forEach(type => {
        const breakdown = sessionBreakdown[type];
        breakdown.rate = breakdown.total > 0 ? (breakdown.attended / breakdown.total) * 100 : 0;
      });

      // Participation scores
      const scoresWithValues = attendanceRecords?.filter(r => r.participation_score !== null) || [];
      const averageScore = scoresWithValues.length > 0 
        ? scoresWithValues.reduce((sum, r) => sum + (r.participation_score || 0), 0) / scoresWithValues.length 
        : 0;

      // Risk assessment
      let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';
      const riskFactors: string[] = [];
      
      if (attendanceRate < 60) {
        riskLevel = 'critical';
        riskFactors.push('Attendance rate below 60%');
      } else if (attendanceRate < 75) {
        riskLevel = 'high';
        riskFactors.push('Attendance rate below 75%');
      } else if (attendanceRate < 90) {
        riskLevel = 'medium';
        riskFactors.push('Attendance rate below 90%');
      }

      if (consecutiveAbsences >= 3) {
        riskFactors.push(`${consecutiveAbsences} consecutive absences`);
        if (riskLevel === 'low') riskLevel = 'medium';
      }

      if (longestAbsenceStreak >= 5) {
        riskFactors.push(`Longest absence streak: ${longestAbsenceStreak} sessions`);
        if (riskLevel === 'low' || riskLevel === 'medium') riskLevel = 'high';
      }

      // Generate recommendations
      const recommendations: string[] = [];
      if (attendanceRate < 90) {
        recommendations.push('Schedule parent/guardian meeting to discuss attendance concerns');
      }
      if (consecutiveAbsences >= 3) {
        recommendations.push('Immediate intervention required - contact family');
      }
      if (averageScore < 3 && scoresWithValues.length > 0) {
        recommendations.push('Consider additional support for class participation');
      }
      if (punctualityRate < 80) {
        recommendations.push('Address punctuality issues with student and family');
      }

      return {
        student: {
          id: student.id,
          first_name: student.first_name,
          last_name: student.last_name,
          student_number: student.student_number,
          grade_level: student.grade_level,
          gender: student.gender,
          guardian_name: student.guardian_name,
          guardian_contact: student.guardian_contact,
        },
        school: {
          name: student.school?.name || '',
          location: student.school?.location || '',
        },
        period: {
          start_date: params.startDate.toISOString().split('T')[0],
          end_date: params.endDate.toISOString().split('T')[0],
          total_days: Math.ceil((params.endDate.getTime() - params.startDate.getTime()) / (1000 * 60 * 60 * 24)),
        },
        attendance_summary: {
          total_sessions: totalSessions,
          sessions_attended: sessionsAttended,
          sessions_absent: sessionsAbsent,
          sessions_late: sessionsLate,
          sessions_excused: sessionsExcused,
          attendance_rate: attendanceRate,
          punctuality_rate: punctualityRate,
          consecutive_absences: consecutiveAbsences,
          longest_absence_streak: longestAbsenceStreak,
        },
        session_breakdown: sessionBreakdown,
        participation_scores: {
          average_score: averageScore,
          sessions_with_scores: scoresWithValues.length,
          score_trend: 'stable', // TODO: Calculate trend
        },
        attendance_pattern: attendanceRecords?.map(record => ({
          date: record.session?.session_date || '',
          session_name: record.session?.session_name || '',
          status: record.attendance_status,
          participation_score: record.participation_score || undefined,
          notes: record.behavior_notes || record.absence_reason || undefined,
        })) || [],
        recommendations,
        risk_assessment: {
          level: riskLevel,
          factors: riskFactors,
          intervention_needed: riskLevel === 'high' || riskLevel === 'critical',
        },
      };
    },
    enabled: !!profile?.id && !!params.studentId,
  });
};

// Hook to generate class summary report
export const useClassSummaryReport = (sessionId: string) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['class-summary-report', sessionId],
    queryFn: async (): Promise<ClassSummaryReport> => {
      // Fetch session info
      const { data: session, error: sessionError } = await supabase
        .from('attendance_sessions')
        .select(`
          *,
          school:schools(name),
          facilitator:profiles!attendance_sessions_facilitator_id_fkey(name)
        `)
        .eq('id', sessionId)
        .single();

      if (sessionError) throw sessionError;

      // Fetch attendance records for this session
      const { data: attendanceRecords, error: attendanceError } = await supabase
        .from('student_attendance')
        .select(`
          *,
          student:students(
            first_name,
            last_name,
            student_number
          )
        `)
        .eq('session_id', sessionId)
        .order('student(last_name)', { ascending: true });

      if (attendanceError) throw attendanceError;

      const totalEnrolled = attendanceRecords?.length || 0;
      const present = attendanceRecords?.filter(r => r.attendance_status === 'present').length || 0;
      const absent = attendanceRecords?.filter(r => r.attendance_status === 'absent').length || 0;
      const late = attendanceRecords?.filter(r => r.attendance_status === 'late').length || 0;
      const excused = attendanceRecords?.filter(r => r.attendance_status === 'excused').length || 0;

      const attendanceRate = totalEnrolled > 0 ? ((present + late) / totalEnrolled) * 100 : 0;

      // Participation analysis
      const recordsWithScores = attendanceRecords?.filter(r => r.participation_score !== null) || [];
      const averageParticipation = recordsWithScores.length > 0
        ? recordsWithScores.reduce((sum, r) => sum + (r.participation_score || 0), 0) / recordsWithScores.length
        : 0;

      const highPerformers = recordsWithScores
        .filter(r => (r.participation_score || 0) >= 4)
        .map(r => `${r.student?.first_name} ${r.student?.last_name}`)
        .filter(Boolean);

      const needsAttention = recordsWithScores
        .filter(r => (r.participation_score || 0) <= 2)
        .map(r => `${r.student?.first_name} ${r.student?.last_name}`)
        .filter(Boolean);

      // Table assignments (if applicable)
      const tableAssignments: Array<{
        table_number: number;
        students: string[];
        participation_average: number;
      }> = [];

      if (session.round_tables_count > 0) {
        for (let i = 1; i <= session.round_tables_count; i++) {
          const tableStudents = attendanceRecords?.filter(r => r.table_number === i) || [];
          const tableNames = tableStudents
            .map(r => `${r.student?.first_name} ${r.student?.last_name}`)
            .filter(Boolean);

          const tableScores = tableStudents.filter(r => r.participation_score !== null);
          const tableAverage = tableScores.length > 0
            ? tableScores.reduce((sum, r) => sum + (r.participation_score || 0), 0) / tableScores.length
            : 0;

          if (tableNames.length > 0) {
            tableAssignments.push({
              table_number: i,
              students: tableNames,
              participation_average: tableAverage,
            });
          }
        }
      }

      return {
        class_info: {
          session_name: session.session_name,
          session_type: session.session_type,
          session_date: session.session_date,
          facilitator: session.facilitator?.name || session.teacher_name || 'Not assigned',
          school: session.school?.name || '',
          grade_level: session.grade_level,
        },
        attendance_summary: {
          total_enrolled: totalEnrolled,
          present,
          absent,
          late,
          excused,
          attendance_rate: attendanceRate,
        },
        student_details: attendanceRecords?.map(record => ({
          student_name: `${record.student?.first_name} ${record.student?.last_name}`,
          student_number: record.student?.student_number || '',
          status: record.attendance_status,
          check_in_time: record.check_in_time || undefined,
          participation_score: record.participation_score || undefined,
          table_number: record.table_number || undefined,
          notes: record.behavior_notes || record.absence_reason || undefined,
        })) || [],
        participation_analysis: {
          average_participation: averageParticipation,
          high_performers: highPerformers,
          needs_attention: needsAttention,
        },
        table_assignments: tableAssignments.length > 0 ? tableAssignments : undefined,
      };
    },
    enabled: !!profile?.id && !!sessionId,
  });
};

// Hook to generate school-level report
export const useSchoolLevelReport = (params: ReportParams) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['school-level-report', params],
    queryFn: async (): Promise<SchoolLevelReport> => {
      if (!params.schoolId) throw new Error('School ID is required for school-level report');

      // Fetch school info
      const { data: school, error: schoolError } = await supabase
        .from('schools')
        .select('*')
        .eq('id', params.schoolId)
        .maybeSingle();

      if (schoolError) throw schoolError;
      if (!school) throw new Error('School not found');

      // Get total students in school
      const { data: students, error: studentsError } = await supabase
        .from('students')
        .select('id, grade_level')
        .eq('school_id', params.schoolId)
        .eq('status', 'active');

      if (studentsError) throw studentsError;

      // Get all sessions in the period
      const { data: sessions, error: sessionsError } = await supabase
        .from('attendance_sessions')
        .select('id, session_type, session_date')
        .eq('school_id', params.schoolId)
        .gte('session_date', params.startDate.toISOString().split('T')[0])
        .lte('session_date', params.endDate.toISOString().split('T')[0]);

      if (sessionsError) throw sessionsError;

      // Get all attendance records for the period
      const { data: attendanceRecords, error: attendanceError } = await supabase
        .from('student_attendance')
        .select(`
          *,
          student:students(grade_level, first_name, last_name),
          session:attendance_sessions(session_type, session_date)
        `)
        .eq('school_id', params.schoolId)
        .gte('recorded_at', params.startDate.toISOString())
        .lte('recorded_at', params.endDate.toISOString());

      if (attendanceError) throw attendanceError;

      // Calculate overall statistics
      const totalRecords = attendanceRecords?.length || 0;
      const attendedRecords = attendanceRecords?.filter(r => ['present', 'late'].includes(r.attendance_status)).length || 0;
      const punctualRecords = attendanceRecords?.filter(r => r.attendance_status === 'present').length || 0;

      const overallAttendanceRate = totalRecords > 0 ? (attendedRecords / totalRecords) * 100 : 0;
      const punctualityRate = attendedRecords > 0 ? (punctualRecords / attendedRecords) * 100 : 0;

      // Calculate students at risk (using simple logic for now)
      const studentAttendanceRates = new Map<string, { attended: number; total: number }>();
      attendanceRecords?.forEach(record => {
        const studentId = record.student_id;
        if (!studentAttendanceRates.has(studentId)) {
          studentAttendanceRates.set(studentId, { attended: 0, total: 0 });
        }
        const stats = studentAttendanceRates.get(studentId)!;
        stats.total++;
        if (['present', 'late'].includes(record.attendance_status)) {
          stats.attended++;
        }
      });

      const studentsAtRisk = Array.from(studentAttendanceRates.values())
        .filter(stats => stats.total > 0 && (stats.attended / stats.total) < 0.75)
        .length;

      // Grade breakdown
      const gradeStats = new Map<number, { total: number; records: number; attended: number; sessions: Set<string> }>();
      students?.forEach(student => {
        if (!gradeStats.has(student.grade_level)) {
          gradeStats.set(student.grade_level, { total: 0, records: 0, attended: 0, sessions: new Set() });
        }
        gradeStats.get(student.grade_level)!.total++;
      });

      attendanceRecords?.forEach(record => {
        const grade = record.student?.grade_level;
        if (grade && gradeStats.has(grade)) {
          const stats = gradeStats.get(grade)!;
          stats.records++;
          if (['present', 'late'].includes(record.attendance_status)) {
            stats.attended++;
          }
          if (record.session?.session_date) {
            stats.sessions.add(record.session.session_date);
          }
        }
      });

      const gradeBreakdown = Array.from(gradeStats.entries()).map(([grade, stats]) => ({
        grade,
        total_students: stats.total,
        attendance_rate: stats.records > 0 ? (stats.attended / stats.records) * 100 : 0,
        sessions_conducted: stats.sessions.size,
      }));

      // Session type breakdown
      const sessionTypeStats = new Map<string, { sessions: Set<string>; records: number; attended: number }>();
      attendanceRecords?.forEach(record => {
        const sessionType = record.session?.session_type || 'unknown';
        if (!sessionTypeStats.has(sessionType)) {
          sessionTypeStats.set(sessionType, { sessions: new Set(), records: 0, attended: 0 });
        }
        const stats = sessionTypeStats.get(sessionType)!;
        stats.records++;
        if (['present', 'late'].includes(record.attendance_status)) {
          stats.attended++;
        }
        if (record.session_id) {
          stats.sessions.add(record.session_id);
        }
      });

      const sessionTypeBreakdown = Array.from(sessionTypeStats.entries()).map(([sessionType, stats]) => ({
        session_type: sessionType,
        total_sessions: stats.sessions.size,
        average_attendance: stats.records > 0 ? (stats.attended / stats.records) * 100 : 0,
        total_participants: stats.records,
      }));

      return {
        school: {
          name: school.name,
          location: school.location || '',
          total_students: students?.length || 0,
        },
        period: {
          start_date: params.startDate.toISOString().split('T')[0],
          end_date: params.endDate.toISOString().split('T')[0],
          total_sessions: sessions?.length || 0,
        },
        overall_statistics: {
          total_attendance_records: totalRecords,
          overall_attendance_rate: overallAttendanceRate,
          punctuality_rate: punctualityRate,
          students_at_risk: studentsAtRisk,
        },
        grade_breakdown: gradeBreakdown,
        session_type_breakdown: sessionTypeBreakdown,
        trends: [], // TODO: Implement monthly trends
        top_performers: [], // TODO: Implement top performers
        students_needing_intervention: [], // TODO: Implement intervention list
      };
    },
    enabled: !!profile?.id && !!params.schoolId,
  });
};
