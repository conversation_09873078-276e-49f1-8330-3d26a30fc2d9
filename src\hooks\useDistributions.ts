
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';
import { DistributionSubmissionData } from './useDistributionForm';

// Type definitions for the hook
type BookDistribution = Database['public']['Functions']['get_book_distributions']['Returns'][0];
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type BookInventory = Database['public']['Functions']['get_book_inventory']['Returns'][0];
type AddDistributionArgs = Database['public']['Functions']['add_book_distribution']['Args'];

export const useDistributions = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Fetch book distributions
  const {
    data: distributions = [],
    isLoading: distributionsLoading,
    error: distributionsError
  } = useQuery({
    queryKey: ['distributions'],
    queryFn: async (): Promise<BookDistribution[]> => {
      const { data, error } = await supabase
        .rpc('get_book_distributions');

      if (error) {
        console.error('Error fetching distributions:', error);
        throw error;
      }
      return data || [];
    },
  });

  // Fetch schools
  const {
    data: schools = [],
    isLoading: schoolsLoading
  } = useQuery({
    queryKey: ['schools'],
    queryFn: async (): Promise<SchoolWithDivision[]> => {
      const { data, error } = await supabase
        .rpc('get_schools_with_divisions');

      if (error) {
        console.error('Error fetching schools:', error);
        throw error;
      }
      return data || [];
    },
  });

  // Fetch book inventory
  const {
    data: inventory = [],
    isLoading: inventoryLoading
  } = useQuery({
    queryKey: ['inventory'],
    queryFn: async (): Promise<BookInventory[]> => {
      const { data, error } = await supabase
        .rpc('get_book_inventory');

      if (error) {
        console.error('Error fetching inventory:', error);
        throw error;
      }
      return data || [];
    },
  });

  // Add distribution mutation
  const addDistributionMutation = useMutation({
    mutationFn: async (distributionData: DistributionSubmissionData): Promise<string> => {
      console.log('Adding distribution:', distributionData);

      const { data, error } = await supabase
        .rpc('add_book_distribution', {
          p_school_id: distributionData.school_id,
          p_inventory_id: distributionData.inventory_id,
          p_quantity: distributionData.quantity,
          p_supervisor_id: distributionData.supervisor_id,
          p_notes: distributionData.notes || '',
          p_book_title: distributionData.book_title,
          p_status: distributionData.status || 'completed',
          p_distribution_date: distributionData.distribution_date || new Date().toISOString().split('T')[0],
        });

      if (error) {
        console.error('Distribution creation error:', error);
        throw error;
      }

      console.log('Distribution created successfully:', data);
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['distributions'] });
      queryClient.invalidateQueries({ queryKey: ['inventory'] });
      toast({
        title: "Success",
        description: "Book distribution logged successfully",
      });
      console.log('Distribution mutation successful:', data);
    },
    onError: (error: Error) => {
      console.error('Distribution mutation error:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to log distribution",
        variant: "destructive",
      });
    },
  });

  return {
    distributions,
    schools,
    inventory,
    isLoading: distributionsLoading || schoolsLoading || inventoryLoading,
    error: distributionsError,
    addDistribution: addDistributionMutation.mutate,
    isAdding: addDistributionMutation.isPending,
  };
};
