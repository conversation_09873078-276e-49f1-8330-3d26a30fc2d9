import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { BookOpen, AlertCircle, Loader2, ChevronDown, ChevronUp, Package, Edit } from 'lucide-react';
import { useBookOperations, BookWithInventory } from '@/hooks/useBookOperations';
import { BookCondition, BookLanguage } from '@/types/book';
import { validateBookForm, validateField, type BookFormData } from '@/utils/bookValidation';
import { toast } from 'sonner';

interface EditBookModalProps {
  isOpen: boolean;
  onClose: () => void;
  book: BookWithInventory | null;
}

// BookFormData is now imported from validation utils

const EditBookModal: React.FC<EditBookModalProps> = ({
  isOpen,
  onClose,
  book,
}) => {
  const [formData, setFormData] = useState<BookFormData>({
    title: '',
    language: 'english',
    description: '',
    total_quantity: '',
    condition: 'good',
    minimum_threshold: '10',
    notes: '',
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({});
  const [showQuickTips, setShowQuickTips] = useState(false);

  const { updateBook, isUpdatingBook } = useBookOperations();

  // Populate form when book changes
  useEffect(() => {
    if (book) {
      setFormData({
        title: book.title || '',
        language: book.language,
        description: book.description || '',
        total_quantity: book.total_quantity ? book.total_quantity.toString() : '',
        condition: book.condition,
        minimum_threshold: book.minimum_threshold ? book.minimum_threshold.toString() : '10',
        notes: book.inventory_notes || '',
      });
    }
  }, [book]);

  const validateForm = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!formData.title.trim()) errors.push('Book title is required');

    if (formData.total_quantity) {
      const quantity = parseInt(formData.total_quantity);
      if (isNaN(quantity) || quantity < 0) {
        errors.push('Total quantity must be a valid non-negative number');
      }
    }



    if (formData.minimum_threshold) {
      const threshold = parseInt(formData.minimum_threshold);
      if (isNaN(threshold) || threshold < 0) {
        errors.push('Minimum threshold must be a valid non-negative number');
      }
    }

    return { isValid: errors.length === 0, errors };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!book) return;

    const validation = validateForm();
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      return;
    }

    try {
      const bookData = {
        title: formData.title.trim(),
        language: formData.language,
        description: formData.description.trim() || undefined,
        total_quantity: formData.total_quantity ? parseInt(formData.total_quantity) : 0,
        condition: formData.condition,
        minimum_threshold: formData.minimum_threshold ? parseInt(formData.minimum_threshold) : 10,
        notes: formData.notes.trim() || undefined,
      };

      await updateBook({ bookId: book.id, bookData });
      
      setValidationErrors([]);
      onClose();
      toast.success('Book updated successfully!');
    } catch (error) {
      console.error('Failed to update book:', error);
      toast.error('Failed to update book. Please try again.');
    }
  };

  const handleClose = () => {
    setValidationErrors([]);
    onClose();
  };

  const updateField = (field: keyof BookFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  if (!book) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Edit className="h-5 w-5 text-blue-600" />
            Edit Book: {book.title}
          </DialogTitle>
          <DialogDescription>
            Update book information and inventory details. Changes will be tracked for audit purposes.
          </DialogDescription>
        </DialogHeader>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Book Title *</Label>
                  <Select value={formData.title} onValueChange={(value) => updateField('title', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a book title" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="iLead">iLead</SelectItem>
                      <SelectItem value="iDo">iDo</SelectItem>
                      <SelectItem value="iChoose">iChoose</SelectItem>
                    </SelectContent>
                  </Select>
                </div>


                <div className="space-y-2">
                  <Label htmlFor="language">Language *</Label>
                  <Select value={formData.language} onValueChange={(value: BookLanguage) => updateField('language', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="english">English</SelectItem>
                      <SelectItem value="luganda">Luganda</SelectItem>
                      <SelectItem value="runyankole">Runyankole</SelectItem>
                      <SelectItem value="ateso">Ateso</SelectItem>
                      <SelectItem value="luo">Luo</SelectItem>
                      <SelectItem value="lugbara">Lugbara</SelectItem>
                      <SelectItem value="runyoro">Runyoro</SelectItem>
                      <SelectItem value="lusoga">Lusoga</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

              </div>
              <div className="space-y-2">
                <Label htmlFor="description">Description/Notes</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => updateField('description', e.target.value)}
                  placeholder="Brief description of the book content, target audience, or special features"
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>



          {/* Inventory Information */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Package className="h-4 w-4" />
                Inventory Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="total_quantity">Total Quantity Available</Label>
                  <Input
                    id="total_quantity"
                    type="number"
                    value={formData.total_quantity}
                    onChange={(e) => updateField('total_quantity', e.target.value)}
                    placeholder="e.g., 100"
                    min="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="minimum_threshold">Minimum Stock Threshold</Label>
                  <Input
                    id="minimum_threshold"
                    type="number"
                    value={formData.minimum_threshold}
                    onChange={(e) => updateField('minimum_threshold', e.target.value)}
                    placeholder="e.g., 10"
                    min="0"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="condition">Book Condition</Label>
                  <Select value={formData.condition} onValueChange={(value: BookCondition) => updateField('condition', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="new">New</SelectItem>
                      <SelectItem value="good">Good</SelectItem>
                      <SelectItem value="fair">Fair</SelectItem>
                      <SelectItem value="poor">Poor</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => updateField('notes', e.target.value)}
                  placeholder="Any additional notes about the inventory, supplier information, or special handling instructions"
                  rows={2}
                />
              </div>
            </CardContent>
          </Card>

          {/* Quick Tips Section */}
          <Card>
            <CardHeader className="pb-3">
              <Collapsible open={showQuickTips} onOpenChange={setShowQuickTips}>
                <CollapsibleTrigger asChild>
                  <Button variant="ghost" className="w-full justify-between p-0 h-auto">
                    <CardTitle className="text-sm">Quick Tips</CardTitle>
                    {showQuickTips ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <CardContent className="pt-4 space-y-3">
                    <div className="text-sm text-gray-600 space-y-2">
                      <p><strong>Editing Books:</strong> Changes to book information will be tracked for audit purposes. Only modify information that needs updating.</p>
                      <p><strong>Inventory Updates:</strong> Changing total quantity will affect available stock. Ensure accuracy to prevent distribution issues.</p>
                      <p><strong>Stock Threshold:</strong> Update minimum quantity to receive appropriate low stock alerts for this book.</p>
                      <p><strong>Condition:</strong> Update condition if the overall state of the book inventory has changed since initial entry.</p>
                    </div>
                  </CardContent>
                </CollapsibleContent>
              </Collapsible>
            </CardHeader>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isUpdatingBook}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isUpdatingBook}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isUpdatingBook ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating Book...
                </>
              ) : (
                <>
                  <BookOpen className="h-4 w-4 mr-2" />
                  Update Book
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default EditBookModal;
