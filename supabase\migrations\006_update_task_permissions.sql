-- Update task creation permissions to allow all users to create tasks
-- Task T1.2: Update Task Permissions

-- Drop the existing create_task function
DROP FUNCTION IF EXISTS create_task(VARCHAR, TEXT, task_priority, TIMESTAMP WITH TIME ZONE, UUID, UUID);

-- Create updated function that allows all users to create tasks
CREATE OR REPLACE FUNCTION create_task(
    p_title VARCHAR,
    p_description TEXT DEFAULT NULL,
    p_priority task_priority DEFAULT 'medium',
    p_due_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_assigned_to UUID DEFAULT NULL,
    p_school_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    task_id UUID;
    current_user_role VARCHAR;
BEGIN
    -- Get current user's role
    SELECT role INTO current_user_role 
    FROM profiles 
    WHERE id = auth.uid();
    
    -- Check if user exists
    IF current_user_role IS NULL THEN
        RAISE EXCEPTION 'User not found or not authenticated';
    END IF;
    
    -- If assigning to someone else, check permissions
    IF p_assigned_to IS NOT NULL AND p_assigned_to != auth.uid() THEN
        IF current_user_role NOT IN ('admin', 'program_officer') THEN
            RAISE EXCEPTION 'Only administrators and program officers can assign tasks to other users';
        END IF;
    END IF;
    
    -- If linking to a school, check permissions (only admins and program officers)
    IF p_school_id IS NOT NULL THEN
        IF current_user_role NOT IN ('admin', 'program_officer') THEN
            RAISE EXCEPTION 'Only administrators and program officers can link tasks to schools';
        END IF;
    END IF;
    
    -- If no assignment specified, assign to self
    IF p_assigned_to IS NULL THEN
        p_assigned_to := auth.uid();
    END IF;

    -- Insert new task
    INSERT INTO tasks (title, description, priority, due_date, assigned_to, created_by, school_id)
    VALUES (p_title, p_description, p_priority, p_due_date, p_assigned_to, auth.uid(), p_school_id)
    RETURNING id INTO task_id;

    RETURN task_id;
END;
$$;

-- Update RLS policies to allow all authenticated users to create tasks
DROP POLICY IF EXISTS "Admins and program officers can create tasks" ON tasks;

CREATE POLICY "All authenticated users can create tasks" ON tasks
    FOR INSERT WITH CHECK (
        auth.uid() IS NOT NULL AND
        created_by = auth.uid()
    );

-- Add policy for task assignment restrictions
CREATE POLICY "Task assignment restrictions" ON tasks
    FOR INSERT WITH CHECK (
        -- Users can assign tasks to themselves
        (assigned_to = auth.uid()) OR
        -- Or admins/program officers can assign to anyone
        (assigned_to != auth.uid() AND EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ))
    );
