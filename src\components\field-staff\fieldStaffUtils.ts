import { useAuth } from '@/hooks/useAuth';

/**
 * Hook to check field staff access permissions
 */
export const useFieldStaffAccess = () => {
  const { user, profile } = useAuth();

  const canViewAllStaffData = profile?.role === 'admin' || profile?.role === 'program_officer';
  const canViewOwnData = profile?.role === 'field_staff';
  const canManageTimesheets = profile?.role === 'admin' || profile?.role === 'program_officer';
  const canApproveReports = profile?.role === 'admin' || profile?.role === 'program_officer';
  const canCheckInOut = profile?.role === 'field_staff' || profile?.role === 'admin' || profile?.role === 'program_officer';

  const canAccessStaffData = (targetUserId?: string) => {
    if (!user || !profile) return false;

    // Admin and program officers can access all data
    if (profile.role === 'admin' || profile.role === 'program_officer') {
      return true;
    }

    // Field staff can only access their own data
    if (profile.role === 'field_staff') {
      return !targetUserId || user.id === targetUserId;
    }

    return false;
  };

  return {
    canViewAllStaffData,
    canViewOwnData,
    canManageTimesheets,
    canApproveReports,
    canCheckInOut,
    canAccessStaffData,
    userRole: profile?.role,
    userId: user?.id,
  };
};
