
import { useState } from 'react';

export const useSchoolForm = () => {
  const [formData, setFormData] = useState({
    // Required fields
    name: '',
    school_type: 'primary' as 'primary' | 'secondary' | 'tertiary' | 'vocational',
    division_id: '',

    // Optional fields
    code: '',
    student_count: '',
    teacher_count: '',
    contact_phone: '',
    email: '',
    classes_count: '',
    streams_per_class: '',
    head_teacher_name: '',
    deputy_head_teacher_name: '',
    year_established: '',
    ownership_type: 'government' as 'government' | 'private' | 'community',
    location_description: '',
    nearest_health_center: '',
    distance_to_main_road: '',
    infrastructure_notes: '',
  });

  const resetForm = () => {
    setFormData({
      // Required fields
      name: '',
      school_type: 'primary',
      division_id: '',

      // Optional fields
      code: '',
      student_count: '',
      teacher_count: '',
      contact_phone: '',
      email: '',
      classes_count: '',
      streams_per_class: '',
      head_teacher_name: '',
      deputy_head_teacher_name: '',
      year_established: '',
      ownership_type: 'government',
      location_description: '',
      nearest_health_center: '',
      distance_to_main_road: '',
      infrastructure_notes: '',
    });
  };

  return {
    formData,
    setFormData,
    resetForm,
  };
};
