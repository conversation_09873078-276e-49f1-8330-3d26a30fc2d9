-- Migration 022: Fix PostGIS dependency by implementing simple distance calculation
-- This migration replaces PostGIS functions with basic PostgreSQL functions

-- Create a simple distance calculation function using the haversine formula
CREATE OR REPLACE FUNCTION calculate_distance_meters(
    lat1 DECIMAL(10,8),
    lon1 DECIMAL(11,8),
    lat2 DECIMAL(10,8),
    lon2 DECIMAL(11,8)
)
RETURNS DECIMAL(10,2)  -- Updated to allow larger distances
LANGUAGE plpgsql
IMMUTABLE
AS $$
DECLARE
    earth_radius CONSTANT DECIMAL := 6371000; -- Earth radius in meters
    dlat DECIMAL;
    dlon DECIMAL;
    a DECIMAL;
    c DECIMAL;
    distance DECIMAL;
BEGIN
    -- Convert degrees to radians
    dlat := radians(lat2 - lat1);
    dlon := radians(lon2 - lon1);
    
    -- Haversine formula
    a := sin(dlat/2) * sin(dlat/2) + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2) * sin(dlon/2);
    c := 2 * atan2(sqrt(a), sqrt(1-a));
    distance := earth_radius * c;
    
    RETURN distance;
END;
$$;

-- Update field_staff_checkin function to use simple distance calculation
CREATE OR REPLACE FUNCTION field_staff_checkin(
    p_school_id UUID,
    p_latitude DECIMAL(10,8),
    p_longitude DECIMAL(11,8),
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_verification_method VARCHAR(50) DEFAULT 'gps',
    p_device_info JSONB DEFAULT '{}',
    p_network_info JSONB DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT false
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_id UUID;
    existing_attendance_id UUID;
    distance_meters DECIMAL(10,2);
    school_location POINT;
    school_lat DECIMAL(10,8);
    school_lon DECIMAL(11,8);
BEGIN
    -- Check if user is field staff
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Only field staff can check in';
    END IF;

    -- Check if staff already checked in today at this school
    SELECT id INTO existing_attendance_id
    FROM field_staff_attendance
    WHERE staff_id = auth.uid()
    AND school_id = p_school_id
    AND attendance_date = CURRENT_DATE
    AND status = 'active';

    IF existing_attendance_id IS NOT NULL THEN
        RAISE EXCEPTION 'Already checked in at this school today';
    END IF;

    -- Get school location for distance calculation (if available)
    SELECT location_coordinates INTO school_location
    FROM schools WHERE id = p_school_id;

    -- Calculate distance from school using simple calculation
    IF school_location IS NOT NULL THEN
        -- Use PostgreSQL point operators instead of PostGIS functions
        school_lat := school_location[1];  -- Y coordinate (latitude)
        school_lon := school_location[0];  -- X coordinate (longitude)
        distance_meters := calculate_distance_meters(p_latitude, p_longitude, school_lat, school_lon);
    END IF;

    -- Insert attendance record
    INSERT INTO field_staff_attendance (
        staff_id, school_id, attendance_date, check_in_time,
        check_in_location, check_in_accuracy, check_in_address,
        distance_from_school, location_verified, verification_method,
        device_info, network_info, offline_sync, status
    )
    VALUES (
        auth.uid(), p_school_id, CURRENT_DATE, NOW(),
        POINT(p_longitude, p_latitude), p_accuracy, p_address,
        distance_meters, (distance_meters IS NULL OR distance_meters <= 100),
        p_verification_method, p_device_info, p_network_info, p_offline_sync, 'active'
    )
    RETURNING id INTO attendance_id;

    -- Log activity
    INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description)
    VALUES (
        'task_created'::activity_type,
        auth.uid(),
        'task'::entity_type,
        attendance_id,
        'Checked in at school: ' || (SELECT name FROM schools WHERE id = p_school_id)
    );

    RETURN attendance_id;
END;
$$;

-- Update staff_gps_checkin function to use simple distance calculation
CREATE OR REPLACE FUNCTION staff_gps_checkin(
    p_school_id UUID,
    p_session_id UUID DEFAULT NULL,
    p_latitude DECIMAL(10,8),
    p_longitude DECIMAL(11,8),
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address_description TEXT DEFAULT NULL,
    p_verification_method VARCHAR(50) DEFAULT 'gps',
    p_device_info JSONB DEFAULT '{}',
    p_network_info JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    log_id UUID;
    school_location POINT;
    distance_meters DECIMAL(10,2);
    school_lat DECIMAL(10,8);
    school_lon DECIMAL(11,8);
BEGIN
    -- Check if user is field staff
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Only field staff can check in with GPS';
    END IF;

    -- Get school location for distance calculation
    SELECT location_coordinates INTO school_location
    FROM schools WHERE id = p_school_id;

    -- Calculate distance from school using simple calculation
    IF school_location IS NOT NULL THEN
        -- Use PostgreSQL point operators instead of PostGIS functions
        school_lat := school_location[1];  -- Y coordinate (latitude)
        school_lon := school_location[0];  -- X coordinate (longitude)
        distance_meters := calculate_distance_meters(p_latitude, p_longitude, school_lat, school_lon);
    END IF;

    -- Insert location log
    INSERT INTO staff_location_logs (
        staff_id, school_id, session_id, check_in_status,
        location_coordinates, location_accuracy, address_description,
        distance_from_school, location_verified, verification_method,
        device_info, network_info
    )
    VALUES (
        auth.uid(), p_school_id, p_session_id, 'checked_in'::check_in_status,
        POINT(p_longitude, p_latitude), p_accuracy, p_address_description,
        distance_meters, (distance_meters IS NULL OR distance_meters <= 100),
        p_verification_method, p_device_info, p_network_info
    )
    RETURNING id INTO log_id;

    RETURN log_id;
END;
$$;

-- Update distance_from_school fields to allow larger distances (up to 99,999km)
ALTER TABLE field_staff_attendance
ALTER COLUMN distance_from_school TYPE DECIMAL(10,2);

ALTER TABLE staff_location_logs
ALTER COLUMN distance_from_school TYPE DECIMAL(10,2);
