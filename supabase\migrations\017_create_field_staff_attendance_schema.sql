-- Field Staff Attendance and Reporting System
-- Migration 017: Enhanced field staff check-in/check-out with field reports and timesheets

-- Create field report status enum
CREATE TYPE field_report_status AS ENUM ('draft', 'submitted', 'reviewed', 'approved');

-- Create field activity type enum  
CREATE TYPE field_activity_type AS ENUM ('leadership_training', 'school_visit', 'community_engagement', 'assessment', 'meeting', 'other');

-- Field staff daily attendance table (extends staff_location_logs concept)
CREATE TABLE field_staff_attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID REFERENCES profiles(id) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    attendance_date DATE NOT NULL,
    check_in_time TIMESTAMP WITH TIME ZONE NOT NULL,
    check_in_location POINT NOT NULL,
    check_in_accuracy DECIMAL(8,2),
    check_in_address TEXT,
    check_out_time TIMESTAMP WITH TIME ZONE,
    check_out_location POINT,
    check_out_accuracy DECIMAL(8,2),
    check_out_address TEXT,
    total_duration_minutes INTEGER,
    distance_from_school DECIMAL(8,2),
    location_verified BOOLEAN DEFAULT false,
    verification_method VARCHAR(50) DEFAULT 'gps',
    device_info JSONB DEFAULT '{}',
    network_info JSONB DEFAULT '{}',
    offline_sync BOOLEAN DEFAULT false,
    sync_timestamp TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active', -- 'active', 'completed', 'cancelled'
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Field reports table for departure reports
CREATE TABLE field_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    attendance_id UUID REFERENCES field_staff_attendance(id) NOT NULL,
    staff_id UUID REFERENCES profiles(id) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    report_date DATE NOT NULL,
    activity_type field_activity_type NOT NULL,
    
    -- Round table training data
    round_table_sessions_count INTEGER DEFAULT 0,
    total_students_attended INTEGER DEFAULT 0,
    students_per_session INTEGER DEFAULT 8,
    
    -- Daily activities
    activities_conducted TEXT[] DEFAULT '{}',
    topics_covered TEXT[] DEFAULT '{}',
    materials_used TEXT[] DEFAULT '{}',
    
    -- Challenges and wins
    challenges_encountered TEXT,
    wins_achieved TEXT,
    general_observations TEXT,
    lessons_learned TEXT,
    
    -- Follow-up actions
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_actions TEXT,
    next_visit_planned DATE,
    
    -- Report metadata
    report_status field_report_status DEFAULT 'draft',
    submitted_at TIMESTAMP WITH TIME ZONE,
    reviewed_by UUID REFERENCES profiles(id),
    reviewed_at TIMESTAMP WITH TIME ZONE,
    review_comments TEXT,
    
    -- Location and verification
    report_location POINT,
    location_verified BOOLEAN DEFAULT false,
    photos TEXT[] DEFAULT '{}',
    attachments JSONB DEFAULT '{}',
    
    -- Offline support
    offline_created BOOLEAN DEFAULT false,
    sync_timestamp TIMESTAMP WITH TIME ZONE,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Daily timesheet summaries for admin dashboard
CREATE TABLE daily_timesheets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID REFERENCES profiles(id) NOT NULL,
    timesheet_date DATE NOT NULL,
    
    -- Time tracking
    total_work_hours DECIMAL(4,2) DEFAULT 0,
    total_schools_visited INTEGER DEFAULT 0,
    total_students_reached INTEGER DEFAULT 0,
    total_sessions_conducted INTEGER DEFAULT 0,
    
    -- Activity summary
    primary_activities TEXT[] DEFAULT '{}',
    schools_visited JSONB DEFAULT '{}', -- Array of school objects with details
    key_achievements TEXT,
    main_challenges TEXT,
    
    -- Performance metrics
    productivity_score DECIMAL(3,2), -- 1-5 scale
    impact_score DECIMAL(3,2), -- 1-5 scale
    efficiency_rating VARCHAR(20), -- 'excellent', 'good', 'average', 'needs_improvement'
    
    -- Status and approval
    timesheet_status VARCHAR(20) DEFAULT 'draft', -- 'draft', 'submitted', 'approved', 'rejected'
    submitted_at TIMESTAMP WITH TIME ZONE,
    approved_by UUID REFERENCES profiles(id),
    approved_at TIMESTAMP WITH TIME ZONE,
    approval_comments TEXT,
    
    -- Auto-generated vs manual
    auto_generated BOOLEAN DEFAULT true,
    manual_adjustments TEXT,
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure one timesheet per staff per day
    UNIQUE(staff_id, timesheet_date)
);

-- Field staff performance analytics
CREATE TABLE field_staff_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID REFERENCES profiles(id) NOT NULL,
    analysis_period VARCHAR(20) NOT NULL, -- 'weekly', 'monthly', 'quarterly', 'annual'
    period_start_date DATE NOT NULL,
    period_end_date DATE NOT NULL,
    
    -- Attendance metrics
    total_work_days INTEGER NOT NULL,
    days_worked INTEGER NOT NULL,
    attendance_rate DECIMAL(5,2) NOT NULL,
    average_hours_per_day DECIMAL(4,2),
    
    -- Activity metrics
    total_schools_visited INTEGER DEFAULT 0,
    unique_schools_visited INTEGER DEFAULT 0,
    total_students_reached INTEGER DEFAULT 0,
    total_sessions_conducted INTEGER DEFAULT 0,
    average_students_per_session DECIMAL(5,2),
    
    -- Performance metrics
    average_productivity_score DECIMAL(3,2),
    average_impact_score DECIMAL(3,2),
    on_time_check_in_rate DECIMAL(5,2),
    report_completion_rate DECIMAL(5,2),
    
    -- Trends and insights
    performance_trend VARCHAR(20), -- 'improving', 'declining', 'stable'
    top_challenges TEXT[],
    key_achievements TEXT[],
    recommendations TEXT,
    
    -- Calculated fields
    efficiency_rating VARCHAR(20),
    overall_rating DECIMAL(3,2), -- 1-5 scale
    
    last_calculated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_field_staff_attendance_staff_id ON field_staff_attendance(staff_id);
CREATE INDEX idx_field_staff_attendance_school_id ON field_staff_attendance(school_id);
CREATE INDEX idx_field_staff_attendance_date ON field_staff_attendance(attendance_date);
CREATE INDEX idx_field_staff_attendance_check_in_time ON field_staff_attendance(check_in_time);

CREATE INDEX idx_field_reports_attendance_id ON field_reports(attendance_id);
CREATE INDEX idx_field_reports_staff_id ON field_reports(staff_id);
CREATE INDEX idx_field_reports_school_id ON field_reports(school_id);
CREATE INDEX idx_field_reports_date ON field_reports(report_date);
CREATE INDEX idx_field_reports_status ON field_reports(report_status);

CREATE INDEX idx_daily_timesheets_staff_id ON daily_timesheets(staff_id);
CREATE INDEX idx_daily_timesheets_date ON daily_timesheets(timesheet_date);
CREATE INDEX idx_daily_timesheets_status ON daily_timesheets(timesheet_status);

CREATE INDEX idx_field_staff_analytics_staff_id ON field_staff_analytics(staff_id);
CREATE INDEX idx_field_staff_analytics_period ON field_staff_analytics(analysis_period, period_start_date, period_end_date);

-- Create triggers for updated_at columns
CREATE TRIGGER update_field_staff_attendance_updated_at
    BEFORE UPDATE ON field_staff_attendance
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_field_reports_updated_at
    BEFORE UPDATE ON field_reports
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_daily_timesheets_updated_at
    BEFORE UPDATE ON daily_timesheets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE field_staff_attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE field_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE daily_timesheets ENABLE ROW LEVEL SECURITY;
ALTER TABLE field_staff_analytics ENABLE ROW LEVEL SECURITY;

-- RLS Policies for field_staff_attendance
CREATE POLICY "Staff can view their own attendance records" ON field_staff_attendance
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Staff can create their own attendance records" ON field_staff_attendance
    FOR INSERT WITH CHECK (
        staff_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Staff can update their own attendance records" ON field_staff_attendance
    FOR UPDATE USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- RLS Policies for field_reports
CREATE POLICY "Staff can view their own field reports" ON field_reports
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Staff can create their own field reports" ON field_reports
    FOR INSERT WITH CHECK (
        staff_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Staff can update their own field reports" ON field_reports
    FOR UPDATE USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- RLS Policies for daily_timesheets
CREATE POLICY "Staff can view their own timesheets" ON daily_timesheets
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "System can manage timesheets" ON daily_timesheets
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

-- RLS Policies for field_staff_analytics
CREATE POLICY "Staff can view their own analytics" ON field_staff_analytics
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Admins can manage analytics" ON field_staff_analytics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );
