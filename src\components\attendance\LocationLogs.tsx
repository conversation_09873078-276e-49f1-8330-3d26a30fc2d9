import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  MapPin, 
  Clock, 
  Navigation, 
  CheckCircle, 
  XCircle, 
  Calendar,
  Filter,
  Download,
  Eye,
  Smartphone,
  Wifi
} from 'lucide-react';
import { useStaffLocationLogs } from '@/hooks/attendance/useGPSTracking';
import { useAuth } from '@/hooks/useAuth';
import { useSchools } from '@/hooks/useSchools';
import { Database } from '@/integrations/supabase/types';

type CheckInStatus = Database['public']['Enums']['check_in_status'];

interface LocationLogsProps {
  staffId?: string;
  schoolId?: string;
  showFilters?: boolean;
}

const LocationLogs: React.FC<LocationLogsProps> = ({
  staffId,
  schoolId: defaultSchoolId,
  showFilters = true,
}) => {
  const { profile } = useAuth();
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || '');
  const [statusFilter, setStatusFilter] = useState<CheckInStatus | 'all'>('all');
  const [dateFilter, setDateFilter] = useState('');

  const { data: schools } = useSchools();
  const { data: locationLogs, isLoading } = useStaffLocationLogs(
    staffId || profile?.id,
    selectedSchoolId || undefined
  );

  // Filter logs based on selected filters
  const filteredLogs = locationLogs?.filter(log => {
    if (statusFilter && statusFilter !== 'all' && log.check_in_status !== statusFilter) return false;
    if (dateFilter && !log.check_in_time.startsWith(dateFilter)) return false;
    return true;
  }) || [];

  const getStatusIcon = (status: CheckInStatus) => {
    switch (status) {
      case 'checked_in':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'checked_out':
        return <XCircle className="h-4 w-4 text-gray-600" />;
      case 'break':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'active':
        return <Navigation className="h-4 w-4 text-blue-600" />;
      default:
        return <MapPin className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: CheckInStatus) => {
    switch (status) {
      case 'checked_in':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'checked_out':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'break':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'active':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDuration = (minutes: number | null) => {
    if (!minutes) return 'N/A';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getAccuracyLabel = (accuracy: number) => {
    if (accuracy <= 10) return 'Excellent';
    if (accuracy <= 50) return 'Good';
    return 'Poor';
  };

  const getAccuracyColor = (accuracy: number) => {
    if (accuracy <= 10) return 'text-green-600';
    if (accuracy <= 50) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Location Logs
              </CardTitle>
              <CardDescription>
                GPS check-in/out history and location tracking
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>

        {/* Filters */}
        {showFilters && (
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">School</label>
                <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                  <SelectTrigger>
                    <SelectValue placeholder="All schools" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All schools</SelectItem>
                    {schools?.map((school) => (
                      <SelectItem key={school.id} value={school.id}>
                        {school.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Status</label>
                <Select value={statusFilter} onValueChange={(value: CheckInStatus | 'all') => setStatusFilter(value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="checked_in">Checked In</SelectItem>
                    <SelectItem value="checked_out">Checked Out</SelectItem>
                    <SelectItem value="break">Break</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">Date</label>
                <Input
                  type="date"
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                />
              </div>

              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedSchoolId('');
                    setStatusFilter('all');
                    setDateFilter('');
                  }}
                  className="w-full"
                >
                  <Filter className="h-4 w-4 mr-2" />
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Location Logs List */}
      <div className="space-y-4">
        {filteredLogs.length === 0 ? (
          <Card>
            <CardContent className="text-center py-8">
              <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No location logs found</h3>
              <p className="text-gray-600">
                {locationLogs?.length === 0 
                  ? "No GPS check-ins recorded yet."
                  : "No logs match your current filters."
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          filteredLogs.map((log) => (
            <Card key={log.id}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    {getStatusIcon(log.check_in_status)}
                    <div>
                      <div className="font-medium">
                        {log.school?.name || 'Unknown School'}
                      </div>
                      <div className="text-sm text-gray-600">
                        {log.session?.session_name && (
                          <span>{log.session.session_name} • </span>
                        )}
                        {new Date(log.check_in_time).toLocaleString()}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Badge className={getStatusColor(log.check_in_status)}>
                      {log.check_in_status.replace('_', ' ')}
                    </Badge>
                    {log.location_verified && (
                      <Badge variant="outline" className="text-green-600 border-green-200">
                        Verified
                      </Badge>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  {/* Location Details */}
                  <div>
                    <div className="text-sm font-medium mb-1">Location</div>
                    <div className="text-sm text-gray-600">
                      {log.address_description || 'No description'}
                      <br />
                      <span className="font-mono text-xs">
                        {log.location_coordinates[1].toFixed(6)}, {log.location_coordinates[0].toFixed(6)}
                      </span>
                    </div>
                  </div>

                  {/* Accuracy & Distance */}
                  <div>
                    <div className="text-sm font-medium mb-1">Accuracy & Distance</div>
                    <div className="text-sm text-gray-600">
                      <span className={getAccuracyColor(log.location_accuracy)}>
                        {getAccuracyLabel(log.location_accuracy)} (±{log.location_accuracy.toFixed(0)}m)
                      </span>
                      <br />
                      {log.distance_from_school && (
                        <span>
                          {log.distance_from_school.toFixed(0)}m from school
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Duration */}
                  <div>
                    <div className="text-sm font-medium mb-1">Duration</div>
                    <div className="text-sm text-gray-600">
                      {log.check_out_time ? (
                        <>
                          {formatDuration(log.total_duration_minutes)}
                          <br />
                          <span className="text-xs">
                            Out: {new Date(log.check_out_time).toLocaleTimeString()}
                          </span>
                        </>
                      ) : (
                        <span className="text-yellow-600">Still checked in</span>
                      )}
                    </div>
                  </div>
                </div>

                {/* Technical Details */}
                <div className="border-t pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs text-gray-500">
                    <div className="flex items-center gap-1">
                      <Smartphone className="h-3 w-3" />
                      {log.verification_method}
                    </div>
                    <div className="flex items-center gap-1">
                      <Wifi className="h-3 w-3" />
                      {log.network_info?.onLine ? 'Online' : 'Offline'}
                    </div>
                    {log.offline_sync && (
                      <div className="text-yellow-600">
                        Synced offline data
                      </div>
                    )}
                  </div>
                  
                  {log.notes && (
                    <div className="mt-2 text-sm text-gray-600">
                      <strong>Notes:</strong> {log.notes}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Summary Stats */}
      {filteredLogs.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">{filteredLogs.length}</div>
                <div className="text-sm text-gray-600">Total Logs</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {filteredLogs.filter(l => l.check_in_status === 'checked_out').length}
                </div>
                <div className="text-sm text-gray-600">Completed Visits</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {filteredLogs.filter(l => l.location_verified).length}
                </div>
                <div className="text-sm text-gray-600">Verified Locations</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {formatDuration(
                    filteredLogs
                      .filter(l => l.total_duration_minutes)
                      .reduce((sum, l) => sum + (l.total_duration_minutes || 0), 0)
                  )}
                </div>
                <div className="text-sm text-gray-600">Total Time</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default LocationLogs;
