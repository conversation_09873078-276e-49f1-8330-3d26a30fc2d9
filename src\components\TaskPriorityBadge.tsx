import React from 'react';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, ArrowUp, ArrowDown, Minus } from 'lucide-react';
import { Database } from '@/integrations/supabase/types';

type TaskPriority = Database['public']['Enums']['task_priority'];

interface TaskPriorityBadgeProps {
  priority: TaskPriority;
  className?: string;
  showIcon?: boolean;
}

const TaskPriorityBadge: React.FC<TaskPriorityBadgeProps> = ({ 
  priority, 
  className, 
  showIcon = true 
}) => {
  const getPriorityConfig = (priority: TaskPriority) => {
    switch (priority) {
      case 'urgent':
        return {
          label: 'Urgent',
          icon: AlertTriangle,
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 hover:bg-red-200 border-red-200'
        };
      case 'high':
        return {
          label: 'High',
          icon: ArrowUp,
          variant: 'default' as const,
          className: 'bg-orange-100 text-orange-800 hover:bg-orange-200 border-orange-200'
        };
      case 'medium':
        return {
          label: 'Medium',
          icon: Minus,
          variant: 'secondary' as const,
          className: 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200 border-yellow-200'
        };
      case 'low':
        return {
          label: 'Low',
          icon: ArrowDown,
          variant: 'outline' as const,
          className: 'bg-purple-100 text-purple-800 hover:bg-purple-200 border-purple-200'
        };
      default:
        return {
          label: 'Unknown',
          icon: Minus,
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const config = getPriorityConfig(priority);
  const Icon = config.icon;

  return (
    <Badge 
      variant={config.variant}
      className={`${config.className} ${className || ''} flex items-center gap-1`}
    >
      {showIcon && <Icon className="h-3 w-3" />}
      {config.label}
    </Badge>
  );
};

export default TaskPriorityBadge;
