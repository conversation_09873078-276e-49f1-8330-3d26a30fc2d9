import React from 'react';
import { cn } from '@/lib/utils';

interface PageLayoutProps {
  children: React.ReactNode;
  className?: string;
}

/**
 * Standardized page layout wrapper that provides consistent spacing,
 * scrolling behavior, and responsive design across all pages.
 */
const PageLayout: React.FC<PageLayoutProps> = ({ children, className }) => {
  return (
    <div className={cn(
      "flex-1 overflow-y-auto scrollbar-content",
      "p-4 sm:p-6 lg:p-8", // Responsive padding
      "space-y-6", // Consistent vertical spacing between sections
      className
    )}>
      {children}
    </div>
  );
};

export default PageLayout;
