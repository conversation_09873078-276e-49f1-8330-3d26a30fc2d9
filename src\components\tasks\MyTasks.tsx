
import { useState } from 'react';
import { Target, Plus } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import TaskList from '../TaskList';
import CreateTaskDialog from '../CreateTaskDialog';
import TaskDetailsDialog from './TaskDetailsDialog';
import { useMyTasks, useCreateTask, useUpdateTaskStatus, TaskFormData } from '@/hooks/tasks';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

const MyTasks = () => {
  const { profile } = useAuth();
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);
  const { toast } = useToast();

  console.log('🔍 MyTasks component - Current profile:', profile);

  // Check if user can create tasks
  const canCreateTasks = !!profile;
  const canAssignTasks = profile?.role === 'admin' || profile?.role === 'program_officer';

  console.log('👤 User permissions - canCreateTasks:', canCreateTasks, 'canAssignTasks:', canAssignTasks);

  // Use optimized hooks
  const { data: myTasks = [], isLoading, error } = useMyTasks();
  const createTaskMutation = useCreateTask();
  const updateStatusMutation = useUpdateTaskStatus();

  console.log('📊 MyTasks state - isLoading:', isLoading, 'error:', error, 'tasks count:', myTasks.length);

  const handleCreateTask = async (taskData: TaskFormData) => {
    try {
      console.log('🔨 Creating task from MyTasks:', taskData);
      await createTaskMutation.mutateAsync(taskData);
      toast({
        title: "Success",
        description: "Task created successfully",
      });
      setCreateDialogOpen(false);
    } catch (error: unknown) {
      console.error('❌ Failed to create task from MyTasks:', error);
      toast({
        title: "Error",
        description: (error as Error).message || "Failed to create task",
        variant: "destructive",
      });
    }
  };

  const handleViewDetails = (taskId: string) => {
    console.log('👁️ View task details:', taskId);
    setSelectedTaskId(taskId);
  };

  const handleUpdateStatus = (taskId: string, status: Database['public']['Enums']['task_status']) => {
    console.log('🔄 Update task status:', taskId, 'to', status);
    updateStatusMutation.mutate({ taskId, status });
  };

  if (error) {
    console.error('❌ Error in MyTasks component:', error);
    return (
      <div className="p-6 scrollbar-content">
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">
            <Target className="h-12 w-12 mx-auto" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Tasks</h3>
          <p className="text-gray-600">{(error as Error).message || 'Failed to load tasks'}</p>
        </div>
      </div>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="My Tasks"
        description={`Tasks assigned to you (${myTasks.length} total)`}
        icon={Target}
        actions={canCreateTasks ? [
          {
            label: 'New Task',
            onClick: () => setCreateDialogOpen(true),
            icon: Plus,
          }
        ] : []}
      />

      <ContentCard noPadding>
        <TaskList
          tasks={myTasks}
          loading={isLoading}
          currentUserId={profile?.id}
          onViewDetails={handleViewDetails}
          onUpdateStatus={handleUpdateStatus}
        />
      </ContentCard>

      {/* Create Task Dialog */}
      <CreateTaskDialog
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
        onSubmit={handleCreateTask}
        loading={createTaskMutation.isPending}
        canAssignTasks={canAssignTasks}
      />

      {/* Task Details Dialog */}
      <TaskDetailsDialog
        taskId={selectedTaskId}
        open={!!selectedTaskId}
        onOpenChange={(open) => {
          if (!open) {
            setSelectedTaskId(null);
          }
        }}
      />
    </PageLayout>
  );
};

export default MyTasks;
