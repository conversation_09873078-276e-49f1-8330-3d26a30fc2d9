
import { Database } from '@/integrations/supabase/types';

type ActivityType = Database['public']['Enums']['activity_type'];
type EntityType = Database['public']['Enums']['entity_type'];

export interface Activity {
  id: string;
  activity_type: ActivityType;
  user_id: string;
  user_name: string;
  entity_type: EntityType;
  entity_id: string;
  description: string;
  metadata: Database['public']['Tables']['activities']['Row']['metadata'];
  created_at: string;
  entity_details?: Database['public']['Tables']['activities']['Row']['metadata'];
}

export interface ActivityFormData {
  activity_type: ActivityType;
  user_id: string;
  entity_type: EntityType;
  entity_id: string;
  description: string;
  metadata?: Database['public']['Tables']['activities']['Row']['metadata'];
}

export type { ActivityType, EntityType };
