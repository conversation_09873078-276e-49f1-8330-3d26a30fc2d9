import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Users, Loader2, AlertCircle } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Alert, AlertDescription } from '@/components/ui/alert';

interface FieldStaffMember {
  id: string;
  name: string;
  phone: string | null;
  division_name: string;
}

interface FieldStaffSelectorProps {
  value?: string;
  onValueChange: (value: string) => void;
  required?: boolean;
  disabled?: boolean;
  placeholder?: string;
  label?: string;
  showLabel?: boolean;
  className?: string;
}

const FieldStaffSelector: React.FC<FieldStaffSelectorProps> = ({
  value,
  onValueChange,
  required = false,
  disabled = false,
  placeholder = "Select field staff member...",
  label = "Assigned Field Staff",
  showLabel = true,
  className = "",
}) => {
  // Handle the "none" value conversion for backward compatibility
  const handleValueChange = (newValue: string) => {
    if (newValue === "none") {
      onValueChange(""); // Convert "none" back to empty string for the parent component
    } else {
      onValueChange(newValue);
    }
  };

  // Convert empty string to "none" for the Select component
  const selectValue = value === "" ? "none" : value;
  // Fetch field staff members
  const { 
    data: fieldStaffMembers = [], 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['field-staff-members'],
    queryFn: async (): Promise<FieldStaffMember[]> => {
      const { data, error } = await supabase.rpc('get_field_staff_members');
      
      if (error) {
        console.error('Error fetching field staff members:', error);
        throw error;
      }
      
      return data || [];
    },
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });

  if (error) {
    return (
      <div className={className}>
        {showLabel && (
          <Label className="text-sm font-medium mb-2 block">
            {label} {required && <span className="text-red-500">*</span>}
          </Label>
        )}
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Failed to load field staff members. Please try again.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className={className}>
      {showLabel && (
        <Label className="text-sm font-medium mb-2 block">
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            {label} {required && <span className="text-red-500">*</span>}
          </div>
        </Label>
      )}
      
      <Select
        value={selectValue}
        onValueChange={handleValueChange}
        disabled={disabled || isLoading}
      >
        <SelectTrigger className="w-full">
          <SelectValue placeholder={
            isLoading ? (
              <div className="flex items-center gap-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                Loading field staff...
              </div>
            ) : placeholder
          } />
        </SelectTrigger>
        <SelectContent>
          {!required && (
            <SelectItem value="none">
              <span className="text-gray-500">No assignment</span>
            </SelectItem>
          )}

          {fieldStaffMembers.length === 0 && !isLoading ? (
            <SelectItem value="no-staff" disabled>
              <span className="text-gray-500">No field staff members available</span>
            </SelectItem>
          ) : (
            fieldStaffMembers.filter(member => member.id && member.id.trim() !== '').map((member) => (
              <SelectItem key={member.id} value={member.id}>
                <div className="flex flex-col">
                  <span className="font-medium">{member.name}</span>
                  <div className="flex items-center gap-2 text-xs text-gray-500">
                    {member.phone && <span>{member.phone}</span>}
                    <span>•</span>
                    <span>{member.division_name}</span>
                  </div>
                </div>
              </SelectItem>
            ))
          )}
        </SelectContent>
      </Select>
      
      {fieldStaffMembers.length === 0 && !isLoading && !error && (
        <p className="text-sm text-gray-500 mt-1">
          No field staff members found. Contact an administrator to add field staff.
        </p>
      )}
    </div>
  );
};

export default FieldStaffSelector;
