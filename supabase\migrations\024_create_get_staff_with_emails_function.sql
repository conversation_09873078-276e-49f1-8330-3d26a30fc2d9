-- Create function to get staff members with their email addresses
-- This replaces the need for client-side auth.admin.listUsers() calls

CREATE OR REPLACE FUNCTION get_staff_with_emails()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email VARCHAR(255),
    role user_role,
    division_id UUID,
    division_name TEXT,
    phone TEXT,
    country TEXT,
    is_active BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id,
        p.name,
        au.email,
        p.role,
        p.division_id,
        CASE
            WHEN ad.district IS NOT NULL AND ad.sub_county IS NOT NULL
            THEN CONCAT(ad.district, ', ', ad.sub_county)
            ELSE NULL
        END as division_name,
        p.phone,
        p.country,
        p.is_active,
        p.created_at
    FROM profiles p
    LEFT JOIN auth.users au ON p.id = au.id
    LEFT JOIN administrative_divisions ad ON p.division_id = ad.id
    ORDER BY p.created_at DESC;
END;
$$;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION get_staff_with_emails() TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION get_staff_with_emails() IS 'Fetches all staff members with their email addresses from auth.users table. Used by staff management interface.';
