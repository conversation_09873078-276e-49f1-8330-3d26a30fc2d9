
import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { MapPin, School, Users, Filter, Download } from 'lucide-react';
import { useSchoolOperations } from '@/hooks/useSchoolOperations';
import { useAuth } from '@/hooks/useAuth';
import { School as SchoolType } from '@/types/school';

// Type definitions for location grouping
interface LocationGroup {
  location: string;
  district: string;
  sub_county: string;
  schools: SchoolType[];
  totalStudents: number;
  totalTeachers: number;
}

const SchoolLocations = () => {
  const { profile } = useAuth();
  const { schools, divisions, isLoadingSchools } = useSchoolOperations(profile);
  const [selectedDistrict, setSelectedDistrict] = useState<string>('all');
  const [selectedType, setSelectedType] = useState<string>('all');

  // Group schools by district
  const schoolsByLocation = useMemo(() => {
    const filtered = schools.filter(school => {
      const districtMatch = selectedDistrict === 'all' || school.district === selectedDistrict;
      const typeMatch = selectedType === 'all' || school.school_type === selectedType;
      return districtMatch && typeMatch;
    });

    const grouped = filtered.reduce((acc: Record<string, LocationGroup>, school) => {
      const key = `${school.district}, ${school.sub_county}`;
      if (!acc[key]) {
        acc[key] = {
          location: key,
          district: school.district,
          sub_county: school.sub_county,
          schools: [],
          totalStudents: 0,
          totalTeachers: 0,
        };
      }
      acc[key].schools.push(school);
      acc[key].totalStudents += school.student_count || 0;
      acc[key].totalTeachers += school.teacher_count || 0;
      return acc;
    }, {});

    return Object.values(grouped).sort((a: LocationGroup, b: LocationGroup) => b.schools.length - a.schools.length);
  }, [schools, selectedDistrict, selectedType]);

  // Get unique districts
  const uniqueDistricts = useMemo(() => {
    const districts = [...new Set(schools.map(school => school.district))].filter(Boolean);
    return districts.sort();
  }, [schools]);

  // School type distribution
  const typeDistribution = useMemo(() => {
    const filtered = schools.filter(school => {
      const districtMatch = selectedDistrict === 'all' || school.district === selectedDistrict;
      return districtMatch;
    });

    return filtered.reduce((acc: Record<string, number>, school) => {
      acc[school.school_type] = (acc[school.school_type] || 0) + 1;
      return acc;
    }, {});
  }, [schools, selectedDistrict]);

  const exportLocationData = () => {
    const csvData = schoolsByLocation.map((location: LocationGroup) => [
      location.district,
      location.sub_county,
      location.schools.length,
      location.totalStudents,
      location.totalTeachers,
      location.schools.map((s: SchoolType) => s.name).join('; ')
    ]);

    const headers = ['District', 'Sub County', 'Schools Count', 'Total Students', 'Total Teachers', 'School Names'];
    const csvContent = [
      headers.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `school_locations_${new Date().toISOString().split('T')[0]}.csv`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  if (isLoadingSchools) {
    return (
      <div className="p-6">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-2 text-gray-600">Loading school locations...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <MapPin className="h-6 w-6 mr-2 text-purple-600" />
          <div>
            <h1 className="text-3xl font-bold text-gray-900">School Locations</h1>
            <p className="text-gray-600 mt-1">Geographic distribution of schools in the iLead program</p>
          </div>
        </div>
        <Button onClick={exportLocationData} variant="outline">
          <Download className="h-4 w-4 mr-2" />
          Export Data
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Filter className="h-5 w-5 mr-2" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">District</label>
              <Select value={selectedDistrict} onValueChange={setSelectedDistrict}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Districts</SelectItem>
                  {uniqueDistricts.map(district => (
                    <SelectItem key={district} value={district}>{district}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700">School Type</label>
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="primary">Primary</SelectItem>
                  <SelectItem value="secondary">Secondary</SelectItem>
                  <SelectItem value="tertiary">Tertiary</SelectItem>
                  <SelectItem value="vocational">Vocational</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <MapPin className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Locations</p>
                <p className="text-2xl font-bold text-gray-900">{schoolsByLocation.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <School className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Schools</p>
                <p className="text-2xl font-bold text-gray-900">
                  {schools.filter(school => {
                    const districtMatch = selectedDistrict === 'all' || school.district === selectedDistrict;
                    const typeMatch = selectedType === 'all' || school.school_type === selectedType;
                    return districtMatch && typeMatch;
                  }).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Students</p>
                <p className="text-2xl font-bold text-gray-900">
                  {schoolsByLocation.reduce((sum: number, loc: LocationGroup) => sum + loc.totalStudents, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Teachers</p>
                <p className="text-2xl font-bold text-gray-900">
                  {schoolsByLocation.reduce((sum: number, loc: LocationGroup) => sum + loc.totalTeachers, 0).toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* School Type Distribution */}
      <Card>
        <CardHeader>
          <CardTitle>School Type Distribution</CardTitle>
          <CardDescription>
            Distribution of schools by type in the selected region
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            {Object.entries(typeDistribution).map(([type, count]) => (
              <div key={type} className="flex items-center space-x-2">
                <Badge variant={
                  type === 'primary' ? 'default' :
                  type === 'secondary' ? 'secondary' :
                  'outline'
                }>
                  {type}
                </Badge>
                <span className="text-lg font-semibold">{count as number}</span>
                <span className="text-sm text-gray-600">schools</span>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Location Details */}
      <Card>
        <CardHeader>
          <CardTitle>Schools by Location</CardTitle>
          <CardDescription>
            Detailed breakdown of schools in each location
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {schoolsByLocation.map((location: LocationGroup, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold text-lg text-gray-900">{location.location}</h3>
                    <p className="text-sm text-gray-600">
                      {location.schools.length} schools • {location.totalStudents.toLocaleString()} students • {location.totalTeachers} teachers
                    </p>
                  </div>
                  <Badge variant="outline">
                    {Math.round(location.totalStudents / (location.totalTeachers || 1))}:1 ratio
                  </Badge>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                  {location.schools.map((school: SchoolType) => (
                    <div key={school.id} className="bg-gray-50 rounded p-3">
                      <div className="flex items-center justify-between mb-1">
                        <h4 className="font-medium text-sm">{school.name}</h4>
                        <Badge variant={
                          school.school_type === 'primary' ? 'default' :
                          school.school_type === 'secondary' ? 'secondary' :
                          'outline'
                        } className="text-xs">
                          {school.school_type}
                        </Badge>
                      </div>
                      <div className="text-xs text-gray-600">
                        {school.student_count || 0} students • {school.teacher_count || 0} teachers
                      </div>
                      {school.code && (
                        <div className="text-xs text-gray-500 mt-1">{school.code}</div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            ))}

            {schoolsByLocation.length === 0 && (
              <div className="text-center py-8 text-gray-500">
                No schools found matching the selected criteria.
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SchoolLocations;
