import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type StudentAttendance = Database['public']['Tables']['student_attendance']['Row'];
type AttendanceStatus = Database['public']['Enums']['attendance_status'];

export interface AttendanceRecord {
  student_id: string;
  attendance_status: AttendanceStatus;
  check_in_time?: string;
  late_minutes?: number;
  table_number?: number;
  participation_score?: number;
  behavior_notes?: string;
  absence_reason?: string;
}

export interface BulkAttendanceData {
  session_id: string;
  attendance_records: AttendanceRecord[];
}

// Hook to fetch student attendance for a session
export const useSessionAttendance = (sessionId: string) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['session-attendance', sessionId, profile?.id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('student_attendance')
        .select(`
          *,
          student:students(
            id,
            first_name,
            last_name,
            student_number,
            grade_level,
            gender
          ),
          session:attendance_sessions(
            session_name,
            session_type,
            session_date,
            start_time
          )
        `)
        .eq('session_id', sessionId)
        .order('student(last_name)', { ascending: true });

      if (error) {
        console.error('Error fetching session attendance:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!profile?.id && !!sessionId,
  });
};

// Hook to fetch student attendance history
export const useStudentAttendanceHistory = (
  studentId: string, 
  schoolId?: string,
  dateRange?: { start: Date; end: Date }
) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['student-attendance-history', studentId, schoolId, dateRange, profile?.id],
    queryFn: async () => {
      let query = supabase
        .from('student_attendance')
        .select(`
          *,
          session:attendance_sessions(
            id,
            session_name,
            session_type,
            session_date,
            start_time,
            end_time,
            school:schools(name)
          )
        `)
        .eq('student_id', studentId)
        .order('recorded_at', { ascending: false });

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      if (dateRange) {
        query = query
          .gte('recorded_at', dateRange.start.toISOString())
          .lte('recorded_at', dateRange.end.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching student attendance history:', error);
        throw error;
      }

      return data || [];
    },
    enabled: !!profile?.id && !!studentId,
  });
};

// Hook to record individual student attendance
export const useRecordStudentAttendance = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      sessionId,
      studentId,
      attendanceStatus,
      checkInTime,
      lateMinutes = 0,
      tableNumber,
      participationScore,
      behaviorNotes,
      absenceReason,
    }: {
      sessionId: string;
      studentId: string;
      attendanceStatus: AttendanceStatus;
      checkInTime?: string;
      lateMinutes?: number;
      tableNumber?: number;
      participationScore?: number;
      behaviorNotes?: string;
      absenceReason?: string;
    }) => {
      const { data, error } = await supabase.rpc('record_student_attendance', {
        p_session_id: sessionId,
        p_student_id: studentId,
        p_attendance_status: attendanceStatus,
        p_check_in_time: checkInTime,
        p_late_minutes: lateMinutes,
        p_table_number: tableNumber,
        p_participation_score: participationScore,
        p_behavior_notes: behaviorNotes,
        p_absence_reason: absenceReason,
      });

      if (error) {
        console.error('Error recording student attendance:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['session-attendance', variables.sessionId] });
      queryClient.invalidateQueries({ queryKey: ['student-attendance-history', variables.studentId] });
      toast({
        title: 'Success',
        description: 'Student attendance recorded successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to record student attendance:', error);
      toast({
        title: 'Error',
        description: 'Failed to record attendance. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to bulk record attendance for multiple students
export const useBulkRecordAttendance = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ session_id, attendance_records }: BulkAttendanceData) => {
      const { data, error } = await supabase.rpc('bulk_record_attendance', {
        p_session_id: session_id,
        p_attendance_records: attendance_records,
      });

      if (error) {
        console.error('Error bulk recording attendance:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (recordsProcessed, variables) => {
      queryClient.invalidateQueries({ queryKey: ['session-attendance', variables.session_id] });
      // Invalidate attendance history for all affected students
      variables.attendance_records.forEach(record => {
        queryClient.invalidateQueries({ queryKey: ['student-attendance-history', record.student_id] });
      });
      
      toast({
        title: 'Success',
        description: `Successfully recorded attendance for ${recordsProcessed} students`,
      });
    },
    onError: (error: Error) => {
      console.error('Failed to bulk record attendance:', error);
      toast({
        title: 'Error',
        description: 'Failed to record bulk attendance. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to update student attendance
export const useUpdateStudentAttendance = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      attendanceId, 
      updates 
    }: { 
      attendanceId: string; 
      updates: Partial<StudentAttendance> 
    }) => {
      const { data, error } = await supabase
        .from('student_attendance')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', attendanceId)
        .select()
        .single();

      if (error) {
        console.error('Error updating student attendance:', error);
        throw error;
      }

      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['session-attendance', data.session_id] });
      queryClient.invalidateQueries({ queryKey: ['student-attendance-history', data.student_id] });
      toast({
        title: 'Success',
        description: 'Student attendance updated successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to update student attendance:', error);
      toast({
        title: 'Error',
        description: 'Failed to update attendance. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to get attendance statistics for a student
export const useStudentAttendanceStats = (studentId: string, schoolId?: string) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['student-attendance-stats', studentId, schoolId],
    queryFn: async () => {
      let query = supabase
        .from('student_attendance')
        .select('attendance_status, session:attendance_sessions(session_date)')
        .eq('student_id', studentId);

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching student attendance stats:', error);
        throw error;
      }

      const stats = {
        total_sessions: data.length,
        present: data.filter(a => a.attendance_status === 'present').length,
        absent: data.filter(a => a.attendance_status === 'absent').length,
        late: data.filter(a => a.attendance_status === 'late').length,
        excused: data.filter(a => a.attendance_status === 'excused').length,
        attendance_rate: data.length > 0 
          ? ((data.filter(a => ['present', 'late'].includes(a.attendance_status)).length / data.length) * 100).toFixed(1)
          : '0',
      };

      return stats;
    },
    enabled: !!profile?.id && !!studentId,
  });
};
