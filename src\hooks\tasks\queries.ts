
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';
import { queryKeys } from '@/lib/queryClient';
import { TaskQueryParams } from './types';

// Recent tasks hook for dashboard - simplified without performance monitoring
export const useRecentTasks = (limit: number = 5) => {
  return useQuery({
    queryKey: queryKeys.tasks.recent(limit),
    queryFn: async () => {
      console.log('🔍 Fetching recent tasks with limit:', limit);
      
      const { data, error } = await supabase
        .rpc('get_recent_tasks', { p_limit: limit });

      if (error) {
        console.error('❌ Error fetching recent tasks:', error);
        throw error;
      }
      
      console.log('✅ Recent tasks fetched successfully:', data?.length || 0, 'tasks');
      console.log('📋 Recent tasks data:', data);
      return data || [];
    },
    staleTime: 30000,
  });
};

// Hook for fetching tasks with filters and comment counts
export const useTasks = (params: TaskQueryParams = {}) => {
  return useQuery({
    queryKey: queryKeys.tasks.list(params),
    queryFn: async () => {
      console.log('🔍 Fetching tasks with params:', params);
      
      const { data, error } = await supabase
        .rpc('get_tasks_optimized', {
          p_user_id: params.userId || null,
          p_status_filter: params.statusFilter === 'all' ? null : params.statusFilter || null,
          p_assigned_filter: params.assignedFilter || null,
          p_include_comments: params.includeComments || false,
          p_limit: params.limit || null,
        });

      if (error) {
        console.error('❌ Error fetching tasks:', error);
        throw error;
      }

      console.log('✅ Tasks fetched successfully:', data?.length || 0, 'tasks');
      console.log('📋 Tasks data:', data);
      return data || [];
    },
    staleTime: 30000,
  });
};

// Hook for fetching tasks assigned to the current user
export const useMyTasks = () => {
  return useQuery({
    queryKey: ['my-tasks'],
    queryFn: async () => {
      console.log('🔍 Fetching my tasks...');
      
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('❌ No authenticated user found');
        return [];
      }

      console.log('👤 Current user ID:', user.id);

      const { data, error } = await supabase
        .rpc('get_tasks_optimized', {
          p_user_id: user.id,
          p_status_filter: null,
          p_assigned_filter: null,
          p_include_comments: false,
          p_limit: null,
        });

      if (error) {
        console.error('❌ Error fetching my tasks:', error);
        throw error;
      }

      console.log('✅ My tasks fetched successfully:', data?.length || 0, 'tasks');
      console.log('📋 My tasks data:', data);
      return data || [];
    },
    staleTime: 30000,
  });
};

// Hook for fetching tasks managed by the current user (created by the user)
// For admins, this returns ALL tasks in the system
export const useManagedTasks = () => {
  return useQuery({
    queryKey: ['managed-tasks'],
    queryFn: async () => {
      console.log('🔍 Fetching managed tasks...');

      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        console.log('❌ No authenticated user found');
        return [];
      }

      console.log('👤 Current user ID:', user.id);

      // Get user profile to check role
      const { data: profile } = await supabase
        .from('profiles')
        .select('role')
        .eq('id', user.id)
        .maybeSingle();

      const isAdmin = profile?.role === 'admin';
      console.log('👑 Is admin:', isAdmin);

      const { data, error } = await supabase
        .rpc('get_tasks_optimized', {
          p_user_id: null,
          p_status_filter: null,
          // For admins, show all tasks (null assigned_filter)
          // For others, show only tasks they created/manage
          p_assigned_filter: isAdmin ? null : user.id,
          p_include_comments: false,
          p_limit: null,
        });

      if (error) {
        console.error('❌ Error fetching managed tasks:', error);
        throw error;
      }

      console.log('✅ Managed tasks fetched successfully:', data?.length || 0, 'tasks');
      console.log('📋 Managed tasks data:', data);
      return data || [];
    },
    staleTime: 30000,
  });
};

// Hook for fetching completed tasks
export const useCompletedTasks = () => {
  return useTasks({ statusFilter: 'completed' });
};

// Hook for fetching overdue tasks
export const useOverdueTasks = () => {
  return useQuery({
    queryKey: ['overdue-tasks'],
    queryFn: async () => {
      console.log('🔍 Fetching overdue tasks...');
      
      const { data, error } = await supabase
        .rpc('get_tasks_optimized', {
          p_user_id: null,
          p_status_filter: 'pending',
          p_assigned_filter: null,
          p_include_comments: false,
          p_limit: null,
        });

      if (error) {
        console.error('❌ Error fetching overdue tasks:', error);
        throw error;
      }

      // Filter for overdue tasks on the client side
      const overdueTasks = (data || []).filter(task => 
        task.due_date && 
        new Date(task.due_date) < new Date() && 
        task.status !== 'completed'
      );

      console.log('✅ Overdue tasks loaded:', overdueTasks.length, 'tasks');
      console.log('📋 Overdue tasks data:', overdueTasks);
      return overdueTasks;
    },
    staleTime: 30000,
  });
};
