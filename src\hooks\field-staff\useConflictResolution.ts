/**
 * useConflictResolution - Focused hook for managing sync conflicts
 * Handles conflict detection, resolution, and management
 */

import { useCallback, useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  ConflictData,
  OfflineData,
  CONFLICT_RESOLUTION
} from '@/types/offlineSync.types';
import { 
  getAllConflicts,
  removeConflict,
  resolveConflict as resolveConflictUtil
} from '@/utils/conflictResolution';

export const useConflictResolution = (
  addToOfflineQueue: (
    type: OfflineData['type'], 
    data: Record<string, unknown>, 
    priority: OfflineData['priority']
  ) => string
) => {
  const [conflicts, setConflicts] = useState<ConflictData[]>([]);

  // Update conflicts list
  const updateConflicts = useCallback(() => {
    try {
      const conflictData = getAllConflicts();
      setConflicts(conflictData);
    } catch (error) {
      console.error('Failed to update conflicts:', error);
    }
  }, []);

  // Resolve conflict
  const resolveConflict = useCallback((
    conflictId: string,
    strategy: keyof typeof CONFLICT_RESOLUTION,
    customResolution?: Record<string, unknown>
  ): boolean => {
    try {
      const conflict = conflicts.find(c => c.id === conflictId);
      if (!conflict) {
        toast.error('Conflict not found');
        return false;
      }

      const resolvedData = resolveConflictUtil(conflict, strategy, customResolution);
      const success = removeConflict(conflictId);
      
      if (success) {
        updateConflicts();
        toast.success('Conflict resolved');
        
        // Add resolved data back to sync queue if needed
        if (strategy !== 'SERVER_WINS') {
          addToOfflineQueue(conflict.type as OfflineData['type'], resolvedData, 'HIGH');
        }
      }
      
      return success;
    } catch (error) {
      console.error('Failed to resolve conflict:', error);
      toast.error('Failed to resolve conflict');
      return false;
    }
  }, [conflicts, addToOfflineQueue, updateConflicts]);

  // Resolve all conflicts with same strategy
  const resolveAllConflicts = useCallback((
    strategy: keyof typeof CONFLICT_RESOLUTION
  ): number => {
    let resolvedCount = 0;
    
    conflicts.forEach(conflict => {
      if (resolveConflict(conflict.id, strategy)) {
        resolvedCount++;
      }
    });
    
    if (resolvedCount > 0) {
      toast.success(`Resolved ${resolvedCount} conflicts`);
    }
    
    return resolvedCount;
  }, [conflicts, resolveConflict]);

  // Get conflicts by type
  const getConflictsByType = useCallback((type: string): ConflictData[] => {
    return conflicts.filter(conflict => conflict.type === type);
  }, [conflicts]);

  // Get conflict count
  const getConflictCount = useCallback((): number => {
    return conflicts.length;
  }, [conflicts]);

  // Check if has conflicts
  const hasConflicts = useCallback((): boolean => {
    return conflicts.length > 0;
  }, [conflicts]);

  // Initialize conflicts
  useEffect(() => {
    updateConflicts();
  }, [updateConflicts]);

  return {
    conflicts,
    resolveConflict,
    resolveAllConflicts,
    getConflictsByType,
    getConflictCount,
    hasConflicts,
    updateConflicts,
  };
};
