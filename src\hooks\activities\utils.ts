
import { ActivityType } from './types';

// Helper function to format activity time
export const formatActivityTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
  } else {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days} day${days !== 1 ? 's' : ''} ago`;
  }
};

// Helper function to get activity icon
export const getActivityIcon = (activityType: ActivityType): string => {
  switch (activityType) {
    case 'task_created':
      return '📝';
    case 'task_updated':
      return '✏️';
    case 'task_completed':
      return '✅';
    case 'distribution_logged':
      return '📚';
    case 'school_added':
      return '🏫';
    case 'comment_added':
      return '💬';
    default:
      return '📋';
  }
};

// Helper function to get activity color
export const getActivityColor = (activityType: ActivityType): string => {
  switch (activityType) {
    case 'task_created':
      return 'text-blue-600 bg-blue-100';
    case 'task_updated':
      return 'text-yellow-600 bg-yellow-100';
    case 'task_completed':
      return 'text-green-600 bg-green-100';
    case 'distribution_logged':
      return 'text-orange-600 bg-orange-100';
    case 'school_added':
      return 'text-purple-600 bg-purple-100';
    case 'comment_added':
      return 'text-gray-600 bg-gray-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};
