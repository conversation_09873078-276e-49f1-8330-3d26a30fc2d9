/**
 * iLead Program Lesson Topics
 * Organized by curriculum: iChoose, iLead, and iDo
 */

export interface LessonTopic {
  id: string;
  title: string;
  category: 'iChoose' | 'iLead' | 'iDo';
}

export const ICHOOSE_LESSONS: LessonTopic[] = [
  // Group 1: Core Values
  { id: 'ichoose-1', title: '1. Choices', category: 'iChoose' },
  { id: 'ichoose-2', title: '2. Growth', category: 'iChoose' },
  { id: 'ichoose-3', title: '3. Attitude', category: 'iChoose' },
  { id: 'ichoose-4', title: '4. Commitment', category: 'iChoose' },
  
  // Group 2: Relationships
  { id: 'ichoose-5', title: '5. Relationships', category: 'iChoose' },
  { id: 'ichoose-6', title: '6. Character', category: 'iChoose' },
  { id: 'ichoose-7', title: '7. Forgiveness', category: 'iChoose' },
  { id: 'ichoose-8', title: '8. Self-Worth', category: 'iChoose' },
  
  // Group 3: Leadership Qualities
  { id: 'ichoose-9', title: '9. Responsibility', category: 'iChoose' },
  { id: 'ichoose-10', title: '10. Courage', category: 'iChoose' },
  { id: 'ichoose-11', title: '11. Initiative', category: 'iChoose' },
  { id: 'ichoose-12', title: '12. Priorities', category: 'iChoose' },
  
  // Group 4: Personal Development
  { id: 'ichoose-13', title: '13. Teachability', category: 'iChoose' },
  { id: 'ichoose-14', title: '14. Self-Discipline', category: 'iChoose' },
  { id: 'ichoose-15', title: '15. Resilience', category: 'iChoose' },
  { id: 'ichoose-16', title: '16. Influence', category: 'iChoose' },
];

export const ILEAD_LESSONS: LessonTopic[] = [
  // Group 1: Foundation
  { id: 'ilead-1', title: '1. The Definition of Leadership - Influence', category: 'iLead' },
  { id: 'ilead-2', title: '2. The Foundation of Leadership - Values', category: 'iLead' },
  { id: 'ilead-3', title: '3. The Heart of Leadership - Servanthood', category: 'iLead' },
  { id: 'ilead-4', title: '4. The Motives of Leadership - Character', category: 'iLead' },
  
  // Group 2: Leadership Essentials
  { id: 'ilead-5', title: '5. The Necessity of Leadership - Accountability', category: 'iLead' },
  { id: 'ilead-6', title: '6. The Success of Leadership - Teamwork', category: 'iLead' },
  { id: 'ilead-7', title: '7. The Fuel of Leadership - Passion', category: 'iLead' },
  { id: 'ilead-8', title: '8. The Test of Leadership - Courage', category: 'iLead' },
  
  // Group 3: Leadership Skills
  { id: 'ilead-9', title: '9. The Connection of Leadership - Communication', category: 'iLead' },
  { id: 'ilead-10', title: '10. The Core of Leadership - Strengths', category: 'iLead' },
  { id: 'ilead-11', title: '11. The Picture of Leadership - Example', category: 'iLead' },
  { id: 'ilead-12', title: '12. The Focus of Leadership - Priorities', category: 'iLead' },
  
  // Group 4: Leadership Vision
  { id: 'ilead-13', title: '13. The Compass of Leadership - Vision', category: 'iLead' },
  { id: 'ilead-14', title: '14. The Preparation of Leadership - Practice', category: 'iLead' },
  { id: 'ilead-15', title: '15. The Goal of Leadership - Make a Difference', category: 'iLead' },
  { id: 'ilead-16', title: '16. The Message of Leadership - Hope', category: 'iLead' },
];

export const IDO_LESSONS: LessonTopic[] = [
  // Group 1: Personal Influence
  { id: 'ido-1', title: '1. My Questions Influence My Actions', category: 'iDo' },
  { id: 'ido-2', title: '2. My Values Influence My Actions', category: 'iDo' },
  { id: 'ido-3', title: '3. My Priorities Influence My Actions', category: 'iDo' },
  { id: 'ido-4', title: '4. My Thoughts Influence My Actions', category: 'iDo' },
  
  // Group 2: External Influences
  { id: 'ido-5', title: '5. My Friends Influence My Actions', category: 'iDo' },
  { id: 'ido-6', title: '6. My Experiences Influence My Actions', category: 'iDo' },
  { id: 'ido-7', title: '7. My Growth Influences My Actions', category: 'iDo' },
  { id: 'ido-8', title: '8. My Pain Influences My Actions', category: 'iDo' },
  
  // Group 3: Social Influences
  { id: 'ido-9', title: '9. My Conversations Influence My Actions', category: 'iDo' },
  { id: 'ido-10', title: '10. My Self-Image Influences My Actions', category: 'iDo' },
  { id: 'ido-11', title: '11. My Habits Influences My Actions', category: 'iDo' },
  { id: 'ido-12', title: '12. My Relationships Influence My Actions', category: 'iDo' },
  
  // Group 4: Life Influences
  { id: 'ido-13', title: '13. My Listening Influences My Actions', category: 'iDo' },
  { id: 'ido-14', title: '14. My Health Influences My Actions', category: 'iDo' },
  { id: 'ido-15', title: '15. My Perspective Influences My Actions', category: 'iDo' },
  { id: 'ido-16', title: '16. My Dreams Influence My Actions', category: 'iDo' },
];

// Combined list of all lessons
export const ALL_LESSON_TOPICS: LessonTopic[] = [
  ...ICHOOSE_LESSONS,
  ...ILEAD_LESSONS,
  ...IDO_LESSONS,
];

// Helper functions
export const getLessonsByCategory = (category: 'iChoose' | 'iLead' | 'iDo'): LessonTopic[] => {
  return ALL_LESSON_TOPICS.filter(lesson => lesson.category === category);
};

export const getLessonById = (id: string): LessonTopic | undefined => {
  return ALL_LESSON_TOPICS.find(lesson => lesson.id === id);
};

export const LESSON_CATEGORIES = ['iChoose', 'iLead', 'iDo'] as const;
