import React from 'react';
import { ContentCard } from '@/components/layout';
import { LucideIcon } from 'lucide-react';

interface ComingSoonCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  placeholderIcon: LucideIcon;
  features: string[];
  className?: string;
}

/**
 * Standardized "Coming Soon" component for Impact Measurement modules
 * Provides consistent styling and layout for modules under development
 */
const ComingSoonCard: React.FC<ComingSoonCardProps> = ({
  title,
  description,
  icon,
  placeholderIcon: PlaceholderIcon,
  features,
  className
}) => {
  return (
    <ContentCard
      title={title}
      description={description}
      icon={icon}
      className={className}
    >
      <div className="text-center py-12">
        <div className="bg-gray-100 p-4 rounded-lg inline-block mb-4">
          <PlaceholderIcon className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          {title} Coming Soon
        </h3>
        <p className="text-gray-600 mb-4">
          {description}
        </p>
        <div className="space-y-2 text-sm text-gray-500">
          {features.map((feature, index) => (
            <p key={index}>• {feature}</p>
          ))}
        </div>
      </div>
    </ContentCard>
  );
};

export default ComingSoonCard;
