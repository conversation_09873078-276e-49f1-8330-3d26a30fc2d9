import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  Clock, 
  Users, 
  MapPin,
  BookOpen,
  Target,
  User,
  School,
  Timer,
  CheckCircle,
  XCircle,
  AlertCircle,
  Edit,
  Play,
  Square,
  RotateCcw,
  FileText,
  TrendingUp
} from 'lucide-react';
import { useAttendanceSession } from '@/hooks/attendance/useAttendanceSessions';
import { useSessionAttendance } from '@/hooks/attendance/useStudentAttendance';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import AttendanceTracker from './AttendanceTracker';
import { toast } from 'sonner';

type AttendanceStatus = Database['public']['Enums']['attendance_status'];

interface SessionDetailsProps {
  sessionId: string;
  onBack?: () => void;
}

const SessionDetails: React.FC<SessionDetailsProps> = ({
  sessionId,
  onBack,
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  
  const { data: session, isLoading: sessionLoading, error: sessionError } = useAttendanceSession(sessionId);
  const { data: attendance, isLoading: attendanceLoading } = useSessionAttendance(sessionId);

  if (sessionLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-3 text-gray-600">Loading session details...</span>
        </div>
      </PageLayout>
    );
  }

  if (sessionError || !session) {
    return (
      <PageLayout>
        <ContentCard>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Session Not Found</h3>
            <p className="text-gray-600 mb-4">
              The requested session could not be found or you don't have permission to view it.
            </p>
            <Button onClick={onBack}>
              Go Back
            </Button>
          </div>
        </ContentCard>
      </PageLayout>
    );
  }

  const getSessionStatus = () => {
    const now = new Date();
    const sessionDate = new Date(session.session_date);
    const startTime = new Date(`${session.session_date}T${session.start_time}`);

    if (session.actual_end_time) return 'completed';
    if (session.actual_start_time && !session.actual_end_time) return 'active';
    if (sessionDate.toDateString() === now.toDateString() && startTime <= now) return 'ready';
    if (sessionDate > now) return 'upcoming';
    if (sessionDate < now) return 'missed';
    return 'scheduled';
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-50' },
      active: { variant: 'default' as const, icon: Timer, color: 'text-blue-600', bg: 'bg-blue-50' },
      ready: { variant: 'secondary' as const, icon: Clock, color: 'text-orange-600', bg: 'bg-orange-50' },
      upcoming: { variant: 'outline' as const, icon: Calendar, color: 'text-gray-600', bg: 'bg-gray-50' },
      missed: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600', bg: 'bg-red-50' },
      scheduled: { variant: 'outline' as const, icon: Clock, color: 'text-gray-600', bg: 'bg-gray-50' },
    };

    const config = variants[status as keyof typeof variants] || variants.scheduled;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const calculateAttendanceStats = () => {
    if (!attendance) return { total: 0, present: 0, absent: 0, late: 0, excused: 0, rate: 0 };
    
    const total = attendance.length;
    const present = attendance.filter(a => a.attendance_status === 'present').length;
    const absent = attendance.filter(a => a.attendance_status === 'absent').length;
    const late = attendance.filter(a => a.attendance_status === 'late').length;
    const excused = attendance.filter(a => a.attendance_status === 'excused').length;
    const rate = total > 0 ? Math.round(((present + late) / total) * 100) : 0;

    return { total, present, absent, late, excused, rate };
  };

  const status = getSessionStatus();
  const attendanceStats = calculateAttendanceStats();

  const handleStartSession = async () => {
    try {
      // TODO: Implement start session functionality
      toast.success('Session started successfully');
    } catch (error) {
      toast.error('Failed to start session');
    }
  };

  const handleEndSession = async () => {
    try {
      // TODO: Implement end session functionality
      toast.success('Session ended successfully');
    } catch (error) {
      toast.error('Failed to end session');
    }
  };

  return (
    <PageLayout>
      <PageHeader
        title={session.session_name}
        description={`${session.session_type.replace('_', ' ')} session at ${session.school?.name}`}
        action={
          <div className="flex items-center gap-2">
            {onBack && (
              <Button variant="outline" onClick={onBack}>
                ← Back
              </Button>
            )}
            <Button variant="outline">
              <Edit className="h-4 w-4 mr-2" />
              Edit Session
            </Button>
          </div>
        }
      />

      {/* Session Status and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center gap-2">
                  Session Status
                  {getStatusBadge(status)}
                </CardTitle>
                <CardDescription>
                  {new Date(session.session_date).toLocaleDateString('en-US', {
                    weekday: 'long',
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })} • {session.start_time} - {session.end_time || 'TBD'}
                </CardDescription>
              </div>
              <div className="flex gap-2">
                {status === 'ready' && (
                  <Button onClick={handleStartSession}>
                    <Play className="h-4 w-4 mr-2" />
                    Start Session
                  </Button>
                )}
                {status === 'active' && (
                  <Button variant="destructive" onClick={handleEndSession}>
                    <Square className="h-4 w-4 mr-2" />
                    End Session
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{attendanceStats.total}</div>
                <div className="text-sm text-gray-600">Total Students</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{attendanceStats.present}</div>
                <div className="text-sm text-gray-600">Present</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{attendanceStats.absent}</div>
                <div className="text-sm text-gray-600">Absent</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{attendanceStats.rate}%</div>
                <div className="text-sm text-gray-600">Attendance Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Session Info</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex items-center gap-2 text-sm">
              <School className="h-4 w-4 text-gray-500" />
              <span className="font-medium">School:</span>
              <span>{session.school?.name}</span>
            </div>
            
            <div className="flex items-center gap-2 text-sm">
              <BookOpen className="h-4 w-4 text-gray-500" />
              <span className="font-medium">Type:</span>
              <span className="capitalize">{session.session_type.replace('_', ' ')}</span>
            </div>

            {session.facilitator && (
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Facilitator:</span>
                <span>{session.facilitator.name}</span>
              </div>
            )}

            {session.grade_level && (
              <div className="flex items-center gap-2 text-sm">
                <Target className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Grade:</span>
                <span>{session.grade_level}</span>
              </div>
            )}

            {session.subject && (
              <div className="flex items-center gap-2 text-sm">
                <BookOpen className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Subject:</span>
                <span>{session.subject}</span>
              </div>
            )}

            {session.location && (
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Location:</span>
                <span>{session.location}</span>
              </div>
            )}

            {session.max_capacity && (
              <div className="flex items-center gap-2 text-sm">
                <Users className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Capacity:</span>
                <span>{session.max_capacity} students</span>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Tabs for detailed information */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="attendance">Attendance</TabsTrigger>
          <TabsTrigger value="details">Details</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Session Description */}
            {session.session_description && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <FileText className="h-5 w-5" />
                    Description
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 whitespace-pre-wrap">{session.session_description}</p>
                </CardContent>
              </Card>
            )}

            {/* Learning Objectives */}
            {session.learning_objectives && session.learning_objectives.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Learning Objectives
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {session.learning_objectives.map((objective, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{objective}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Materials Needed */}
            {session.materials_needed && session.materials_needed.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Materials Needed
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {session.materials_needed.map((material, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <div className="h-2 w-2 bg-gray-400 rounded-full mt-2 flex-shrink-0" />
                        <span className="text-gray-700">{material}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Session Timeline */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="h-2 w-2 bg-blue-600 rounded-full" />
                    <div>
                      <div className="font-medium">Scheduled Start</div>
                      <div className="text-sm text-gray-600">{session.start_time}</div>
                    </div>
                  </div>
                  
                  {session.actual_start_time && (
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-green-600 rounded-full" />
                      <div>
                        <div className="font-medium">Actual Start</div>
                        <div className="text-sm text-gray-600">{session.actual_start_time}</div>
                      </div>
                    </div>
                  )}

                  {session.end_time && (
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-gray-400 rounded-full" />
                      <div>
                        <div className="font-medium">Scheduled End</div>
                        <div className="text-sm text-gray-600">{session.end_time}</div>
                      </div>
                    </div>
                  )}

                  {session.actual_end_time && (
                    <div className="flex items-center gap-3">
                      <div className="h-2 w-2 bg-red-600 rounded-full" />
                      <div>
                        <div className="font-medium">Actual End</div>
                        <div className="text-sm text-gray-600">{session.actual_end_time}</div>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="attendance">
          <AttendanceTracker
            sessionId={sessionId}
            sessionName={session.session_name}
            sessionDate={session.session_date}
            sessionType={session.session_type}
            roundTablesCount={session.round_tables_count || 0}
            studentsPerTable={session.students_per_table || 8}
          />
        </TabsContent>

        <TabsContent value="details">
          <Card>
            <CardHeader>
              <CardTitle>Session Details</CardTitle>
              <CardDescription>Complete information about this session</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Session ID</label>
                    <p className="text-sm text-gray-900 font-mono">{session.id}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">Created</label>
                    <p className="text-sm text-gray-900">
                      {new Date(session.created_at).toLocaleString()}
                    </p>
                  </div>

                  <div>
                    <label className="text-sm font-medium text-gray-600">Last Updated</label>
                    <p className="text-sm text-gray-900">
                      {new Date(session.updated_at).toLocaleString()}
                    </p>
                  </div>

                  {session.planned_duration_minutes && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Planned Duration</label>
                      <p className="text-sm text-gray-900">{session.planned_duration_minutes} minutes</p>
                    </div>
                  )}
                </div>

                <div className="space-y-4">
                  {session.round_tables_count && session.round_tables_count > 0 && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Round Tables</label>
                      <p className="text-sm text-gray-900">
                        {session.round_tables_count} tables, {session.students_per_table || 8} students per table
                      </p>
                    </div>
                  )}

                  {session.teacher_name && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Teacher</label>
                      <p className="text-sm text-gray-900">{session.teacher_name}</p>
                    </div>
                  )}

                  {session.session_notes && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Notes</label>
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">{session.session_notes}</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reports">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Session Reports
              </CardTitle>
              <CardDescription>Analytics and reports for this session</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Reports Coming Soon</h3>
                <p className="text-gray-600">
                  Detailed analytics and reports for this session will be available here.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default SessionDetails;
