export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      books: {
        Row: {
          id: string
          title: string
          author: string
          isbn: string | null
          publication_year: number | null
          category: Database["public"]["Enums"]["book_category"]
          grade_level: Database["public"]["Enums"]["grade_level"]
          language: Database["public"]["Enums"]["book_language"]
          publisher: string | null
          description: string | null
          created_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          title: string
          author: string
          isbn?: string | null
          publication_year?: number | null
          category: Database["public"]["Enums"]["book_category"]
          grade_level: Database["public"]["Enums"]["grade_level"]
          language: Database["public"]["Enums"]["book_language"]
          publisher?: string | null
          description?: string | null
          created_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          title?: string
          author?: string
          isbn?: string | null
          publication_year?: number | null
          category?: Database["public"]["Enums"]["book_category"]
          grade_level?: Database["public"]["Enums"]["grade_level"]
          language?: Database["public"]["Enums"]["book_language"]
          publisher?: string | null
          description?: string | null
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "books_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      book_inventory: {
        Row: {
          id: string
          book_id: string
          total_quantity: number
          available_quantity: number
          distributed_quantity: number
          damaged_quantity: number
          lost_quantity: number
          minimum_threshold: number
          condition: Database["public"]["Enums"]["book_condition"]
          storage_location: string | null
          cost_per_unit: number | null
          notes: string | null
          last_updated_by: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          book_id: string
          total_quantity: number
          available_quantity: number
          distributed_quantity?: number
          damaged_quantity?: number
          lost_quantity?: number
          minimum_threshold: number
          condition: Database["public"]["Enums"]["book_condition"]
          storage_location?: string | null
          cost_per_unit?: number | null
          notes?: string | null
          last_updated_by: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          book_id?: string
          total_quantity?: number
          available_quantity?: number
          distributed_quantity?: number
          damaged_quantity?: number
          lost_quantity?: number
          minimum_threshold?: number
          condition?: Database["public"]["Enums"]["book_condition"]
          storage_location?: string | null
          cost_per_unit?: number | null
          notes?: string | null
          last_updated_by?: string
          created_at?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "book_inventory_book_id_fkey"
            columns: ["book_id"]
            isOneToOne: false
            referencedRelation: "books"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "book_inventory_last_updated_by_fkey"
            columns: ["last_updated_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          }
        ]
      }
      profiles: {
        Row: {
          id: string
          email: string
          full_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string | null
          role?: Database["public"]["Enums"]["user_role"]
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
      schools: {
        Row: {
          id: string
          name: string
          code: string
          school_type: Database["public"]["Enums"]["school_type"]
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          code: string
          school_type: Database["public"]["Enums"]["school_type"]
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          code?: string
          school_type?: Database["public"]["Enums"]["school_type"]
          created_at?: string
          updated_at?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      get_user_profile: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          email: string
          full_name: string | null
          role: Database["public"]["Enums"]["user_role"]
          created_at: string
          updated_at: string
        }[]
      }
      get_books_with_inventory: {
        Args: Record<PropertyKey, never>
        Returns: {
          id: string
          title: string
          author: string
          isbn: string | null
          publication_year: number | null
          category: Database["public"]["Enums"]["book_category"]
          grade_level: Database["public"]["Enums"]["grade_level"]
          language: Database["public"]["Enums"]["book_language"]
          publisher: string | null
          description: string | null
          created_by: string
          created_at: string
          updated_at: string
          total_quantity: number
          available_quantity: number
          distributed_quantity: number
          damaged_quantity: number
          lost_quantity: number
          minimum_threshold: number
          condition: Database["public"]["Enums"]["book_condition"]
          storage_location: string | null
          cost_per_unit: number | null
          inventory_notes: string | null
        }[]
      }
      add_book_with_inventory: {
        Args: {
          p_title: string
          p_author: string
          p_category: Database["public"]["Enums"]["book_category"]
          p_grade_level: Database["public"]["Enums"]["grade_level"]
          p_isbn?: string | null
          p_publication_year?: number | null
          p_language: Database["public"]["Enums"]["book_language"]
          p_publisher?: string | null
          p_description?: string | null
          p_total_quantity: number
          p_condition: Database["public"]["Enums"]["book_condition"]
          p_storage_location?: string | null
          p_cost_per_unit?: number | null
          p_minimum_threshold: number
          p_notes?: string | null
        }
        Returns: string
      }
    }
    Enums: {
      book_category:
        | "mathematics"
        | "english"
        | "science"
        | "social_studies"
        | "religious_education"
        | "physical_education"
        | "art"
        | "music"
        | "local_language"
        | "life_skills"
        | "other"
      book_condition: "new" | "good" | "fair" | "poor"
      book_language:
        | "english"
        | "luganda"
        | "runyankole"
        | "ateso"
        | "luo"
        | "lugbara"
        | "runyoro"
        | "lusoga"
        | "other"
      grade_level:
        | "nursery"
        | "baby_class"
        | "middle_class"
        | "top_class"
        | "p1"
        | "p2"
        | "p3"
        | "p4"
        | "p5"
        | "p6"
        | "p7"
        | "s1"
        | "s2"
        | "s3"
        | "s4"
        | "s5"
        | "s6"
      school_type: "primary" | "secondary" | "tertiary" | "vocational"
      user_role: "admin" | "program_officer" | "field_staff"
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type PublicSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  PublicTableNameOrOptions extends
    | keyof (PublicSchema["Tables"] & PublicSchema["Views"])
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
        Database[PublicTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? (Database[PublicTableNameOrOptions["schema"]]["Tables"] &
      Database[PublicTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : PublicTableNameOrOptions extends keyof (PublicSchema["Tables"] &
        PublicSchema["Views"])
    ? (PublicSchema["Tables"] &
        PublicSchema["Views"])[PublicTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  PublicTableNameOrOptions extends
    | keyof PublicSchema["Tables"]
    | { schema: keyof Database },
  TableName extends PublicTableNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = PublicTableNameOrOptions extends { schema: keyof Database }
  ? Database[PublicTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : PublicTableNameOrOptions extends keyof PublicSchema["Tables"]
    ? PublicSchema["Tables"][PublicTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  PublicEnumNameOrOptions extends
    | keyof PublicSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends PublicEnumNameOrOptions extends { schema: keyof Database }
    ? keyof Database[PublicEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = PublicEnumNameOrOptions extends { schema: keyof Database }
  ? Database[PublicEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : PublicEnumNameOrOptions extends keyof PublicSchema["Enums"]
    ? PublicSchema["Enums"][PublicEnumNameOrOptions]
    : never
