
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { FileText, MapPin } from 'lucide-react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import PhotoUpload from './PhotoUpload';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type FieldReportInsert = Database['public']['Tables']['field_reports']['Insert'];

type ReportType = 'distribution' | 'inspection' | 'training';

interface FieldReportFormProps {
  schoolId?: string;
  distributionId?: string;
  currentUser: Profile;
}

const FieldReportForm = ({ schoolId, distributionId, currentUser }: FieldReportFormProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState({
    school_id: schoolId || '',
    report_type: 'distribution' as ReportType,
    title: '',
    description: '',
    findings: '',
    recommendations: '',
    photos: [] as string[],
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch schools for the dropdown
  const { data: schools = [] } = useQuery({
    queryKey: ['schools'],
    queryFn: async (): Promise<SchoolWithDivision[]> => {
      const { data, error } = await supabase
        .rpc('get_schools_with_divisions');

      if (error) throw error;
      return data || [];
    },
  });

  // Add field report mutation
  const addReportMutation = useMutation({
    mutationFn: async (reportData: typeof formData): Promise<Database['public']['Tables']['field_reports']['Row']> => {
      const insertData: FieldReportInsert = {
        school_id: reportData.school_id || null,
        distribution_id: distributionId || null,
        report_type: reportData.report_type,
        title: reportData.title,
        description: reportData.description,
        findings: reportData.findings || null,
        recommendations: reportData.recommendations || null,
        photos: reportData.photos.length > 0 ? reportData.photos : null,
        reported_by: currentUser?.id || null,
        gps_coordinates: null, // Will be updated when GPS is implemented
      };

      const { data, error } = await supabase
        .from('field_reports')
        .insert(insertData)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      setFormData({
        school_id: schoolId || '',
        report_type: 'distribution',
        title: '',
        description: '',
        findings: '',
        recommendations: '',
        photos: [],
      });
      setIsOpen(false);
      toast({
        title: "Success",
        description: "Field report submitted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to submit field report",
        variant: "destructive",
      });
    },
  });

  const handlePhotoUploaded = (url: string, fileName: string) => {
    setFormData(prev => ({
      ...prev,
      photos: [...prev.photos, url]
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    addReportMutation.mutate(formData);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="w-full">
          <FileText className="h-4 w-4 mr-2" />
          Submit Field Report
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Field Report</DialogTitle>
          <DialogDescription>
            Document field activities and observations
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="school">School</Label>
              <Select
                value={formData.school_id}
                onValueChange={(value) => setFormData({ ...formData, school_id: value })}
                disabled={!!schoolId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a school" />
                </SelectTrigger>
                <SelectContent className="bg-white z-50">
                  {schools.map((school: SchoolWithDivision) => (
                    <SelectItem key={school.id} value={school.id}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="report_type">Report Type</Label>
              <Select
                value={formData.report_type}
                onValueChange={(value: ReportType) => setFormData({ ...formData, report_type: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="bg-white z-50">
                  <SelectItem value="distribution">Book Distribution</SelectItem>
                  <SelectItem value="inspection">School Inspection</SelectItem>
                  <SelectItem value="training">Teacher Training</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Report Title</Label>
            <Input
              id="title"
              value={formData.title}
              onChange={(e) => setFormData({ ...formData, title: e.target.value })}
              placeholder="Brief title for the report"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Describe the activities conducted"
              rows={3}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="findings">Key Findings</Label>
            <Textarea
              id="findings"
              value={formData.findings}
              onChange={(e) => setFormData({ ...formData, findings: e.target.value })}
              placeholder="What did you observe or discover?"
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="recommendations">Recommendations</Label>
            <Textarea
              id="recommendations"
              value={formData.recommendations}
              onChange={(e) => setFormData({ ...formData, recommendations: e.target.value })}
              placeholder="What actions should be taken?"
              rows={3}
            />
          </div>

          <PhotoUpload
            onPhotoUploaded={handlePhotoUploaded}
            distributionId={distributionId}
            maxFiles={5}
          />

          <Button 
            type="submit" 
            className="w-full bg-green-600 hover:bg-green-700"
            disabled={addReportMutation.isPending}
          >
            {addReportMutation.isPending ? 'Submitting Report...' : 'Submit Field Report'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default FieldReportForm;
