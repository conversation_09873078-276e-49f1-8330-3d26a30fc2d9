import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Play, 
  Download,
  Database,
  Wifi,
  MapPin,
  Bell,
  BarChart3,
  Shield,
  Zap,
  RefreshCw
} from 'lucide-react';
import { 
  generateValidationReport,
  validateDatabaseSchema,
  validateDataConsistency,
  validateGPSFunctionality,
  validateNotificationSystem,
  validatePerformance,
  performSystemHealthCheck
} from '@/utils/attendance/systemValidation';
import { useSchools } from '@/hooks/useSchools';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

interface AttendanceSystemValidatorProps {
  defaultSchoolId?: string;
}

const AttendanceSystemValidator: React.FC<AttendanceSystemValidatorProps> = ({
  defaultSchoolId,
}) => {
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || '');
  const [validationInProgress, setValidationInProgress] = useState(false);
  const [validationProgress, setValidationProgress] = useState(0);
  const [validationReport, setValidationReport] = useState<{
    [key: string]: {
      isValid?: boolean;
      database_connectivity?: boolean;
      errors?: string[];
      warnings?: string[];
    };
    overall_status?: {
      is_healthy: boolean;
      critical_issues?: string[];
      warnings?: string[];
      suggestions?: string[];
    };
    performance_metrics?: {
      query_response_times: Record<string, number>;
      concurrent_user_capacity: number;
      offline_capability: boolean;
    };
    timestamp: string;
  } | null>(null);
  const [currentTest, setCurrentTest] = useState('');

  const { data: schools } = useSchools();

  const runComprehensiveValidation = async () => {
    setValidationInProgress(true);
    setValidationProgress(0);
    setCurrentTest('Initializing...');

    try {
      // Step 1: Database Schema
      setCurrentTest('Validating database schema...');
      setValidationProgress(10);
      await new Promise(resolve => setTimeout(resolve, 500)); // Visual delay

      // Step 2: Data Consistency
      setCurrentTest('Checking data consistency...');
      setValidationProgress(25);
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 3: GPS Functionality
      setCurrentTest('Testing GPS functionality...');
      setValidationProgress(40);
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 4: Notification System
      setCurrentTest('Validating notification system...');
      setValidationProgress(55);
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 5: Performance Metrics
      setCurrentTest('Measuring performance...');
      setValidationProgress(70);
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 6: System Health
      setCurrentTest('Performing system health check...');
      setValidationProgress(85);
      await new Promise(resolve => setTimeout(resolve, 500));

      // Step 7: Generate Report
      setCurrentTest('Generating comprehensive report...');
      setValidationProgress(95);
      const report = await generateValidationReport(selectedSchoolId || undefined);
      
      setValidationProgress(100);
      setCurrentTest('Validation complete!');
      setValidationReport(report);

    } catch (error) {
      console.error('Validation failed:', error);
      setCurrentTest('Validation failed');
    } finally {
      setValidationInProgress(false);
    }
  };

  const runIndividualTest = async (testType: string) => {
    setValidationInProgress(true);
    setCurrentTest(`Running ${testType} test...`);

    try {
      let result;
      switch (testType) {
        case 'schema':
          result = await validateDatabaseSchema();
          break;
        case 'data':
          result = await validateDataConsistency(selectedSchoolId || undefined);
          break;
        case 'gps':
          result = await validateGPSFunctionality();
          break;
        case 'notifications':
          result = await validateNotificationSystem(selectedSchoolId || undefined);
          break;
        case 'performance':
          result = await validatePerformance(selectedSchoolId || undefined);
          break;
        case 'health':
          result = await performSystemHealthCheck(selectedSchoolId || undefined);
          break;
        default:
          throw new Error('Unknown test type');
      }

      // Update report with individual test result
      setValidationReport(prev => ({
        ...prev,
        [testType]: result,
        timestamp: new Date().toISOString(),
      }));

    } catch (error) {
      console.error(`${testType} test failed:`, error);
    } finally {
      setValidationInProgress(false);
      setCurrentTest('');
    }
  };

  const exportReport = () => {
    if (!validationReport) return;

    const reportData = JSON.stringify(validationReport, null, 2);
    const blob = new Blob([reportData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `attendance-system-validation-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const getStatusIcon = (isValid: boolean | undefined) => {
    if (isValid === undefined) return <AlertTriangle className="h-4 w-4 text-gray-400" />;
    return isValid ? 
      <CheckCircle className="h-4 w-4 text-green-600" /> : 
      <XCircle className="h-4 w-4 text-red-600" />;
  };

  const getStatusColor = (isValid: boolean | undefined) => {
    if (isValid === undefined) return 'bg-gray-100 text-gray-800 border-gray-200';
    return isValid ? 
      'bg-green-100 text-green-800 border-green-200' : 
      'bg-red-100 text-red-800 border-red-200';
  };

  const testCategories = [
    {
      id: 'schema',
      title: 'Database Schema',
      description: 'Validate table structure and relationships',
      icon: Database,
      color: 'bg-blue-100 text-blue-600',
    },
    {
      id: 'data',
      title: 'Data Consistency',
      description: 'Check for data integrity issues',
      icon: Shield,
      color: 'bg-green-100 text-green-600',
    },
    {
      id: 'gps',
      title: 'GPS Functionality',
      description: 'Test location services and accuracy',
      icon: MapPin,
      color: 'bg-purple-100 text-purple-600',
    },
    {
      id: 'notifications',
      title: 'Notification System',
      description: 'Validate alert and messaging features',
      icon: Bell,
      color: 'bg-orange-100 text-orange-600',
    },
    {
      id: 'performance',
      title: 'Performance Metrics',
      description: 'Measure system speed and capacity',
      icon: Zap,
      color: 'bg-yellow-100 text-yellow-600',
    },
    {
      id: 'health',
      title: 'System Health',
      description: 'Overall system status check',
      icon: BarChart3,
      color: 'bg-red-100 text-red-600',
    },
  ];

  return (
    <PageLayout>
      <PageHeader
        title="Attendance System Validator"
        description="Comprehensive testing and validation tools for the attendance tracking system"
      />

      {/* Validation Controls */}
      <ContentCard>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <select
              value={selectedSchoolId}
              onChange={(e) => setSelectedSchoolId(e.target.value)}
              className="border rounded-md px-3 py-2"
            >
              <option value="">All Schools</option>
              {schools?.map((school) => (
                <option key={school.id} value={school.id}>
                  {school.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex gap-2">
            <Button
              onClick={runComprehensiveValidation}
              disabled={validationInProgress}
              className="flex items-center gap-2"
            >
              {validationInProgress ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Play className="h-4 w-4" />
              )}
              Run Full Validation
            </Button>
            
            {validationReport && (
              <Button variant="outline" onClick={exportReport}>
                <Download className="h-4 w-4 mr-2" />
                Export Report
              </Button>
            )}
          </div>
        </div>

        {/* Progress Indicator */}
        {validationInProgress && (
          <div className="mt-4 space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>{currentTest}</span>
              <span>{validationProgress}%</span>
            </div>
            <Progress value={validationProgress} className="w-full" />
          </div>
        )}
      </ContentCard>

      {/* Test Categories */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {testCategories.map((category) => {
          const Icon = category.icon;
          const testResult = validationReport?.[category.id];
          const isValid = testResult?.isValid || testResult?.database_connectivity;
          
          return (
            <Card key={category.id} className="relative">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${category.color}`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{category.title}</CardTitle>
                      <CardDescription>{category.description}</CardDescription>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusIcon(isValid)}
                    <Badge className={getStatusColor(isValid)}>
                      {isValid === undefined ? 'Not Tested' : isValid ? 'Passed' : 'Failed'}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => runIndividualTest(category.id)}
                  disabled={validationInProgress}
                  className="w-full"
                >
                  {validationInProgress ? 'Testing...' : 'Run Test'}
                </Button>
                
                {testResult && (
                  <div className="mt-3 space-y-2">
                    {testResult.errors?.length > 0 && (
                      <Alert variant="destructive">
                        <XCircle className="h-4 w-4" />
                        <AlertDescription>
                          {testResult.errors.length} error(s) found
                        </AlertDescription>
                      </Alert>
                    )}
                    {testResult.warnings?.length > 0 && (
                      <Alert>
                        <AlertTriangle className="h-4 w-4" />
                        <AlertDescription>
                          {testResult.warnings.length} warning(s) found
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>
                )}
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Detailed Results */}
      {validationReport && (
        <Tabs defaultValue="summary" className="space-y-6">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="summary">Summary</TabsTrigger>
            <TabsTrigger value="issues">Issues</TabsTrigger>
            <TabsTrigger value="performance">Performance</TabsTrigger>
            <TabsTrigger value="recommendations">Recommendations</TabsTrigger>
          </TabsList>

          <TabsContent value="summary">
            <ContentCard>
              <CardHeader>
                <CardTitle>Validation Summary</CardTitle>
                <CardDescription>
                  Overall system health and validation results
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className={`text-3xl font-bold ${
                      validationReport.overall_status?.is_healthy ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {validationReport.overall_status?.is_healthy ? 'HEALTHY' : 'ISSUES'}
                    </div>
                    <div className="text-sm text-gray-600">System Status</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-red-600">
                      {validationReport.overall_status?.critical_issues?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Critical Issues</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-yellow-600">
                      {validationReport.overall_status?.warnings?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Warnings</div>
                  </div>
                  <div className="text-center">
                    <div className="text-3xl font-bold text-blue-600">
                      {validationReport.overall_status?.suggestions?.length || 0}
                    </div>
                    <div className="text-sm text-gray-600">Suggestions</div>
                  </div>
                </div>

                <div className="mt-6 text-sm text-gray-500">
                  Report generated: {new Date(validationReport.timestamp).toLocaleString()}
                </div>
              </CardContent>
            </ContentCard>
          </TabsContent>

          <TabsContent value="issues">
            <ContentCard>
              <CardHeader>
                <CardTitle>Issues & Warnings</CardTitle>
                <CardDescription>
                  Detailed list of problems found during validation
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {validationReport.overall_status?.critical_issues?.length > 0 && (
                    <div>
                      <h3 className="font-medium text-red-600 mb-2">Critical Issues</h3>
                      <ul className="space-y-1">
                        {validationReport.overall_status.critical_issues.map((issue: string, index: number) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <XCircle className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" />
                            {issue}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {validationReport.overall_status?.warnings?.length > 0 && (
                    <div>
                      <h3 className="font-medium text-yellow-600 mb-2">Warnings</h3>
                      <ul className="space-y-1">
                        {validationReport.overall_status.warnings.map((warning: string, index: number) => (
                          <li key={index} className="text-sm flex items-start gap-2">
                            <AlertTriangle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                            {warning}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {(!validationReport.overall_status?.critical_issues?.length && 
                    !validationReport.overall_status?.warnings?.length) && (
                    <div className="text-center py-8">
                      <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                      <h3 className="text-lg font-medium text-gray-900 mb-2">No Issues Found</h3>
                      <p className="text-gray-600">
                        All validation tests passed successfully.
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </ContentCard>
          </TabsContent>

          <TabsContent value="performance">
            <ContentCard>
              <CardHeader>
                <CardTitle>Performance Metrics</CardTitle>
                <CardDescription>
                  System performance and capacity analysis
                </CardDescription>
              </CardHeader>
              <CardContent>
                {validationReport.performance_metrics ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <h3 className="font-medium mb-3">Query Response Times</h3>
                      <div className="space-y-2">
                        {Object.entries(validationReport.performance_metrics.query_response_times).map(([query, time]) => (
                          <div key={query} className="flex justify-between text-sm">
                            <span className="capitalize">{query.replace('_', ' ')}</span>
                            <span className={`font-medium ${
                              (time as number) < 500 ? 'text-green-600' : 
                              (time as number) < 1000 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {(time as number).toFixed(0)}ms
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <div>
                      <h3 className="font-medium mb-3">System Capacity</h3>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Estimated Concurrent Users</span>
                          <span className="font-medium">{validationReport.performance_metrics.concurrent_user_capacity}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Offline Capability</span>
                          <span className={`font-medium ${
                            validationReport.performance_metrics.offline_capability ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {validationReport.performance_metrics.offline_capability ? 'Available' : 'Not Available'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No performance data available. Run performance test to see metrics.
                  </div>
                )}
              </CardContent>
            </ContentCard>
          </TabsContent>

          <TabsContent value="recommendations">
            <ContentCard>
              <CardHeader>
                <CardTitle>Recommendations</CardTitle>
                <CardDescription>
                  Suggested improvements and optimizations
                </CardDescription>
              </CardHeader>
              <CardContent>
                {validationReport.overall_status?.suggestions?.length > 0 ? (
                  <ul className="space-y-2">
                    {validationReport.overall_status.suggestions.map((suggestion: string, index: number) => (
                      <li key={index} className="text-sm flex items-start gap-2">
                        <CheckCircle className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" />
                        {suggestion}
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No specific recommendations at this time. System appears to be functioning optimally.
                  </div>
                )}
              </CardContent>
            </ContentCard>
          </TabsContent>
        </Tabs>
      )}
    </PageLayout>
  );
};

export default AttendanceSystemValidator;
