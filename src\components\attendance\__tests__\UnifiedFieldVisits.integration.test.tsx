import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { UnifiedFieldVisits } from '../UnifiedFieldVisits';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/integrations/supabase/client');
jest.mock('sonner');
jest.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({
    profile: {
      id: 'test-field-staff-id',
      role: 'field_staff',
      name: 'Test Field Staff'
    }
  })
}));

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockToast = toast as jest.Mocked<typeof toast>;

// Mock geolocation
const mockGeolocation = {
  getCurrentPosition: jest.fn(),
  watchPosition: jest.fn(),
  clearWatch: jest.fn(),
};

Object.defineProperty(global.navigator, 'geolocation', {
  value: mockGeolocation,
  writable: true,
});

// Test wrapper
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('UnifiedFieldVisits - Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    Object.defineProperty(global.navigator, 'onLine', {
      value: true,
      writable: true,
    });

    // Mock successful GPS position
    mockGeolocation.getCurrentPosition.mockImplementation((success) => {
      success({
        coords: {
          latitude: 40.7128,
          longitude: -74.0060,
          accuracy: 10,
          speed: null,
          heading: null,
        },
        timestamp: Date.now(),
      });
    });

    // Mock Supabase responses
    type MockQueryBuilder = {
      select: jest.Mock;
      eq: jest.Mock;
      order: jest.Mock;
      limit: jest.Mock;
      maybeSingle: jest.Mock;
      insert: jest.Mock;
      update: jest.Mock;
    };

    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({
        data: null,
        error: null,
      }),
      insert: jest.fn().mockReturnThis(),
      update: jest.fn().mockReturnThis(),
    } as MockQueryBuilder);

    mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
  });

  it('should render field staff interface correctly', async () => {
    // Mock sessions data
    const mockSessions = [
      {
        id: 'session-1',
        session_date: new Date().toISOString().split('T')[0],
        start_time: '09:00:00',
        end_time: '10:00:00',
        session_type: 'leadership_training',
        status: 'scheduled',
        school: { name: 'Test School' },
        facilitator_id: 'test-field-staff-id',
        student_attendance: [],
      },
    ];

    type MockSessionQueryBuilder = {
      select: jest.Mock;
      eq: jest.Mock;
      gte: jest.Mock;
      lte: jest.Mock;
      order: jest.Mock;
    };

    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
    } as MockSessionQueryBuilder);

    (mockSupabase.from().select().eq().gte().lte().order as jest.Mock).mockResolvedValue({
      data: mockSessions,
      error: null,
    });

    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    // Should show field staff specific interface
    await waitFor(() => {
      expect(screen.getByText('My Field Visits')).toBeInTheDocument();
    });

    // Should show today's sessions
    expect(screen.getByText('My Sessions Today')).toBeInTheDocument();
    
    // Should show session card
    await waitFor(() => {
      expect(screen.getByText('Test School')).toBeInTheDocument();
    });
  });

  it('should handle GPS check-in workflow', async () => {
    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    // Click on check-in tab
    const checkInTab = screen.getByRole('tab', { name: /check.in/i });
    fireEvent.click(checkInTab);

    await waitFor(() => {
      expect(screen.getByText('Check In')).toBeInTheDocument();
    });

    // Click get location button
    const getLocationButton = screen.getByRole('button', { name: /get.*location/i });
    fireEvent.click(getLocationButton);

    await waitFor(() => {
      expect(mockGeolocation.getCurrentPosition).toHaveBeenCalled();
    });

    // Should show location status
    await waitFor(() => {
      expect(screen.getByText(/location.*acquired/i)).toBeInTheDocument();
    });
  });

  it('should handle offline mode gracefully', async () => {
    // Simulate offline mode
    Object.defineProperty(global.navigator, 'onLine', {
      value: false,
      writable: true,
    });

    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    // Should show offline indicator
    await waitFor(() => {
      expect(screen.getByText(/offline/i)).toBeInTheDocument();
    });

    // Try to check in while offline
    const checkInTab = screen.getByRole('tab', { name: /check.in/i });
    fireEvent.click(checkInTab);

    const getLocationButton = screen.getByRole('button', { name: /get.*location/i });
    fireEvent.click(getLocationButton);

    // Should queue for offline sync
    await waitFor(() => {
      expect(mockToast.info).toHaveBeenCalledWith(
        expect.stringContaining('offline')
      );
    });
  });

  it('should handle GPS errors appropriately', async () => {
    // Mock GPS error
    const mockError = {
      code: 1, // PERMISSION_DENIED
      message: 'Permission denied',
      PERMISSION_DENIED: 1,
      POSITION_UNAVAILABLE: 2,
      TIMEOUT: 3,
    };

    mockGeolocation.getCurrentPosition.mockImplementation((_, error) => {
      error(mockError);
    });

    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    const checkInTab = screen.getByRole('tab', { name: /check.in/i });
    fireEvent.click(checkInTab);

    const getLocationButton = screen.getByRole('button', { name: /get.*location/i });
    fireEvent.click(getLocationButton);

    // Should show appropriate error message
    await waitFor(() => {
      expect(screen.getByText(/location access denied/i)).toBeInTheDocument();
    });
  });

  it('should sync offline data when coming back online', async () => {
    // Start offline
    Object.defineProperty(global.navigator, 'onLine', {
      value: false,
      writable: true,
    });

    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    // Perform action while offline
    const checkInTab = screen.getByRole('tab', { name: /check.in/i });
    fireEvent.click(checkInTab);

    // Go back online
    Object.defineProperty(global.navigator, 'onLine', {
      value: true,
      writable: true,
    });

    // Trigger online event
    fireEvent(window, new Event('online'));

    // Should attempt to sync
    await waitFor(() => {
      expect(mockSupabase.rpc).toHaveBeenCalled();
    });
  });

  it('should handle session attendance tracking', async () => {
    const mockSessions = [
      {
        id: 'session-1',
        session_date: new Date().toISOString().split('T')[0],
        start_time: '09:00:00',
        session_type: 'leadership_training',
        status: 'in_progress',
        school: { name: 'Test School' },
        facilitator_id: 'test-field-staff-id',
        student_attendance: [
          { id: 'att-1', student_id: 'student-1', status: 'present' },
          { id: 'att-2', student_id: 'student-2', status: 'absent' },
        ],
      },
    ];

    type MockQueryBuilder = {
      select: jest.Mock;
      eq: jest.Mock;
      gte: jest.Mock;
      lte: jest.Mock;
      order: jest.Mock;
    };

    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
    } as MockQueryBuilder);

    (mockSupabase.from().select().eq().gte().lte().order as jest.Mock).mockResolvedValue({
      data: mockSessions,
      error: null,
    });

    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    // Should show session with attendance count
    await waitFor(() => {
      expect(screen.getByText('2 students')).toBeInTheDocument();
    });

    // Should show active session status
    expect(screen.getByText('Active')).toBeInTheDocument();
  });

  it('should handle battery optimization settings', async () => {
    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    const checkInTab = screen.getByRole('tab', { name: /check.in/i });
    fireEvent.click(checkInTab);

    // Should show battery optimization options
    await waitFor(() => {
      expect(screen.getByText(/battery.*optimization/i)).toBeInTheDocument();
    });

    // Toggle battery optimization
    const batteryToggle = screen.getByRole('switch', { name: /battery.*optimization/i });
    fireEvent.click(batteryToggle);

    // Should update GPS tracking behavior
    expect(batteryToggle).toBeChecked();
  });

  it('should display appropriate empty states', async () => {
    // Mock empty sessions
    type MockQueryBuilder2 = {
      select: jest.Mock;
      eq: jest.Mock;
      gte: jest.Mock;
      lte: jest.Mock;
      order: jest.Mock;
    };

    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      gte: jest.fn().mockReturnThis(),
      lte: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
    } as MockQueryBuilder2);

    (mockSupabase.from().select().eq().gte().lte().order as jest.Mock).mockResolvedValue({
      data: [],
      error: null,
    });

    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    // Should show empty state for sessions
    await waitFor(() => {
      expect(screen.getByText(/no sessions scheduled/i)).toBeInTheDocument();
    });

    expect(screen.getByText(/check back later/i)).toBeInTheDocument();
  });

  it('should handle check-out workflow', async () => {
    // Mock active check-in session
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      order: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({
        data: {
          id: 'log-1',
          check_in_status: 'checked_in',
          check_in_time: new Date().toISOString(),
          school: { name: 'Test School' },
        },
        error: null,
      }),
      update: jest.fn().mockReturnThis(),
    } as MockQueryBuilder);

    render(<UnifiedFieldVisits />, { wrapper: createWrapper() });

    const checkInTab = screen.getByRole('tab', { name: /check.in/i });
    fireEvent.click(checkInTab);

    // Should show check-out option when checked in
    await waitFor(() => {
      expect(screen.getByText(/checked in/i)).toBeInTheDocument();
    });

    const checkOutButton = screen.getByRole('button', { name: /check.*out/i });
    fireEvent.click(checkOutButton);

    // Should perform check-out
    await waitFor(() => {
      expect(mockSupabase.from().update).toHaveBeenCalled();
    });
  });
});
