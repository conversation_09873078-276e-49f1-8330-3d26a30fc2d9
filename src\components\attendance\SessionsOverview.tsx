import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  Clock, 
  Users, 
  Plus, 
  Search, 
  Filter,
  BookOpen,
  Target,
  MapPin,
  CheckCircle,
  XCircle,
  Timer,
  Edit,
  Trash2,
  Eye,
  AlertCircle,
  TrendingUp,
  School,
  User
} from 'lucide-react';
import { useAttendanceSessions, useDeleteAttendanceSession } from '@/hooks/attendance/useAttendanceSessions';
import { useSchools } from '@/hooks/useSchools';
import { useAuth } from '@/hooks/useAuth';
import CreateSessionDialog from './CreateSessionDialog';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { toast } from 'sonner';

type SessionType = Database['public']['Enums']['session_type'];
type AttendanceSession = Database['public']['Tables']['attendance_sessions']['Row'] & {
  school?: { name: string; location_coordinates?: unknown };
  facilitator?: { name: string };
  student_attendance?: Database['public']['Tables']['student_attendance']['Row'][];
};

interface SessionsOverviewProps {
  defaultSchoolId?: string;
  onSessionSelect?: (sessionId: string) => void;
}

const SessionsOverview: React.FC<SessionsOverviewProps> = ({
  defaultSchoolId,
  onSessionSelect,
}) => {
  const { profile } = useAuth();
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || 'all');
  const [sessionTypeFilter, setSessionTypeFilter] = useState<SessionType | 'all'>('all');
  const [dateFilter, setDateFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const { data: schools, isLoading: schoolsLoading } = useSchools();
  const { data: sessions, isLoading: sessionsLoading, error: sessionsError } = useAttendanceSessions(
    selectedSchoolId && selectedSchoolId !== 'all' ? selectedSchoolId : undefined,
    dateFilter ? {
      start: new Date(dateFilter),
      end: new Date(dateFilter + 'T23:59:59')
    } : undefined
  );
  const deleteSession = useDeleteAttendanceSession();

  // Filter sessions based on search and filters
  const filteredSessions = useMemo(() => {
    if (!sessions) return [];
    
    return sessions.filter(session => {
      if (sessionTypeFilter && sessionTypeFilter !== 'all' && session.session_type !== sessionTypeFilter) return false;
      if (searchTerm && !session.session_name.toLowerCase().includes(searchTerm.toLowerCase())) return false;
      return true;
    });
  }, [sessions, sessionTypeFilter, searchTerm]);

  // Group sessions by date
  const sessionsByDate = useMemo(() => {
    const grouped: Record<string, AttendanceSession[]> = {};
    filteredSessions.forEach(session => {
      const date = session.session_date;
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(session);
    });
    return grouped;
  }, [filteredSessions]);

  // Calculate statistics
  const stats = useMemo(() => {
    const total = filteredSessions.length;
    const today = new Date().toISOString().split('T')[0];
    const todaySessions = filteredSessions.filter(s => s.session_date === today).length;
    const completedSessions = filteredSessions.filter(s => s.end_time && s.actual_end_time).length;
    const activeSessions = filteredSessions.filter(s => s.start_time && !s.actual_end_time).length;

    return {
      total,
      today: todaySessions,
      completed: completedSessions,
      active: activeSessions,
      upcoming: total - completedSessions - activeSessions,
    };
  }, [filteredSessions]);

  const handleDeleteSession = async (sessionId: string, sessionName: string) => {
    if (window.confirm(`Are you sure you want to delete "${sessionName}"? This action cannot be undone.`)) {
      try {
        await deleteSession.mutateAsync(sessionId);
        toast.success('Session deleted successfully');
      } catch (error) {
        toast.error('Failed to delete session');
      }
    }
  };

  const getSessionStatus = (session: AttendanceSession) => {
    const now = new Date();
    const sessionDate = new Date(session.session_date);
    const startTime = new Date(`${session.session_date}T${session.start_time}`);
    const endTime = session.end_time ? new Date(`${session.session_date}T${session.end_time}`) : null;

    if (session.actual_end_time) return 'completed';
    if (session.actual_start_time && !session.actual_end_time) return 'active';
    if (sessionDate.toDateString() === now.toDateString() && startTime <= now) return 'ready';
    if (sessionDate > now) return 'upcoming';
    if (sessionDate < now) return 'missed';
    return 'scheduled';
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: { variant: 'default' as const, icon: CheckCircle, color: 'text-green-600' },
      active: { variant: 'default' as const, icon: Timer, color: 'text-blue-600' },
      ready: { variant: 'secondary' as const, icon: Clock, color: 'text-orange-600' },
      upcoming: { variant: 'outline' as const, icon: Calendar, color: 'text-gray-600' },
      missed: { variant: 'destructive' as const, icon: XCircle, color: 'text-red-600' },
      scheduled: { variant: 'outline' as const, icon: Clock, color: 'text-gray-600' },
    };

    const config = variants[status as keyof typeof variants] || variants.scheduled;
    const Icon = config.icon;

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className={`h-3 w-3 ${config.color}`} />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  if (sessionsError) {
    return (
      <PageLayout>
        <PageHeader
          title="Sessions Overview"
          description="Manage and track attendance sessions"
        />
        <ContentCard>
          <div className="text-center py-8">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Failed to Load Sessions</h3>
            <p className="text-gray-600 mb-4">
              There was an error loading the attendance sessions. Please try refreshing the page.
            </p>
            <Button onClick={() => window.location.reload()}>
              Refresh Page
            </Button>
          </div>
        </ContentCard>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Sessions Overview"
        description="Manage and track attendance sessions across all schools"
        action={
          <CreateSessionDialog
            trigger={
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Session
              </Button>
            }
          />
        }
      />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Sessions</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <BookOpen className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Today</p>
                <p className="text-2xl font-bold">{stats.today}</p>
              </div>
              <Calendar className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active</p>
                <p className="text-2xl font-bold">{stats.active}</p>
              </div>
              <Timer className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completed</p>
                <p className="text-2xl font-bold">{stats.completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Upcoming</p>
                <p className="text-2xl font-bold">{stats.upcoming}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <ContentCard className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search sessions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
              <SelectTrigger className="w-full md:w-[200px]">
                <SelectValue placeholder="Select school" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Schools</SelectItem>
                {schools?.map((school) => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={sessionTypeFilter} onValueChange={(value) => setSessionTypeFilter(value as SessionType | 'all')}>
              <SelectTrigger className="w-full md:w-[150px]">
                <SelectValue placeholder="Session type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="class">Class</SelectItem>
                <SelectItem value="leadership_program">Leadership</SelectItem>
                <SelectItem value="training">Training</SelectItem>
                <SelectItem value="assessment">Assessment</SelectItem>
                <SelectItem value="meeting">Meeting</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>

            <Input
              type="date"
              value={dateFilter}
              onChange={(e) => setDateFilter(e.target.value)}
              className="w-full md:w-[150px]"
            />
          </div>
        </CardContent>
      </ContentCard>

      {/* Sessions List */}
      {sessionsLoading || schoolsLoading ? (
        <ContentCard>
          <div className="flex items-center justify-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            <span className="ml-3 text-gray-600">Loading sessions...</span>
          </div>
        </ContentCard>
      ) : Object.keys(sessionsByDate).length === 0 ? (
        <ContentCard>
          <div className="text-center py-12">
            <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-medium text-gray-900 mb-2">No sessions found</h3>
            <p className="text-gray-600 mb-6">
              {sessions?.length === 0 
                ? "No sessions have been created yet. Create your first session to get started."
                : "No sessions match your current filters. Try adjusting your search criteria."
              }
            </p>
            <CreateSessionDialog
              trigger={
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Session
                </Button>
              }
            />
          </div>
        </ContentCard>
      ) : (
        <div className="space-y-6">
          {Object.entries(sessionsByDate)
            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
            .map(([date, dateSessions]) => (
              <ContentCard key={date}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    {new Date(date).toLocaleDateString('en-US', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                    <Badge variant="outline">{dateSessions.length} sessions</Badge>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {dateSessions.map((session) => {
                      const status = getSessionStatus(session);
                      const attendanceCount = session.student_attendance?.length || 0;
                      
                      return (
                        <Card key={session.id} className="hover:shadow-md transition-shadow cursor-pointer">
                          <CardContent className="p-4">
                            <div className="flex items-start justify-between mb-3">
                              <div className="flex-1">
                                <h4 className="font-medium text-gray-900 mb-1 line-clamp-2">
                                  {session.session_name}
                                </h4>
                                <div className="flex items-center gap-2 text-sm text-gray-600 mb-2">
                                  <School className="h-4 w-4" />
                                  <span className="truncate">{session.school?.name || 'Unknown School'}</span>
                                </div>
                              </div>
                              {getStatusBadge(status)}
                            </div>

                            <div className="space-y-2 text-sm text-gray-600">
                              <div className="flex items-center gap-2">
                                <Clock className="h-4 w-4" />
                                <span>{session.start_time} - {session.end_time || 'TBD'}</span>
                              </div>
                              
                              <div className="flex items-center gap-2">
                                <BookOpen className="h-4 w-4" />
                                <span className="capitalize">{session.session_type.replace('_', ' ')}</span>
                              </div>

                              {session.facilitator && (
                                <div className="flex items-center gap-2">
                                  <User className="h-4 w-4" />
                                  <span className="truncate">{session.facilitator.name}</span>
                                </div>
                              )}

                              <div className="flex items-center gap-2">
                                <Users className="h-4 w-4" />
                                <span>{attendanceCount} students</span>
                              </div>

                              {session.location && (
                                <div className="flex items-center gap-2">
                                  <MapPin className="h-4 w-4" />
                                  <span className="truncate">{session.location}</span>
                                </div>
                              )}
                            </div>

                            <div className="flex items-center justify-between mt-4 pt-3 border-t">
                              <div className="flex gap-2">
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => onSessionSelect?.(session.id)}
                                >
                                  <Eye className="h-4 w-4 mr-1" />
                                  View
                                </Button>
                                
                                {(profile?.role === 'admin' || session.facilitator_id === profile?.id) && (
                                  <>
                                    <Button size="sm" variant="outline">
                                      <Edit className="h-4 w-4" />
                                    </Button>
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={() => handleDeleteSession(session.id, session.session_name)}
                                    >
                                      <Trash2 className="h-4 w-4" />
                                    </Button>
                                  </>
                                )}
                              </div>
                              
                              <div className="text-xs text-gray-500">
                                Created {new Date(session.created_at).toLocaleDateString()}
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </CardContent>
              </ContentCard>
            ))}
        </div>
      )}
    </PageLayout>
  );
};

export default SessionsOverview;
