import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  AlertTriangle, 
  CheckCircle,
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON> as PieChartIcon,
  Download,
  RefreshCw
} from 'lucide-react';
import { 
  useAttendanceTrends, 
  useSchoolAttendanceSummary, 
  useStudentsAtRisk,
  useCalculateAttendanceAnalytics 
} from '@/hooks/attendance/useAttendanceAnalytics';
import { useSchools } from '@/hooks/useSchools';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

interface AttendanceAnalyticsDashboardProps {
  defaultSchoolId?: string;
}

const AttendanceAnalyticsDashboard: React.FC<AttendanceAnalyticsDashboardProps> = ({
  defaultSchoolId,
}) => {
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || 'all');
  const [trendPeriod, setTrendPeriod] = useState<'daily' | 'weekly' | 'monthly'>('daily');
  const [trendDays, setTrendDays] = useState(30);
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
    end: new Date(),
  });

  const { data: schools } = useSchools();
  const { data: trends, isLoading: trendsLoading } = useAttendanceTrends(
    selectedSchoolId && selectedSchoolId !== 'all' ? selectedSchoolId : undefined,
    undefined,
    trendPeriod,
    trendDays
  );
  const { data: summary, isLoading: summaryLoading } = useSchoolAttendanceSummary(
    selectedSchoolId && selectedSchoolId !== 'all' ? selectedSchoolId : undefined,
    dateRange
  );
  const { data: studentsAtRisk, isLoading: riskLoading } = useStudentsAtRisk(
    selectedSchoolId && selectedSchoolId !== 'all' ? selectedSchoolId : undefined,
    'high'
  );

  const calculateAnalytics = useCalculateAttendanceAnalytics();

  const handleRecalculateAnalytics = () => {
    if (!selectedSchoolId || selectedSchoolId === 'all') return;

    calculateAnalytics.mutate({
      schoolId: selectedSchoolId,
      periodStart: dateRange.start,
      periodEnd: dateRange.end,
      analysisPeriod: 'monthly',
    });
  };

  // Chart colors
  const COLORS = {
    present: '#10B981',
    absent: '#EF4444',
    late: '#F59E0B',
    excused: '#3B82F6',
  };

  const pieColors = ['#10B981', '#EF4444', '#F59E0B', '#3B82F6'];

  // Prepare pie chart data
  const pieData = summary ? [
    { name: 'Present', value: summary.present, color: COLORS.present },
    { name: 'Absent', value: summary.absent, color: COLORS.absent },
    { name: 'Late', value: summary.late, color: COLORS.late },
    { name: 'Excused', value: summary.excused, color: COLORS.excused },
  ].filter(item => item.value > 0) : [];

  // Prepare grade breakdown data
  const gradeData = summary ? Object.entries(summary.by_grade).map(([grade, data]) => ({
    grade: `Grade ${grade}`,
    attendance_rate: data.rate,
    total: data.total,
    present: data.present,
  })) : [];

  // Prepare gender breakdown data
  const genderData = summary ? Object.entries(summary.by_gender).map(([gender, data]) => ({
    gender,
    attendance_rate: data.rate,
    total: data.total,
    present: data.present,
  })) : [];

  return (
    <PageLayout>
      <PageHeader
        title="Attendance Analytics"
        description="Comprehensive attendance analysis and reporting dashboard"
      />

      {/* Controls */}
      <ContentCard>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select school" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All schools</SelectItem>
                {schools?.filter(school => school.id && school.id.trim() !== '').map((school) => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={trendPeriod} onValueChange={(value: 'daily' | 'weekly' | 'monthly') => setTrendPeriod(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
              </SelectContent>
            </Select>

            <Select value={trendDays.toString()} onValueChange={(value) => setTrendDays(parseInt(value))}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7">7 days</SelectItem>
                <SelectItem value="30">30 days</SelectItem>
                <SelectItem value="90">90 days</SelectItem>
                <SelectItem value="365">1 year</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRecalculateAnalytics}
              disabled={!selectedSchoolId || selectedSchoolId === 'all' || calculateAnalytics.isPending}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${calculateAnalytics.isPending ? 'animate-spin' : ''}`} />
              Recalculate
            </Button>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>
      </ContentCard>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summary?.attendance_rate.toFixed(1) || '0'}%
                </p>
                <p className="text-sm text-gray-600">Overall Attendance Rate</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summary?.punctuality_rate.toFixed(1) || '0'}%
                </p>
                <p className="text-sm text-gray-600">Punctuality Rate</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summary?.total_records || 0}
                </p>
                <p className="text-sm text-gray-600">Total Records</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg">
                <Users className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {studentsAtRisk?.length || 0}
                </p>
                <p className="text-sm text-gray-600">Students at Risk</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-lg">
                <AlertTriangle className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analytics Tabs */}
      <Tabs defaultValue="trends" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
          <TabsTrigger value="risk">At Risk</TabsTrigger>
          <TabsTrigger value="comparative">Comparative</TabsTrigger>
        </TabsList>

        {/* Trends Tab */}
        <TabsContent value="trends">
          <ContentCard>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Attendance Trends
              </CardTitle>
              <CardDescription>
                Attendance patterns over time showing daily, weekly, or monthly trends
              </CardDescription>
            </CardHeader>
            <CardContent>
              {trendsLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                </div>
              ) : trends && trends.length > 0 ? (
                <ResponsiveContainer width="100%" height={400}>
                  <LineChart data={trends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis 
                      dataKey="date" 
                      tickFormatter={(value) => {
                        const date = new Date(value);
                        return trendPeriod === 'daily' 
                          ? date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
                          : trendPeriod === 'weekly'
                          ? `Week of ${date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}`
                          : date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
                      }}
                    />
                    <YAxis domain={[0, 100]} />
                    <Tooltip 
                      formatter={(value, name) => [
                        `${Number(value).toFixed(1)}%`,
                        name === 'attendance_rate' ? 'Attendance Rate' : name
                      ]}
                      labelFormatter={(value) => {
                        const date = new Date(value);
                        return date.toLocaleDateString('en-US', { 
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        });
                      }}
                    />
                    <Legend />
                    <Line 
                      type="monotone" 
                      dataKey="attendance_rate" 
                      stroke={COLORS.present}
                      strokeWidth={3}
                      name="Attendance Rate"
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No trend data available for the selected period
                </div>
              )}
            </CardContent>
          </ContentCard>
        </TabsContent>

        {/* Breakdown Tab */}
        <TabsContent value="breakdown" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Attendance Status Breakdown */}
            <ContentCard>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChartIcon className="h-5 w-5" />
                  Attendance Status
                </CardTitle>
              </CardHeader>
              <CardContent>
                {summaryLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  </div>
                ) : pieData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={pieData}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                        outerRadius={100}
                        fill="#8884d8"
                        dataKey="value"
                      >
                        {pieData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No attendance data available
                  </div>
                )}
              </CardContent>
            </ContentCard>

            {/* Grade Level Breakdown */}
            <ContentCard>
              <CardHeader>
                <CardTitle>Attendance by Grade Level</CardTitle>
              </CardHeader>
              <CardContent>
                {summaryLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  </div>
                ) : gradeData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={gradeData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="grade" />
                      <YAxis domain={[0, 100]} />
                      <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, 'Attendance Rate']} />
                      <Bar dataKey="attendance_rate" fill={COLORS.present} />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No grade-level data available
                  </div>
                )}
              </CardContent>
            </ContentCard>
          </div>

          {/* Gender Breakdown */}
          <ContentCard>
            <CardHeader>
              <CardTitle>Attendance by Gender</CardTitle>
            </CardHeader>
            <CardContent>
              {summaryLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                </div>
              ) : genderData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={genderData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="gender" />
                    <YAxis domain={[0, 100]} />
                    <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, 'Attendance Rate']} />
                    <Bar dataKey="attendance_rate" fill={COLORS.present} />
                  </BarChart>
                </ResponsiveContainer>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No gender breakdown data available
                </div>
              )}
            </CardContent>
          </ContentCard>
        </TabsContent>

        {/* At Risk Tab */}
        <TabsContent value="risk">
          <ContentCard>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Students at Risk
              </CardTitle>
              <CardDescription>
                Students with low attendance rates requiring intervention
              </CardDescription>
            </CardHeader>
            <CardContent>
              {riskLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                </div>
              ) : studentsAtRisk && studentsAtRisk.length > 0 ? (
                <div className="space-y-4">
                  {studentsAtRisk.map((analytics) => (
                    <div key={analytics.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div>
                          <div className="font-medium">
                            {analytics.student?.first_name} {analytics.student?.last_name}
                          </div>
                          <div className="text-sm text-gray-600">
                            {analytics.student?.student_number} • Grade {analytics.student?.grade_level}
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge 
                            variant={analytics.risk_level === 'critical' ? 'destructive' : 'secondary'}
                            className={
                              analytics.risk_level === 'critical' ? '' :
                              analytics.risk_level === 'high' ? 'bg-orange-100 text-orange-800' :
                              'bg-yellow-100 text-yellow-800'
                            }
                          >
                            {analytics.risk_level} risk
                          </Badge>
                          <span className="text-lg font-bold text-red-600">
                            {analytics.attendance_rate.toFixed(1)}%
                          </span>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <div className="text-gray-600">Total Sessions</div>
                          <div className="font-medium">{analytics.total_sessions}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Absences</div>
                          <div className="font-medium">{analytics.sessions_absent}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Consecutive Absences</div>
                          <div className="font-medium">{analytics.consecutive_absences}</div>
                        </div>
                        <div>
                          <div className="text-gray-600">Guardian Contact</div>
                          <div className="font-medium">{analytics.student?.guardian_contact || 'N/A'}</div>
                        </div>
                      </div>

                      {analytics.intervention_recommended && (
                        <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded text-sm text-red-800">
                          <strong>Intervention Recommended:</strong> This student requires immediate attention due to poor attendance.
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Students at Risk</h3>
                  <p className="text-gray-600">
                    All students are maintaining good attendance rates.
                  </p>
                </div>
              )}
            </CardContent>
          </ContentCard>
        </TabsContent>

        {/* Comparative Tab */}
        <TabsContent value="comparative">
          <ContentCard>
            <div className="text-center py-8">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Comparative Analysis</h3>
              <p className="text-gray-600">
                School-to-school and period-to-period comparison features coming soon.
              </p>
            </div>
          </ContentCard>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default AttendanceAnalyticsDashboard;
