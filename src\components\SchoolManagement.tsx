
import React, { useState, useMemo } from 'react';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import ExportManager from './ExportManager';
import AddSchoolForm from './schools/AddSchoolForm';
import AddSchoolModal from './schools/AddSchoolModal';
import SchoolImportTemplate from './schools/SchoolImportTemplate';
import SchoolsTable from './schools/SchoolsTable';
import SchoolsGrid from './schools/SchoolsGrid';
import SchoolDetailView from './schools/SchoolDetailView';
import SchoolFilters from './schools/SchoolFilters';
import SchoolBulkOperations from './schools/SchoolBulkOperations';
import SchoolViewModeToggle, { ViewMode } from './schools/SchoolViewModeToggle';
import { useSchoolOperations } from '@/hooks/useSchoolOperations';
import { useSchoolsEnhanced } from '@/hooks/useSchoolsEnhanced';
import { SchoolFilters as SchoolFiltersType, SchoolSort, School } from '@/types/school';
import { Database } from '@/integrations/supabase/types';

// Use Supabase generated types
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface SchoolManagementProps {
  currentUser: Profile;
  schoolTypeFilter?: 'primary' | 'secondary' | 'tertiary' | 'vocational';
  statusFilter?: 'active' | 'inactive' | 'pending';
}

const SchoolManagement = ({ currentUser, schoolTypeFilter, statusFilter }: SchoolManagementProps) => {
  const { divisions, addSchool, isAddingSchool } = useSchoolOperations(currentUser);
  const { 
    useSchoolsFiltered, 
    useSchoolStatistics,
    updateSchool, 
    deleteSchool, 
    bulkUpdateStatus,
    isUpdating,
    isDeleting,
    isBulkUpdating
  } = useSchoolsEnhanced();

  const { toast } = useToast();
  
  // State management
  const [selectedSchool, setSelectedSchool] = useState<School | null>(null);
  const [showDetails, setShowDetails] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [selectedSchools, setSelectedSchools] = useState<string[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [showRegisterModal, setShowRegisterModal] = useState(false);
  const [filters, setFilters] = useState<SchoolFiltersType>({
    school_type: schoolTypeFilter,
    registration_status: statusFilter,
  });
  const [sort, setSort] = useState<SchoolSort>({ field: 'name', direction: 'asc' });

  const pageSize = 50;
  const pagination = useMemo(() => ({
    limit: pageSize,
    offset: (currentPage - 1) * pageSize
  }), [currentPage, pageSize]);

  // Data fetching
  const { data: schools = [], isLoading: isLoadingSchools, error } = useSchoolsFiltered(filters, sort, pagination);
  const { data: statistics } = useSchoolStatistics();

  const canManageSchools = currentUser?.role === 'admin' || currentUser?.role === 'program_officer';
  const canExport = currentUser?.role === 'admin' || currentUser?.role === 'program_officer';
  const isAdmin = currentUser?.role === 'admin';

  // Get unique districts for filter
  const districts = useMemo(() => {
    const uniqueDistricts = new Set(schools.map(school => school.district).filter(Boolean));
    return Array.from(uniqueDistricts).sort();
  }, [schools]);

  const totalCount = schools[0]?.total_count || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  // Event handlers
  const handleViewDetails = (school: School) => {
    setSelectedSchool(school);
    setShowDetails(true);
  };

  const handleEditSchool = (_school: School) => {
    // TODO: Implement edit modal
    toast({
      title: "Edit School",
      description: "Edit functionality will be implemented next",
    });
  };

  const handleDeleteSchool = (school: School) => {
    if (!isAdmin) {
      toast({
        title: "Access Denied",
        description: "Only administrators can delete schools",
        variant: "destructive",
      });
      return;
    }
    
    if (confirm(`Are you sure you want to delete ${school.name}? This action cannot be undone.`)) {
      deleteSchool(school.id);
    }
  };

  const handleToggleActive = (school: School) => {
    if (!canManageSchools) {
      toast({
        title: "Access Denied",
        description: "You don't have permission to modify schools",
        variant: "destructive",
      });
      return;
    }

    const newStatus = school.registration_status === 'inactive' ? 'active' : 'inactive';
    updateSchool({ 
      schoolId: school.id, 
      schoolData: { registration_status: newStatus } 
    });
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      setSelectedSchools(schools.map(school => school.id));
    } else {
      setSelectedSchools([]);
    }
  };

  const handleSelectSchool = (schoolId: string, selected: boolean) => {
    if (selected) {
      setSelectedSchools(prev => [...prev, schoolId]);
    } else {
      setSelectedSchools(prev => prev.filter(id => id !== schoolId));
    }
  };

  const handleBulkStatusUpdate = (schoolIds: string[], status: School['registration_status']) => {
    if (!status) return;
    bulkUpdateStatus({ schoolIds, status });
    setSelectedSchools([]);
  };

  const handleExport = (format: 'csv' | 'excel') => {
    toast({
      title: "Export",
      description: `Exporting ${schools.length} schools as ${format.toUpperCase()}...`,
    });
    // TODO: Implement actual export functionality
  };

  const handleClearFilters = () => {
    setFilters({
      school_type: schoolTypeFilter,
      registration_status: statusFilter,
    });
    setCurrentPage(1);
  };

  const handleFiltersChange = (newFilters: SchoolFiltersType) => {
    setFilters(newFilters);
    setCurrentPage(1);
  };

  const handleSortChange = (newSort: SchoolSort) => {
    setSort(newSort);
    setCurrentPage(1);
  };

  if (error) {
    return (
      <div className="p-6">
        <div className="text-center py-12">
          <p className="text-red-600">Error loading schools: {error.message}</p>
        </div>
      </div>
    );
  }

  if (isLoadingSchools) {
    return (
      <div className="p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">School Management</h1>
            <p className="text-gray-600 mt-2">Manage schools in the iLead program</p>
          </div>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
          <span className="ml-2 text-gray-600">Loading schools...</span>
        </div>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">School Management</h1>
            <p className="text-gray-600 mt-2">
              {schoolTypeFilter ? `${schoolTypeFilter.charAt(0).toUpperCase() + schoolTypeFilter.slice(1)} schools` : 
               statusFilter ? `${statusFilter.charAt(0).toUpperCase() + statusFilter.slice(1)} schools` :
               'All schools'} ({totalCount})
            </p>
            {statistics && (
              <p className="text-sm text-gray-500 mt-1">
                {statistics.total_students.toLocaleString()} students • {statistics.total_teachers.toLocaleString()} teachers
              </p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <SchoolViewModeToggle viewMode={viewMode} onViewModeChange={setViewMode} />
            <ExportManager dataType="schools" title="Schools" />

            {canManageSchools && (
              <div className="flex gap-2">
                <SchoolImportTemplate />
                <Button
                  onClick={() => setShowRegisterModal(true)}
                  className="bg-purple-600 hover:bg-purple-700 text-white"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Register School
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Filters */}
        <SchoolFilters
          filters={filters}
          sort={sort}
          onFiltersChange={handleFiltersChange}
          onSortChange={handleSortChange}
          onClearFilters={handleClearFilters}
          districts={districts}
        />

        {/* Bulk Operations */}
        <SchoolBulkOperations
          schools={schools}
          selectedSchools={selectedSchools}
          onSelectAll={handleSelectAll}
          onSelectSchool={handleSelectSchool}
          onBulkStatusUpdate={handleBulkStatusUpdate}
          onExport={handleExport}
          canManageSchools={canManageSchools}
          canExport={canExport}
          isAdmin={isAdmin}
        />

        {/* Schools Display */}
        {viewMode === 'table' ? (
          <SchoolsTable
            schools={schools}
            currentUser={currentUser}
            canManageSchools={canManageSchools}
            onViewDetails={handleViewDetails}
            onEditSchool={handleEditSchool}
            onDeleteSchool={handleDeleteSchool}
            onToggleActive={handleToggleActive}
          />
        ) : (
          <SchoolsGrid
            schools={schools}
            selectedSchools={selectedSchools}
            onSelectSchool={handleSelectSchool}
            onViewDetails={handleViewDetails}
            onEditSchool={handleEditSchool}
            onDeleteSchool={handleDeleteSchool}
            onToggleActive={handleToggleActive}
            canManageSchools={canManageSchools}
            isAdmin={isAdmin}
          />
        )}

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex items-center justify-between">
            <p className="text-sm text-gray-700">
              Showing {((currentPage - 1) * pageSize) + 1} to {Math.min(currentPage * pageSize, totalCount)} of {totalCount} results
            </p>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                className="px-3 py-1 border rounded disabled:opacity-50"
              >
                Previous
              </button>
              <span className="px-3 py-1">
                Page {currentPage} of {totalPages}
              </span>
              <button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="px-3 py-1 border rounded disabled:opacity-50"
              >
                Next
              </button>
            </div>
          </div>
        )}

        {schools.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-500">
              {schoolTypeFilter ? `No ${schoolTypeFilter} schools found` :
               statusFilter ? `No ${statusFilter} schools found` :
               'No schools found'}
            </p>
          </div>
        )}
      </div>

      {/* School Details Dialog */}
      <Dialog open={showDetails} onOpenChange={setShowDetails}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedSchool && (
            <SchoolDetailView
              school={selectedSchool}
              currentUser={currentUser}
              onEdit={() => {
                setShowDetails(false);
                handleEditSchool(selectedSchool);
              }}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Register School Modal */}
      <AddSchoolModal
        isOpen={showRegisterModal}
        onClose={() => setShowRegisterModal(false)}
      />
    </>
  );
};

export default SchoolManagement;
