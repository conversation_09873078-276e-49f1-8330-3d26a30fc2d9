-- Attendance Tracking Functions
-- Migration 016: Database functions for comprehensive attendance management

-- Function to create a new attendance session
CREATE OR REPLACE FUNCTION create_attendance_session(
    p_session_name VARCHAR(255),
    p_session_type session_type,
    p_school_id UUID,
    p_grade_level INTEGER DEFAULT NULL,
    p_subject VARCHAR(100) DEFAULT NULL,
    p_teacher_name VARCHAR(100) DEFAULT NULL,
    p_session_date DATE,
    p_start_time TIME,
    p_end_time TIME DEFAULT NULL,
    p_planned_duration_minutes INTEGER DEFAULT NULL,
    p_location VARCHAR(255) DEFAULT NULL,
    p_max_capacity INTEGER DEFAULT NULL,
    p_round_tables_count INTEGER DEFAULT 0,
    p_session_description TEXT DEFAULT NULL,
    p_learning_objectives TEXT[] DEFAULT NULL,
    p_materials_needed TEXT[] DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    session_id UUID;
BEGIN
    -- Check if user has permission to create sessions
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to create attendance sessions';
    END IF;

    -- Insert new session
    INSERT INTO attendance_sessions (
        session_name, session_type, school_id, grade_level, subject, 
        teacher_name, facilitator_id, session_date, start_time, end_time,
        planned_duration_minutes, location, max_capacity, round_tables_count,
        session_description, learning_objectives, materials_needed, created_by
    )
    VALUES (
        p_session_name, p_session_type, p_school_id, p_grade_level, p_subject,
        p_teacher_name, auth.uid(), p_session_date, p_start_time, p_end_time,
        p_planned_duration_minutes, p_location, p_max_capacity, p_round_tables_count,
        p_session_description, p_learning_objectives, p_materials_needed, auth.uid()
    )
    RETURNING id INTO session_id;

    -- Log activity
    INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description)
    VALUES (
        'task_created'::activity_type,
        auth.uid(),
        'task'::entity_type,
        session_id,
        'Created attendance session: ' || p_session_name
    );

    RETURN session_id;
END;
$$;

-- Function to record student attendance
CREATE OR REPLACE FUNCTION record_student_attendance(
    p_session_id UUID,
    p_student_id UUID,
    p_attendance_status attendance_status,
    p_check_in_time TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_late_minutes INTEGER DEFAULT 0,
    p_table_number INTEGER DEFAULT NULL,
    p_participation_score INTEGER DEFAULT NULL,
    p_behavior_notes TEXT DEFAULT NULL,
    p_absence_reason TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_id UUID;
    session_school_id UUID;
    student_school_id UUID;
BEGIN
    -- Check if user has permission to record attendance
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to record attendance';
    END IF;

    -- Get school IDs for validation
    SELECT school_id INTO session_school_id FROM attendance_sessions WHERE id = p_session_id;
    SELECT school_id INTO student_school_id FROM students WHERE id = p_student_id;

    -- Validate that session and student belong to the same school
    IF session_school_id != student_school_id THEN
        RAISE EXCEPTION 'Student and session must belong to the same school';
    END IF;

    -- Insert or update attendance record
    INSERT INTO student_attendance (
        session_id, student_id, school_id, attendance_status,
        check_in_time, late_minutes, table_number, participation_score,
        behavior_notes, absence_reason, recorded_by
    )
    VALUES (
        p_session_id, p_student_id, session_school_id, p_attendance_status,
        COALESCE(p_check_in_time, NOW()), p_late_minutes, p_table_number,
        p_participation_score, p_behavior_notes, p_absence_reason, auth.uid()
    )
    ON CONFLICT (session_id, student_id) 
    DO UPDATE SET
        attendance_status = p_attendance_status,
        check_in_time = COALESCE(p_check_in_time, student_attendance.check_in_time),
        late_minutes = p_late_minutes,
        table_number = p_table_number,
        participation_score = p_participation_score,
        behavior_notes = p_behavior_notes,
        absence_reason = p_absence_reason,
        updated_by = auth.uid(),
        updated_at = NOW()
    RETURNING id INTO attendance_id;

    RETURN attendance_id;
END;
$$;

-- Function to bulk record attendance for multiple students
CREATE OR REPLACE FUNCTION bulk_record_attendance(
    p_session_id UUID,
    p_attendance_records JSONB
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    record JSONB;
    records_processed INTEGER := 0;
BEGIN
    -- Check permissions
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to record attendance';
    END IF;

    -- Process each attendance record
    FOR record IN SELECT * FROM jsonb_array_elements(p_attendance_records)
    LOOP
        PERFORM record_student_attendance(
            p_session_id,
            (record->>'student_id')::UUID,
            (record->>'attendance_status')::attendance_status,
            CASE WHEN record->>'check_in_time' IS NOT NULL 
                 THEN (record->>'check_in_time')::TIMESTAMP WITH TIME ZONE 
                 ELSE NULL END,
            COALESCE((record->>'late_minutes')::INTEGER, 0),
            CASE WHEN record->>'table_number' IS NOT NULL 
                 THEN (record->>'table_number')::INTEGER 
                 ELSE NULL END,
            CASE WHEN record->>'participation_score' IS NOT NULL 
                 THEN (record->>'participation_score')::INTEGER 
                 ELSE NULL END,
            record->>'behavior_notes',
            record->>'absence_reason'
        );
        
        records_processed := records_processed + 1;
    END LOOP;

    RETURN records_processed;
END;
$$;

-- Function to log staff GPS check-in
CREATE OR REPLACE FUNCTION staff_gps_checkin(
    p_school_id UUID,
    p_session_id UUID DEFAULT NULL,
    p_latitude DECIMAL(10,8),
    p_longitude DECIMAL(11,8),
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address_description TEXT DEFAULT NULL,
    p_verification_method VARCHAR(50) DEFAULT 'gps',
    p_device_info JSONB DEFAULT '{}',
    p_network_info JSONB DEFAULT '{}'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    log_id UUID;
    school_location POINT;
    distance_meters DECIMAL(8,2);
BEGIN
    -- Check if user is field staff
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Only field staff can check in with GPS';
    END IF;

    -- Get school location for distance calculation
    SELECT location_coordinates INTO school_location
    FROM schools WHERE id = p_school_id;

    -- Calculate distance from school (simplified calculation)
    IF school_location IS NOT NULL THEN
        distance_meters := ST_Distance(
            ST_GeogFromText('POINT(' || p_longitude || ' ' || p_latitude || ')'),
            ST_GeogFromText('POINT(' || ST_X(school_location) || ' ' || ST_Y(school_location) || ')')
        );
    END IF;

    -- Insert location log
    INSERT INTO staff_location_logs (
        staff_id, school_id, session_id, check_in_status,
        location_coordinates, location_accuracy, address_description,
        distance_from_school, location_verified, verification_method,
        device_info, network_info
    )
    VALUES (
        auth.uid(), p_school_id, p_session_id, 'checked_in'::check_in_status,
        POINT(p_longitude, p_latitude), p_accuracy, p_address_description,
        distance_meters, (distance_meters IS NULL OR distance_meters <= 100),
        p_verification_method, p_device_info, p_network_info
    )
    RETURNING id INTO log_id;

    RETURN log_id;
END;
$$;

-- Function to calculate attendance analytics
CREATE OR REPLACE FUNCTION calculate_attendance_analytics(
    p_school_id UUID,
    p_student_id UUID DEFAULT NULL,
    p_period_start DATE,
    p_period_end DATE,
    p_analysis_period VARCHAR(20) DEFAULT 'monthly'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    analytics_id UUID;
    total_sessions INTEGER;
    sessions_attended INTEGER;
    sessions_absent INTEGER;
    sessions_late INTEGER;
    sessions_excused INTEGER;
    attendance_rate DECIMAL(5,2);
    punctuality_rate DECIMAL(5,2);
    avg_participation DECIMAL(3,2);
    consecutive_absences INTEGER;
    risk_level VARCHAR(20);
BEGIN
    -- Calculate attendance statistics
    SELECT
        COUNT(*) as total,
        COUNT(CASE WHEN sa.attendance_status = 'present' THEN 1 END) as attended,
        COUNT(CASE WHEN sa.attendance_status = 'absent' THEN 1 END) as absent,
        COUNT(CASE WHEN sa.attendance_status = 'late' THEN 1 END) as late,
        COUNT(CASE WHEN sa.attendance_status = 'excused' THEN 1 END) as excused,
        AVG(CASE WHEN sa.participation_score IS NOT NULL THEN sa.participation_score END)
    INTO total_sessions, sessions_attended, sessions_absent, sessions_late, sessions_excused, avg_participation
    FROM student_attendance sa
    JOIN attendance_sessions ats ON sa.session_id = ats.id
    WHERE sa.school_id = p_school_id
    AND (p_student_id IS NULL OR sa.student_id = p_student_id)
    AND ats.session_date BETWEEN p_period_start AND p_period_end;

    -- Calculate rates
    attendance_rate := CASE WHEN total_sessions > 0
                           THEN (sessions_attended::DECIMAL / total_sessions::DECIMAL) * 100
                           ELSE 0 END;

    punctuality_rate := CASE WHEN (sessions_attended + sessions_late) > 0
                            THEN (sessions_attended::DECIMAL / (sessions_attended + sessions_late)::DECIMAL) * 100
                            ELSE 0 END;

    -- Determine risk level
    risk_level := CASE
        WHEN attendance_rate >= 90 THEN 'low'
        WHEN attendance_rate >= 75 THEN 'medium'
        WHEN attendance_rate >= 60 THEN 'high'
        ELSE 'critical'
    END;

    -- Insert analytics record
    INSERT INTO attendance_analytics (
        school_id, student_id, analysis_period, period_start_date, period_end_date,
        total_sessions, sessions_attended, sessions_absent, sessions_late, sessions_excused,
        attendance_rate, punctuality_rate, average_participation_score,
        risk_level, intervention_recommended, created_by
    )
    VALUES (
        p_school_id, p_student_id, p_analysis_period, p_period_start, p_period_end,
        total_sessions, sessions_attended, sessions_absent, sessions_late, sessions_excused,
        attendance_rate, punctuality_rate, avg_participation,
        risk_level, (risk_level IN ('high', 'critical')), auth.uid()
    )
    RETURNING id INTO analytics_id;

    RETURN analytics_id;
END;
$$;
