import { useState, useEffect, useCallback } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

// Type definitions for device and network info
interface DeviceInfo {
  userAgent: string;
  platform: string;
  language: string;
  cookieEnabled: boolean;
  onLine: boolean;
  screen: {
    width: number;
    height: number;
    colorDepth: number;
  };
}

interface NetworkConnection {
  effectiveType?: string;
  downlink?: number;
  rtt?: number;
}

interface NetworkInfo {
  onLine: boolean;
  connection: NetworkConnection | null;
}
import { Database } from '@/integrations/supabase/types';

type CheckInStatus = Database['public']['Enums']['check_in_status'];

interface GPSPosition {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
}

interface LocationLog {
  id: string;
  staff_id: string;
  school_id: string;
  session_id?: string;
  check_in_status: CheckInStatus;
  location_coordinates: [number, number];
  location_accuracy: number;
  address_description?: string;
  check_in_time: string;
  check_out_time?: string;
  total_duration_minutes?: number;
  distance_from_school: number;
  location_verified: boolean;
  verification_method: string;
  device_info: DeviceInfo;
  network_info: NetworkInfo;
  offline_sync: boolean;
  notes?: string;
  school?: {
    name: string;
  };
  session?: {
    session_name: string;
    session_type: string;
  };
}

interface CheckInData {
  school_id: string;
  session_id?: string;
  latitude: number;
  longitude: number;
  accuracy: number;
  address_description?: string;
  verification_method?: string;
  device_info?: Partial<DeviceInfo>;
  network_info?: Partial<NetworkInfo>;
}

// Hook for GPS position tracking (check-in only approach)
export const useGPSPosition = () => {
  const [position, setPosition] = useState<GPSPosition | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [retryCount, setRetryCount] = useState(0);

  const getCurrentPosition = useCallback((): Promise<GPSPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error('Geolocation is not supported by this browser'));
        return;
      }

      setIsLoading(true);
      setError(null);

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const gpsPosition: GPSPosition = {
            latitude: position.coords.latitude,
            longitude: position.coords.longitude,
            accuracy: position.coords.accuracy,
            timestamp: position.timestamp,
          };
          setPosition(gpsPosition);
          setIsLoading(false);
          resolve(gpsPosition);
        },
        (error) => {
          let errorMessage = 'Failed to get location';
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location access denied by user';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information unavailable';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out';
              break;
          }
          setError(errorMessage);
          setIsLoading(false);
          reject(new Error(errorMessage));
        },
        {
          enableHighAccuracy: false, // Disabled for battery optimization
          timeout: 30000, // 30 seconds - longer timeout for battery savings
          maximumAge: 180000, // 3 minutes - longer cache for check-in scenarios
        }
      );
    });
  }, []);

  // Removed continuous tracking functions (startWatching, stopWatching)
  // This hook now focuses on check-in-only GPS capture for better battery optimization

  return {
    position,
    error,
    isLoading,
    getCurrentPosition,
    // Removed continuous tracking functions for battery optimization
    // Use getCurrentPosition() for check-in-only location capture
  };
};

// Hook for staff location logs
export const useStaffLocationLogs = (staffId?: string, schoolId?: string) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['staff-location-logs', staffId, profile?.id, schoolId],
    queryFn: async () => {
      let query = supabase
        .from('staff_location_logs')
        .select(`
          *,
          school:schools(name),
          session:attendance_sessions(session_name, session_type)
        `)
        .order('check_in_time', { ascending: false });

      if (staffId) {
        query = query.eq('staff_id', staffId);
      } else if (profile?.id) {
        query = query.eq('staff_id', profile.id);
      }

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching staff location logs:', error);
        throw error;
      }

      return data as LocationLog[];
    },
    enabled: !!(profile?.id || staffId),
  });
};

// Hook for GPS check-in
export const useGPSCheckIn = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (checkInData: CheckInData) => {
      // Get device and network info
      const deviceInfo = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        cookieEnabled: navigator.cookieEnabled,
        onLine: navigator.onLine,
        screen: {
          width: screen.width,
          height: screen.height,
          colorDepth: screen.colorDepth,
        },
      };

      // Type assertion for navigator.connection (experimental API)
      const navigatorWithConnection = navigator as Navigator & {
        connection?: {
          effectiveType?: string;
          downlink?: number;
          rtt?: number;
        };
      };

      const networkInfo: NetworkInfo = {
        onLine: navigator.onLine,
        connection: navigatorWithConnection.connection ? {
          effectiveType: navigatorWithConnection.connection.effectiveType,
          downlink: navigatorWithConnection.connection.downlink,
          rtt: navigatorWithConnection.connection.rtt,
        } : null,
      };

      const { data, error } = await supabase.rpc('staff_gps_checkin', {
        p_school_id: checkInData.school_id,
        p_session_id: checkInData.session_id,
        p_latitude: checkInData.latitude,
        p_longitude: checkInData.longitude,
        p_accuracy: checkInData.accuracy,
        p_address_description: checkInData.address_description,
        p_verification_method: checkInData.verification_method || 'gps',
        p_device_info: { ...deviceInfo, ...checkInData.device_info },
        p_network_info: { ...networkInfo, ...checkInData.network_info },
      });

      if (error) {
        console.error('Error during GPS check-in:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['staff-location-logs'] });
      toast({
        title: 'Success',
        description: 'Successfully checked in with GPS location',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to check in:', error);

      // Provide more specific error messages based on error type
      let title = 'Check-in Failed';
      let description = 'Failed to record GPS check-in. Please try again.';

      if (error.message.includes('permission')) {
        title = 'Location Permission Required';
        description = 'Please enable location permissions in your browser settings and try again.';
      } else if (error.message.includes('timeout')) {
        title = 'GPS Timeout';
        description = 'GPS signal took too long. Try moving to an open area or check your device settings.';
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        title = 'Network Error';
        description = 'Check your internet connection and try again. Data will be saved offline if needed.';
      } else if (error.message.includes('accuracy')) {
        title = 'Poor GPS Signal';
        description = 'GPS accuracy is too low. Move to an open area for better signal.';
      }

      toast({
        title,
        description: error.message || description,
        variant: 'destructive',
      });
    },
  });
};

// Hook for GPS check-out
export const useGPSCheckOut = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { profile } = useAuth();

  return useMutation({
    mutationFn: async (logId?: string) => {
      // If no logId provided, find the most recent checked-in record
      let targetLogId = logId;

      if (!targetLogId && profile?.id) {
        const { data: currentLog, error: findError } = await supabase
          .from('staff_location_logs')
          .select('id')
          .eq('staff_id', profile.id)
          .eq('check_in_status', 'checked_in')
          .order('check_in_time', { ascending: false })
          .limit(1)
          .maybeSingle();

        if (findError) {
          console.error('Error finding current check-in:', findError);
          throw new Error('Unable to find current check-in session');
        }

        if (!currentLog) {
          throw new Error('No active check-in session found');
        }

        targetLogId = currentLog.id;
      }

      if (!targetLogId) {
        throw new Error('No check-in session specified');
      }

      // First, verify the record exists and is in checked_in status
      const { data: existingLog, error: verifyError } = await supabase
        .from('staff_location_logs')
        .select('id, check_in_status, check_in_time')
        .eq('id', targetLogId)
        .maybeSingle();

      if (verifyError) {
        console.error('Error verifying location log:', verifyError);
        throw verifyError;
      }

      if (!existingLog) {
        throw new Error('Location log not found');
      }

      if (existingLog.check_in_status !== 'checked_in') {
        throw new Error('This session is already checked out or inactive');
      }

      // Perform the check-out update
      const checkOutTime = new Date().toISOString();
      const { data, error } = await supabase
        .from('staff_location_logs')
        .update({
          check_in_status: 'checked_out' as CheckInStatus,
          check_out_time: checkOutTime,
        })
        .eq('id', targetLogId)
        .select()
        .maybeSingle();

      if (error) {
        console.error('Error during GPS check-out:', error);
        throw error;
      }

      if (!data) {
        throw new Error('Failed to update location log');
      }

      // Calculate and update duration
      if (data.check_in_time) {
        const checkIn = new Date(data.check_in_time);
        const checkOut = new Date(checkOutTime);
        const durationMinutes = Math.floor((checkOut.getTime() - checkIn.getTime()) / (1000 * 60));

        await supabase
          .from('staff_location_logs')
          .update({ total_duration_minutes: durationMinutes })
          .eq('id', targetLogId);
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['staff-location-logs'] });
      toast({
        title: 'Success',
        description: 'Successfully checked out',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to check out:', error);

      // Provide more specific error messages for check-out failures
      let title = 'Check-out Failed';
      let description = 'Failed to record check-out. Please try again.';

      if (error.message.includes('not found')) {
        title = 'No Active Session';
        description = 'No active check-in session found. You may already be checked out.';
      } else if (error.message.includes('already checked out')) {
        title = 'Already Checked Out';
        description = 'This session has already been checked out.';
      } else if (error.message.includes('network') || error.message.includes('fetch')) {
        title = 'Network Error';
        description = 'Check your internet connection. Your check-out will be saved offline if needed.';
      }

      toast({
        title,
        description: error.message || description,
        variant: 'destructive',
      });
    },
  });
};

// Hook to get current check-in status
export const useCurrentCheckInStatus = () => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['current-checkin-status', profile?.id],
    queryFn: async () => {
      if (!profile?.id) return null;

      const { data, error } = await supabase
        .from('staff_location_logs')
        .select('*')
        .eq('staff_id', profile.id)
        .eq('check_in_status', 'checked_in')
        .order('check_in_time', { ascending: false })
        .limit(1)
        .maybeSingle();

      if (error) {
        console.error('Error fetching current check-in status:', error);
        throw error;
      }

      return data as LocationLog | null;
    },
    enabled: !!profile?.id,
    refetchInterval: 30000, // Refetch every 30 seconds
  });
};

// Utility function to calculate distance between two points
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};
