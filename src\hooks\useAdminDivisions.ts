import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

// Type definition for administrative divisions
type AdminDivision = Database['public']['Functions']['get_admin_divisions']['Returns'][0];

/**
 * Hook to fetch administrative divisions (districts, sub-counties, etc.)
 * Used for school registration and location-based filtering
 */
export const useAdminDivisions = () => {
  return useQuery({
    queryKey: ['admin-divisions'],
    queryFn: async (): Promise<AdminDivision[]> => {
      console.log('Fetching administrative divisions...');
      
      const { data, error } = await supabase
        .rpc('get_admin_divisions');

      if (error) {
        console.error('Error fetching administrative divisions:', error);
        throw error;
      }

      console.log('✅ Administrative divisions fetched successfully:', data?.length || 0, 'divisions');
      return data || [];
    },
    staleTime: 10 * 60 * 1000, // 10 minutes - divisions don't change frequently
    cacheTime: 30 * 60 * 1000, // 30 minutes
  });
};

/**
 * Hook to fetch divisions filtered by district
 * @param district - District name to filter by
 */
export const useAdminDivisionsByDistrict = (district?: string) => {
  return useQuery({
    queryKey: ['admin-divisions', 'by-district', district],
    queryFn: async (): Promise<AdminDivision[]> => {
      console.log('Fetching administrative divisions for district:', district);
      
      const { data, error } = await supabase
        .rpc('get_admin_divisions');

      if (error) {
        console.error('Error fetching administrative divisions:', error);
        throw error;
      }

      // Filter by district if provided
      const filteredData = district 
        ? (data || []).filter(division => division.district === district)
        : data || [];

      console.log('✅ Filtered administrative divisions fetched:', filteredData.length, 'divisions');
      return filteredData;
    },
    enabled: !!district, // Only run if district is provided
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to get unique districts from administrative divisions
 */
export const useDistricts = () => {
  return useQuery({
    queryKey: ['districts'],
    queryFn: async (): Promise<string[]> => {
      console.log('Fetching unique districts...');
      
      const { data, error } = await supabase
        .rpc('get_admin_divisions');

      if (error) {
        console.error('Error fetching districts:', error);
        throw error;
      }

      // Extract unique districts
      const districts = Array.from(
        new Set((data || []).map(division => division.district))
      ).filter(Boolean).sort();

      console.log('✅ Unique districts fetched:', districts.length, 'districts');
      return districts;
    },
    staleTime: 15 * 60 * 1000, // 15 minutes
  });
};

/**
 * Hook to get sub-counties for a specific district
 * @param district - District name to get sub-counties for
 */
export const useSubCountiesByDistrict = (district: string) => {
  return useQuery({
    queryKey: ['sub-counties', district],
    queryFn: async (): Promise<string[]> => {
      console.log('Fetching sub-counties for district:', district);
      
      const { data, error } = await supabase
        .rpc('get_admin_divisions');

      if (error) {
        console.error('Error fetching sub-counties:', error);
        throw error;
      }

      // Filter by district and extract sub-counties
      const subCounties = (data || [])
        .filter(division => division.district === district)
        .map(division => division.sub_county)
        .filter(Boolean)
        .sort();

      // Remove duplicates
      const uniqueSubCounties = Array.from(new Set(subCounties));

      console.log('✅ Sub-counties fetched for', district, ':', uniqueSubCounties.length, 'sub-counties');
      return uniqueSubCounties;
    },
    enabled: !!district, // Only run if district is provided
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Utility function to format division for display
 * @param division - AdminDivision object
 * @returns formatted string for display
 */
export const formatDivision = (division: AdminDivision): string => {
  const parts = [division.district, division.sub_county].filter(Boolean);
  return parts.join(', ');
};

/**
 * Utility function to find division by ID
 * @param divisions - Array of AdminDivision objects
 * @param divisionId - ID to search for
 * @returns AdminDivision object or undefined
 */
export const findDivisionById = (divisions: AdminDivision[], divisionId: string): AdminDivision | undefined => {
  return divisions.find(division => division.id === divisionId);
};
