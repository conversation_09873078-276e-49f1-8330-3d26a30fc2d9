/**
 * Type definitions for offline sync functionality
 */

export interface OfflineData {
  id: string;
  type: 'check_in' | 'check_out' | 'field_report' | 'photo_upload';
  data: Record<string, unknown>;
  timestamp: number;
  retryCount: number;
  maxRetries: number;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  checksum?: string;
  version?: number;
  conflictResolution?: 'CLIENT_WINS' | 'SERVER_WINS' | 'MERGE' | 'MANUAL';
  // Photo-specific fields
  blobData?: Blob;
  uploadProgress?: number;
  uploadSessionId?: string;
}

export interface OfflineSyncStatus {
  isOnline: boolean;
  pendingItems: number;
  lastSyncTime: Date | null;
  isSyncing: boolean;
  syncProgress: number;
  failedItems: number;
  conflictItems: number;
  storageUsagePercent: number;
  lastCleanup: Date | null;
  totalStorageSize: number;
}

export interface SyncConflict {
  id: string;
  localData: Record<string, unknown>;
  serverData: Record<string, unknown>;
  conflictFields: string[];
  timestamp: number;
}

export interface StorageStats {
  totalItems: number;
  totalSize: number;
  conflictItems: number;
  conflictSize: number;
  photoItems: number;
  photoSize: number;
  lastCleanup: number;
  storageUsagePercent: number;
  oldestItem: number;
  newestItem: number;
}

export interface CleanupResult {
  itemsRemoved: number;
  sizeFreed: number;
  conflictsRemoved: number;
  photosRemoved: number;
}

export interface ConflictData {
  id: string;
  localData: Record<string, unknown>;
  serverData: Record<string, unknown>;
  conflictFields: string[];
  timestamp: number;
  type: string;
}

export interface SyncStats {
  totalPending: number;
  highPriority: number;
  failed: number;
  conflicts: number;
  oldestItem: number | null;
}

export interface SyncResult {
  success: boolean;
  processed: number;
  failed: number;
  conflicts: number;
  errors: string[];
}

export interface PhotoUploadProgress {
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'failed';
  error?: string;
}

export interface BatchSyncOptions {
  batchSize?: number;
  maxConcurrent?: number;
  retryFailedItems?: boolean;
  prioritizeHighPriority?: boolean;
}

export interface StorageConfig {
  maxStorageSize: number;
  cleanupThreshold: number;
  maxRetentionDays: number;
  compressionEnabled: boolean;
}

export interface SyncConfig {
  syncBatchSize: number;
  maxConcurrentUploads: number;
  retryDelayMs: number;
  maxRetryAttempts: number;
  conflictResolutionStrategy: 'CLIENT_WINS' | 'SERVER_WINS' | 'MERGE' | 'MANUAL';
}

// Storage keys constants
export const STORAGE_KEYS = {
  OFFLINE_DATA: 'ilead_offline_data',
  CONFLICTS: 'ilead_sync_conflicts',
  STORAGE_STATS: 'ilead_storage_stats',
  SYNC_STATUS: 'ilead_sync_status',
  CLEANUP_TIMESTAMP: 'ilead_last_cleanup',
  PHOTO_CACHE: 'ilead_photo_cache',
} as const;

// Default configuration values
export const DEFAULT_CONFIG: SyncConfig & StorageConfig = {
  // Sync config
  syncBatchSize: 10,
  maxConcurrentUploads: 3,
  retryDelayMs: 1000,
  maxRetryAttempts: 3,
  conflictResolutionStrategy: 'MANUAL',
  
  // Storage config
  maxStorageSize: 50 * 1024 * 1024, // 50MB
  cleanupThreshold: 0.8, // 80%
  maxRetentionDays: 30,
  compressionEnabled: true,
};

// Priority levels for sync operations
export const SYNC_PRIORITIES = {
  LOW: 1,
  MEDIUM: 2,
  HIGH: 3,
  CRITICAL: 4,
} as const;

// Sync operation types
export const SYNC_TYPES = {
  CHECK_IN: 'check_in',
  CHECK_OUT: 'check_out',
  FIELD_REPORT: 'field_report',
  PHOTO_UPLOAD: 'photo_upload',
} as const;

// Conflict resolution strategies
export const CONFLICT_RESOLUTION = {
  CLIENT_WINS: 'CLIENT_WINS',
  SERVER_WINS: 'SERVER_WINS',
  MERGE: 'MERGE',
  MANUAL: 'MANUAL',
} as const;
