
import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { List, Search, Filter, BookOpen, Calendar, MapPin, Eye } from 'lucide-react';
import { useDistributions } from '@/hooks/useDistributions';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import DistributionDetailsModal from './DistributionDetailsModal';

// Type definitions
type BookDistribution = Database['public']['Functions']['get_book_distributions']['Returns'][0];
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];

const DistributionHistory = () => {
  const { distributions, schools, isLoading } = useDistributions();
  const [searchTerm, setSearchTerm] = useState('');
  const [schoolFilter, setSchoolFilter] = useState('all');
  const [sortBy, setSortBy] = useState('date_desc');
  const [selectedDistribution, setSelectedDistribution] = useState<BookDistribution | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const handleViewDetails = (distribution: BookDistribution) => {
    setSelectedDistribution(distribution);
    setIsDetailsModalOpen(true);
  };

  // Filter distributions for history (completed more than 24 hours ago)
  const historicalDistributions = distributions.filter((dist: BookDistribution) => {
    const distDate = new Date(dist.delivery_date);
    const twentyFourHoursAgo = new Date();
    twentyFourHoursAgo.setHours(twentyFourHoursAgo.getHours() - 24);

    // Include only completed distributions from more than 24 hours ago
    return distDate < twentyFourHoursAgo && dist.status === 'completed';
  });

  // Filter and sort distributions
  const filteredDistributions = historicalDistributions
    .filter((dist: BookDistribution) => {
      const matchesSearch = !searchTerm ||
        dist.book_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dist.school_name?.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesSchool = schoolFilter === 'all' || dist.school_id === schoolFilter;

      return matchesSearch && matchesSchool;
    })
    .sort((a: BookDistribution, b: BookDistribution) => {
      switch (sortBy) {
        case 'date_desc':
          return new Date(b.delivery_date).getTime() - new Date(a.delivery_date).getTime();
        case 'date_asc':
          return new Date(a.delivery_date).getTime() - new Date(b.delivery_date).getTime();
        case 'quantity_desc':
          return b.quantity - a.quantity;
        case 'quantity_asc':
          return a.quantity - b.quantity;
        default:
          return 0;
      }
    });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case 'planned':
        return <Badge variant="outline">Planned</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Distribution History"
        description={`Completed distributions from more than 24 hours ago - ${historicalDistributions.length} total`}
        icon={List}
      />

      <ContentCard title="Filters & Search" icon={Filter}>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
            <Input
              placeholder="Search distributions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10"
            />
          </div>
          <Select value={schoolFilter} onValueChange={setSchoolFilter}>
            <SelectTrigger>
              <SelectValue placeholder="All Schools" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              <SelectItem value="all">All Schools</SelectItem>
              {schools.map((school: SchoolWithDivision) => (
                <SelectItem key={school.id} value={school.id}>
                  {school.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger>
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent className="bg-white z-50">
              <SelectItem value="date_desc">Latest First</SelectItem>
              <SelectItem value="date_asc">Oldest First</SelectItem>
              <SelectItem value="quantity_desc">Highest Quantity</SelectItem>
              <SelectItem value="quantity_asc">Lowest Quantity</SelectItem>
            </SelectContent>
          </Select>
          <div className="text-sm text-gray-600 flex items-center">
            Showing {filteredDistributions.length} of {historicalDistributions.length} historical distributions
          </div>
        </div>
      </ContentCard>

      <ContentCard noPadding>
        {filteredDistributions.length > 0 ? (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Book Title</TableHead>
                <TableHead>School</TableHead>
                <TableHead>Quantity</TableHead>
                <TableHead>Delivery Date</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDistributions.map((distribution: BookDistribution) => (
                <TableRow key={distribution.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    <div className="flex items-center">
                      <BookOpen className="h-4 w-4 text-purple-600 mr-2" />
                      {distribution.book_title || 'Unknown Book'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-gray-500 mr-2" />
                      {distribution.school_name || 'Unknown School'}
                    </div>
                  </TableCell>
                  <TableCell>
                    <span className="font-medium">{distribution.quantity}</span> copies
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                      {distribution.delivery_date}
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(distribution.status)}
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleViewDetails(distribution)}
                      className="flex items-center gap-2"
                    >
                      <Eye className="h-4 w-4" />
                      View Details
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        ) : (
          <div className="text-center py-12">
            <List className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No distributions found</h3>
            <p className="text-gray-600">Try adjusting your search or filter criteria</p>
          </div>
        )}
      </ContentCard>

      {/* Distribution Details Modal */}
      {selectedDistribution && (
        <DistributionDetailsModal
          distribution={selectedDistribution}
          isOpen={isDetailsModalOpen}
          onClose={() => {
            setIsDetailsModalOpen(false);
            setSelectedDistribution(null);
          }}
        />
      )}
    </PageLayout>
  );
};

export default DistributionHistory;
