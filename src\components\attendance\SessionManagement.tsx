import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Calendar, 
  Clock, 
  Users, 
  Plus, 
  Search, 
  Filter,
  BookOpen,
  Target,
  MapPin,
  CheckCircle,
  XCircle,
  Timer,
  Edit,
  Trash2,
  Eye
} from 'lucide-react';
import { useAttendanceSessions, useDeleteAttendanceSession } from '@/hooks/attendance/useAttendanceSessions';
import { useSchools } from '@/hooks/useSchools';
import { useAuth } from '@/hooks/useAuth';
import CreateSessionDialog from './CreateSessionDialog';
import AttendanceTracker from './AttendanceTracker';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

type SessionType = Database['public']['Enums']['session_type'];

interface SessionManagementProps {
  defaultSchoolId?: string;
}

const SessionManagement: React.FC<SessionManagementProps> = ({
  defaultSchoolId,
}) => {
  const { profile } = useAuth();
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || 'all');
  const [sessionTypeFilter, setSessionTypeFilter] = useState<SessionType | 'all'>('all');
  const [dateFilter, setDateFilter] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSessionId, setSelectedSessionId] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('sessions');

  const { data: schools } = useSchools();
  const { data: sessions, isLoading } = useAttendanceSessions(
    selectedSchoolId && selectedSchoolId !== 'all' ? selectedSchoolId : undefined,
    dateFilter ? {
      start: new Date(dateFilter),
      end: new Date(dateFilter + 'T23:59:59')
    } : undefined
  );
  const deleteSession = useDeleteAttendanceSession();

  // Filter sessions based on search and filters
  const filteredSessions = sessions?.filter(session => {
    if (sessionTypeFilter && sessionTypeFilter !== 'all' && session.session_type !== sessionTypeFilter) return false;
    if (searchTerm && !session.session_name.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  }) || [];

  // Group sessions by date
  const sessionsByDate = filteredSessions.reduce((groups, session) => {
    const date = session.session_date;
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(session);
    return groups;
  }, {} as Record<string, typeof filteredSessions>);

  const getSessionTypeIcon = (type: SessionType) => {
    switch (type) {
      case 'class':
        return <BookOpen className="h-4 w-4" />;
      case 'leadership_program':
        return <Target className="h-4 w-4" />;
      case 'training':
        return <Users className="h-4 w-4" />;
      case 'assessment':
        return <CheckCircle className="h-4 w-4" />;
      case 'meeting':
        return <Calendar className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getSessionTypeColor = (type: SessionType) => {
    switch (type) {
      case 'class':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'leadership_program':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'training':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'assessment':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'meeting':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getAttendanceStats = (session: Database['public']['Tables']['attendance_sessions']['Row'] & {
    student_attendance?: Array<{
      attendance_status: Database['public']['Enums']['attendance_status'];
    }>;
  }) => {
    const attendance = session.student_attendance || [];
    const total = attendance.length;
    const present = attendance.filter((a) => a.attendance_status === 'present').length;
    const absent = attendance.filter((a) => a.attendance_status === 'absent').length;
    const late = attendance.filter((a) => a.attendance_status === 'late').length;

    return { total, present, absent, late };
  };

  const handleDeleteSession = async (sessionId: string) => {
    if (window.confirm('Are you sure you want to delete this session? This action cannot be undone.')) {
      try {
        await deleteSession.mutateAsync(sessionId);
      } catch (error) {
        console.error('Failed to delete session:', error);
      }
    }
  };

  const handleSessionCreated = (sessionId: string) => {
    setSelectedSessionId(sessionId);
    setActiveTab('attendance');
  };

  if (selectedSessionId && activeTab === 'attendance') {
    const session = sessions?.find(s => s.id === selectedSessionId);
    if (session) {
      return (
        <AttendanceTracker
          sessionId={session.id}
          sessionName={session.session_name}
          sessionDate={session.session_date}
          sessionType={session.session_type}
          roundTablesCount={session.round_tables_count || 0}
          studentsPerTable={session.students_per_table || 8}
        />
      );
    }
  }

  return (
    <PageLayout>
      <PageHeader
        title="Session Management"
        description="Create and manage attendance sessions for classes, leadership programs, and training"
      />

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="attendance">Attendance</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="sessions" className="space-y-6">
          {/* Filters and Search */}
          <ContentCard>
            <div className="space-y-4">
              {/* Mobile-responsive filters */}
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search sessions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-full"
                    />
                  </div>

                  <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="All schools" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All schools</SelectItem>
                      {schools?.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Select value={sessionTypeFilter} onValueChange={(value: SessionType | 'all') => setSessionTypeFilter(value)}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All types</SelectItem>
                      <SelectItem value="class">Class</SelectItem>
                      <SelectItem value="leadership_program">Leadership Program</SelectItem>
                      <SelectItem value="training">Training</SelectItem>
                      <SelectItem value="assessment">Assessment</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>

                  <Input
                    type="date"
                    value={dateFilter}
                    onChange={(e) => setDateFilter(e.target.value)}
                    className="w-full"
                  />
                </div>

                <CreateSessionDialog
                  onSessionCreated={handleSessionCreated}
                  trigger={
                    <Button className="w-full sm:w-auto">
                      <Plus className="h-4 w-4 mr-2" />
                      Create Session
                    </Button>
                  }
                />
              </div>

              {/* Clear Filters */}
              {(searchTerm || (selectedSchoolId && selectedSchoolId !== 'all') || (sessionTypeFilter && sessionTypeFilter !== 'all') || dateFilter) && (
                <div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm('');
                      setSelectedSchoolId('all');
                      setSessionTypeFilter('all');
                      setDateFilter('');
                    }}
                  >
                    <Filter className="h-4 w-4 mr-2" />
                    Clear Filters
                  </Button>
                </div>
              )}
            </div>
          </ContentCard>

          {/* Sessions List */}
          {isLoading ? (
            <ContentCard>
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              </div>
            </ContentCard>
          ) : Object.keys(sessionsByDate).length === 0 ? (
            <ContentCard>
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No sessions found</h3>
                <p className="text-gray-600 mb-4">
                  {sessions?.length === 0 
                    ? "No sessions have been created yet."
                    : "No sessions match your current filters."
                  }
                </p>
                <CreateSessionDialog
                  onSessionCreated={handleSessionCreated}
                  trigger={
                    <Button>
                      <Plus className="h-4 w-4 mr-2" />
                      Create Your First Session
                    </Button>
                  }
                />
              </div>
            </ContentCard>
          ) : (
            <div className="space-y-6">
              {Object.entries(sessionsByDate)
                .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                .map(([date, dateSessions]) => (
                  <ContentCard key={date}>
                    <CardHeader>
                      <CardTitle className="flex items-center gap-2">
                        <Calendar className="h-5 w-5" />
                        {new Date(date).toLocaleDateString('en-US', {
                          weekday: 'long',
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                        })}
                      </CardTitle>
                      <CardDescription>
                        {dateSessions.length} session{dateSessions.length !== 1 ? 's' : ''} scheduled
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {dateSessions
                          .sort((a, b) => a.start_time.localeCompare(b.start_time))
                          .map((session) => {
                            const stats = getAttendanceStats(session);
                            const isCompleted = session.is_completed;
                            
                            return (
                              <div key={session.id} className="border rounded-lg p-4">
                                <div className="flex items-start justify-between mb-3">
                                  <div className="flex items-start gap-3">
                                    <div className={`p-2 rounded-lg ${getSessionTypeColor(session.session_type).replace('text-', 'bg-').replace('border-', '').replace('bg-', 'bg-').split(' ')[0]}`}>
                                      {getSessionTypeIcon(session.session_type)}
                                    </div>
                                    <div>
                                      <div className="font-medium text-lg">{session.session_name}</div>
                                      <div className="text-sm text-gray-600 space-y-1">
                                        <div className="flex items-center gap-4">
                                          <span className="flex items-center gap-1">
                                            <Clock className="h-3 w-3" />
                                            {session.start_time} - {session.end_time || 'TBD'}
                                          </span>
                                          {session.location && (
                                            <span className="flex items-center gap-1">
                                              <MapPin className="h-3 w-3" />
                                              {session.location}
                                            </span>
                                          )}
                                          {session.grade_level && (
                                            <span>Grade {session.grade_level}</span>
                                          )}
                                        </div>
                                        {session.school?.name && (
                                          <div>{session.school.name}</div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div className="flex items-center gap-2">
                                    <Badge className={getSessionTypeColor(session.session_type)}>
                                      {session.session_type.replace('_', ' ')}
                                    </Badge>
                                    {isCompleted && (
                                      <Badge className="bg-green-100 text-green-800 border-green-200">
                                        Completed
                                      </Badge>
                                    )}
                                  </div>
                                </div>

                                {/* Session Details */}
                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mb-4">
                                  {/* Attendance Stats */}
                                  <div>
                                    <div className="text-sm font-medium mb-1">Attendance</div>
                                    <div className="flex items-center gap-2 text-sm">
                                      <span className="text-green-600">{stats.present} present</span>
                                      <span className="text-red-600">{stats.absent} absent</span>
                                      <span className="text-yellow-600">{stats.late} late</span>
                                      <span className="text-gray-600">({stats.total} total)</span>
                                    </div>
                                  </div>

                                  {/* Capacity */}
                                  <div>
                                    <div className="text-sm font-medium mb-1">Capacity</div>
                                    <div className="text-sm text-gray-600">
                                      {session.max_capacity ? `Max ${session.max_capacity} students` : 'No limit'}
                                      {session.round_tables_count > 0 && (
                                        <span> • {session.round_tables_count} tables</span>
                                      )}
                                    </div>
                                  </div>

                                  {/* Facilitator */}
                                  <div>
                                    <div className="text-sm font-medium mb-1">Facilitator</div>
                                    <div className="text-sm text-gray-600">
                                      {session.facilitator?.name || session.teacher_name || 'Not assigned'}
                                    </div>
                                  </div>
                                </div>

                                {/* Actions */}
                                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 pt-3 border-t">
                                  <div className="flex flex-wrap items-center gap-2">
                                    <Button
                                      size="sm"
                                      onClick={() => {
                                        setSelectedSessionId(session.id);
                                        setActiveTab('attendance');
                                      }}
                                    >
                                      <Eye className="h-4 w-4 mr-2" />
                                      View Attendance
                                    </Button>
                                    
                                    {(profile?.role === 'admin' || profile?.role === 'program_officer' || session.facilitator_id === profile?.id) && (
                                      <>
                                        <Button variant="outline" size="sm">
                                          <Edit className="h-4 w-4 mr-2" />
                                          Edit
                                        </Button>
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => handleDeleteSession(session.id)}
                                          className="text-red-600 hover:text-red-700"
                                        >
                                          <Trash2 className="h-4 w-4 mr-2" />
                                          Delete
                                        </Button>
                                      </>
                                    )}
                                  </div>

                                  <div className="text-xs text-gray-500">
                                    Created {new Date(session.created_at).toLocaleDateString()}
                                  </div>
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </CardContent>
                  </ContentCard>
                ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="attendance">
          <ContentCard>
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Session</h3>
              <p className="text-gray-600">
                Choose a session from the Sessions tab to view and manage attendance.
              </p>
            </div>
          </ContentCard>
        </TabsContent>

        <TabsContent value="analytics">
          <ContentCard>
            <div className="text-center py-8">
              <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Analytics Coming Soon</h3>
              <p className="text-gray-600">
                Session analytics and reporting features will be available here.
              </p>
            </div>
          </ContentCard>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default SessionManagement;
