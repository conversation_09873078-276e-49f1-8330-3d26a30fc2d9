import { toast } from 'sonner';

// Standardized error types for field operations
export interface FieldError {
  code: string;
  message: string;
  type: 'GPS' | 'NETWORK' | 'VALIDATION' | 'SYNC' | 'PERMISSION' | 'UNKNOWN';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  recoverable: boolean;
  retryable: boolean;
  userAction?: string;
  technicalDetails?: string;
  timestamp: number;
}

// Error codes for different scenarios
export const ERROR_CODES = {
  // GPS Errors
  GPS_PERMISSION_DENIED: 'GPS_001',
  GPS_UNAVAILABLE: 'GPS_002',
  GPS_TIMEOUT: 'GPS_003',
  GPS_ACCURACY_LOW: 'GPS_004',
  GPS_NOT_SUPPORTED: 'GPS_005',
  
  // Network Errors
  NETWORK_OFFLINE: 'NET_001',
  NETWORK_TIMEOUT: 'NET_002',
  NETWORK_SERVER_ERROR: 'NET_003',
  NETWORK_RATE_LIMITED: 'NET_004',
  
  // Sync Errors
  SYNC_CONFLICT: 'SYNC_001',
  SYNC_DATA_CORRUPTION: 'SYNC_002',
  SYNC_QUOTA_EXCEEDED: 'SYNC_003',
  
  // Validation Errors
  VALIDATION_REQUIRED_FIELD: 'VAL_001',
  VALIDATION_INVALID_FORMAT: 'VAL_002',
  VALIDATION_OUT_OF_RANGE: 'VAL_003',
  
  // Permission Errors
  PERMISSION_INSUFFICIENT: 'PERM_001',
  PERMISSION_EXPIRED: 'PERM_002',
} as const;

// Error recovery strategies
export interface RecoveryStrategy {
  type: 'RETRY' | 'FALLBACK' | 'USER_ACTION' | 'IGNORE' | 'MANUAL_FALLBACK' | 'RETRY_OR_MANUAL';
  maxAttempts?: number;
  backoffMs?: number;
  fallbackAction?: () => Promise<void>;
  userPrompt?: string;
}

// Enhanced error class for field operations
export class FieldOperationError extends Error {
  public readonly fieldError: FieldError;
  public readonly recovery?: RecoveryStrategy;

  constructor(
    code: string,
    message: string,
    type: FieldError['type'],
    severity: FieldError['severity'] = 'MEDIUM',
    recovery?: RecoveryStrategy
  ) {
    super(message);
    this.name = 'FieldOperationError';
    
    this.fieldError = {
      code,
      message,
      type,
      severity,
      recoverable: !!recovery,
      retryable: recovery?.type === 'RETRY',
      timestamp: Date.now(),
    };
    
    this.recovery = recovery;
  }

  static fromGPSError(error: GeolocationPositionError): FieldOperationError {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        return new FieldOperationError(
          ERROR_CODES.GPS_PERMISSION_DENIED,
          'Location access denied by user',
          'GPS',
          'HIGH',
          {
            type: 'MANUAL_FALLBACK',
            userPrompt: 'Please enable location permissions in your browser settings and refresh the page, or use manual location entry.',
            fallbackAction: async () => {
              toast.info('GPS permission denied. You can use manual location entry as an alternative.');
            },
          }
        );

      case error.POSITION_UNAVAILABLE:
        return new FieldOperationError(
          ERROR_CODES.GPS_UNAVAILABLE,
          'Location information unavailable',
          'GPS',
          'MEDIUM',
          {
            type: 'MANUAL_FALLBACK',
            userPrompt: 'GPS signal unavailable. Try moving to an open area or use manual location entry.',
            fallbackAction: async () => {
              toast.info('GPS unavailable. Switching to manual location entry option...');
            },
          }
        );

      case error.TIMEOUT:
        return new FieldOperationError(
          ERROR_CODES.GPS_TIMEOUT,
          'Location request timed out',
          'GPS',
          'MEDIUM',
          {
            type: 'RETRY_OR_MANUAL',
            maxAttempts: 3,
            backoffMs: 2000,
            userPrompt: 'GPS timeout. You can retry or use manual location entry.',
            fallbackAction: async () => {
              toast.info('GPS timeout. You can retry or enter location manually.');
            },
          }
        );

      default:
        return new FieldOperationError(
          'GPS_UNKNOWN',
          'Unknown GPS error',
          'GPS',
          'MEDIUM',
          {
            type: 'MANUAL_FALLBACK',
            userPrompt: 'GPS error occurred. Please use manual location entry.',
            fallbackAction: async () => {
              toast.warning('GPS error. Manual location entry is available.');
            },
          }
        );
    }
  }

  static fromNetworkError(error: Error, isOnline: boolean): FieldOperationError {
    if (!isOnline) {
      return new FieldOperationError(
        ERROR_CODES.NETWORK_OFFLINE,
        'Device is offline',
        'NETWORK',
        'MEDIUM',
        {
          type: 'FALLBACK',
          fallbackAction: async () => {
            toast.info('Saving data offline. Will sync when connection is restored.');
          },
        }
      );
    }

    if (error.message.includes('timeout')) {
      return new FieldOperationError(
        ERROR_CODES.NETWORK_TIMEOUT,
        'Network request timed out',
        'NETWORK',
        'MEDIUM',
        {
          type: 'RETRY',
          maxAttempts: 3,
          backoffMs: 5000,
        }
      );
    }

    return new FieldOperationError(
      ERROR_CODES.NETWORK_SERVER_ERROR,
      'Server error occurred',
      'NETWORK',
      'HIGH',
      {
        type: 'RETRY',
        maxAttempts: 2,
        backoffMs: 10000,
      }
    );
  }
}

// Error handler with retry logic and user feedback
export class ErrorHandler {
  private static retryAttempts = new Map<string, number>();

  static async handle(error: FieldOperationError): Promise<boolean> {
    const { fieldError, recovery } = error;
    
    // Log error for monitoring
    console.error('Field Operation Error:', {
      code: fieldError.code,
      message: fieldError.message,
      type: fieldError.type,
      severity: fieldError.severity,
      timestamp: new Date(fieldError.timestamp).toISOString(),
    });

    // Handle based on recovery strategy
    if (!recovery) {
      this.showUserError(fieldError);
      return false;
    }

    switch (recovery.type) {
      case 'RETRY':
        return this.handleRetry(error);

      case 'FALLBACK':
        return this.handleFallback(error);

      case 'USER_ACTION':
        return this.handleUserAction(error);

      case 'MANUAL_FALLBACK':
        return this.handleManualFallback(error);

      case 'RETRY_OR_MANUAL':
        return this.handleRetryOrManual(error);

      case 'IGNORE':
        return true;

      default:
        this.showUserError(fieldError);
        return false;
    }
  }

  private static async handleRetry(error: FieldOperationError): Promise<boolean> {
    const { fieldError, recovery } = error;
    const key = `${fieldError.code}_${fieldError.timestamp}`;
    const attempts = this.retryAttempts.get(key) || 0;

    if (attempts >= (recovery?.maxAttempts || 3)) {
      this.retryAttempts.delete(key);
      this.showUserError(fieldError, 'Maximum retry attempts reached');
      return false;
    }

    this.retryAttempts.set(key, attempts + 1);
    
    if (recovery?.backoffMs) {
      await new Promise(resolve => setTimeout(resolve, recovery.backoffMs * Math.pow(2, attempts)));
    }

    toast.info(`Retrying... (${attempts + 1}/${recovery?.maxAttempts || 3})`);
    return true;
  }

  private static async handleFallback(error: FieldOperationError): Promise<boolean> {
    const { recovery } = error;
    
    if (recovery?.fallbackAction) {
      try {
        await recovery.fallbackAction();
        return true;
      } catch (fallbackError) {
        console.error('Fallback action failed:', fallbackError);
        this.showUserError(error.fieldError, 'Fallback action failed');
        return false;
      }
    }

    return false;
  }

  private static handleUserAction(error: FieldOperationError): boolean {
    const { fieldError, recovery } = error;

    if (recovery?.userPrompt) {
      toast.error(fieldError.message, {
        description: recovery.userPrompt,
        duration: 10000,
        action: {
          label: 'Dismiss',
          onClick: () => {},
        },
      });
    } else {
      this.showUserError(fieldError);
    }

    return false;
  }

  private static async handleManualFallback(error: FieldOperationError): Promise<boolean> {
    const { fieldError, recovery } = error;

    // Execute fallback action if provided
    if (recovery?.fallbackAction) {
      try {
        await recovery.fallbackAction();
      } catch (fallbackError) {
        console.error('Fallback action failed:', fallbackError);
      }
    }

    // Show user prompt for manual entry
    if (recovery?.userPrompt) {
      toast.info(fieldError.message, {
        description: recovery.userPrompt,
        duration: 10000,
        action: {
          label: 'Manual Entry',
          onClick: () => {
            // This will be handled by the component that catches this error
            window.dispatchEvent(new CustomEvent('gps-manual-fallback-requested', {
              detail: { error: fieldError }
            }));
          },
        },
      });
    }

    return false; // Manual intervention required
  }

  private static async handleRetryOrManual(error: FieldOperationError): Promise<boolean> {
    const { fieldError, recovery } = error;

    // First try retry logic
    const retrySuccess = await this.handleRetry(error);

    if (!retrySuccess) {
      // If retry fails, offer manual fallback
      return this.handleManualFallback(error);
    }

    return retrySuccess;
  }

  private static showUserError(error: FieldError, additionalInfo?: string): void {
    const description = additionalInfo ? `${error.message}. ${additionalInfo}` : error.message;
    
    switch (error.severity) {
      case 'CRITICAL':
        toast.error('Critical Error', {
          description,
          duration: 15000,
        });
        break;
      
      case 'HIGH':
        toast.error('Error', {
          description,
          duration: 8000,
        });
        break;
      
      case 'MEDIUM':
        toast.warning('Warning', {
          description,
          duration: 5000,
        });
        break;
      
      case 'LOW':
        toast.info('Notice', {
          description,
          duration: 3000,
        });
        break;
    }
  }

  static clearRetryHistory(): void {
    this.retryAttempts.clear();
  }
}

// Utility function for exponential backoff
export const exponentialBackoff = (attempt: number, baseDelay: number = 1000): number => {
  return Math.min(baseDelay * Math.pow(2, attempt), 30000); // Max 30 seconds
};

// Network status monitoring
export const createNetworkMonitor = (onStatusChange: (isOnline: boolean) => void) => {
  const handleOnline = () => onStatusChange(true);
  const handleOffline = () => onStatusChange(false);

  window.addEventListener('online', handleOnline);
  window.addEventListener('offline', handleOffline);

  return () => {
    window.removeEventListener('online', handleOnline);
    window.removeEventListener('offline', handleOffline);
  };
};
