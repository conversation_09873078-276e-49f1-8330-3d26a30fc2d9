
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { SchoolFormData } from '@/types/school';

interface ContactInformationSectionProps {
  formData: SchoolFormData;
  onFormDataChange: (data: SchoolFormData) => void;
}

const ContactInformationSection = ({ formData, onFormDataChange }: ContactInformationSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Contact Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="contact_phone">Contact Phone</Label>
          <Input
            id="contact_phone"
            value={formData.contact_phone}
            onChange={(e) => onFormDataChange({ ...formData, contact_phone: e.target.value })}
            placeholder="+256-700123456"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="email">Email Address</Label>
          <Input
            id="email"
            type="email"
            value={formData.email}
            onChange={(e) => onFormDataChange({ ...formData, email: e.target.value })}
            placeholder="<EMAIL>"
          />
        </div>
      </div>
    </div>
  );
};

export default ContactInformationSection;
