iLead Field Beacon - Product Brief
Objective: Develop iLead Field Beacon, a scalable, user-friendly web-based platform to streamline task management, field reporting, attendance tracking, and performance monitoring for iLead’s leadership training initiatives, empowering African students through training and resource distribution (e.g., book giveaways).

Overview
Phase
Key Features
Phase 1: Core Platform (MVP)
User Authentication, Dashboard, Book Distribution Tracking, Basic Task Management & Reporting
Phase 2: Enhanced Tracking & Reporting
Attendance & Session Tracking, Student & School Management, Notifications
Phase 3: Performance & Analytics
Performance Assessment, Advanced Reporting, Admin Insights
Phase 4: Scaling & Optimization
Multi-Region Support, Mobile Optimization, Stakeholder Dashboards


Detailed Roadmap
Phase 1: Core Platform (MVP) – Weeks 1-3
Goal: Launch a functional system for task assignment, book distribution monitoring, field reporting, and basic tracking.
Features:
User Authentication & Roles
Secure login for Admin, Program Officers, and Field Staff.
Role-based access control (Admins manage tasks, Field Staff submit reports).
Dashboard
Task summary (pending, in progress, completed).
Book distribution overview (schools served, total books delivered).
Recent reports (e.g., latest book delivery logs).
Quick actions: Assign tasks, submit reports.
Task Management
Create/assign tasks (title, description, priority, due date).
Update task status (Pending, In Progress, Completed).
Support comments and file attachments (e.g., delivery photos).
Field Reporting
Log arrival/departure (GPS-mocked, notes, photos).
Book delivery summaries (school, quantity, challenges, outcomes).
Activity Feed
Real-time updates on tasks, reports, and comments.

Phase 2: Enhanced Tracking & Reporting – Week 4
Goal: Enhance functionality with attendance tracking, student engagement, and school management.
Features:
Attendance & Session Tracking
GPS-based staff check-in/check-out at schools.
Session logs (class details, students present).
Round-table tracking (8 students per table for leadership sessions).
School Management
Maintain a list of onboarded schools (name, location, student count).
Update student counts per term.
Notifications & Reminders
Alerts for pending or assigned tasks.
Deadline reminders for reports or deliveries.
Admin Panel Enhancements
Filter tasks and reports by user, school, or region.

Phase 3: Performance & Analytics – Week 5
Goal: Enable performance tracking, champion assignments, and advanced reporting.
Features:
Performance Assessment
Assign “Champion” teachers per school to lead initiatives.
Track staff performance (tasks completed, reports submitted).
Comprehensive Reporting
Generate reports by staff, region, or country.
Export data in Excel or PDF formats.
Admin Insights Dashboard
Display key metrics (schools, students, sessions).
Visualize trends and progress (e.g., book distribution over time).

Phase 4: Scaling & Optimization – Weeks 6-7
Goal: Expand functionality for multi-region support and stakeholder engagement.
Features:
Multi-Region & Country Support
Regional dashboards (e.g., Western, Eastern regions).
Country-level reporting for broader impact analysis.
Stakeholder Dashboards
General dashboard for prospects (total schools, students, impact).
Staff-specific dashboard (personalized task and performance stats).
Mobile Optimization
Fully responsive design for field staff using mobile devices.
Feedback & Iteration
Integrate user feedback for continuous improvement.
Address bugs and refine UI/UX.

Expected Outcomes
Improved Operational Efficiency: Streamlined task and report management.
Better Tracking: Real-time data on attendance, sessions, and book distributions.
Data-Driven Decisions: Insights into performance and regional impact.
Stakeholder Engagement: Transparent dashboards for internal and external visibility.

Prototype: iLead Field Beacon (14-Day Timeline)
Objective: Deliver a functional web-based prototype by June 15, 2025, focused on monitoring book distribution to schools, tracking which schools received books and the quantities distributed, while ensuring scalability for future features.
Key Features for Prototype:
User Authentication
Secure login for Admins (manage schools/reports) and Field Staff (log deliveries).
Role-based access control.
School Management
Add/list schools (name, location, student count, contact details).
Track book deliveries per school (title, quantity, date, supervisor).
Book Distribution Tracking
Log book deliveries: Select school, enter book details (e.g., “English Textbooks, 50 copies”), and submit.
Include GPS-mocked check-in (location + timestamp) and notes/photos.
Basic Dashboard
Display summary: Schools served, total books distributed, recent deliveries.
Quick actions: Add school, log delivery, view reports.
Report Export (Excel)
Export book distribution data (school name, books delivered, date, supervisor).
Filter by school or date range.
Mobile-Friendly UI
Responsive design for field staff to log deliveries via mobile devices.
Core Workflow:
Field Staff: Selects a school from a dropdown, enters book details (title, quantity), checks in with GPS-mocked location, and submits a report.
Admin: Adds schools, views real-time updates on the dashboard (e.g., “25 schools served, 1,500 books distributed”), and exports reports.
Data Simplicity:
Focus on school name, book details (title, quantity), delivery date, and supervisor name.
Exclude attendance tracking, performance metrics, and multi-region support for the prototype.
Deliverables:
Web-based prototype (deployed by June 15, 2025).
Excel export functionality for book distribution data.
Test data: Pre-loaded list of 10 schools and sample book deliveries.
Timeline:
Week 1 (June 2–8, 2025): Build authentication, school management, book distribution logging, and basic dashboard. Test core workflow.
Week 2 (June 9–15, 2025): Add report export, mobile optimization, and final testing. Deploy prototype with mock data.