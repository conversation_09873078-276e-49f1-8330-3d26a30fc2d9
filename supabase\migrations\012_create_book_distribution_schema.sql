-- Book Distribution Management Schema
-- This migration creates the missing database infrastructure for book distribution feature
-- Priority: CRITICAL - Required for distribution feature to function

-- Create book_inventory table
CREATE TABLE book_inventory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    book_title VARCHAR(255) NOT NULL,
    quantity_available INTEGER NOT NULL DEFAULT 0 CHECK (quantity_available >= 0),
    language VARCHAR(50),
    grade_level VARCHAR(20),
    subject VARCHAR(100),
    isbn VARCHAR(20),
    publisher VARCHAR(200),
    publication_year INTEGER,
    cost_per_unit DECIMAL(10,2),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create book_distributions table  
CREATE TABLE book_distributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_id UUID REFERENCES schools(id) NOT NULL,
    inventory_id UUID REFERENCES book_inventory(id) NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    supervisor_id UUID REFERENCES profiles(id) NOT NULL,
    notes TEXT,
    delivery_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    status VARCHAR(20) DEFAULT 'completed' CHECK (status IN ('planned', 'in_progress', 'completed', 'cancelled')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create distribution_photos table for photo uploads
CREATE TABLE distribution_photos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    distribution_id UUID REFERENCES book_distributions(id) ON DELETE CASCADE NOT NULL,
    photo_url TEXT NOT NULL,
    caption TEXT,
    uploaded_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for performance
CREATE INDEX idx_book_distributions_school_id ON book_distributions(school_id);
CREATE INDEX idx_book_distributions_supervisor_id ON book_distributions(supervisor_id);
CREATE INDEX idx_book_distributions_inventory_id ON book_distributions(inventory_id);
CREATE INDEX idx_book_distributions_delivery_date ON book_distributions(delivery_date);
CREATE INDEX idx_book_distributions_status ON book_distributions(status);
CREATE INDEX idx_book_inventory_title ON book_inventory(book_title);
CREATE INDEX idx_book_inventory_grade_level ON book_inventory(grade_level);
CREATE INDEX idx_book_inventory_subject ON book_inventory(subject);
CREATE INDEX idx_distribution_photos_distribution_id ON distribution_photos(distribution_id);

-- Add updated_at trigger for book_inventory
CREATE TRIGGER update_book_inventory_updated_at
    BEFORE UPDATE ON book_inventory
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add updated_at trigger for book_distributions
CREATE TRIGGER update_book_distributions_updated_at
    BEFORE UPDATE ON book_distributions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE book_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_distributions ENABLE ROW LEVEL SECURITY;
ALTER TABLE distribution_photos ENABLE ROW LEVEL SECURITY;

-- RLS Policies for book_inventory
-- All authenticated users can view inventory
CREATE POLICY "book_inventory_select" ON book_inventory
    FOR SELECT TO authenticated USING (true);

-- Only admins and program officers can manage inventory
CREATE POLICY "book_inventory_insert" ON book_inventory  
    FOR INSERT TO authenticated WITH CHECK (
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'program_officer'))
    );

CREATE POLICY "book_inventory_update" ON book_inventory
    FOR UPDATE TO authenticated USING (
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'program_officer'))
    );

CREATE POLICY "book_inventory_delete" ON book_inventory
    FOR DELETE TO authenticated USING (
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- RLS Policies for book_distributions
-- Role-based access: admins see all, program officers see all, field staff see only their own
CREATE POLICY "book_distributions_select" ON book_distributions
    FOR SELECT TO authenticated USING (
        -- Admins see all distributions
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
        OR
        -- Program officers see all distributions
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'program_officer')
        OR
        -- Field staff see only their own distributions
        supervisor_id = auth.uid()
    );

-- All authenticated users can create distributions (they become the supervisor)
CREATE POLICY "book_distributions_insert" ON book_distributions
    FOR INSERT TO authenticated WITH CHECK (
        supervisor_id = auth.uid()
    );

-- Users can update their own distributions, admins and program officers can update any
CREATE POLICY "book_distributions_update" ON book_distributions  
    FOR UPDATE TO authenticated USING (
        supervisor_id = auth.uid()
        OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'program_officer'))
    );

-- Only admins can delete distributions
CREATE POLICY "book_distributions_delete" ON book_distributions
    FOR DELETE TO authenticated USING (
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- RLS Policies for distribution_photos
-- Same access pattern as distributions
CREATE POLICY "distribution_photos_select" ON distribution_photos
    FOR SELECT TO authenticated USING (
        EXISTS (
            SELECT 1 FROM book_distributions bd 
            WHERE bd.id = distribution_id 
            AND (
                bd.supervisor_id = auth.uid()
                OR EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'program_officer'))
            )
        )
    );

CREATE POLICY "distribution_photos_insert" ON distribution_photos
    FOR INSERT TO authenticated WITH CHECK (
        uploaded_by = auth.uid()
        AND EXISTS (
            SELECT 1 FROM book_distributions bd 
            WHERE bd.id = distribution_id 
            AND bd.supervisor_id = auth.uid()
        )
    );

CREATE POLICY "distribution_photos_delete" ON distribution_photos
    FOR DELETE TO authenticated USING (
        uploaded_by = auth.uid()
        OR EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- Add triggers for activity feed (book_distributions already has trigger in migration 003)
-- Update the existing trigger to include book_title from inventory
CREATE OR REPLACE FUNCTION log_activity()
RETURNS TRIGGER AS $$
BEGIN
    -- Log task creation
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'task_created',
            NEW.created_by,
            'task',
            NEW.id,
            'Created task: ' || NEW.title,
            jsonb_build_object(
                'task_id', NEW.id,
                'title', NEW.title,
                'priority', NEW.priority,
                'assigned_to', NEW.assigned_to,
                'school_id', NEW.school_id
            )
        );
        RETURN NEW;
    END IF;

    -- Log task updates
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' THEN
        -- Only log if status changed
        IF OLD.status != NEW.status THEN
            INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
            VALUES (
                CASE 
                    WHEN NEW.status = 'completed' THEN 'task_completed'
                    ELSE 'task_updated'
                END,
                auth.uid(),
                'task',
                NEW.id,
                CASE 
                    WHEN NEW.status = 'completed' THEN 'Completed task: ' || NEW.title
                    ELSE 'Updated task: ' || NEW.title || ' (Status: ' || NEW.status || ')'
                END,
                jsonb_build_object(
                    'task_id', NEW.id,
                    'title', NEW.title,
                    'old_status', OLD.status,
                    'new_status', NEW.status,
                    'assigned_to', NEW.assigned_to,
                    'school_id', NEW.school_id
                )
            );
        END IF;
        RETURN NEW;
    END IF;

    -- Log book distribution with book title from inventory
    IF TG_TABLE_NAME = 'book_distributions' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'distribution_logged',
            NEW.supervisor_id,
            'distribution',
            NEW.id,
            'Logged book distribution: ' || NEW.quantity || ' books',
            jsonb_build_object(
                'distribution_id', NEW.id,
                'school_id', NEW.school_id,
                'quantity', NEW.quantity,
                'inventory_id', NEW.inventory_id
            )
        );
        RETURN NEW;
    END IF;

    -- Log school additions
    IF TG_TABLE_NAME = 'schools' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'school_added',
            NEW.created_by,
            'school',
            NEW.id,
            'Added school: ' || NEW.name,
            jsonb_build_object(
                'school_id', NEW.id,
                'name', NEW.name,
                'school_type', NEW.school_type,
                'student_count', NEW.student_count
            )
        );
        RETURN NEW;
    END IF;

    -- Log task comments
    IF TG_TABLE_NAME = 'task_comments' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'comment_added',
            NEW.user_id,
            'comment',
            NEW.id,
            'Added comment to task',
            jsonb_build_object(
                'comment_id', NEW.id,
                'task_id', NEW.task_id,
                'comment_preview', LEFT(NEW.comment, 100)
            )
        );
        RETURN NEW;
    END IF;

    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for book_distributions (if not already exists)
DROP TRIGGER IF EXISTS log_book_distribution_activity ON book_distributions;
CREATE TRIGGER log_book_distribution_activity
    AFTER INSERT ON book_distributions
    FOR EACH ROW
    EXECUTE FUNCTION log_activity();

-- Create trigger for distribution_photos
CREATE TRIGGER log_distribution_photo_activity
    AFTER INSERT ON distribution_photos
    FOR EACH ROW
    EXECUTE FUNCTION log_activity();
