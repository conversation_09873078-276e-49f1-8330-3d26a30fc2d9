-- Impact Measurement RPC Functions
-- This migration creates functions for impact analytics and reporting

-- Function to get student learning outcomes summary
CREATE OR REPLACE FUNCTION get_student_learning_outcomes(
    p_school_id UUID DEFAULT NULL,
    p_academic_year VARCHAR DEFAULT NULL,
    p_subject subject_type DEFAULT NULL
)
RETURNS TABLE (
    school_id UUID,
    school_name VARCHAR,
    subject subject_type,
    total_students BIGINT,
    avg_pre_score DECIMAL,
    avg_post_score DECIMAL,
    avg_improvement DECIMAL,
    students_improved BIGINT,
    improvement_rate DECIMAL
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id as school_id,
        s.name as school_name,
        sa.subject,
        COUNT(DISTINCT sa.student_id) as total_students,
        ROUND(AVG(sa.pre_assessment_score), 2) as avg_pre_score,
        ROUND(AVG(sa.post_assessment_score), 2) as avg_post_score,
        ROUND(AVG(sa.improvement_percentage), 2) as avg_improvement,
        COUNT(CASE WHEN sa.improvement_percentage > 0 THEN 1 END) as students_improved,
        ROUND(
            (COUNT(CASE WHEN sa.improvement_percentage > 0 THEN 1 END)::DECIMAL / 
             COUNT(DISTINCT sa.student_id)::DECIMAL) * 100, 2
        ) as improvement_rate
    FROM student_assessments sa
    JOIN schools s ON sa.school_id = s.id
    WHERE 
        (p_school_id IS NULL OR sa.school_id = p_school_id) AND
        (p_academic_year IS NULL OR sa.academic_year = p_academic_year) AND
        (p_subject IS NULL OR sa.subject = p_subject) AND
        sa.pre_assessment_score IS NOT NULL AND
        sa.post_assessment_score IS NOT NULL
    GROUP BY s.id, s.name, sa.subject
    ORDER BY s.name, sa.subject;
END;
$$;

-- Function to get school performance metrics
CREATE OR REPLACE FUNCTION get_school_performance_metrics(
    p_school_id UUID DEFAULT NULL,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    school_id UUID,
    school_name VARCHAR,
    avg_attendance_rate DECIMAL,
    total_improvements INTEGER,
    avg_teacher_student_ratio DECIMAL,
    infrastructure_score INTEGER,
    performance_trend VARCHAR
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id as school_id,
        s.name as school_name,
        ROUND(AVG(sar.attendance_rate), 2) as avg_attendance_rate,
        COUNT(sii.id)::INTEGER as total_improvements,
        ROUND(AVG(tsr.ratio_value), 2) as avg_teacher_student_ratio,
        CASE 
            WHEN COUNT(sii.id) >= 5 THEN 5
            WHEN COUNT(sii.id) >= 3 THEN 4
            WHEN COUNT(sii.id) >= 2 THEN 3
            WHEN COUNT(sii.id) >= 1 THEN 2
            ELSE 1
        END as infrastructure_score,
        CASE 
            WHEN AVG(sar.attendance_rate) > 85 AND COUNT(sii.id) >= 3 THEN 'Excellent'
            WHEN AVG(sar.attendance_rate) > 75 AND COUNT(sii.id) >= 2 THEN 'Good'
            WHEN AVG(sar.attendance_rate) > 65 THEN 'Fair'
            ELSE 'Needs Improvement'
        END as performance_trend
    FROM schools s
    LEFT JOIN school_attendance_records sar ON s.id = sar.school_id
    LEFT JOIN school_infrastructure_improvements sii ON s.id = sii.school_id
    LEFT JOIN teacher_student_ratios tsr ON s.id = tsr.school_id
    WHERE 
        (p_school_id IS NULL OR s.id = p_school_id) AND
        (p_start_date IS NULL OR sar.record_date >= p_start_date) AND
        (p_end_date IS NULL OR sar.record_date <= p_end_date)
    GROUP BY s.id, s.name
    ORDER BY s.name;
END;
$$;

-- Function to get teacher training effectiveness
CREATE OR REPLACE FUNCTION get_teacher_training_effectiveness(
    p_school_id UUID DEFAULT NULL,
    p_training_type training_type DEFAULT NULL,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    training_program_id UUID,
    program_name VARCHAR,
    training_type training_type,
    total_participants BIGINT,
    completion_rate DECIMAL,
    avg_improvement_score DECIMAL,
    certification_rate DECIMAL,
    avg_satisfaction_rating DECIMAL,
    effectiveness_score INTEGER
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        ttp.id as training_program_id,
        ttp.program_name,
        ttp.training_type,
        COUNT(ttpa.id) as total_participants,
        ROUND(
            (COUNT(CASE WHEN ttpa.attendance_percentage >= 80 THEN 1 END)::DECIMAL / 
             COUNT(ttpa.id)::DECIMAL) * 100, 2
        ) as completion_rate,
        ROUND(AVG(ttpa.improvement_score), 2) as avg_improvement_score,
        ROUND(
            (COUNT(CASE WHEN ttpa.certification_received = true THEN 1 END)::DECIMAL / 
             COUNT(ttpa.id)::DECIMAL) * 100, 2
        ) as certification_rate,
        ROUND(
            AVG(CASE 
                WHEN ttpa.feedback_rating = 'very_satisfied' THEN 5
                WHEN ttpa.feedback_rating = 'satisfied' THEN 4
                WHEN ttpa.feedback_rating = 'neutral' THEN 3
                WHEN ttpa.feedback_rating = 'dissatisfied' THEN 2
                WHEN ttpa.feedback_rating = 'very_dissatisfied' THEN 1
                ELSE NULL
            END), 2
        ) as avg_satisfaction_rating,
        CASE 
            WHEN AVG(ttpa.improvement_score) >= 80 AND 
                 COUNT(CASE WHEN ttpa.certification_received = true THEN 1 END)::DECIMAL / COUNT(ttpa.id)::DECIMAL >= 0.8 
            THEN 5
            WHEN AVG(ttpa.improvement_score) >= 70 THEN 4
            WHEN AVG(ttpa.improvement_score) >= 60 THEN 3
            WHEN AVG(ttpa.improvement_score) >= 50 THEN 2
            ELSE 1
        END as effectiveness_score
    FROM teacher_training_programs ttp
    LEFT JOIN teacher_training_participation ttpa ON ttp.id = ttpa.training_program_id
    WHERE 
        (p_school_id IS NULL OR ttpa.school_id = p_school_id) AND
        (p_training_type IS NULL OR ttp.training_type = p_training_type) AND
        (p_start_date IS NULL OR ttp.start_date >= p_start_date) AND
        (p_end_date IS NULL OR ttp.end_date <= p_end_date)
    GROUP BY ttp.id, ttp.program_name, ttp.training_type
    HAVING COUNT(ttpa.id) > 0
    ORDER BY ttp.program_name;
END;
$$;

-- Function to get beneficiary feedback summary
CREATE OR REPLACE FUNCTION get_beneficiary_feedback_summary(
    p_school_id UUID DEFAULT NULL,
    p_feedback_type feedback_type DEFAULT NULL,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    feedback_type feedback_type,
    total_responses BIGINT,
    avg_satisfaction_score DECIMAL,
    satisfaction_distribution JSONB,
    common_themes JSONB
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sr.respondent_type as feedback_type,
        COUNT(sr.id) as total_responses,
        ROUND(
            AVG(CASE 
                WHEN sr.overall_satisfaction = 'very_satisfied' THEN 5
                WHEN sr.overall_satisfaction = 'satisfied' THEN 4
                WHEN sr.overall_satisfaction = 'neutral' THEN 3
                WHEN sr.overall_satisfaction = 'dissatisfied' THEN 2
                WHEN sr.overall_satisfaction = 'very_dissatisfied' THEN 1
                ELSE NULL
            END), 2
        ) as avg_satisfaction_score,
        jsonb_build_object(
            'very_satisfied', COUNT(CASE WHEN sr.overall_satisfaction = 'very_satisfied' THEN 1 END),
            'satisfied', COUNT(CASE WHEN sr.overall_satisfaction = 'satisfied' THEN 1 END),
            'neutral', COUNT(CASE WHEN sr.overall_satisfaction = 'neutral' THEN 1 END),
            'dissatisfied', COUNT(CASE WHEN sr.overall_satisfaction = 'dissatisfied' THEN 1 END),
            'very_dissatisfied', COUNT(CASE WHEN sr.overall_satisfaction = 'very_dissatisfied' THEN 1 END)
        ) as satisfaction_distribution,
        jsonb_agg(DISTINCT sr.additional_comments) FILTER (WHERE sr.additional_comments IS NOT NULL) as common_themes
    FROM survey_responses sr
    WHERE 
        (p_school_id IS NULL OR sr.school_id = p_school_id) AND
        (p_feedback_type IS NULL OR sr.respondent_type = p_feedback_type) AND
        (p_start_date IS NULL OR sr.response_date >= p_start_date) AND
        (p_end_date IS NULL OR sr.response_date <= p_end_date)
    GROUP BY sr.respondent_type
    ORDER BY sr.respondent_type;
END;
$$;

-- Function to get longitudinal impact trends
CREATE OR REPLACE FUNCTION get_longitudinal_impact_trends(
    p_cohort_id UUID DEFAULT NULL,
    p_school_id UUID DEFAULT NULL,
    p_years INTEGER DEFAULT 3
)
RETURNS TABLE (
    cohort_name VARCHAR,
    tracking_year INTEGER,
    total_students BIGINT,
    avg_attendance_rate DECIMAL,
    avg_academic_performance DECIMAL,
    retention_rate DECIMAL,
    at_risk_students BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        sc.cohort_name,
        lst.tracking_year,
        COUNT(lst.student_id) as total_students,
        ROUND(AVG(lst.attendance_rate), 2) as avg_attendance_rate,
        ROUND(AVG(lst.academic_performance_score), 2) as avg_academic_performance,
        ROUND(
            (COUNT(lst.student_id)::DECIMAL / sc.initial_student_count::DECIMAL) * 100, 2
        ) as retention_rate,
        COUNT(CASE WHEN jsonb_array_length(lst.risk_factors) > 0 THEN 1 END) as at_risk_students
    FROM student_cohorts sc
    LEFT JOIN longitudinal_student_tracking lst ON sc.id = lst.cohort_id
    WHERE
        (p_cohort_id IS NULL OR sc.id = p_cohort_id) AND
        (p_school_id IS NULL OR sc.school_id = p_school_id) AND
        lst.tracking_year >= (EXTRACT(YEAR FROM CURRENT_DATE) - p_years)
    GROUP BY sc.id, sc.cohort_name, lst.tracking_year
    ORDER BY sc.cohort_name, lst.tracking_year;
END;
$$;

-- Function to get impact indicators dashboard
CREATE OR REPLACE FUNCTION get_impact_indicators_dashboard(
    p_school_id UUID DEFAULT NULL,
    p_category VARCHAR DEFAULT NULL
)
RETURNS TABLE (
    indicator_id UUID,
    indicator_name VARCHAR,
    category VARCHAR,
    current_value DECIMAL,
    target_value DECIMAL,
    baseline_value DECIMAL,
    progress_percentage DECIMAL,
    trend VARCHAR,
    last_measured DATE,
    status VARCHAR
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        ii.id as indicator_id,
        ii.indicator_name,
        ii.indicator_category as category,
        ii.current_value,
        ii.target_value,
        ii.baseline_value,
        CASE
            WHEN ii.target_value > ii.baseline_value THEN
                ROUND(((ii.current_value - ii.baseline_value) / (ii.target_value - ii.baseline_value)) * 100, 2)
            WHEN ii.target_value < ii.baseline_value THEN
                ROUND(((ii.baseline_value - ii.current_value) / (ii.baseline_value - ii.target_value)) * 100, 2)
            ELSE 0
        END as progress_percentage,
        CASE
            WHEN ii.current_value > ii.baseline_value THEN 'Improving'
            WHEN ii.current_value < ii.baseline_value THEN 'Declining'
            ELSE 'Stable'
        END as trend,
        ii.last_measured_date as last_measured,
        CASE
            WHEN ii.current_value >= ii.target_value THEN 'Target Achieved'
            WHEN ii.current_value >= (ii.baseline_value + (ii.target_value - ii.baseline_value) * 0.75) THEN 'On Track'
            WHEN ii.current_value >= (ii.baseline_value + (ii.target_value - ii.baseline_value) * 0.5) THEN 'Moderate Progress'
            ELSE 'Needs Attention'
        END as status
    FROM impact_indicators ii
    WHERE
        ii.is_active = true AND
        (p_school_id IS NULL OR ii.school_id = p_school_id) AND
        (p_category IS NULL OR ii.indicator_category = p_category)
    ORDER BY ii.indicator_category, ii.indicator_name;
END;
$$;
