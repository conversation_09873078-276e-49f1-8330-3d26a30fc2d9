import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/types/database.types';
import { FieldReportFormData } from '@/utils/fieldReportValidation';

type FieldReport = Database['public']['Tables']['field_reports']['Row'] & {
  staff_name: string;
  school_name: string;
  attendance_check_in_time: string;
  attendance_check_out_time: string;
};

interface UseFieldReportsOptions {
  staffId?: string;
  schoolId?: string;
  dateFrom?: string;
  dateTo?: string;
  limit?: number;
}

export const useFieldReports = (options: UseFieldReportsOptions = {}) => {
  return useQuery({
    queryKey: ['field-reports', options],
    queryFn: async (): Promise<FieldReport[]> => {
      let query = supabase
        .from('enhanced_field_reports')
        .select('*')
        .order('report_date', { ascending: false });

      // Apply filters
      if (options.staffId) {
        query = query.eq('staff_id', options.staffId);
      }

      if (options.schoolId) {
        query = query.eq('school_id', options.schoolId);
      }

      if (options.dateFrom) {
        query = query.gte('report_date', options.dateFrom);
      }

      if (options.dateTo) {
        query = query.lte('report_date', options.dateTo);
      }

      if (options.limit) {
        query = query.limit(options.limit);
      }

      const { data, error } = await query;

      if (error) {
        throw new Error(`Failed to fetch field reports: ${error.message}`);
      }

      // Enhanced view already includes staff_name, school_name, and attendance times
      return (data || []).map(report => ({
        ...report,
        // Ensure compatibility with existing interface
        attendance_check_in_time: report.check_in_time || '',
        attendance_check_out_time: report.check_out_time || '',
      }));
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useFieldReport = (reportId: string) => {
  return useQuery({
    queryKey: ['field-report', reportId],
    queryFn: async (): Promise<FieldReport> => {
      const { data, error } = await supabase
        .from('enhanced_field_reports')
        .select('*')
        .eq('id', reportId)
        .single();

      if (error) {
        throw new Error(`Failed to fetch field report: ${error.message}`);
      }

      if (!data) {
        throw new Error('Field report not found');
      }

      // Enhanced view already includes staff_name, school_name, and attendance times
      return {
        ...data,
        // Ensure compatibility with existing interface
        attendance_check_in_time: data.check_in_time || '',
        attendance_check_out_time: data.check_out_time || '',
      };
    },
    enabled: !!reportId,
  });
};

// Hook for updating a field report
export const useUpdateFieldReport = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ reportId, data }: { reportId: string; data: FieldReportFormData }) => {
      const { error } = await supabase
        .from('field_reports')
        .update({
          activity_type: data.activity_type,
          round_table_sessions_count: data.round_table_sessions_count,
          total_students_attended: data.total_students_attended,
          students_per_session: data.students_per_session,
          activities_conducted: data.activities_conducted,
          topics_covered: data.topics_covered,
          challenges_encountered: data.challenges_encountered,
          wins_achieved: data.wins_achieved,
          general_observations: data.general_observations,
          lessons_learned: data.lessons_learned,
          follow_up_required: data.follow_up_required,
          follow_up_actions: data.follow_up_actions,
          introduction: data.introduction,
          recommendations: data.recommendations,
          updated_at: new Date().toISOString(),
        })
        .eq('id', reportId);

      if (error) {
        throw new Error(`Failed to update field report: ${error.message}`);
      }

      return { success: true };
    },
    onSuccess: () => {
      // Invalidate and refetch field reports
      queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      queryClient.invalidateQueries({ queryKey: ['field-report'] });
    },
  });
};

// Hook for deleting a field report
export const useDeleteFieldReport = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (reportId: string) => {
      const { error } = await supabase
        .from('field_reports')
        .delete()
        .eq('id', reportId);

      if (error) {
        throw new Error(`Failed to delete field report: ${error.message}`);
      }

      return { success: true };
    },
    onSuccess: () => {
      // Invalidate and refetch field reports
      queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      queryClient.invalidateQueries({ queryKey: ['field-report'] });
    },
  });
};
