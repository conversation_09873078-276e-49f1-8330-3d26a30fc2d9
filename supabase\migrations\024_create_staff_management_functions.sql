-- Staff Management Functions
-- This migration creates the database functions for staff management operations

-- Function to create a user invitation
CREATE OR REPLACE FUNCTION create_user_invitation(
    p_email VARCHAR(255),
    p_name VARCHAR(255),
    p_role user_role DEFAULT 'field_staff',
    p_division_id UUID DEFAULT NULL,
    p_phone VARCHAR(20) DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_invitation_id UUID;
    v_invitation_token VARCHAR(255);
    v_expires_at TIMESTAMP WITH TIME ZONE;
BEGIN
    -- Check if user has admin role
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins can create user invitations.';
    END IF;

    -- Check if email already exists in profiles or invitations
    IF EXISTS (SELECT 1 FROM profiles WHERE name = p_email) THEN
        RAISE EXCEPTION 'User with email % already exists.', p_email;
    END IF;

    IF EXISTS (SELECT 1 FROM user_invitations WHERE email = p_email AND status IN ('pending', 'sent')) THEN
        RAISE EXCEPTION 'Pending invitation already exists for email %.', p_email;
    END IF;

    -- Generate invitation token and expiry
    v_invitation_token := encode(gen_random_bytes(32), 'hex');
    v_expires_at := NOW() + INTERVAL '7 days';

    -- Insert invitation
    INSERT INTO user_invitations (
        email, name, role, division_id, phone, invited_by, 
        invitation_token, expires_at
    ) VALUES (
        p_email, p_name, p_role, p_division_id, p_phone, auth.uid(),
        v_invitation_token, v_expires_at
    ) RETURNING id INTO v_invitation_id;

    -- Log the action
    INSERT INTO staff_audit_log (action, target_email, performed_by, details)
    VALUES ('invitation_sent', p_email, auth.uid(), jsonb_build_object(
        'name', p_name,
        'role', p_role,
        'division_id', p_division_id,
        'invitation_id', v_invitation_id
    ));

    RETURN v_invitation_id;
END;
$$;

-- Function to bulk create user invitations
CREATE OR REPLACE FUNCTION bulk_create_invitations(
    p_users JSONB
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_operation_id UUID;
    v_user JSONB;
    v_invitation_id UUID;
    v_total_count INTEGER;
    v_success_count INTEGER := 0;
    v_error_count INTEGER := 0;
    v_errors JSONB := '[]'::jsonb;
    v_error_detail JSONB;
BEGIN
    -- Check if user has admin role
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins can bulk create invitations.';
    END IF;

    -- Get total count
    v_total_count := jsonb_array_length(p_users);

    -- Create bulk operation record
    INSERT INTO bulk_operations (
        operation_type, initiated_by, total_records
    ) VALUES (
        'user_invitation', auth.uid(), v_total_count
    ) RETURNING id INTO v_operation_id;

    -- Log bulk operation start
    INSERT INTO staff_audit_log (action, performed_by, details)
    VALUES ('bulk_creation_started', auth.uid(), jsonb_build_object(
        'operation_id', v_operation_id,
        'total_records', v_total_count
    ));

    -- Process each user
    FOR i IN 0..v_total_count-1 LOOP
        v_user := p_users->i;
        
        BEGIN
            -- Create invitation
            SELECT create_user_invitation(
                (v_user->>'email')::VARCHAR(255),
                (v_user->>'name')::VARCHAR(255),
                COALESCE((v_user->>'role')::user_role, 'field_staff'),
                CASE WHEN v_user->>'division_id' IS NOT NULL 
                     THEN (v_user->>'division_id')::UUID 
                     ELSE NULL END,
                (v_user->>'phone')::VARCHAR(20)
            ) INTO v_invitation_id;
            
            v_success_count := v_success_count + 1;
            
        EXCEPTION WHEN OTHERS THEN
            v_error_count := v_error_count + 1;
            v_error_detail := jsonb_build_object(
                'email', v_user->>'email',
                'error', SQLERRM
            );
            v_errors := v_errors || v_error_detail;
        END;
    END LOOP;

    -- Update bulk operation
    UPDATE bulk_operations 
    SET 
        successful_records = v_success_count,
        failed_records = v_error_count,
        status = 'completed',
        error_summary = v_errors,
        completed_at = NOW()
    WHERE id = v_operation_id;

    -- Log completion
    INSERT INTO staff_audit_log (action, performed_by, details)
    VALUES ('bulk_creation_completed', auth.uid(), jsonb_build_object(
        'operation_id', v_operation_id,
        'successful_records', v_success_count,
        'failed_records', v_error_count
    ));

    RETURN v_operation_id;
END;
$$;

-- Function to get all staff members with enhanced details
CREATE OR REPLACE FUNCTION get_all_staff()
RETURNS TABLE (
    id UUID,
    name TEXT,
    email TEXT,
    role user_role,
    division_id UUID,
    division_name TEXT,
    phone TEXT,
    country TEXT,
    is_active BOOLEAN,
    requires_password_change BOOLEAN,
    last_password_change TIMESTAMP WITH TIME ZONE,
    invitation_accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has admin role
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE profiles.id = auth.uid() AND profiles.role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins can view all staff.';
    END IF;

    RETURN QUERY
    SELECT
        p.id,
        p.name,
        COALESCE(au.email, p.name) as email,
        p.role,
        p.division_id,
        CASE
            WHEN ad.district IS NOT NULL AND ad.sub_county IS NOT NULL
            THEN CONCAT(ad.district, ', ', ad.sub_county)
            WHEN ad.district IS NOT NULL
            THEN ad.district
            ELSE NULL
        END as division_name,
        p.phone,
        p.country,
        COALESCE(p.is_active, true) as is_active,
        COALESCE(p.requires_password_change, false) as requires_password_change,
        p.last_password_change,
        p.invitation_accepted_at,
        p.created_at
    FROM profiles p
    LEFT JOIN administrative_divisions ad ON p.division_id = ad.id
    LEFT JOIN auth.users au ON p.id = au.id
    ORDER BY p.created_at DESC;
END;
$$;

-- Function to update user role (admin only)
CREATE OR REPLACE FUNCTION update_user_role(
    p_user_id UUID,
    p_new_role user_role
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_old_role user_role;
    v_user_email VARCHAR(255);
BEGIN
    -- Check if user has admin role
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins can update user roles.';
    END IF;

    -- Get current role and email
    SELECT role, name INTO v_old_role, v_user_email
    FROM profiles 
    WHERE id = p_user_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found.';
    END IF;

    -- Update role
    UPDATE profiles 
    SET role = p_new_role 
    WHERE id = p_user_id;

    -- Log the action
    INSERT INTO staff_audit_log (action, target_user_id, target_email, performed_by, details)
    VALUES ('role_changed', p_user_id, v_user_email, auth.uid(), jsonb_build_object(
        'old_role', v_old_role,
        'new_role', p_new_role
    ));

    RETURN TRUE;
END;
$$;

-- Function to deactivate/activate user
CREATE OR REPLACE FUNCTION toggle_user_status(
    p_user_id UUID,
    p_is_active BOOLEAN
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_user_email VARCHAR(255);
    v_action audit_action;
BEGIN
    -- Check if user has admin role
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins can change user status.';
    END IF;

    -- Get user email
    SELECT name INTO v_user_email
    FROM profiles 
    WHERE id = p_user_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'User not found.';
    END IF;

    -- Update status
    UPDATE profiles 
    SET is_active = p_is_active 
    WHERE id = p_user_id;

    -- Determine action for logging
    v_action := CASE WHEN p_is_active THEN 'user_activated' ELSE 'user_deactivated' END;

    -- Log the action
    INSERT INTO staff_audit_log (action, target_user_id, target_email, performed_by, details)
    VALUES (v_action, p_user_id, v_user_email, auth.uid(), jsonb_build_object(
        'new_status', p_is_active
    ));

    RETURN TRUE;
END;
$$;
