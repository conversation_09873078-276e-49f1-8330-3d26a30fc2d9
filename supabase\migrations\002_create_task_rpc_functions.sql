-- Task Management RPC Functions
-- Task T1.2: Task Management RPC Functions

-- Function to create a new task
CREATE OR REPLACE FUNCTION create_task(
    p_title VARCHAR,
    p_description TEXT DEFAULT NULL,
    p_priority task_priority DEFAULT 'medium',
    p_due_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_assigned_to UUID DEFAULT NULL,
    p_school_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    task_id UUID;
BEGIN
    -- Check if user has permission to create tasks
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to create tasks';
    END IF;

    -- Insert new task
    INSERT INTO tasks (title, description, priority, due_date, assigned_to, created_by, school_id)
    VALUES (p_title, p_description, p_priority, p_due_date, p_assigned_to, auth.uid(), p_school_id)
    RETURNING id INTO task_id;

    RETURN task_id;
END;
$$;

-- Function to get tasks with filtering
CREATE OR REPLACE FUNCTION get_tasks(
    p_user_id UUID DEFAULT NULL,
    p_status_filter task_status DEFAULT NULL,
    p_assigned_filter UUID DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    description TEXT,
    priority task_priority,
    status task_status,
    due_date TIMESTAMP WITH TIME ZONE,
    assigned_to UUID,
    assigned_to_name VARCHAR,
    created_by UUID,
    created_by_name VARCHAR,
    school_id UUID,
    school_name VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    comment_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.title,
        t.description,
        t.priority,
        t.status,
        t.due_date,
        t.assigned_to,
        p_assigned.name as assigned_to_name,
        t.created_by,
        p_created.name as created_by_name,
        t.school_id,
        s.name as school_name,
        t.created_at,
        t.updated_at,
        COALESCE(comment_counts.count, 0) as comment_count
    FROM tasks t
    LEFT JOIN profiles p_assigned ON t.assigned_to = p_assigned.id
    LEFT JOIN profiles p_created ON t.created_by = p_created.id
    LEFT JOIN schools s ON t.school_id = s.id
    LEFT JOIN (
        SELECT task_id, COUNT(*) as count
        FROM task_comments
        GROUP BY task_id
    ) comment_counts ON t.id = comment_counts.task_id
    WHERE 
        -- Apply RLS: user can see tasks they're assigned to, created, or if they're admin/program_officer
        (t.assigned_to = auth.uid() OR 
         t.created_by = auth.uid() OR
         EXISTS (
             SELECT 1 FROM profiles 
             WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
         ))
        -- Apply filters
        AND (p_user_id IS NULL OR t.assigned_to = p_user_id OR t.created_by = p_user_id)
        AND (p_status_filter IS NULL OR t.status = p_status_filter)
        AND (p_assigned_filter IS NULL OR t.assigned_to = p_assigned_filter)
    ORDER BY 
        CASE t.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        t.due_date ASC NULLS LAST,
        t.created_at DESC;
END;
$$;

-- Function to update task status
CREATE OR REPLACE FUNCTION update_task_status(
    p_task_id UUID,
    p_new_status task_status,
    p_user_id UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    task_exists BOOLEAN;
BEGIN
    -- Check if task exists and user has permission
    SELECT EXISTS(
        SELECT 1 FROM tasks 
        WHERE id = p_task_id AND (
            assigned_to = auth.uid() OR 
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
            )
        )
    ) INTO task_exists;

    IF NOT task_exists THEN
        RAISE EXCEPTION 'Task not found or insufficient permissions';
    END IF;

    -- Update task status
    UPDATE tasks 
    SET status = p_new_status, updated_at = NOW()
    WHERE id = p_task_id;

    RETURN TRUE;
END;
$$;

-- Function to add task comment
CREATE OR REPLACE FUNCTION add_task_comment(
    p_task_id UUID,
    p_comment TEXT,
    p_user_id UUID DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    task_id UUID,
    user_id UUID,
    user_name VARCHAR,
    comment TEXT,
    created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    comment_id UUID;
    task_exists BOOLEAN;
BEGIN
    -- Check if task exists and user has permission
    SELECT EXISTS(
        SELECT 1 FROM tasks 
        WHERE id = p_task_id AND (
            assigned_to = auth.uid() OR 
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM profiles 
                WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
            )
        )
    ) INTO task_exists;

    IF NOT task_exists THEN
        RAISE EXCEPTION 'Task not found or insufficient permissions';
    END IF;

    -- Insert comment
    INSERT INTO task_comments (task_id, user_id, comment)
    VALUES (p_task_id, auth.uid(), p_comment)
    RETURNING task_comments.id INTO comment_id;

    -- Return comment with user info
    RETURN QUERY
    SELECT 
        tc.id,
        tc.task_id,
        tc.user_id,
        p.name as user_name,
        tc.comment,
        tc.created_at
    FROM task_comments tc
    JOIN profiles p ON tc.user_id = p.id
    WHERE tc.id = comment_id;
END;
$$;

-- Function to get task details with comments and attachments
CREATE OR REPLACE FUNCTION get_task_details(p_task_id UUID)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    description TEXT,
    priority task_priority,
    status task_status,
    due_date TIMESTAMP WITH TIME ZONE,
    assigned_to UUID,
    assigned_to_name VARCHAR,
    created_by UUID,
    created_by_name VARCHAR,
    school_id UUID,
    school_name VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    comments JSON,
    attachments JSON
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission to view this task
    IF NOT EXISTS (
        SELECT 1 FROM tasks
        WHERE tasks.id = p_task_id AND (
            assigned_to = auth.uid() OR
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
            )
        )
    ) THEN
        RAISE EXCEPTION 'Task not found or insufficient permissions';
    END IF;

    RETURN QUERY
    SELECT
        t.id,
        t.title,
        t.description,
        t.priority,
        t.status,
        t.due_date,
        t.assigned_to,
        p_assigned.name as assigned_to_name,
        t.created_by,
        p_created.name as created_by_name,
        t.school_id,
        s.name as school_name,
        t.created_at,
        t.updated_at,
        COALESCE(
            (SELECT json_agg(
                json_build_object(
                    'id', tc.id,
                    'comment', tc.comment,
                    'user_name', p_comment.name,
                    'created_at', tc.created_at
                ) ORDER BY tc.created_at DESC
            ) FROM task_comments tc
            JOIN profiles p_comment ON tc.user_id = p_comment.id
            WHERE tc.task_id = t.id),
            '[]'::json
        ) as comments,
        COALESCE(
            (SELECT json_agg(
                json_build_object(
                    'id', ta.id,
                    'file_name', ta.file_name,
                    'file_url', ta.file_url,
                    'file_size', ta.file_size,
                    'uploaded_by_name', p_upload.name,
                    'created_at', ta.created_at
                ) ORDER BY ta.created_at DESC
            ) FROM task_attachments ta
            JOIN profiles p_upload ON ta.uploaded_by = p_upload.id
            WHERE ta.task_id = t.id),
            '[]'::json
        ) as attachments
    FROM tasks t
    LEFT JOIN profiles p_assigned ON t.assigned_to = p_assigned.id
    LEFT JOIN profiles p_created ON t.created_by = p_created.id
    LEFT JOIN schools s ON t.school_id = s.id
    WHERE t.id = p_task_id;
END;
$$;

-- Function to assign task
CREATE OR REPLACE FUNCTION assign_task(
    p_task_id UUID,
    p_assigned_to UUID,
    p_assigned_by UUID DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    task_exists BOOLEAN;
BEGIN
    -- Check if task exists and user has permission
    SELECT EXISTS(
        SELECT 1 FROM tasks
        WHERE id = p_task_id AND (
            created_by = auth.uid() OR
            EXISTS (
                SELECT 1 FROM profiles
                WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
            )
        )
    ) INTO task_exists;

    IF NOT task_exists THEN
        RAISE EXCEPTION 'Task not found or insufficient permissions';
    END IF;

    -- Check if assigned_to user exists
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = p_assigned_to) THEN
        RAISE EXCEPTION 'Assigned user not found';
    END IF;

    -- Update task assignment
    UPDATE tasks
    SET assigned_to = p_assigned_to, updated_at = NOW()
    WHERE id = p_task_id;

    RETURN TRUE;
END;
$$;
