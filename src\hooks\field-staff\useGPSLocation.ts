import { useState, useEffect, useCallback, useRef } from 'react';
import { toast } from 'sonner';
import { FieldOperationError, Error<PERSON>and<PERSON> } from '@/utils/errorHandling';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  speed?: number;
  heading?: number;
}

interface LocationError {
  code: number;
  message: string;
  recoverable: boolean;
  fallbackSuggestion?: string;
}

interface GPSConfig {
  enableHighAccuracy: boolean;
  timeout: number;
  maximumAge: number;
  adaptivePolling: boolean;
  batteryOptimized: boolean;
  movementThreshold: number; // meters
  stationaryTimeout: number; // milliseconds
}

interface MovementState {
  isStationary: boolean;
  lastMovementTime: number;
  lastPosition: LocationData | null;
  movementHistory: LocationData[];
}

interface UseGPSLocationReturn {
  location: LocationData | null;
  error: LocationError | null;
  loading: boolean;
  getCurrentLocation: (config?: Partial<GPSConfig>) => Promise<LocationData>;
  watchLocation: (config?: Partial<GPSConfig>) => void;
  stopWatching: () => void;
  isSupported: boolean;
  movementState: MovementState;
  batteryOptimized: boolean;
  setBatteryOptimized: (enabled: boolean) => void;
}

// Default GPS configuration optimized for field operations with battery efficiency
const DEFAULT_GPS_CONFIG: GPSConfig = {
  enableHighAccuracy: false, // Disabled by default for battery optimization
  timeout: 20000, // Increased timeout for battery-optimized mode
  maximumAge: 120000, // 2 minutes - longer cache for battery savings
  adaptivePolling: true,
  batteryOptimized: true,
  movementThreshold: 15, // 15 meters - larger threshold to reduce GPS polling
  stationaryTimeout: 600000, // 10 minutes - longer timeout for better battery life
};

export const useGPSLocation = (): UseGPSLocationReturn => {
  const [location, setLocation] = useState<LocationData | null>(null);
  const [error, setError] = useState<LocationError | null>(null);
  const [loading, setLoading] = useState(false);
  // watchId removed - continuous tracking no longer supported
  const [batteryOptimized, setBatteryOptimized] = useState(true);
  const [movementState, setMovementState] = useState<MovementState>({
    isStationary: false,
    lastMovementTime: Date.now(),
    lastPosition: null,
    movementHistory: [],
  });

  const configRef = useRef<GPSConfig>(DEFAULT_GPS_CONFIG);
  const stationaryTimerRef = useRef<NodeJS.Timeout | null>(null);
  const adaptiveIntervalRef = useRef<NodeJS.Timeout | null>(null);

  const isSupported = 'geolocation' in navigator;

  // Enhanced movement detection
  const detectMovement = useCallback((newLocation: LocationData, lastLocation: LocationData | null): boolean => {
    if (!lastLocation) return true;

    const distance = calculateDistance(
      lastLocation.latitude,
      lastLocation.longitude,
      newLocation.latitude,
      newLocation.longitude
    );

    return distance > configRef.current.movementThreshold;
  }, []);

  // Update movement state and optimize polling
  const updateMovementState = useCallback((newLocation: LocationData) => {
    setMovementState(prev => {
      const hasMovement = detectMovement(newLocation, prev.lastPosition);
      const now = Date.now();

      // Update movement history (keep last 5 positions)
      const newHistory = [...prev.movementHistory, newLocation].slice(-5);

      // Determine if stationary
      const isStationary = !hasMovement && (now - prev.lastMovementTime) > configRef.current.stationaryTimeout;

      return {
        isStationary,
        lastMovementTime: hasMovement ? now : prev.lastMovementTime,
        lastPosition: newLocation,
        movementHistory: newHistory,
      };
    });
  }, [detectMovement]);

  const handleSuccess = useCallback((position: GeolocationPosition) => {
    const locationData: LocationData = {
      latitude: position.coords.latitude,
      longitude: position.coords.longitude,
      accuracy: position.coords.accuracy,
      timestamp: position.timestamp,
      speed: position.coords.speed || undefined,
      heading: position.coords.heading || undefined,
    };

    // Update movement state for adaptive polling
    updateMovementState(locationData);

    setLocation(locationData);
    setError(null);
    setLoading(false);
  }, [updateMovementState]);

  const handleError = useCallback(async (err: GeolocationPositionError) => {
    const fieldError = FieldOperationError.fromGPSError(err);

    const errorData: LocationError = {
      code: err.code,
      message: fieldError.fieldError.message,
      recoverable: fieldError.fieldError.recoverable,
      fallbackSuggestion: fieldError.recovery?.userPrompt || getFallbackSuggestion(err.code),
    };

    setError(errorData);
    setLocation(null);
    setLoading(false);

    // Use enhanced error handling
    await ErrorHandler.handle(fieldError);
  }, []);

  const getErrorMessage = (code: number): string => {
    switch (code) {
      case 1:
        return 'Location access denied by user';
      case 2:
        return 'Location information unavailable';
      case 3:
        return 'Location request timed out';
      default:
        return 'Unknown location error';
    }
  };

  const getFallbackSuggestion = (code: number): string => {
    switch (code) {
      case 1:
        return 'Please enable location permissions in your browser settings and refresh the page.';
      case 2:
        return 'Try moving to an area with better GPS signal or use manual location entry.';
      case 3:
        return 'Check your internet connection and try again. Consider using low accuracy mode.';
      default:
        return 'Please try again or contact support if the problem persists.';
    }
  };

  // Adaptive GPS configuration based on movement and battery state
  const getAdaptiveConfig = useCallback((baseConfig: Partial<GPSConfig> = {}): PositionOptions => {
    const config = { ...configRef.current, ...baseConfig };

    // Adjust accuracy and timeout based on movement state and battery optimization
    let enableHighAccuracy = config.enableHighAccuracy;
    let timeout = config.timeout;
    let maximumAge = config.maximumAge;

    if (config.batteryOptimized && batteryOptimized) {
      if (movementState.isStationary) {
        // Aggressive battery optimization when stationary
        enableHighAccuracy = false;
        timeout = 45000; // 45 seconds - longer timeout for battery savings
        maximumAge = 600000; // 10 minutes - much longer cache for stationary users
      } else {
        // Moderate battery optimization when moving
        enableHighAccuracy = false; // Keep disabled for battery optimization
        timeout = 30000; // 30 seconds - increased from 20s
        maximumAge = 180000; // 3 minutes - increased from 2 minutes
      }
    }

    return {
      enableHighAccuracy,
      timeout,
      maximumAge,
    };
  }, [batteryOptimized, movementState.isStationary]);

  // Enhanced getCurrentLocation with retry logic and fallback
  const getCurrentLocation = useCallback((config?: Partial<GPSConfig>): Promise<LocationData> => {
    return new Promise((resolve, reject) => {
      if (!isSupported) {
        const error: LocationError = {
          code: 0,
          message: 'Geolocation not supported',
          recoverable: false,
          fallbackSuggestion: 'Use manual location entry or try a different device.'
        };
        setError(error);
        reject(error);
        return;
      }

      setLoading(true);
      setError(null);

      const options = getAdaptiveConfig(config);
      let retryCount = 0;
      const maxRetries = 3;

      const attemptLocation = () => {
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const locationData: LocationData = {
              latitude: position.coords.latitude,
              longitude: position.coords.longitude,
              accuracy: position.coords.accuracy,
              timestamp: position.timestamp,
              speed: position.coords.speed || undefined,
              heading: position.coords.heading || undefined,
            };

            handleSuccess(position);
            resolve(locationData);
          },
          (err) => {
            retryCount++;

            // Retry with lower accuracy if high accuracy fails
            if (retryCount < maxRetries && err.code === 3) {
              console.log(`GPS attempt ${retryCount} failed, retrying with lower accuracy...`);
              setTimeout(() => {
                const fallbackOptions: PositionOptions = {
                  enableHighAccuracy: false,
                  timeout: 30000,
                  maximumAge: 120000,
                };
                navigator.geolocation.getCurrentPosition(
                  (position) => {
                    handleSuccess(position);
                    resolve({
                      latitude: position.coords.latitude,
                      longitude: position.coords.longitude,
                      accuracy: position.coords.accuracy,
                      timestamp: position.timestamp,
                      speed: position.coords.speed || undefined,
                      heading: position.coords.heading || undefined,
                    });
                  },
                  (finalErr) => {
                    handleError(finalErr);
                    reject({
                      code: finalErr.code,
                      message: getErrorMessage(finalErr.code),
                      recoverable: finalErr.code !== 1,
                      fallbackSuggestion: getFallbackSuggestion(finalErr.code),
                    });
                  },
                  fallbackOptions
                );
              }, 1000 * retryCount); // Exponential backoff
            } else {
              handleError(err);
              reject({
                code: err.code,
                message: getErrorMessage(err.code),
                recoverable: err.code !== 1,
                fallbackSuggestion: getFallbackSuggestion(err.code),
              });
            }
          },
          options
        );
      };

      attemptLocation();
    });
  }, [isSupported, handleSuccess, handleError, getAdaptiveConfig]);

  // Simplified location watching - DEPRECATED
  // Continuous GPS tracking has been removed to improve battery life and support
  // real-world field staff workflows where staff may leave school premises
  const watchLocation = useCallback((config?: Partial<GPSConfig>) => {
    console.warn('watchLocation is deprecated. Use getCurrentLocation for check-in verification only.');

    // For backward compatibility, just get current location once
    getCurrentLocation(config).catch(console.error);
  }, [getCurrentLocation]);

  const stopWatching = useCallback(() => {
    console.warn('stopWatching is deprecated. Continuous GPS tracking has been removed.');

    // Clean up any remaining timers for backward compatibility
    if (adaptiveIntervalRef.current) {
      clearInterval(adaptiveIntervalRef.current);
      adaptiveIntervalRef.current = null;
    }

    if (stationaryTimerRef.current) {
      clearTimeout(stationaryTimerRef.current);
      stationaryTimerRef.current = null;
    }
  }, []);

  // Cleanup on unmount - simplified since continuous tracking is removed
  useEffect(() => {
    return () => {
      if (adaptiveIntervalRef.current) {
        clearInterval(adaptiveIntervalRef.current);
      }
      if (stationaryTimerRef.current) {
        clearTimeout(stationaryTimerRef.current);
      }
    };
  }, []);

  // Monitor battery status if available
  useEffect(() => {
    const updateBatteryOptimization = async () => {
      if ('getBattery' in navigator) {
        try {
          const battery = await (navigator as Navigator & { getBattery(): Promise<{ level: number; charging: boolean }> }).getBattery();
          const shouldOptimize = battery.level < 0.2 || !battery.charging;
          setBatteryOptimized(shouldOptimize);
        } catch (error) {
          console.log('Battery API not available');
        }
      }
    };

    updateBatteryOptimization();
  }, []);

  return {
    location,
    error,
    loading,
    getCurrentLocation,
    watchLocation,
    stopWatching,
    isSupported,
    movementState,
    batteryOptimized,
    setBatteryOptimized,
  };
};

// Utility function to calculate distance between two GPS coordinates
export const calculateDistance = (
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number => {
  const R = 6371e3; // Earth's radius in meters
  const φ1 = (lat1 * Math.PI) / 180;
  const φ2 = (lat2 * Math.PI) / 180;
  const Δφ = ((lat2 - lat1) * Math.PI) / 180;
  const Δλ = ((lon2 - lon1) * Math.PI) / 180;

  const a =
    Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c; // Distance in meters
};

// Utility function to format location for display
export const formatLocation = (location: LocationData): string => {
  return `${location.latitude.toFixed(6)}, ${location.longitude.toFixed(6)}`;
};

// Utility function to get device info
export const getDeviceInfo = () => {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    timestamp: Date.now(),
  };
};

// Utility function to get network info
export const getNetworkInfo = () => {
  const connection = (navigator as Record<string, unknown>).connection || (navigator as Record<string, unknown>).mozConnection || (navigator as Record<string, unknown>).webkitConnection;
  
  return {
    effectiveType: connection?.effectiveType || 'unknown',
    downlink: connection?.downlink || 0,
    rtt: connection?.rtt || 0,
    saveData: connection?.saveData || false,
    online: navigator.onLine,
    timestamp: Date.now(),
  };
};
