import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

// Type definitions for database records
interface StudentRecord {
  id: string;
  first_name: string;
  last_name: string;
  school_id: string;
  grade_level: number;
  school?: {
    name: string;
  };
}

interface AttendanceRecord {
  student_id: string;
  attendance_status: string;
  participation_score?: number;
  student: StudentRecord;
}

interface AssessmentRecord {
  student_id: string;
  pre_assessment_score?: number;
  post_assessment_score?: number;
  improvement_score?: number;
}

interface LongitudinalRecord {
  student_id: string;
  academic_performance_score?: number;
  behavioral_assessment?: string;
  risk_factors?: string[];
  interventions_received?: string[];
}

interface SessionRecord {
  id: string;
  session_name: string;
  session_type: string;
  school_id: string;
  session_date: string;
  round_tables_count?: number;
  school?: {
    name: string;
  };
  student_attendance?: AttendanceRecord[];
}

interface AttendanceImpactCorrelation {
  student_id: string;
  student_name: string;
  school_id: string;
  school_name: string;
  grade_level: number;
  attendance_rate: number;
  academic_performance_score?: number;
  leadership_assessment_score?: number;
  pre_assessment_score?: number;
  post_assessment_score?: number;
  improvement_score?: number;
  participation_average?: number;
  behavioral_assessment?: string;
  risk_factors: string[];
  interventions_received: string[];
  correlation_strength: 'strong' | 'moderate' | 'weak' | 'none';
  impact_category: 'high_positive' | 'positive' | 'neutral' | 'negative' | 'high_negative';
}

interface LeadershipProgramEffectiveness {
  program_id: string;
  program_name: string;
  session_type: string;
  school_id: string;
  school_name: string;
  total_sessions: number;
  average_attendance_rate: number;
  total_participants: number;
  completion_rate: number;
  average_participation_score: number;
  pre_leadership_score: number;
  post_leadership_score: number;
  leadership_improvement: number;
  effectiveness_rating: 'excellent' | 'good' | 'fair' | 'poor';
  attendance_impact_factor: number;
  recommendations: string[];
}

interface LongTermTrackingData {
  student_id: string;
  student_name: string;
  cohort_name: string;
  tracking_years: number[];
  attendance_progression: Array<{
    year: number;
    attendance_rate: number;
    academic_score: number;
    leadership_score?: number;
  }>;
  overall_trend: 'improving' | 'stable' | 'declining';
  retention_status: 'retained' | 'at_risk' | 'dropped_out';
  impact_summary: {
    attendance_correlation: number;
    academic_correlation: number;
    leadership_correlation: number;
    overall_impact_score: number;
  };
}

// Hook to get attendance-academic performance correlation
export const useAttendanceAcademicCorrelation = (
  schoolId?: string,
  gradeLevel?: number,
  dateRange?: { start: Date; end: Date }
) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['attendance-academic-correlation', schoolId, gradeLevel, dateRange],
    queryFn: async (): Promise<AttendanceImpactCorrelation[]> => {
      // Get attendance data for students
      let attendanceQuery = supabase
        .from('student_attendance')
        .select(`
          student_id,
          attendance_status,
          participation_score,
          student:students(
            id,
            first_name,
            last_name,
            grade_level,
            school_id,
            school:schools(name)
          )
        `);

      if (schoolId) {
        attendanceQuery = attendanceQuery.eq('school_id', schoolId);
      }

      if (dateRange) {
        attendanceQuery = attendanceQuery
          .gte('recorded_at', dateRange.start.toISOString())
          .lte('recorded_at', dateRange.end.toISOString());
      }

      const { data: attendanceData, error: attendanceError } = await attendanceQuery;
      if (attendanceError) throw attendanceError;

      // Get academic assessment data
      const { data: assessmentData, error: assessmentError } = await supabase
        .from('student_learning_assessments')
        .select(`
          student_id,
          pre_assessment_score,
          post_assessment_score,
          improvement_score,
          academic_year
        `);

      if (assessmentError) throw assessmentError;

      // Get longitudinal tracking data
      const { data: longitudinalData, error: longitudinalError } = await supabase
        .from('longitudinal_student_tracking')
        .select(`
          student_id,
          attendance_rate,
          academic_performance_score,
          behavioral_assessment,
          risk_factors,
          interventions_received
        `);

      if (longitudinalError) throw longitudinalError;

      // Calculate correlations for each student
      const studentStats = new Map<string, {
        student: StudentRecord;
        attendance_records: AttendanceRecord[];
        assessment: AssessmentRecord | null;
        longitudinal: LongitudinalRecord | null;
      }>();

      // Group attendance data by student
      attendanceData?.forEach(record => {
        const studentId = record.student_id;
        if (!studentStats.has(studentId)) {
          studentStats.set(studentId, {
            student: record.student,
            attendance_records: [],
            assessment: null,
            longitudinal: null,
          });
        }
        studentStats.get(studentId)!.attendance_records.push(record);
      });

      // Add assessment data
      assessmentData?.forEach(assessment => {
        const stats = studentStats.get(assessment.student_id);
        if (stats) {
          stats.assessment = assessment;
        }
      });

      // Add longitudinal data
      longitudinalData?.forEach(longitudinal => {
        const stats = studentStats.get(longitudinal.student_id);
        if (stats) {
          stats.longitudinal = longitudinal;
        }
      });

      // Calculate correlations
      const correlations: AttendanceImpactCorrelation[] = [];

      studentStats.forEach((stats, studentId) => {
        if (!stats.student || (gradeLevel && stats.student.grade_level !== gradeLevel)) {
          return;
        }

        // Calculate attendance rate
        const totalRecords = stats.attendance_records.length;
        const attendedRecords = stats.attendance_records.filter(r => 
          ['present', 'late'].includes(r.attendance_status)
        ).length;
        const attendanceRate = totalRecords > 0 ? (attendedRecords / totalRecords) * 100 : 0;

        // Calculate participation average
        const participationScores = stats.attendance_records
          .filter(r => r.participation_score !== null)
          .map(r => r.participation_score);
        const participationAverage = participationScores.length > 0
          ? participationScores.reduce((sum, score) => sum + score, 0) / participationScores.length
          : undefined;

        // Determine correlation strength and impact category
        let correlationStrength: 'strong' | 'moderate' | 'weak' | 'none' = 'none';
        let impactCategory: 'high_positive' | 'positive' | 'neutral' | 'negative' | 'high_negative' = 'neutral';

        if (stats.assessment && attendanceRate > 0) {
          const improvementScore = stats.assessment.improvement_score || 0;
          
          // Simple correlation logic (in real implementation, use statistical correlation)
          if (attendanceRate >= 90 && improvementScore >= 20) {
            correlationStrength = 'strong';
            impactCategory = 'high_positive';
          } else if (attendanceRate >= 80 && improvementScore >= 10) {
            correlationStrength = 'moderate';
            impactCategory = 'positive';
          } else if (attendanceRate >= 70) {
            correlationStrength = 'weak';
            impactCategory = 'neutral';
          } else if (attendanceRate < 60) {
            correlationStrength = 'moderate';
            impactCategory = 'negative';
          }
        }

        correlations.push({
          student_id: studentId,
          student_name: `${stats.student.first_name} ${stats.student.last_name}`,
          school_id: stats.student.school_id,
          school_name: stats.student.school?.name || '',
          grade_level: stats.student.grade_level,
          attendance_rate: attendanceRate,
          academic_performance_score: stats.longitudinal?.academic_performance_score,
          pre_assessment_score: stats.assessment?.pre_assessment_score,
          post_assessment_score: stats.assessment?.post_assessment_score,
          improvement_score: stats.assessment?.improvement_score,
          participation_average: participationAverage,
          behavioral_assessment: stats.longitudinal?.behavioral_assessment,
          risk_factors: stats.longitudinal?.risk_factors || [],
          interventions_received: stats.longitudinal?.interventions_received || [],
          correlation_strength: correlationStrength,
          impact_category: impactCategory,
        });
      });

      return correlations;
    },
    enabled: !!profile?.id,
  });
};

// Hook to analyze leadership program effectiveness with attendance correlation
export const useLeadershipProgramEffectiveness = (
  schoolId?: string,
  dateRange?: { start: Date; end: Date }
) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['leadership-program-effectiveness', schoolId, dateRange],
    queryFn: async (): Promise<LeadershipProgramEffectiveness[]> => {
      // Get leadership program sessions
      let sessionsQuery = supabase
        .from('attendance_sessions')
        .select(`
          id,
          session_name,
          session_type,
          school_id,
          session_date,
          round_tables_count,
          school:schools(name),
          student_attendance(
            student_id,
            attendance_status,
            participation_score
          )
        `)
        .eq('session_type', 'leadership_program');

      if (schoolId) {
        sessionsQuery = sessionsQuery.eq('school_id', schoolId);
      }

      if (dateRange) {
        sessionsQuery = sessionsQuery
          .gte('session_date', dateRange.start.toISOString().split('T')[0])
          .lte('session_date', dateRange.end.toISOString().split('T')[0]);
      }

      const { data: sessions, error: sessionsError } = await sessionsQuery;
      if (sessionsError) throw sessionsError;

      // Get leadership assessments
      const { data: leadershipAssessments, error: assessmentError } = await supabase
        .from('teacher_training_participants')
        .select(`
          student_id,
          pre_training_score,
          post_training_score,
          improvement_score,
          attendance_percentage,
          certification_received
        `);

      if (assessmentError) throw assessmentError;

      // Group sessions by program/school
      const programStats = new Map<string, {
        program_name: string;
        school_id: string;
        school_name: string;
        sessions: SessionRecord[];
        participants: Set<string>;
        total_attendance_records: AttendanceRecord[];
      }>();

      sessions?.forEach(session => {
        const key = `${session.school_id}-leadership`;
        if (!programStats.has(key)) {
          programStats.set(key, {
            program_name: 'Leadership Development Program',
            school_id: session.school_id,
            school_name: session.school?.name || '',
            sessions: [],
            participants: new Set(),
            total_attendance_records: [],
          });
        }

        const stats = programStats.get(key)!;
        stats.sessions.push(session);
        
        session.student_attendance?.forEach((attendance: AttendanceRecord) => {
          stats.participants.add(attendance.student_id);
          stats.total_attendance_records.push(attendance);
        });
      });

      // Calculate effectiveness metrics
      const effectiveness: LeadershipProgramEffectiveness[] = [];

      programStats.forEach((stats, key) => {
        const totalSessions = stats.sessions.length;
        const totalParticipants = stats.participants.size;
        const attendanceRecords = stats.total_attendance_records;

        // Calculate attendance rate
        const attendedRecords = attendanceRecords.filter(r => 
          ['present', 'late'].includes(r.attendance_status)
        ).length;
        const averageAttendanceRate = attendanceRecords.length > 0 
          ? (attendedRecords / attendanceRecords.length) * 100 
          : 0;

        // Calculate participation score
        const participationScores = attendanceRecords
          .filter(r => r.participation_score !== null)
          .map(r => r.participation_score);
        const averageParticipationScore = participationScores.length > 0
          ? participationScores.reduce((sum, score) => sum + score, 0) / participationScores.length
          : 0;

        // Get leadership assessment data for participants
        const participantAssessments = leadershipAssessments?.filter(assessment =>
          stats.participants.has(assessment.student_id)
        ) || [];

        const completionRate = participantAssessments.length > 0
          ? (participantAssessments.filter(a => a.certification_received).length / participantAssessments.length) * 100
          : 0;

        const preLeadershipScore = participantAssessments.length > 0
          ? participantAssessments.reduce((sum, a) => sum + (a.pre_training_score || 0), 0) / participantAssessments.length
          : 0;

        const postLeadershipScore = participantAssessments.length > 0
          ? participantAssessments.reduce((sum, a) => sum + (a.post_training_score || 0), 0) / participantAssessments.length
          : 0;

        const leadershipImprovement = postLeadershipScore - preLeadershipScore;

        // Calculate attendance impact factor (correlation between attendance and improvement)
        let attendanceImpactFactor = 0;
        if (participantAssessments.length > 0) {
          // Simple correlation calculation
          const correlationData = participantAssessments.map(assessment => ({
            attendance: assessment.attendance_percentage || 0,
            improvement: assessment.improvement_score || 0,
          }));

          if (correlationData.length > 1) {
            // Calculate Pearson correlation coefficient (simplified)
            const n = correlationData.length;
            const sumX = correlationData.reduce((sum, d) => sum + d.attendance, 0);
            const sumY = correlationData.reduce((sum, d) => sum + d.improvement, 0);
            const sumXY = correlationData.reduce((sum, d) => sum + (d.attendance * d.improvement), 0);
            const sumX2 = correlationData.reduce((sum, d) => sum + (d.attendance * d.attendance), 0);
            const sumY2 = correlationData.reduce((sum, d) => sum + (d.improvement * d.improvement), 0);

            const numerator = (n * sumXY) - (sumX * sumY);
            const denominator = Math.sqrt(((n * sumX2) - (sumX * sumX)) * ((n * sumY2) - (sumY * sumY)));
            
            attendanceImpactFactor = denominator !== 0 ? numerator / denominator : 0;
          }
        }

        // Determine effectiveness rating
        let effectivenessRating: 'excellent' | 'good' | 'fair' | 'poor' = 'poor';
        if (averageAttendanceRate >= 85 && leadershipImprovement >= 15 && completionRate >= 80) {
          effectivenessRating = 'excellent';
        } else if (averageAttendanceRate >= 75 && leadershipImprovement >= 10 && completionRate >= 70) {
          effectivenessRating = 'good';
        } else if (averageAttendanceRate >= 65 && leadershipImprovement >= 5) {
          effectivenessRating = 'fair';
        }

        // Generate recommendations
        const recommendations: string[] = [];
        if (averageAttendanceRate < 75) {
          recommendations.push('Improve attendance tracking and engagement strategies');
        }
        if (averageParticipationScore < 3.5) {
          recommendations.push('Enhance interactive activities to boost participation');
        }
        if (leadershipImprovement < 10) {
          recommendations.push('Review curriculum effectiveness and teaching methods');
        }
        if (completionRate < 70) {
          recommendations.push('Implement better support systems for program completion');
        }
        if (attendanceImpactFactor > 0.5) {
          recommendations.push('Strong attendance-improvement correlation - maintain current approach');
        } else if (attendanceImpactFactor < 0.3) {
          recommendations.push('Weak attendance-improvement correlation - review program quality');
        }

        effectiveness.push({
          program_id: key,
          program_name: stats.program_name,
          session_type: 'leadership_program',
          school_id: stats.school_id,
          school_name: stats.school_name,
          total_sessions: totalSessions,
          average_attendance_rate: averageAttendanceRate,
          total_participants: totalParticipants,
          completion_rate: completionRate,
          average_participation_score: averageParticipationScore,
          pre_leadership_score: preLeadershipScore,
          post_leadership_score: postLeadershipScore,
          leadership_improvement: leadershipImprovement,
          effectiveness_rating: effectivenessRating,
          attendance_impact_factor: attendanceImpactFactor,
          recommendations: recommendations,
        });
      });

      return effectiveness;
    },
    enabled: !!profile?.id,
  });
};

// Hook to update longitudinal tracking with attendance data
export const useUpdateLongitudinalTracking = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (studentId: string) => {
      // Calculate current attendance rate for the student
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

      const { data: attendanceRecords, error: attendanceError } = await supabase
        .from('student_attendance')
        .select('attendance_status')
        .eq('student_id', studentId)
        .gte('recorded_at', thirtyDaysAgo.toISOString());

      if (attendanceError) throw attendanceError;

      const totalRecords = attendanceRecords?.length || 0;
      const attendedRecords = attendanceRecords?.filter(r => 
        ['present', 'late'].includes(r.attendance_status)
      ).length || 0;
      const attendanceRate = totalRecords > 0 ? (attendedRecords / totalRecords) * 100 : 0;

      // Update longitudinal tracking record
      const { data, error } = await supabase
        .from('longitudinal_student_tracking')
        .upsert({
          student_id: studentId,
          tracking_year: new Date().getFullYear(),
          attendance_rate: attendanceRate,
          tracking_date: new Date().toISOString().split('T')[0],
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-academic-correlation'] });
      queryClient.invalidateQueries({ queryKey: ['longitudinal-tracking'] });
      toast({
        title: 'Success',
        description: 'Longitudinal tracking updated with attendance data',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to update longitudinal tracking:', error);
      toast({
        title: 'Error',
        description: 'Failed to update tracking data. Please try again.',
        variant: 'destructive',
      });
    },
  });
};
