-- Update RPC functions to work with student leadership training instead of teacher training

-- Drop the old teacher training function
DROP FUNCTION IF EXISTS get_teacher_training_effectiveness(UUID, training_type, DATE, DATE);

-- Create new function for student leadership effectiveness
CREATE OR REPLACE FUNCTION get_student_leadership_effectiveness(
    p_school_id UUID DEFAULT NULL,
    p_program_type leadership_program_type DEFAULT NULL,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    leadership_program_id UUID,
    program_name VARCHAR,
    program_type leadership_program_type,
    total_participants BIGINT,
    completion_rate DECIMAL,
    avg_improvement_score DECIMAL,
    certification_rate DECIMAL,
    avg_satisfaction_rating DECIMAL,
    effectiveness_score INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        slp.id as leadership_program_id,
        slp.program_name,
        slp.program_type,
        COUNT(slpa.id) as total_participants,
        ROUND(
            COUNT(CASE WHEN slpa.attendance_percentage >= 80 THEN 1 END)::DECIMAL / 
            NULLIF(COUNT(slpa.id)::DECIMAL, 0) * 100, 2
        ) as completion_rate,
        ROUND(AVG(slpa.improvement_score), 2) as avg_improvement_score,
        ROUND(
            COUNT(CASE WHEN slpa.certification_received = true THEN 1 END)::DECIMAL / 
            NULLIF(COUNT(slpa.id)::DECIMAL, 0) * 100, 2
        ) as certification_rate,
        ROUND(
            AVG(CASE 
                WHEN slpa.feedback_rating = 'very_satisfied' THEN 5
                WHEN slpa.feedback_rating = 'satisfied' THEN 4
                WHEN slpa.feedback_rating = 'neutral' THEN 3
                WHEN slpa.feedback_rating = 'dissatisfied' THEN 2
                WHEN slpa.feedback_rating = 'very_dissatisfied' THEN 1
                ELSE NULL
            END), 2
        ) as avg_satisfaction_rating,
        CASE 
            WHEN AVG(slpa.improvement_score) >= 80 AND 
                 COUNT(CASE WHEN slpa.certification_received = true THEN 1 END)::DECIMAL / COUNT(slpa.id)::DECIMAL >= 0.8 
            THEN 5
            WHEN AVG(slpa.improvement_score) >= 70 THEN 4
            WHEN AVG(slpa.improvement_score) >= 60 THEN 3
            WHEN AVG(slpa.improvement_score) >= 50 THEN 2
            ELSE 1
        END as effectiveness_score
    FROM student_leadership_programs slp
    LEFT JOIN student_leadership_participation slpa ON slp.id = slpa.leadership_program_id
    WHERE 
        (p_school_id IS NULL OR slpa.school_id = p_school_id) AND
        (p_program_type IS NULL OR slp.program_type = p_program_type) AND
        (p_start_date IS NULL OR slp.start_date >= p_start_date) AND
        (p_end_date IS NULL OR slp.end_date <= p_end_date)
    GROUP BY slp.id, slp.program_name, slp.program_type
    ORDER BY slp.program_name;
END;
$$;

-- Update the comprehensive impact report function to use student leadership data
CREATE OR REPLACE FUNCTION get_comprehensive_impact_report(
    p_school_id UUID DEFAULT NULL,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    report_section VARCHAR,
    metric_name VARCHAR,
    metric_value DECIMAL,
    metric_unit VARCHAR,
    trend_direction VARCHAR,
    benchmark_comparison VARCHAR
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Student Learning Outcomes
    RETURN QUERY
    SELECT 
        'Student Learning Outcomes'::VARCHAR as report_section,
        'Average Learning Improvement'::VARCHAR as metric_name,
        ROUND(AVG(sa.improvement_percentage), 2) as metric_value,
        'percentage'::VARCHAR as metric_unit,
        CASE 
            WHEN AVG(sa.improvement_percentage) >= 15 THEN 'positive'
            WHEN AVG(sa.improvement_percentage) >= 5 THEN 'stable'
            ELSE 'negative'
        END::VARCHAR as trend_direction,
        CASE 
            WHEN AVG(sa.improvement_percentage) >= 20 THEN 'above_benchmark'
            WHEN AVG(sa.improvement_percentage) >= 10 THEN 'meets_benchmark'
            ELSE 'below_benchmark'
        END::VARCHAR as benchmark_comparison
    FROM student_assessments sa
    JOIN students s ON sa.student_id = s.id
    WHERE 
        (p_school_id IS NULL OR s.school_id = p_school_id) AND
        (p_start_date IS NULL OR sa.assessment_date >= p_start_date) AND
        (p_end_date IS NULL OR sa.assessment_date <= p_end_date);

    -- School Performance
    RETURN QUERY
    SELECT 
        'School Performance'::VARCHAR as report_section,
        'Average Attendance Rate'::VARCHAR as metric_name,
        ROUND(AVG(sar.attendance_rate), 2) as metric_value,
        'percentage'::VARCHAR as metric_unit,
        CASE 
            WHEN AVG(sar.attendance_rate) >= 85 THEN 'positive'
            WHEN AVG(sar.attendance_rate) >= 75 THEN 'stable'
            ELSE 'negative'
        END::VARCHAR as trend_direction,
        CASE 
            WHEN AVG(sar.attendance_rate) >= 90 THEN 'above_benchmark'
            WHEN AVG(sar.attendance_rate) >= 80 THEN 'meets_benchmark'
            ELSE 'below_benchmark'
        END::VARCHAR as benchmark_comparison
    FROM school_attendance_records sar
    WHERE 
        (p_school_id IS NULL OR sar.school_id = p_school_id) AND
        (p_start_date IS NULL OR sar.record_date >= p_start_date) AND
        (p_end_date IS NULL OR sar.record_date <= p_end_date);

    -- Student Leadership Training Effectiveness
    RETURN QUERY
    SELECT 
        'Student Leadership Training'::VARCHAR as report_section,
        'Average Leadership Improvement'::VARCHAR as metric_name,
        ROUND(AVG(slpa.improvement_score), 2) as metric_value,
        'score'::VARCHAR as metric_unit,
        CASE 
            WHEN AVG(slpa.improvement_score) >= 70 THEN 'positive'
            WHEN AVG(slpa.improvement_score) >= 50 THEN 'stable'
            ELSE 'negative'
        END::VARCHAR as trend_direction,
        CASE 
            WHEN AVG(slpa.improvement_score) >= 80 THEN 'above_benchmark'
            WHEN AVG(slpa.improvement_score) >= 60 THEN 'meets_benchmark'
            ELSE 'below_benchmark'
        END::VARCHAR as benchmark_comparison
    FROM student_leadership_participation slpa
    JOIN student_leadership_programs slp ON slpa.leadership_program_id = slp.id
    WHERE 
        (p_school_id IS NULL OR slpa.school_id = p_school_id) AND
        (p_start_date IS NULL OR slp.start_date >= p_start_date) AND
        (p_end_date IS NULL OR slp.end_date <= p_end_date);

    -- Beneficiary Satisfaction
    RETURN QUERY
    SELECT 
        'Beneficiary Satisfaction'::VARCHAR as report_section,
        'Average Satisfaction Rating'::VARCHAR as metric_name,
        ROUND(AVG(CASE 
            WHEN sr.rating = 'very_satisfied' THEN 5
            WHEN sr.rating = 'satisfied' THEN 4
            WHEN sr.rating = 'neutral' THEN 3
            WHEN sr.rating = 'dissatisfied' THEN 2
            WHEN sr.rating = 'very_dissatisfied' THEN 1
            ELSE NULL
        END), 2) as metric_value,
        'rating'::VARCHAR as metric_unit,
        CASE 
            WHEN AVG(CASE 
                WHEN sr.rating = 'very_satisfied' THEN 5
                WHEN sr.rating = 'satisfied' THEN 4
                WHEN sr.rating = 'neutral' THEN 3
                WHEN sr.rating = 'dissatisfied' THEN 2
                WHEN sr.rating = 'very_dissatisfied' THEN 1
                ELSE NULL
            END) >= 4 THEN 'positive'
            WHEN AVG(CASE 
                WHEN sr.rating = 'very_satisfied' THEN 5
                WHEN sr.rating = 'satisfied' THEN 4
                WHEN sr.rating = 'neutral' THEN 3
                WHEN sr.rating = 'dissatisfied' THEN 2
                WHEN sr.rating = 'very_dissatisfied' THEN 1
                ELSE NULL
            END) >= 3 THEN 'stable'
            ELSE 'negative'
        END::VARCHAR as trend_direction,
        'meets_benchmark'::VARCHAR as benchmark_comparison
    FROM survey_responses sr
    JOIN beneficiary_feedback_surveys bfs ON sr.survey_id = bfs.id
    WHERE 
        (p_school_id IS NULL OR bfs.school_id = p_school_id) AND
        (p_start_date IS NULL OR sr.response_date >= p_start_date) AND
        (p_end_date IS NULL OR sr.response_date <= p_end_date);
END;
$$;

-- Now we can safely drop the old training_type enum
DROP TYPE IF EXISTS training_type CASCADE;
