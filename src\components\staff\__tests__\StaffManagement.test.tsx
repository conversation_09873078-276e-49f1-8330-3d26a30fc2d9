import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import StaffManagement from '../StaffManagement';
import { useAuth } from '@/hooks/useAuth';
import { useStaffManagement } from '@/hooks/useStaffManagement';

// Define interfaces for test props
interface StaffMember {
  id: string;
  name: string;
  email: string;
}

interface StaffTableProps {
  staffMembers: StaffMember[];
}

interface DialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

// Mock the hooks
vi.mock('@/hooks/useAuth');
vi.mock('@/hooks/useStaffManagement');

// Mock the child components
vi.mock('../StaffTable', () => ({
  default: ({ staffMembers }: StaffTableProps) => (
    <div data-testid="staff-table">
      {staffMembers.map((member) => (
        <div key={member.id} data-testid={`staff-member-${member.id}`}>
          {member.name} - {member.email}
        </div>
      ))}
    </div>
  )
}));

vi.mock('../CreateUserDialog', () => ({
  default: ({ open, onOpenChange }: DialogProps) => (
    open ? (
      <div data-testid="create-user-dialog">
        <button onClick={() => onOpenChange(false)}>Close</button>
      </div>
    ) : null
  )
}));

vi.mock('../BulkUserCreation', () => ({
  default: ({ open, onOpenChange }: DialogProps) => (
    open ? (
      <div data-testid="bulk-user-creation">
        <button onClick={() => onOpenChange(false)}>Close</button>
      </div>
    ) : null
  )
}));

vi.mock('../AuditLogViewer', () => ({
  default: ({ open, onOpenChange }: DialogProps) => (
    open ? (
      <div data-testid="audit-log-viewer">
        <button onClick={() => onOpenChange(false)}>Close</button>
      </div>
    ) : null
  )
}));

const mockStaffMembers = [
  {
    id: '1',
    name: 'John Doe',
    email: '<EMAIL>',
    role: 'field_staff',
    division_id: null,
    division_name: null,
    phone: '+256700000000',
    country: 'Uganda',
    is_active: true,
    requires_password_change: false,
    last_password_change: null,
    invitation_accepted_at: '2024-01-01T00:00:00Z',
    created_at: '2024-01-01T00:00:00Z'
  },
  {
    id: '2',
    name: 'Jane Smith',
    email: '<EMAIL>',
    role: 'program_officer',
    division_id: 'div-1',
    division_name: 'Central Division',
    phone: '+256700000001',
    country: 'Uganda',
    is_active: true,
    requires_password_change: true,
    last_password_change: '2024-01-01T00:00:00Z',
    invitation_accepted_at: '2024-01-01T00:00:00Z',
    created_at: '2024-01-01T00:00:00Z'
  }
];

const mockPendingInvitations = [
  {
    id: 'inv-1',
    email: '<EMAIL>',
    name: 'Pending User',
    role: 'field_staff',
    status: 'pending',
    created_at: '2024-01-01T00:00:00Z',
    expires_at: '2024-01-08T00:00:00Z'
  }
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('StaffManagement', () => {
  const mockUseAuth = vi.mocked(useAuth);
  const mockUseStaffManagement = vi.mocked(useStaffManagement);

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockUseAuth.mockReturnValue({
      profile: {
        id: 'admin-1',
        name: 'Admin User',
        email: '<EMAIL>',
        role: 'admin',
        is_active: true,
        country: 'Uganda',
        division_id: null,
        phone: null,
        requires_password_change: false,
        last_password_change: null,
        invitation_accepted_at: null,
        created_at: '2024-01-01T00:00:00Z'
      },
      user: null,
      loading: false,
      signOut: vi.fn()
    });

    mockUseStaffManagement.mockReturnValue({
      staffMembers: mockStaffMembers,
      pendingInvitations: mockPendingInvitations,
      isLoadingStaff: false,
      isLoadingInvitations: false,
      isCreatingInvitation: false,
      isLoading: false,
      refetchStaff: vi.fn(),
      refetchInvitations: vi.fn(),
      createUserInvitation: vi.fn(),
      createUserAccount: vi.fn(),
      updateUserRole: vi.fn(),
      toggleUserStatus: vi.fn(),
      resendInvitation: vi.fn(),
      generateSecurePassword: vi.fn(() => 'SecurePass123!'),
      bulkCreateInvitations: vi.fn()
    });
  });

  it('renders staff management dashboard for admin users', () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    expect(screen.getByText('Staff Management')).toBeInTheDocument();
    expect(screen.getByText('Manage user accounts, roles, and permissions')).toBeInTheDocument();
  });

  it('displays staff statistics correctly', () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    expect(screen.getByText('Total Staff')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument(); // Total staff count
    expect(screen.getByText('Active Users')).toBeInTheDocument();
    expect(screen.getByText('Pending Invitations')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument(); // Pending invitations count
  });

  it('shows action buttons for admin users', () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    expect(screen.getByRole('button', { name: /add user/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /bulk import/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /export csv/i })).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /audit log/i })).toBeInTheDocument();
  });

  it('opens create user dialog when add user button is clicked', async () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    const addUserButton = screen.getByRole('button', { name: /add user/i });
    fireEvent.click(addUserButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('create-user-dialog')).toBeInTheDocument();
    });
  });

  it('opens bulk user creation dialog when bulk import button is clicked', async () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    const bulkImportButton = screen.getByRole('button', { name: /bulk import/i });
    fireEvent.click(bulkImportButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('bulk-user-creation')).toBeInTheDocument();
    });
  });

  it('opens audit log viewer when audit log button is clicked', async () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    const auditLogButton = screen.getByRole('button', { name: /audit log/i });
    fireEvent.click(auditLogButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('audit-log-viewer')).toBeInTheDocument();
    });
  });

  it('filters staff members by search term', async () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    const searchInput = screen.getByPlaceholderText(/search staff/i);
    fireEvent.change(searchInput, { target: { value: 'john' } });
    
    // The filtering logic is tested through the StaffTable component
    expect(searchInput).toHaveValue('john');
  });

  it('filters staff members by role', async () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    // Find and click the role filter dropdown
    const roleFilter = screen.getByDisplayValue('All Roles');
    fireEvent.click(roleFilter);
    
    // The filtering logic is tested through the component state
    expect(roleFilter).toBeInTheDocument();
  });

  it('displays loading state when staff data is loading', () => {
    mockUseStaffManagement.mockReturnValue({
      ...mockUseStaffManagement(),
      isLoadingStaff: true
    });
    
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    // Loading state should be handled by the StaffTable component
    expect(screen.getByTestId('staff-table')).toBeInTheDocument();
  });

  it('handles export CSV functionality', () => {
    // Mock URL.createObjectURL and related functions
    global.URL.createObjectURL = vi.fn(() => 'mock-url');
    global.URL.revokeObjectURL = vi.fn();

    const mockLink = {
      href: '',
      download: '',
      style: { visibility: '' },
      click: vi.fn()
    } as unknown as HTMLAnchorElement;

    vi.spyOn(document, 'createElement').mockReturnValue(mockLink);
    vi.spyOn(document.body, 'appendChild').mockImplementation(() => mockLink);
    vi.spyOn(document.body, 'removeChild').mockImplementation(() => mockLink);
    
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    const exportButton = screen.getByRole('button', { name: /export csv/i });
    fireEvent.click(exportButton);
    
    expect(mockLink.click).toHaveBeenCalled();
  });

  it('shows staff and invitations tabs', () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    expect(screen.getByRole('tab', { name: /staff members/i })).toBeInTheDocument();
    expect(screen.getByRole('tab', { name: /pending invitations/i })).toBeInTheDocument();
  });

  it('switches between staff and invitations tabs', async () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    const invitationsTab = screen.getByRole('tab', { name: /pending invitations/i });
    fireEvent.click(invitationsTab);
    
    // Tab switching functionality should work
    expect(invitationsTab).toHaveAttribute('data-state', 'active');
  });

  it('closes dialogs when close button is clicked', async () => {
    render(<StaffManagement />, { wrapper: createWrapper() });
    
    // Open create user dialog
    const addUserButton = screen.getByRole('button', { name: /add user/i });
    fireEvent.click(addUserButton);
    
    await waitFor(() => {
      expect(screen.getByTestId('create-user-dialog')).toBeInTheDocument();
    });
    
    // Close the dialog
    const closeButton = screen.getByRole('button', { name: /close/i });
    fireEvent.click(closeButton);
    
    await waitFor(() => {
      expect(screen.queryByTestId('create-user-dialog')).not.toBeInTheDocument();
    });
  });
});
