/**
 * Memory Leak Test Script
 * 
 * This script simulates real-world usage patterns to verify that
 * the memory leak fixes are working correctly.
 */

import { storageManager } from '@/utils/storageManager';

interface TestResult {
  testName: string;
  passed: boolean;
  details: string;
  metrics?: {
    initialSize: number;
    finalSize: number;
    itemsAdded: number;
    itemsRemaining: number;
    cleanupEffectiveness: number;
  };
}

class MemoryLeakTester {
  private results: TestResult[] = [];

  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Memory Leak Tests...\n');

    await this.testStorageLimitsEnforcement();
    await this.testAutomaticCleanup();
    await this.testLongTermAccumulation();
    await this.testStorageQuotaHandling();
    await this.testPeriodicCleanupEffectiveness();

    this.printResults();
    return this.results;
  }

  private async testStorageLimitsEnforcement(): Promise<void> {
    console.log('📊 Testing Storage Limits Enforcement...');

    try {
      // Clear existing data
      localStorage.clear();

      const initialUsage = await storageManager.getStorageUsage();
      const initialSize = initialUsage.total;

      // Add items beyond the limit
      const itemsToAdd = 600;
      const testData = Array.from({ length: itemsToAdd }, (_, i) => ({
        id: `test_item_${i}`,
        type: 'check_in',
        data: { test: `data_${i}`, timestamp: Date.now() },
        timestamp: Date.now() - (i * 1000),
        retryCount: 0,
        maxRetries: 5,
        priority: i % 2 === 0 ? 'HIGH' : 'LOW',
      }));

      localStorage.setItem('field_staff_offline_data', JSON.stringify(testData));

      // Trigger cleanup
      const cleanupReport = await storageManager.performCleanup(true);
      
      const finalUsage = await storageManager.getStorageUsage();
      const remainingData = JSON.parse(localStorage.getItem('field_staff_offline_data') || '[]');

      const passed = remainingData.length <= 500; // Should enforce 500 item limit

      this.results.push({
        testName: 'Storage Limits Enforcement',
        passed,
        details: passed 
          ? `✅ Successfully enforced limit: ${remainingData.length}/500 items`
          : `❌ Failed to enforce limit: ${remainingData.length}/500 items`,
        metrics: {
          initialSize,
          finalSize: finalUsage.total,
          itemsAdded: itemsToAdd,
          itemsRemaining: remainingData.length,
          cleanupEffectiveness: ((itemsToAdd - remainingData.length) / itemsToAdd) * 100,
        },
      });

    } catch (error) {
      this.results.push({
        testName: 'Storage Limits Enforcement',
        passed: false,
        details: `❌ Test failed with error: ${error.message}`,
      });
    }
  }

  private async testAutomaticCleanup(): Promise<void> {
    console.log('🧹 Testing Automatic Cleanup...');

    try {
      localStorage.clear();

      const now = Date.now();
      const oldTimestamp = now - (25 * 60 * 60 * 1000); // 25 hours ago
      const recentTimestamp = now - (1 * 60 * 60 * 1000); // 1 hour ago

      // Create mix of old and recent items
      const testData = [
        // Old successful items (should be cleaned)
        ...Array.from({ length: 50 }, (_, i) => ({
          id: `old_successful_${i}`,
          type: 'check_in',
          data: { test: 'old_data' },
          timestamp: oldTimestamp,
          retryCount: 0, // Successful
          maxRetries: 5,
          priority: 'MEDIUM',
        })),
        // Recent items (should be kept)
        ...Array.from({ length: 20 }, (_, i) => ({
          id: `recent_${i}`,
          type: 'check_in',
          data: { test: 'recent_data' },
          timestamp: recentTimestamp,
          retryCount: 0,
          maxRetries: 5,
          priority: 'HIGH',
        })),
        // Old failed items (should be kept longer)
        ...Array.from({ length: 10 }, (_, i) => ({
          id: `old_failed_${i}`,
          type: 'check_in',
          data: { test: 'failed_data' },
          timestamp: oldTimestamp,
          retryCount: 5, // Failed
          maxRetries: 5,
          priority: 'LOW',
        })),
      ];

      localStorage.setItem('field_staff_offline_data', JSON.stringify(testData));

      const initialCount = testData.length;
      const cleanupReport = await storageManager.performCleanup(true);
      
      const remainingData = JSON.parse(localStorage.getItem('field_staff_offline_data') || '[]');
      const oldSuccessfulRemaining = remainingData.filter(item => item.id.startsWith('old_successful_'));
      const recentRemaining = remainingData.filter(item => item.id.startsWith('recent_'));
      const oldFailedRemaining = remainingData.filter(item => item.id.startsWith('old_failed_'));

      const passed = oldSuccessfulRemaining.length < 50 && recentRemaining.length === 20 && oldFailedRemaining.length === 10;

      this.results.push({
        testName: 'Automatic Cleanup',
        passed,
        details: passed 
          ? `✅ Cleanup working correctly: Old successful: ${oldSuccessfulRemaining.length}/50, Recent: ${recentRemaining.length}/20, Old failed: ${oldFailedRemaining.length}/10`
          : `❌ Cleanup not working: Old successful: ${oldSuccessfulRemaining.length}/50, Recent: ${recentRemaining.length}/20, Old failed: ${oldFailedRemaining.length}/10`,
        metrics: {
          initialSize: initialCount,
          finalSize: remainingData.length,
          itemsAdded: initialCount,
          itemsRemaining: remainingData.length,
          cleanupEffectiveness: ((initialCount - remainingData.length) / initialCount) * 100,
        },
      });

    } catch (error) {
      this.results.push({
        testName: 'Automatic Cleanup',
        passed: false,
        details: `❌ Test failed with error: ${error.message}`,
      });
    }
  }

  private async testLongTermAccumulation(): Promise<void> {
    console.log('📈 Testing Long-term Accumulation Prevention...');

    try {
      localStorage.clear();

      const iterations = 20;
      const itemsPerIteration = 50;
      let totalItemsAdded = 0;

      // Simulate long-term usage
      for (let iteration = 0; iteration < iterations; iteration++) {
        const currentData = JSON.parse(localStorage.getItem('field_staff_offline_data') || '[]');
        
        // Add new items
        const newItems = Array.from({ length: itemsPerIteration }, (_, i) => ({
          id: `iteration_${iteration}_item_${i}`,
          type: 'check_in',
          data: { iteration, item: i },
          timestamp: Date.now() - (iteration * 60 * 1000), // Spread over time
          retryCount: Math.random() > 0.7 ? 5 : 0, // 30% failed, 70% successful
          maxRetries: 5,
          priority: ['LOW', 'MEDIUM', 'HIGH'][Math.floor(Math.random() * 3)],
        }));

        const updatedData = [...currentData, ...newItems];
        localStorage.setItem('field_staff_offline_data', JSON.stringify(updatedData));
        totalItemsAdded += itemsPerIteration;

        // Trigger cleanup every few iterations
        if (iteration % 5 === 0) {
          await storageManager.performCleanup();
        }
      }

      // Final cleanup
      await storageManager.performCleanup(true);

      const finalData = JSON.parse(localStorage.getItem('field_staff_offline_data') || '[]');
      const finalUsage = await storageManager.getStorageUsage();

      // Should not have accumulated all items
      const passed = finalData.length < totalItemsAdded && finalData.length <= 500;

      this.results.push({
        testName: 'Long-term Accumulation Prevention',
        passed,
        details: passed 
          ? `✅ Prevented accumulation: ${finalData.length}/${totalItemsAdded} items remaining`
          : `❌ Failed to prevent accumulation: ${finalData.length}/${totalItemsAdded} items remaining`,
        metrics: {
          initialSize: 0,
          finalSize: finalUsage.total,
          itemsAdded: totalItemsAdded,
          itemsRemaining: finalData.length,
          cleanupEffectiveness: ((totalItemsAdded - finalData.length) / totalItemsAdded) * 100,
        },
      });

    } catch (error) {
      this.results.push({
        testName: 'Long-term Accumulation Prevention',
        passed: false,
        details: `❌ Test failed with error: ${error.message}`,
      });
    }
  }

  private async testStorageQuotaHandling(): Promise<void> {
    console.log('💾 Testing Storage Quota Handling...');

    try {
      localStorage.clear();

      // Create large data to test quota handling
      const largeData = Array.from({ length: 1000 }, (_, i) => ({
        id: `large_item_${i}`,
        type: 'check_in',
        data: { 
          test: 'x'.repeat(1000), // 1KB per item
          index: i,
          timestamp: Date.now(),
        },
        timestamp: Date.now(),
        retryCount: 0,
        maxRetries: 5,
        priority: 'MEDIUM',
      }));

      try {
        localStorage.setItem('field_staff_offline_data', JSON.stringify(largeData));
      } catch (quotaError) {
        // This is expected - should trigger cleanup
        console.log('Quota exceeded as expected, triggering cleanup...');
      }

      const cleanupReport = await storageManager.performCleanup(true);
      const finalUsage = await storageManager.getStorageUsage();

      // Should handle quota gracefully
      const passed = finalUsage.percentage < 90; // Should be under 90% after cleanup

      this.results.push({
        testName: 'Storage Quota Handling',
        passed,
        details: passed 
          ? `✅ Handled quota gracefully: ${finalUsage.percentage.toFixed(1)}% usage`
          : `❌ Failed to handle quota: ${finalUsage.percentage.toFixed(1)}% usage`,
        metrics: {
          initialSize: 0,
          finalSize: finalUsage.total,
          itemsAdded: largeData.length,
          itemsRemaining: JSON.parse(localStorage.getItem('field_staff_offline_data') || '[]').length,
          cleanupEffectiveness: cleanupReport.itemsRemoved > 0 ? 100 : 0,
        },
      });

    } catch (error) {
      this.results.push({
        testName: 'Storage Quota Handling',
        passed: false,
        details: `❌ Test failed with error: ${error.message}`,
      });
    }
  }

  private async testPeriodicCleanupEffectiveness(): Promise<void> {
    console.log('⏰ Testing Periodic Cleanup Effectiveness...');

    try {
      localStorage.clear();

      // Add data that should trigger cleanup
      const testData = Array.from({ length: 300 }, (_, i) => ({
        id: `periodic_test_${i}`,
        type: 'check_in',
        data: { test: 'data'.repeat(100) }, // Larger data
        timestamp: Date.now() - (i * 60 * 1000), // Spread over time
        retryCount: i % 10 === 0 ? 5 : 0, // 10% failed
        maxRetries: 5,
        priority: 'MEDIUM',
      }));

      localStorage.setItem('field_staff_offline_data', JSON.stringify(testData));

      const initialUsage = await storageManager.getStorageUsage();
      const shouldCleanup = await storageManager.shouldCleanup();

      if (shouldCleanup) {
        const cleanupReport = await storageManager.performCleanup();
        const finalUsage = await storageManager.getStorageUsage();

        const passed = finalUsage.percentage < initialUsage.percentage;

        this.results.push({
          testName: 'Periodic Cleanup Effectiveness',
          passed,
          details: passed 
            ? `✅ Cleanup effective: ${initialUsage.percentage.toFixed(1)}% → ${finalUsage.percentage.toFixed(1)}%`
            : `❌ Cleanup ineffective: ${initialUsage.percentage.toFixed(1)}% → ${finalUsage.percentage.toFixed(1)}%`,
          metrics: {
            initialSize: initialUsage.total,
            finalSize: finalUsage.total,
            itemsAdded: testData.length,
            itemsRemaining: JSON.parse(localStorage.getItem('field_staff_offline_data') || '[]').length,
            cleanupEffectiveness: ((initialUsage.total - finalUsage.total) / initialUsage.total) * 100,
          },
        });
      } else {
        this.results.push({
          testName: 'Periodic Cleanup Effectiveness',
          passed: true,
          details: `✅ No cleanup needed: ${initialUsage.percentage.toFixed(1)}% usage`,
        });
      }

    } catch (error) {
      this.results.push({
        testName: 'Periodic Cleanup Effectiveness',
        passed: false,
        details: `❌ Test failed with error: ${error.message}`,
      });
    }
  }

  private printResults(): void {
    console.log('\n📋 Memory Leak Test Results:');
    console.log('=' .repeat(50));

    const passedTests = this.results.filter(r => r.passed).length;
    const totalTests = this.results.length;

    this.results.forEach(result => {
      console.log(`\n${result.testName}:`);
      console.log(`  ${result.details}`);
      
      if (result.metrics) {
        console.log(`  📊 Metrics:`);
        console.log(`    - Items Added: ${result.metrics.itemsAdded}`);
        console.log(`    - Items Remaining: ${result.metrics.itemsRemaining}`);
        console.log(`    - Cleanup Effectiveness: ${result.metrics.cleanupEffectiveness.toFixed(1)}%`);
        console.log(`    - Storage: ${this.formatBytes(result.metrics.initialSize)} → ${this.formatBytes(result.metrics.finalSize)}`);
      }
    });

    console.log('\n' + '='.repeat(50));
    console.log(`🎯 Overall Result: ${passedTests}/${totalTests} tests passed`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All memory leak fixes are working correctly!');
    } else {
      console.log('⚠️  Some tests failed. Please review the implementation.');
    }
  }

  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Export for use in tests or manual execution
export const memoryLeakTester = new MemoryLeakTester();

// Auto-run if this file is executed directly
if (typeof window !== 'undefined' && window.location) {
  // Browser environment - can be run manually
  (window as typeof window & { runMemoryLeakTests: () => Promise<void> }).runMemoryLeakTests = () => memoryLeakTester.runAllTests();
  console.log('💡 Run memory leak tests with: runMemoryLeakTests()');
}
