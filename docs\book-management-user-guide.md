# iLEAD Field Tracker - Book Management User Guide

## Quick Start Guide

### Accessing Book Management
1. Navigate to the **Book Management** section in the main menu
2. Only **Admin users** have access to book management features
3. The main interface shows all books with their current inventory status

## 📚 Managing Books

### Adding a New Book
1. Click the **"Add New Book"** button
2. Fill in the required fields:
   - **Title*** (required)
   - **Author*** (required)
   - **Category/Subject*** (required)
   - **Grade Level*** (required)
   - **Language*** (required)
3. Optional fields:
   - **ISBN** (will be validated if provided)
   - **Publication Year**
   - **Publisher**
   - **Description**
4. Set inventory details:
   - **Total Quantity**
   - **Book Condition**
   - **Storage Location**
   - **Cost Per Unit**
   - **Minimum Stock Threshold**
5. Click **"Add Book"** to save

### Editing an Existing Book
1. Find the book in the list
2. Click the **"Edit"** button on the book card
3. Modify any fields as needed
4. Click **"Update Book"** to save changes
5. All changes are logged for audit purposes

### Viewing Book Details
1. Click the **"View"** button on any book card
2. See complete book information and inventory status
3. Access quick actions from the details dialog

## 📦 Inventory Management

### Updating Inventory
1. Click the **"Inventory"** button on a book card
2. Choose the type of update:
   - **Add Stock**: New deliveries, returns
   - **Remove Stock**: Damaged, lost, corrections
   - **Set Total Quantity**: Reset to specific amount
   - **Adjust Damaged Count**: Update damaged book count
   - **Adjust Lost Count**: Update lost book count
3. Enter the quantity and reason
4. Add notes if needed
5. Click **"Update Inventory"**

### Understanding Stock Status
- **Total Quantity**: All books owned
- **Available**: Books ready for distribution
- **Distributed**: Books currently with schools
- **Damaged/Lost**: Books not available for distribution

### Low Stock Alerts
- Books below their minimum threshold are highlighted
- Critical alerts show for out-of-stock items
- Use the **"Restock"** button for quick inventory updates

## 🚚 Distribution Integration

### Before Creating Distributions
1. Ensure books have sufficient **Available Quantity**
2. Check the **Distribution Integration Test** for system health
3. Verify school information is up to date

### Stock Validation
- The system automatically checks available stock
- Prevents over-distribution beyond available quantities
- Updates inventory when distributions are completed

## ✅ Data Validation Features

### ISBN Validation
- Supports both ISBN-10 and ISBN-13 formats
- Automatic format validation with checksum verification
- Auto-formatting for properly entered ISBNs
- Duplicate ISBN detection

### Real-time Validation
- **Green checkmarks** appear for valid fields
- **Red borders** indicate validation errors
- **Error messages** provide specific guidance
- **Warnings** alert about potential duplicates

### Required Fields
- Title, Author, Category, Grade Level, and Language are mandatory
- Other fields are optional but recommended for complete records

## 🔍 Search and Filter

### Finding Books
- Use the search bar to find books by title, author, or ISBN
- Filter by category, grade level, or language
- Sort by various criteria (title, author, stock level)

### Stock Filters
- **All Books**: Complete inventory
- **Low Stock**: Books below threshold
- **Out of Stock**: Books with zero availability
- **Recently Added**: Newest books first

## 📊 Monitoring and Reports

### Low Stock Monitoring
- Automatic alerts for books below minimum threshold
- Summary statistics for inventory health
- Quick action buttons for restocking

### Activity Tracking
- All book additions, edits, and inventory changes are logged
- Audit trail includes user, timestamp, and change details
- Activity history available for compliance reporting

## 🛠️ Troubleshooting

### Common Issues

**"ISBN already exists" error**
- Check if the book is already in the system
- Verify the ISBN is correct
- Contact admin if you believe this is an error

**"Insufficient inventory" during distribution**
- Check the available quantity for the book
- Update inventory if new stock has arrived
- Consider partial distribution if appropriate

**Validation errors**
- Read error messages carefully
- Ensure all required fields are filled
- Check that quantities are reasonable numbers
- Verify ISBN format if provided

### Getting Help
- Use the **Distribution Integration Test** to check system health
- Contact your system administrator for technical issues
- Refer to this guide for common procedures

## 💡 Best Practices

### Data Entry
1. **Always enter complete information** when adding books
2. **Use consistent naming** for authors and publishers
3. **Set appropriate minimum thresholds** based on distribution patterns
4. **Include storage locations** for easy physical tracking

### Inventory Management
1. **Update inventory promptly** when stock changes
2. **Use specific reasons** for inventory adjustments
3. **Add detailed notes** for unusual changes
4. **Monitor low stock alerts** regularly

### Distribution Planning
1. **Check stock levels** before planning distributions
2. **Update inventory** when new stock arrives
3. **Coordinate with field staff** on distribution schedules
4. **Review distribution reports** for planning insights

## 📞 Support

For technical support or questions about the book management system:
1. Check this user guide first
2. Use the built-in validation messages for guidance
3. Contact your system administrator
4. Report any bugs or issues through proper channels

---

**Last Updated**: 2025-06-23  
**Version**: 1.0  
**For**: iLEAD Field Tracker Book Management System
