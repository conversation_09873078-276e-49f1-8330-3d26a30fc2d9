import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  FileText,
  Download,
  Calendar,
  Filter,
  BarChart3,
  TrendingUp,
  Users,
  School,
  Award,
  MessageSquare
} from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';

interface ImpactReportsProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const ImpactReports: React.FC<ImpactReportsProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [selectedReportType, setSelectedReportType] = useState('comprehensive');
  const [selectedPeriod, setSelectedPeriod] = useState('quarterly');

  const reportTypes = [
    {
      id: 'comprehensive',
      title: 'Comprehensive Impact Report',
      description: 'Complete overview of all impact metrics and outcomes',
      icon: BarChart3,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      id: 'student-outcomes',
      title: 'Student Learning Outcomes Report',
      description: 'Detailed analysis of student academic progress',
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-100'
    },
    {
      id: 'school-performance',
      title: 'School Performance Report',
      description: 'Infrastructure, attendance, and operational metrics',
      icon: School,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    },
    {
      id: 'teacher-training',
      title: 'Teacher Training Impact Report',
      description: 'Training effectiveness and professional development',
      icon: Award,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      id: 'beneficiary-feedback',
      title: 'Beneficiary Feedback Report',
      description: 'Stakeholder satisfaction and feedback analysis',
      icon: MessageSquare,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100'
    },
    {
      id: 'longitudinal',
      title: 'Longitudinal Impact Report',
      description: 'Multi-year trends and sustained impact analysis',
      icon: TrendingUp,
      color: 'text-pink-600',
      bgColor: 'bg-pink-100'
    }
  ];

  const reportPeriods = [
    { value: 'monthly', label: 'Monthly' },
    { value: 'quarterly', label: 'Quarterly' },
    { value: 'annual', label: 'Annual' },
    { value: 'custom', label: 'Custom Period' }
  ];

  const mockReportData = {
    comprehensive: {
      summary: 'This comprehensive report covers all impact measurement areas for the selected period.',
      keyMetrics: [
        { label: 'Students Impacted', value: '2,847', change: '+12.3%' },
        { label: 'Schools Served', value: '23', change: '+2' },
        { label: 'Teachers Trained', value: '156', change: '+28.4%' },
        { label: 'Avg Learning Improvement', value: '21.4%', change: '+3.2%' }
      ],
      sections: [
        'Executive Summary',
        'Student Learning Outcomes',
        'School Performance Metrics',
        'Teacher Training Impact',
        'Beneficiary Feedback Analysis',
        'Longitudinal Trends',
        'Recommendations'
      ]
    },
    'student-outcomes': {
      summary: 'Detailed analysis of student academic progress and learning outcomes.',
      keyMetrics: [
        { label: 'Students Assessed', value: '1,247', change: '+8.7%' },
        { label: 'Avg Improvement', value: '21.4%', change: '+3.2%' },
        { label: 'Subjects Tracked', value: '5', change: '0' },
        { label: 'Success Rate', value: '89.3%', change: '+5.1%' }
      ],
      sections: [
        'Assessment Overview',
        'Subject-wise Performance',
        'Grade Progression Analysis',
        'Learning Improvement Trends',
        'Recommendations'
      ]
    }
  };

  const selectedReport = mockReportData[selectedReportType as keyof typeof mockReportData] || mockReportData.comprehensive;

  const handleGenerateReport = () => {
    // Mock report generation
    console.log('Generating report:', selectedReportType, selectedPeriod);
    // In real implementation, this would trigger report generation
  };

  const handleDownloadReport = (format: string) => {
    // Mock download
    console.log('Downloading report in format:', format);
    // In real implementation, this would download the report
  };

  // Access control is now handled by AdminOnlyWrapper

  return (
    <PageLayout>
      <PageHeader
        title="Impact Reports"
        description="Generate comprehensive impact measurement reports"
        icon={FileText}
        actions={[
          {
            label: 'Filters',
            onClick: () => {},
            icon: Filter,
            variant: 'outline'
          },
          {
            label: 'Date Range',
            onClick: () => {},
            icon: Calendar,
            variant: 'outline'
          }
        ]}
      />

      {/* Report Configuration */}
      <Card>
        <CardHeader>
          <CardTitle>Report Configuration</CardTitle>
          <CardDescription>
            Select report type and parameters for generation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Type
              </label>
              <Select value={selectedReportType} onValueChange={setSelectedReportType}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {reportTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      <div className="flex items-center space-x-2">
                        <type.icon className="h-4 w-4" />
                        <span>{type.title}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Report Period
              </label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {reportPeriods.map((period) => (
                    <SelectItem key={period.value} value={period.value}>
                      {period.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="mt-6 flex justify-end">
            <Button 
              onClick={handleGenerateReport}
              className="bg-ilead-green hover:bg-ilead-dark-green"
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Report Preview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Report Preview</CardTitle>
              <CardDescription>
                {reportTypes.find(t => t.id === selectedReportType)?.description}
              </CardDescription>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDownloadReport('pdf')}
              >
                <Download className="h-4 w-4 mr-2" />
                PDF
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDownloadReport('excel')}
              >
                <Download className="h-4 w-4 mr-2" />
                Excel
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDownloadReport('csv')}
              >
                <Download className="h-4 w-4 mr-2" />
                CSV
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Report Summary */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-2">Report Summary</h3>
            <p className="text-gray-600">{selectedReport.summary}</p>
          </div>
          
          {/* Key Metrics */}
          <div className="mb-6">
            <h3 className="text-lg font-semibold mb-4">Key Metrics</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {selectedReport.keyMetrics.map((metric, index) => (
                <div key={index} className="p-4 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-600">{metric.label}</p>
                  <p className="text-2xl font-bold text-gray-900">{metric.value}</p>
                  <p className={`text-sm ${
                    metric.change.startsWith('+') ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.change}
                  </p>
                </div>
              ))}
            </div>
          </div>
          
          {/* Report Sections */}
          <div>
            <h3 className="text-lg font-semibold mb-4">Report Sections</h3>
            <div className="space-y-2">
              {selectedReport.sections.map((section, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <span className="font-medium">{section}</span>
                  <span className="text-sm text-gray-500">Section {index + 1}</span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Available Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Available Report Types</CardTitle>
          <CardDescription>
            Choose from various impact measurement report formats
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {reportTypes.map((type) => {
              const Icon = type.icon;
              return (
                <div
                  key={type.id}
                  className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                    selectedReportType === type.id
                      ? 'border-ilead-green bg-green-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedReportType(type.id)}
                >
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`${type.bgColor} p-2 rounded-lg`}>
                      <Icon className={`h-5 w-5 ${type.color}`} />
                    </div>
                    <h4 className="font-medium">{type.title}</h4>
                  </div>
                  <p className="text-sm text-gray-600">{type.description}</p>
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>
    </PageLayout>
  );
};

export default ImpactReports;
