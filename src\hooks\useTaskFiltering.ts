
import { useMemo } from 'react';
import { Database } from '@/integrations/supabase/types';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

interface UseTaskFilteringProps {
  tasks: Task[];
  searchTerm: string;
  statusFilter: string;
  priorityFilter: string;
  currentPage?: number;
  itemsPerPage?: number;
}

export const useTaskFiltering = ({
  tasks,
  searchTerm,
  statusFilter,
  priorityFilter,
  currentPage = 1,
  itemsPerPage = 10
}: UseTaskFilteringProps) => {
  const { filteredTasks, sortedTasks, paginatedTasks, totalPages } = useMemo(() => {
    // Filter logic
    const filtered = tasks.filter(task => {
      const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           task.description?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           task.assigned_to_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           task.school_name?.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesStatus = statusFilter === 'all' || task.status === statusFilter;
      const matchesPriority = priorityFilter === 'all' || task.priority === priorityFilter;
      
      return matchesSearch && matchesStatus && matchesPriority;
    });

    // Sort logic
    const sorted = filtered.sort((a, b) => {
      const priorityOrder = { 'urgent': 1, 'high': 2, 'medium': 3, 'low': 4 };
      const aPriority = priorityOrder[a.priority] || 5;
      const bPriority = priorityOrder[b.priority] || 5;
      
      if (aPriority !== bPriority) {
        return aPriority - bPriority;
      }
      
      if (a.due_date && b.due_date) {
        return new Date(a.due_date).getTime() - new Date(b.due_date).getTime();
      } else if (a.due_date) {
        return -1;
      } else if (b.due_date) {
        return 1;
      }
      
      return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
    });

    // Pagination logic
    const totalPages = Math.ceil(sorted.length / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const paginated = sorted.slice(startIndex, startIndex + itemsPerPage);

    return {
      filteredTasks: filtered,
      sortedTasks: sorted,
      paginatedTasks: paginated,
      totalPages
    };
  }, [tasks, searchTerm, statusFilter, priorityFilter, currentPage, itemsPerPage]);

  return {
    filteredTasks,
    sortedTasks,
    paginatedTasks,
    totalPages,
    totalItems: sortedTasks.length
  };
};
