-- Staff Management System Schema
-- This migration creates the infrastructure for comprehensive staff management
-- including bulk user creation, invitation tracking, and audit logging

-- Create user invitation status enum
CREATE TYPE invitation_status AS ENUM ('pending', 'sent', 'accepted', 'expired', 'failed');

-- Create audit action enum for logging
CREATE TYPE audit_action AS ENUM (
    'user_created', 
    'user_updated', 
    'user_deleted', 
    'user_activated', 
    'user_deactivated',
    'role_changed',
    'invitation_sent',
    'invitation_resent',
    'bulk_creation_started',
    'bulk_creation_completed'
);

-- User invitations table for tracking invitation process
CREATE TABLE user_invitations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHA<PERSON>(255) NOT NULL,
    role user_role NOT NULL DEFAULT 'field_staff',
    division_id UUID REFERENCES divisions(id),
    phone VARCHAR(20),
    invited_by UUID REFERENCES profiles(id) NOT NULL,
    invitation_token VARCHAR(255) UNIQUE NOT NULL,
    status invitation_status DEFAULT 'pending',
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    sent_at TIMESTAMP WITH TIME ZONE,
    accepted_at TIMESTAMP WITH TIME ZONE,
    failed_reason TEXT,
    retry_count INTEGER DEFAULT 0,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Staff management audit log
CREATE TABLE staff_audit_log (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    action audit_action NOT NULL,
    target_user_id UUID, -- Can be null for bulk operations
    target_email VARCHAR(255), -- For tracking before user creation
    performed_by UUID REFERENCES profiles(id) NOT NULL,
    details JSONB DEFAULT '{}', -- Flexible storage for action details
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bulk operation tracking
CREATE TABLE bulk_operations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    operation_type VARCHAR(50) NOT NULL, -- 'user_creation', 'user_update', etc.
    initiated_by UUID REFERENCES profiles(id) NOT NULL,
    total_records INTEGER NOT NULL,
    successful_records INTEGER DEFAULT 0,
    failed_records INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'in_progress', -- 'in_progress', 'completed', 'failed'
    error_summary JSONB DEFAULT '[]', -- Array of error details
    started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE,
    metadata JSONB DEFAULT '{}'
);

-- Add password reset tracking to profiles (extend existing table)
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS requires_password_change BOOLEAN DEFAULT false;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS last_password_change TIMESTAMP WITH TIME ZONE;
ALTER TABLE profiles ADD COLUMN IF NOT EXISTS invitation_accepted_at TIMESTAMP WITH TIME ZONE;

-- Create indexes for performance
CREATE INDEX idx_user_invitations_email ON user_invitations(email);
CREATE INDEX idx_user_invitations_status ON user_invitations(status);
CREATE INDEX idx_user_invitations_invited_by ON user_invitations(invited_by);
CREATE INDEX idx_user_invitations_expires_at ON user_invitations(expires_at);
CREATE INDEX idx_staff_audit_log_action ON staff_audit_log(action);
CREATE INDEX idx_staff_audit_log_target_user ON staff_audit_log(target_user_id);
CREATE INDEX idx_staff_audit_log_performed_by ON staff_audit_log(performed_by);
CREATE INDEX idx_staff_audit_log_created_at ON staff_audit_log(created_at);
CREATE INDEX idx_bulk_operations_initiated_by ON bulk_operations(initiated_by);
CREATE INDEX idx_bulk_operations_status ON bulk_operations(status);

-- Create updated_at trigger function if it doesn't exist
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_user_invitations_updated_at 
    BEFORE UPDATE ON user_invitations 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE bulk_operations ENABLE ROW LEVEL SECURITY;

-- RLS Policies for user_invitations
CREATE POLICY "Admins can manage all invitations" ON user_invitations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- RLS Policies for staff_audit_log
CREATE POLICY "Admins can view all audit logs" ON staff_audit_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "System can insert audit logs" ON staff_audit_log
    FOR INSERT WITH CHECK (true);

-- RLS Policies for bulk_operations
CREATE POLICY "Admins can view all bulk operations" ON bulk_operations
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );
