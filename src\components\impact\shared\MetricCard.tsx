import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { LucideIcon } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: LucideIcon;
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'indigo' | 'red' | 'yellow';
  trend?: {
    value: string;
    isPositive: boolean;
  };
  className?: string;
}

/**
 * Standardized metric card component for Impact Measurement modules
 * Provides consistent styling and layout for key performance indicators
 */
const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon: Icon,
  color = 'blue',
  trend,
  className
}) => {
  const colorClasses = {
    blue: {
      border: 'border-l-blue-500',
      iconBg: 'bg-blue-100',
      iconColor: 'text-blue-600'
    },
    green: {
      border: 'border-l-green-500',
      iconBg: 'bg-green-100',
      iconColor: 'text-green-600'
    },
    purple: {
      border: 'border-l-purple-500',
      iconBg: 'bg-purple-100',
      iconColor: 'text-purple-600'
    },
    orange: {
      border: 'border-l-orange-500',
      iconBg: 'bg-orange-100',
      iconColor: 'text-orange-600'
    },
    indigo: {
      border: 'border-l-indigo-500',
      iconBg: 'bg-indigo-100',
      iconColor: 'text-indigo-600'
    },
    red: {
      border: 'border-l-red-500',
      iconBg: 'bg-red-100',
      iconColor: 'text-red-600'
    },
    yellow: {
      border: 'border-l-yellow-500',
      iconBg: 'bg-yellow-100',
      iconColor: 'text-yellow-600'
    }
  };

  const colors = colorClasses[color];

  return (
    <Card className={cn(`border-l-4 ${colors.border}`, className)}>
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className={`${colors.iconBg} p-2 rounded-lg`}>
            <Icon className={`h-5 w-5 ${colors.iconColor}`} />
          </div>
          <div className="flex-1">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">{value}</p>
                <p className="text-sm text-gray-600">{title}</p>
              </div>
              {trend && (
                <div className={cn(
                  "text-xs font-medium px-2 py-1 rounded-full",
                  trend.isPositive 
                    ? "bg-green-100 text-green-800" 
                    : "bg-red-100 text-red-800"
                )}>
                  {trend.isPositive ? '+' : ''}{trend.value}
                </div>
              )}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MetricCard;
