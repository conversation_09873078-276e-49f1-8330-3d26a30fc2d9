import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Package, AlertCircle, Loader2, Plus, Minus, RotateCcw, TrendingUp, TrendingDown } from 'lucide-react';
import { BookWithInventory } from '@/hooks/useBookOperations';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

interface InventoryUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  book: BookWithInventory | null;
  onUpdate?: () => void;
}

type UpdateType = 'add' | 'subtract' | 'set' | 'adjust_damaged' | 'adjust_lost';

interface InventoryUpdateData {
  updateType: UpdateType;
  quantity: string;
  reason: string;
  notes: string;
}

const InventoryUpdateModal: React.FC<InventoryUpdateModalProps> = ({
  isOpen,
  onClose,
  book,
  onUpdate,
}) => {
  const [formData, setFormData] = useState<InventoryUpdateData>({
    updateType: 'add',
    quantity: '',
    reason: '',
    notes: '',
  });

  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  const [isUpdating, setIsUpdating] = useState(false);

  // Reset form when book changes
  useEffect(() => {
    if (book) {
      setFormData({
        updateType: 'add',
        quantity: '',
        reason: '',
        notes: '',
      });
      setValidationErrors([]);
    }
  }, [book]);

  const validateForm = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!formData.quantity.trim()) {
      errors.push('Quantity is required');
    } else {
      const quantity = parseInt(formData.quantity);
      if (isNaN(quantity) || quantity <= 0) {
        errors.push('Quantity must be a positive number');
      }

      // Additional validation for subtract operations
      if (formData.updateType === 'subtract' && book) {
        if (quantity > book.available_quantity) {
          errors.push(`Cannot subtract ${quantity}. Only ${book.available_quantity} available.`);
        }
      }
    }

    if (!formData.reason.trim()) {
      errors.push('Reason for update is required');
    }

    return { isValid: errors.length === 0, errors };
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!book) return;

    const validation = validateForm();
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      return;
    }

    setIsUpdating(true);

    try {
      const quantity = parseInt(formData.quantity);
      let newTotalQuantity = book.total_quantity;
      let newAvailableQuantity = book.available_quantity;
      let newDamagedQuantity = book.damaged_quantity;
      let newLostQuantity = book.lost_quantity;

      // Calculate new quantities based on update type
      switch (formData.updateType) {
        case 'add':
          newTotalQuantity += quantity;
          newAvailableQuantity += quantity;
          break;
        case 'subtract':
          newTotalQuantity -= quantity;
          newAvailableQuantity -= quantity;
          break;
        case 'set': {
          const difference = quantity - book.total_quantity;
          newTotalQuantity = quantity;
          newAvailableQuantity = book.available_quantity + difference;
          break;
        }
        case 'adjust_damaged':
          newDamagedQuantity = quantity;
          // Recalculate available quantity
          newAvailableQuantity = newTotalQuantity - book.distributed_quantity - newDamagedQuantity - newLostQuantity;
          break;
        case 'adjust_lost':
          newLostQuantity = quantity;
          // Recalculate available quantity
          newAvailableQuantity = newTotalQuantity - book.distributed_quantity - newDamagedQuantity - newLostQuantity;
          break;
      }

      // Ensure quantities don't go negative
      if (newTotalQuantity < 0 || newAvailableQuantity < 0) {
        throw new Error('Update would result in negative quantities');
      }

      // Get the inventory ID from the book
      const { data: inventoryData, error: inventoryError } = await supabase
        .from('book_inventory')
        .select('id')
        .eq('book_id', book.id)
        .single();

      if (inventoryError) {
        throw inventoryError;
      }

      // Update inventory using the database function
      const { error: updateError } = await supabase.rpc('update_book_inventory', {
        p_inventory_id: inventoryData.id,
        p_total_quantity: newTotalQuantity,
        p_available_quantity: newAvailableQuantity,
        p_distributed_quantity: book.distributed_quantity,
        p_damaged_quantity: newDamagedQuantity,
        p_lost_quantity: newLostQuantity,
        p_notes: formData.notes.trim() || null,
      });

      if (updateError) {
        throw updateError;
      }

      // Log the inventory update activity
      await supabase.rpc('log_activity', {
        p_activity_type: 'inventory_update',
        p_user_id: (await supabase.auth.getUser()).data.user?.id,
        p_entity_type: 'book',
        p_entity_id: book.id,
        p_description: `Inventory updated: ${formData.updateType} ${quantity} units. Reason: ${formData.reason}`,
        p_metadata: {
          update_type: formData.updateType,
          quantity_change: quantity,
          reason: formData.reason,
          previous_total: book.total_quantity,
          new_total: newTotalQuantity,
          previous_available: book.available_quantity,
          new_available: newAvailableQuantity,
        },
      });

      setValidationErrors([]);
      onClose();
      onUpdate?.();
      toast.success('Inventory updated successfully!');
    } catch (error) {
      console.error('Failed to update inventory:', error);
      toast.error('Failed to update inventory. Please try again.');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleClose = () => {
    setValidationErrors([]);
    onClose();
  };

  const updateField = (field: keyof InventoryUpdateData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear validation errors when user starts typing
    if (validationErrors.length > 0) {
      setValidationErrors([]);
    }
  };

  const getUpdateTypeIcon = (type: UpdateType) => {
    switch (type) {
      case 'add': return <Plus className="h-4 w-4" />;
      case 'subtract': return <Minus className="h-4 w-4" />;
      case 'set': return <RotateCcw className="h-4 w-4" />;
      case 'adjust_damaged': return <TrendingDown className="h-4 w-4" />;
      case 'adjust_lost': return <TrendingDown className="h-4 w-4" />;
      default: return <Package className="h-4 w-4" />;
    }
  };

  const getUpdateTypeDescription = (type: UpdateType) => {
    switch (type) {
      case 'add': return 'Add new stock (e.g., new delivery, returns)';
      case 'subtract': return 'Remove stock (e.g., damaged, lost, corrections)';
      case 'set': return 'Set total quantity to specific amount';
      case 'adjust_damaged': return 'Update damaged quantity count';
      case 'adjust_lost': return 'Update lost quantity count';
      default: return '';
    }
  };

  if (!book) return null;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5 text-blue-600" />
            Update Inventory: {book.title}
          </DialogTitle>
          <DialogDescription>
            Make adjustments to book inventory quantities. All changes will be logged for audit purposes.
          </DialogDescription>
        </DialogHeader>

        {/* Current Inventory Status */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Current Inventory Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="text-center p-3 bg-blue-50 rounded">
                <p className="font-medium text-blue-600">{book.total_quantity}</p>
                <p className="text-xs text-gray-600">Total</p>
              </div>
              <div className="text-center p-3 bg-green-50 rounded">
                <p className="font-medium text-green-600">{book.available_quantity}</p>
                <p className="text-xs text-gray-600">Available</p>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded">
                <p className="font-medium text-orange-600">{book.distributed_quantity}</p>
                <p className="text-xs text-gray-600">Distributed</p>
              </div>
              <div className="text-center p-3 bg-red-50 rounded">
                <p className="font-medium text-red-600">{book.damaged_quantity + book.lost_quantity}</p>
                <p className="text-xs text-gray-600">Damaged/Lost</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Update Type */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Update Type</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="updateType">Type of Update *</Label>
                <Select value={formData.updateType} onValueChange={(value: UpdateType) => updateField('updateType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="add">
                      <div className="flex items-center gap-2">
                        <Plus className="h-4 w-4" />
                        Add Stock
                      </div>
                    </SelectItem>
                    <SelectItem value="subtract">
                      <div className="flex items-center gap-2">
                        <Minus className="h-4 w-4" />
                        Remove Stock
                      </div>
                    </SelectItem>
                    <SelectItem value="set">
                      <div className="flex items-center gap-2">
                        <RotateCcw className="h-4 w-4" />
                        Set Total Quantity
                      </div>
                    </SelectItem>
                    <SelectItem value="adjust_damaged">
                      <div className="flex items-center gap-2">
                        <TrendingDown className="h-4 w-4" />
                        Adjust Damaged Count
                      </div>
                    </SelectItem>
                    <SelectItem value="adjust_lost">
                      <div className="flex items-center gap-2">
                        <TrendingDown className="h-4 w-4" />
                        Adjust Lost Count
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-gray-600">
                  {getUpdateTypeDescription(formData.updateType)}
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="quantity">Quantity *</Label>
                <Input
                  id="quantity"
                  type="number"
                  value={formData.quantity}
                  onChange={(e) => updateField('quantity', e.target.value)}
                  placeholder="Enter quantity"
                  min="1"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="reason">Reason for Update *</Label>
                <Select value={formData.reason} onValueChange={(value) => updateField('reason', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select reason" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="new_delivery">New Delivery</SelectItem>
                    <SelectItem value="stock_return">Stock Return</SelectItem>
                    <SelectItem value="inventory_correction">Inventory Correction</SelectItem>
                    <SelectItem value="damaged_books">Damaged Books</SelectItem>
                    <SelectItem value="lost_books">Lost Books</SelectItem>
                    <SelectItem value="theft">Theft</SelectItem>
                    <SelectItem value="donation">Donation</SelectItem>
                    <SelectItem value="audit_adjustment">Audit Adjustment</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="notes">Additional Notes</Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => updateField('notes', e.target.value)}
                  placeholder="Provide additional details about this inventory update..."
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isUpdating}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isUpdating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                <>
                  {getUpdateTypeIcon(formData.updateType)}
                  <span className="ml-2">Update Inventory</span>
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default InventoryUpdateModal;
