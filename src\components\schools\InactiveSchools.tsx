
import React from 'react';
import { UserX } from 'lucide-react';
import SchoolManagement from '../SchoolManagement';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

const InactiveSchools = () => {
  const { profile } = useAuth();

  return (
    <PageLayout>
      <PageHeader
        title="Inactive Schools"
        description="View and manage inactive schools"
        icon={UserX}
      />

      <ContentCard noPadding>
        <SchoolManagement currentUser={profile} statusFilter="inactive" />
      </ContentCard>
    </PageLayout>
  );
};

export default InactiveSchools;
