
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RefreshCw, CheckSquare, BookOpen, School, MessageSquare, User, Filter, ChevronDown } from 'lucide-react';
import { useRecentActivities, formatActivityTime, getActivityIcon, getActivityColor } from '@/hooks/useActivities';

interface ActivityFeedProps {
  limit?: number;
  className?: string;
  showFilters?: boolean;
  enableInfiniteScroll?: boolean;
}

const ActivityFeed: React.FC<ActivityFeedProps> = ({
  limit = 10,
  className,
  showFilters = false,
  enableInfiniteScroll = false
}) => {
  const [activityTypeFilter, setActivityTypeFilter] = useState<string>('all');
  const [currentLimit, setCurrentLimit] = useState(limit);
  const [showFilterPanel, setShowFilterPanel] = useState(false);
  
  const { data: activities = [], isLoading, error, refetch } = useRecentActivities(currentLimit);

  // Filter activities based on selected filters
  const filteredActivities = React.useMemo(() => {
    if (activityTypeFilter === 'all') {
      return activities;
    }
    return activities.filter(activity => activity.activity_type === activityTypeFilter);
  }, [activities, activityTypeFilter]);

  const loadMoreActivities = () => {
    setCurrentLimit(prev => prev + 10);
  };

  const resetFilters = () => {
    setActivityTypeFilter('all');
    setCurrentLimit(limit);
  };

  if (error) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Activity Feed
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-4 text-red-600">
            Error loading activities. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Activity Feed
          <div className="flex items-center gap-2">
            {showFilters && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowFilterPanel(!showFilterPanel)}
              >
                <Filter className="h-4 w-4 mr-1" />
                <ChevronDown className={`h-3 w-3 transition-transform ${showFilterPanel ? 'rotate-180' : ''}`} />
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardTitle>

        {/* Filter Panel */}
        {showFilters && showFilterPanel && (
          <div className="space-y-3 pt-4 border-t">
            <div className="flex items-center gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium text-gray-700 mb-1 block">
                  Activity Type
                </label>
                <Select value={activityTypeFilter} onValueChange={setActivityTypeFilter}>
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder="Filter by activity type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Activities</SelectItem>
                    <SelectItem value="task_created">Task Created</SelectItem>
                    <SelectItem value="task_updated">Task Updated</SelectItem>
                    <SelectItem value="task_completed">Task Completed</SelectItem>
                    <SelectItem value="distribution_logged">Distribution Logged</SelectItem>
                    <SelectItem value="school_added">School Added</SelectItem>
                    <SelectItem value="comment_added">Comment Added</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-end">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={resetFilters}
                  className="mb-0"
                >
                  Reset
                </Button>
              </div>
            </div>
          </div>
        )}
      </CardHeader>
      <CardContent className="max-h-96 overflow-y-auto scrollbar-thin">
        {isLoading ? (
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-start space-x-3">
                <div className="w-8 h-8 bg-gray-200 rounded-full animate-pulse" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded animate-pulse" />
                  <div className="h-3 bg-gray-200 rounded w-1/2 animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        ) : filteredActivities.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            {activityTypeFilter === 'all'
              ? 'No recent activities to display.'
              : 'No activities match the selected filter.'
            }
          </div>
        ) : (
          <div className="space-y-4">
            {filteredActivities.map((activity) => {
              const iconEmoji = getActivityIcon(activity.activity_type);
              const colorClasses = getActivityColor(activity.activity_type);
              
              return (
                <div key={activity.id} className="flex items-start space-x-3">
                  <div className={`p-2 rounded-full ${colorClasses}`}>
                    <span className="text-sm">{iconEmoji}</span>
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm text-gray-900">
                      <span className="font-medium">{activity.user_name}</span>{' '}
                      {activity.description}
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatActivityTime(activity.created_at)}
                    </p>
                  </div>
                </div>
              );
            })}

            {/* Load More Button for Infinite Scroll */}
            {enableInfiniteScroll && filteredActivities.length >= currentLimit && (
              <div className="text-center pt-4">
                <Button
                  variant="outline"
                  onClick={loadMoreActivities}
                  disabled={isLoading}
                >
                  {isLoading ? 'Loading...' : 'Load More Activities'}
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ActivityFeed;
