
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --header-height: 7rem; /* 112px - increased to ensure proper clearance with subtitle */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 258 47% 29%; /* iLEAD purple (was green) */
    --primary-foreground: 210 40% 98%;

    --secondary: 27 81% 52%; /* iLEAD orange */
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 258 47% 29%; /* iLEAD purple (was green) */

    --sidebar-background: 0 0% 100%;
    --sidebar-foreground: 222.2 84% 4.9%;
    --sidebar-primary: 258 47% 29%;
    --sidebar-primary-foreground: 210 40% 98%;
    --sidebar-accent: 27 81% 52%;
    --sidebar-accent-foreground: 222.2 47.4% 11.2%;
    --sidebar-border: 214.3 31.8% 91.4%;
    --sidebar-ring: 258 47% 29%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 258 47% 29%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 27 81% 52%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 258 47% 29%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 258 47% 29%;
    --sidebar-primary-foreground: 222.2 47.4% 11.2%;
    --sidebar-accent: 27 81% 52%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 258 47% 29%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom Scrollbar Styles */
@layer utilities {
  /* Webkit browsers (Chrome, Safari, Edge) */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: rgb(209 213 219) rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: rgb(209 213 219);
    border-radius: 4px;
    border: 1px solid rgb(243 244 246);
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: rgb(156 163 175);
  }

  /* Force scrollbar to always be visible */
  .scrollbar-thin::-webkit-scrollbar-thumb {
    min-height: 20px;
  }

  /* Alternative scrollbar that's always visible */
  .scrollbar-visible {
    scrollbar-width: auto;
    scrollbar-color: rgb(156 163 175) rgb(243 244 246);
    overflow-y: scroll !important;
  }

  .scrollbar-visible::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    background: rgb(243 244 246);
  }

  .scrollbar-visible::-webkit-scrollbar-track {
    background: rgb(243 244 246);
    border-radius: 6px;
    margin: 2px;
  }

  .scrollbar-visible::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 6px;
    min-height: 40px;
    border: 2px solid rgb(243 244 246);
  }

  .scrollbar-visible::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  .scrollbar-visible::-webkit-scrollbar-corner {
    background: rgb(243 244 246);
  }

  /* Custom scrollbar for main content areas */
  .scrollbar-content {
    scrollbar-width: thin;
    scrollbar-color: rgb(156 163 175) rgb(249 250 251);
  }

  .scrollbar-content::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-content::-webkit-scrollbar-track {
    background: rgb(249 250 251);
    border-radius: 4px;
  }

  .scrollbar-content::-webkit-scrollbar-thumb {
    background: rgb(156 163 175);
    border-radius: 4px;
  }

  .scrollbar-content::-webkit-scrollbar-thumb:hover {
    background: rgb(107 114 128);
  }

  /* Hide scrollbar but keep functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Standardized page spacing utilities */
  .page-container {
    @apply p-4 sm:p-6 lg:p-8 space-y-6;
  }

  /* Mobile-specific utilities */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Mobile-friendly button sizes */
  .btn-mobile {
    @apply min-h-[44px] min-w-[44px] touch-manipulation;
  }

  /* Mobile-friendly form inputs */
  .input-mobile {
    @apply min-h-[44px] text-base;
  }

  /* Mobile table improvements */
  .table-mobile-scroll {
    @apply overflow-x-auto scrollbar-thin;
  }

  /* Mobile card spacing */
  .card-mobile {
    @apply p-4 space-y-3;
  }

  .page-header {
    @apply flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4;
  }

  .page-title {
    @apply text-2xl sm:text-3xl font-bold text-gray-900 leading-tight;
  }

  .page-description {
    @apply text-gray-600 mt-1 text-sm sm:text-base;
  }

  .content-section {
    @apply space-y-4;
  }

  .action-buttons {
    @apply flex items-center gap-2 flex-wrap;
  }
}
