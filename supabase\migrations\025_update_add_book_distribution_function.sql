-- Update add_book_distribution function to support status and distribution_date parameters
-- This migration adds support for the new distribution logging requirements

-- Drop the existing function
DROP FUNCTION IF EXISTS add_book_distribution(UUID, UUID, INTEGER, UUID, TEXT, VARCHAR);

-- Create the updated function with new parameters
CREATE OR REPLACE FUNCTION add_book_distribution(
    p_school_id UUID,
    p_inventory_id UUID,
    p_quantity INTEGER,
    p_supervisor_id UUID,
    p_notes TEXT,
    p_book_title VARCHAR, -- This parameter is kept for compatibility but not used
    p_status TEXT DEFAULT 'completed',
    p_distribution_date DATE DEFAULT CURRENT_DATE
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    distribution_id UUID;
    current_available_qty INTEGER;
    book_id_var UUID;
BEGIN
    -- Validate inputs
    IF p_quantity <= 0 THEN
        RAISE EXCEPTION 'Quantity must be greater than 0';
    END IF;

    -- Check if school exists
    IF NOT EXISTS (SELECT 1 FROM schools WHERE id = p_school_id) THEN
        RAISE EXCEPTION 'School not found';
    END IF;

    -- Check if supervisor exists
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = p_supervisor_id) THEN
        RAISE EXCEPTION 'Supervisor not found';
    END IF;

    -- Get inventory details and check availability
    SELECT bi.available_quantity, bi.book_id
    INTO current_available_qty, book_id_var
    FROM book_inventory bi
    WHERE bi.id = p_inventory_id;

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Book inventory not found';
    END IF;

    IF current_available_qty < p_quantity THEN
        RAISE EXCEPTION 'Insufficient inventory. Available: %, Requested: %', current_available_qty, p_quantity;
    END IF;

    -- Validate status
    IF p_status NOT IN ('planned', 'in_progress', 'completed', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status. Must be one of: planned, in_progress, completed, cancelled';
    END IF;

    -- Create distribution record
    INSERT INTO book_distributions (
        book_id,
        inventory_id,
        school_id,
        quantity,
        planned_date,
        actual_delivery_date,
        status,
        supervisor_id,
        notes,
        created_by,
        created_at,
        updated_at
    ) VALUES (
        book_id_var,
        p_inventory_id,
        p_school_id,
        p_quantity,
        p_distribution_date,
        CASE WHEN p_status = 'completed' THEN p_distribution_date ELSE NULL END,
        p_status::distribution_status,
        p_supervisor_id,
        p_notes,
        p_supervisor_id,
        NOW(),
        NOW()
    ) RETURNING id INTO distribution_id;

    -- Update inventory only if status is completed
    IF p_status = 'completed' THEN
        UPDATE book_inventory
        SET
            available_quantity = available_quantity - p_quantity,
            distributed_quantity = distributed_quantity + p_quantity,
            updated_at = NOW()
        WHERE id = p_inventory_id;
    END IF;

    RETURN distribution_id;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION add_book_distribution TO authenticated;

-- Update the function signature in the database types
COMMENT ON FUNCTION add_book_distribution IS 'Creates a new book distribution with support for status and distribution date';
