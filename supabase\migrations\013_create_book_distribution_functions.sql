-- Book Distribution RPC Functions
-- This migration creates the missing RPC functions for book distribution feature
-- Priority: CRITICAL - Required for frontend components to function

-- Function to get book distributions with joins
CREATE OR REPLACE FUNCTION get_book_distributions()
RETURNS TABLE (
    id UUID,
    school_id UUID,
    school_name VARCHAR,
    book_title VARCHAR,
    quantity INTEGER,
    supervisor_id UUID,
    supervisor_name VARCHAR,
    delivery_date TEXT,
    notes TEXT,
    status VARCHAR
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bd.id,
        bd.school_id,
        s.name as school_name,
        bi.book_title,
        bd.quantity,
        bd.supervisor_id,
        p.name as supervisor_name,
        bd.delivery_date::TEXT,
        COALESCE(bd.notes, '') as notes,
        bd.status
    FROM book_distributions bd
    LEFT JOIN schools s ON bd.school_id = s.id
    LEFT JOIN book_inventory bi ON bd.inventory_id = bi.id  
    LEFT JOIN profiles p ON bd.supervisor_id = p.id
    ORDER BY bd.delivery_date DESC;
END;
$$;

-- Function to get book inventory
CREATE OR REPLACE FUNCTION get_book_inventory()
RETURNS TABLE (
    id UUID,
    book_title VARCHAR,
    quantity_available INTEGER,
    language VARCHAR,
    grade_level VARCHAR,
    subject VARCHAR
)
LANGUAGE plpgsql
SECURITY DEFINER  
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bi.id,
        bi.book_title,
        bi.quantity_available,
        COALESCE(bi.language, '') as language,
        COALESCE(bi.grade_level, '') as grade_level,
        COALESCE(bi.subject, '') as subject
    FROM book_inventory bi
    WHERE bi.quantity_available > 0
    ORDER BY bi.book_title;
END;
$$;

-- Function to add book distribution with inventory management
CREATE OR REPLACE FUNCTION add_book_distribution(
    p_school_id UUID,
    p_inventory_id UUID,
    p_quantity INTEGER,
    p_supervisor_id UUID,
    p_notes TEXT,
    p_book_title VARCHAR -- This parameter is kept for compatibility but not used
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    distribution_id UUID;
    available_quantity INTEGER;
    book_title_var VARCHAR;
BEGIN
    -- Validate inputs
    IF p_quantity <= 0 THEN
        RAISE EXCEPTION 'Quantity must be greater than 0';
    END IF;
    
    -- Check if school exists
    IF NOT EXISTS (SELECT 1 FROM schools WHERE id = p_school_id) THEN
        RAISE EXCEPTION 'School not found';
    END IF;
    
    -- Check if supervisor exists
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = p_supervisor_id) THEN
        RAISE EXCEPTION 'Supervisor not found';
    END IF;
    
    -- Check if inventory item exists and get available quantity
    SELECT quantity_available, book_title 
    INTO available_quantity, book_title_var
    FROM book_inventory 
    WHERE id = p_inventory_id;
    
    IF available_quantity IS NULL THEN
        RAISE EXCEPTION 'Book inventory item not found';
    END IF;
    
    IF available_quantity < p_quantity THEN
        RAISE EXCEPTION 'Insufficient inventory. Available: %, Requested: %', available_quantity, p_quantity;
    END IF;
    
    -- Insert distribution record
    INSERT INTO book_distributions (
        school_id, 
        inventory_id, 
        quantity, 
        supervisor_id, 
        notes,
        status
    ) VALUES (
        p_school_id, 
        p_inventory_id, 
        p_quantity, 
        p_supervisor_id, 
        COALESCE(p_notes, ''),
        'completed'
    ) RETURNING id INTO distribution_id;
    
    -- Update inventory quantity
    UPDATE book_inventory 
    SET quantity_available = quantity_available - p_quantity,
        updated_at = NOW()
    WHERE id = p_inventory_id;
    
    RETURN distribution_id;
END;
$$;

-- Function to get distribution details with photos
CREATE OR REPLACE FUNCTION get_distribution_details(p_distribution_id UUID)
RETURNS TABLE (
    id UUID,
    school_id UUID,
    school_name VARCHAR,
    book_title VARCHAR,
    quantity INTEGER,
    supervisor_id UUID,
    supervisor_name VARCHAR,
    delivery_date TEXT,
    notes TEXT,
    status VARCHAR,
    photos JSON
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bd.id,
        bd.school_id,
        s.name as school_name,
        bi.book_title,
        bd.quantity,
        bd.supervisor_id,
        p.name as supervisor_name,
        bd.delivery_date::TEXT,
        COALESCE(bd.notes, '') as notes,
        bd.status,
        COALESCE(
            (SELECT json_agg(
                json_build_object(
                    'id', dp.id,
                    'photo_url', dp.photo_url,
                    'caption', dp.caption,
                    'uploaded_by', dp.uploaded_by,
                    'created_at', dp.created_at
                )
            ) FROM distribution_photos dp WHERE dp.distribution_id = bd.id),
            '[]'::json
        ) as photos
    FROM book_distributions bd
    LEFT JOIN schools s ON bd.school_id = s.id
    LEFT JOIN book_inventory bi ON bd.inventory_id = bi.id  
    LEFT JOIN profiles p ON bd.supervisor_id = p.id
    WHERE bd.id = p_distribution_id;
END;
$$;

-- Function to update distribution status
CREATE OR REPLACE FUNCTION update_distribution_status(
    p_distribution_id UUID,
    p_status VARCHAR,
    p_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Validate status
    IF p_status NOT IN ('planned', 'in_progress', 'completed', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status. Must be one of: planned, in_progress, completed, cancelled';
    END IF;
    
    -- Check if distribution exists and user has permission
    IF NOT EXISTS (
        SELECT 1 FROM book_distributions bd
        WHERE bd.id = p_distribution_id
        AND (
            bd.supervisor_id = auth.uid()
            OR EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role IN ('admin', 'program_officer'))
        )
    ) THEN
        RAISE EXCEPTION 'Distribution not found or insufficient permissions';
    END IF;
    
    -- Update the distribution
    UPDATE book_distributions 
    SET 
        status = p_status,
        notes = CASE WHEN p_notes IS NOT NULL THEN p_notes ELSE notes END,
        updated_at = NOW()
    WHERE id = p_distribution_id;
    
    RETURN TRUE;
END;
$$;

-- Function to get distribution statistics
CREATE OR REPLACE FUNCTION get_distribution_statistics()
RETURNS TABLE (
    total_distributions BIGINT,
    total_books_distributed BIGINT,
    distributions_this_month BIGINT,
    books_this_month BIGINT,
    active_distributions BIGINT,
    schools_served BIGINT,
    top_books JSON
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*) FROM book_distributions) as total_distributions,
        (SELECT COALESCE(SUM(quantity), 0) FROM book_distributions) as total_books_distributed,
        (SELECT COUNT(*) FROM book_distributions 
         WHERE delivery_date >= date_trunc('month', CURRENT_DATE)) as distributions_this_month,
        (SELECT COALESCE(SUM(quantity), 0) FROM book_distributions 
         WHERE delivery_date >= date_trunc('month', CURRENT_DATE)) as books_this_month,
        (SELECT COUNT(*) FROM book_distributions 
         WHERE status IN ('planned', 'in_progress')) as active_distributions,
        (SELECT COUNT(DISTINCT school_id) FROM book_distributions) as schools_served,
        (SELECT COALESCE(
            json_agg(
                json_build_object(
                    'book_title', bi.book_title,
                    'total_distributed', dist_stats.total_quantity
                ) ORDER BY dist_stats.total_quantity DESC
            ), '[]'::json
        ) FROM (
            SELECT bd.inventory_id, SUM(bd.quantity) as total_quantity
            FROM book_distributions bd
            GROUP BY bd.inventory_id
            ORDER BY total_quantity DESC
            LIMIT 5
        ) dist_stats
        LEFT JOIN book_inventory bi ON dist_stats.inventory_id = bi.id
        ) as top_books;
END;
$$;

-- Function to add inventory item
CREATE OR REPLACE FUNCTION add_book_inventory(
    p_book_title VARCHAR,
    p_quantity_available INTEGER,
    p_language VARCHAR DEFAULT NULL,
    p_grade_level VARCHAR DEFAULT NULL,
    p_subject VARCHAR DEFAULT NULL,
    p_isbn VARCHAR DEFAULT NULL,
    p_publisher VARCHAR DEFAULT NULL,
    p_publication_year INTEGER DEFAULT NULL,
    p_cost_per_unit DECIMAL DEFAULT NULL,
    p_description TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    inventory_id UUID;
BEGIN
    -- Check permissions
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to add inventory';
    END IF;
    
    -- Validate inputs
    IF p_book_title IS NULL OR trim(p_book_title) = '' THEN
        RAISE EXCEPTION 'Book title is required';
    END IF;
    
    IF p_quantity_available < 0 THEN
        RAISE EXCEPTION 'Quantity must be non-negative';
    END IF;
    
    -- Insert inventory item
    INSERT INTO book_inventory (
        book_title,
        quantity_available,
        language,
        grade_level,
        subject,
        isbn,
        publisher,
        publication_year,
        cost_per_unit,
        description
    ) VALUES (
        trim(p_book_title),
        p_quantity_available,
        p_language,
        p_grade_level,
        p_subject,
        p_isbn,
        p_publisher,
        p_publication_year,
        p_cost_per_unit,
        p_description
    ) RETURNING id INTO inventory_id;
    
    RETURN inventory_id;
END;
$$;

-- Function to update inventory quantity
CREATE OR REPLACE FUNCTION update_inventory_quantity(
    p_inventory_id UUID,
    p_quantity_change INTEGER,
    p_operation VARCHAR DEFAULT 'add' -- 'add' or 'set'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_quantity INTEGER;
    new_quantity INTEGER;
BEGIN
    -- Check permissions
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to update inventory';
    END IF;
    
    -- Get current quantity
    SELECT quantity_available INTO current_quantity
    FROM book_inventory
    WHERE id = p_inventory_id;
    
    IF current_quantity IS NULL THEN
        RAISE EXCEPTION 'Inventory item not found';
    END IF;
    
    -- Calculate new quantity
    IF p_operation = 'add' THEN
        new_quantity := current_quantity + p_quantity_change;
    ELSIF p_operation = 'set' THEN
        new_quantity := p_quantity_change;
    ELSE
        RAISE EXCEPTION 'Invalid operation. Must be "add" or "set"';
    END IF;
    
    -- Validate new quantity
    IF new_quantity < 0 THEN
        RAISE EXCEPTION 'Resulting quantity cannot be negative. Current: %, Change: %', current_quantity, p_quantity_change;
    END IF;
    
    -- Update quantity
    UPDATE book_inventory
    SET quantity_available = new_quantity,
        updated_at = NOW()
    WHERE id = p_inventory_id;
    
    RETURN TRUE;
END;
$$;
