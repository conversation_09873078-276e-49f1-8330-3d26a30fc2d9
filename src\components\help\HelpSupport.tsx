import React from 'react';
import { HelpCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import ContactSupport from './ContactSupport';
import { useAuth } from '@/hooks/useAuth';

interface HelpSupportProps {
  onViewChange?: (view: string) => void;
}

const HelpSupport: React.FC<HelpSupportProps> = ({ onViewChange }) => {
  const { profile } = useAuth();

  return (
    <PageLayout>
      <PageHeader
        title="Help & Support"
        description="Get help and contact our support team"
        icon={HelpCircle}
      />

      <ContentCard>
        <ContactSupport />
      </ContentCard>

      {/* Quick Access Card for Documentation (Admin Only) */}
      {profile?.role === 'admin' && (
        <ContentCard
          title="Administrator Resources"
          description="Additional resources available for system administrators"
          icon={HelpCircle}
          className="border-ilead-green/20 bg-ilead-green/5"
        >
          <p className="text-sm text-gray-600 mb-3">
            Access comprehensive system documentation, user guides, and administrative resources.
          </p>
          <Button
            variant="link"
            onClick={() => onViewChange?.('help-docs')}
            className="text-ilead-green hover:text-ilead-dark-green font-medium text-sm p-0 h-auto"
          >
            View System Documentation →
          </Button>
        </ContentCard>
      )}
    </PageLayout>
  );
};

export default HelpSupport;
