import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import {
  MapPin,
  Search,
  AlertTriangle,
  CheckCircle,
  Loader2,
  Navigation,
  Info
} from 'lucide-react';
import { toast } from 'sonner';

interface LocationData {
  latitude: number;
  longitude: number;
  accuracy: number;
  timestamp: number;
  source: 'manual' | 'gps' | 'network';
  address?: string;
  notes?: string;
}

interface ManualLocationEntryProps {
  onLocationSelected: (location: LocationData) => void;
  onCancel?: () => void;
  isLoading?: boolean;
  disabled?: boolean;
  showGPSRetry?: boolean;
  onGPSRetry?: () => void;
  className?: string;
}

const ManualLocationEntry: React.FC<ManualLocationEntryProps> = ({
  onLocationSelected,
  onCancel,
  isLoading = false,
  disabled = false,
  showGPSRetry = true,
  onGPSRetry,
  className = ''
}) => {
  const [latitude, setLatitude] = useState('');
  const [longitude, setLongitude] = useState('');
  const [address, setAddress] = useState('');
  const [notes, setNotes] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);
  const [isUsingCurrentLocation, setIsUsingCurrentLocation] = useState(false);

  // Validate coordinates
  const validateCoordinates = useCallback((lat: string, lng: string): boolean => {
    const latNum = parseFloat(lat);
    const lngNum = parseFloat(lng);
    
    if (isNaN(latNum) || isNaN(lngNum)) {
      setValidationError('Please enter valid numeric coordinates');
      return false;
    }
    
    if (latNum < -90 || latNum > 90) {
      setValidationError('Latitude must be between -90 and 90 degrees');
      return false;
    }
    
    if (lngNum < -180 || lngNum > 180) {
      setValidationError('Longitude must be between -180 and 180 degrees');
      return false;
    }
    
    setValidationError(null);
    return true;
  }, []);

  // Try to get network-based location as fallback
  const tryNetworkLocation = useCallback(async () => {
    setIsUsingCurrentLocation(true);
    setValidationError(null);
    
    try {
      // Try with low accuracy and longer timeout for network-based location
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(
          resolve,
          reject,
          {
            enableHighAccuracy: false,
            timeout: 10000,
            maximumAge: 300000, // 5 minutes cache acceptable for fallback
          }
        );
      });

      setLatitude(position.coords.latitude.toFixed(6));
      setLongitude(position.coords.longitude.toFixed(6));
      toast.success('Network location obtained successfully');
      
    } catch (error) {
      console.log('Network location also failed:', error);
      toast.warning('Network location unavailable. Please enter coordinates manually.');
    } finally {
      setIsUsingCurrentLocation(false);
    }
  }, []);

  // Handle manual location submission
  const handleSubmit = useCallback(async () => {
    if (!validateCoordinates(latitude, longitude)) {
      return;
    }

    setIsValidating(true);
    
    try {
      const locationData: LocationData = {
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude),
        accuracy: 1000, // Manual entry has low accuracy
        timestamp: Date.now(),
        source: 'manual',
        address: address.trim() || undefined,
        notes: notes.trim() || undefined,
      };

      // Basic validation for reasonable coordinates (rough bounds for Uganda)
      const lat = locationData.latitude;
      const lng = locationData.longitude;
      
      if (lat < -5 || lat > 5 || lng < 28 || lng > 36) {
        const proceed = window.confirm(
          'The coordinates appear to be outside Uganda. Are you sure this is correct?'
        );
        if (!proceed) {
          setIsValidating(false);
          return;
        }
      }

      onLocationSelected(locationData);
      toast.success('Manual location set successfully');
      
    } catch (error) {
      console.error('Manual location submission failed:', error);
      setValidationError('Failed to process location. Please try again.');
    } finally {
      setIsValidating(false);
    }
  }, [latitude, longitude, address, notes, validateCoordinates, onLocationSelected]);

  const isFormValid = latitude && longitude && !validationError;

  return (
    <Card className={`w-full max-w-md ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Manual Location Entry
        </CardTitle>
        <CardDescription>
          GPS unavailable? No problem! Try network location first, or enter coordinates manually.
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* GPS Retry Option */}
        {showGPSRetry && onGPSRetry && (
          <Alert className="border-blue-200 bg-blue-50">
            <Info className="h-4 w-4 text-blue-600" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-blue-800">GPS location failed</p>
                  <p className="text-sm text-blue-600">Try the options below or retry GPS if you've moved to a better location.</p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={onGPSRetry}
                  disabled={disabled || isLoading}
                  className="ml-3 border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  <Navigation className="h-4 w-4 mr-1" />
                  Retry GPS
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Network Location Fallback */}
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">Recommended</Badge>
            <span className="text-sm font-medium">Try Network Location First</span>
          </div>
          <Button
            variant="outline"
            onClick={tryNetworkLocation}
            disabled={disabled || isLoading || isUsingCurrentLocation}
            className="w-full border-green-300 text-green-700 hover:bg-green-50"
          >
            {isUsingCurrentLocation ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Search className="h-4 w-4 mr-2" />
            )}
            Use Network Location (Easier)
          </Button>
          <p className="text-xs text-gray-600">
            Uses your internet connection to estimate location. More accurate than manual entry.
          </p>
        </div>

        {/* Manual Coordinate Entry */}
        <div className="space-y-3">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Manual Coordinate Entry</span>
            <Badge variant="outline" className="text-xs">Advanced</Badge>
          </div>
          <p className="text-xs text-gray-600">
            Get coordinates from Google Maps, GPS device, or another mapping app.
            <br />
            <strong>Tip:</strong> Long-press on Google Maps to copy coordinates.
          </p>
          <div className="grid grid-cols-2 gap-3">
            <div>
              <Label htmlFor="latitude" className="text-sm">
                Latitude <span className="text-red-500">*</span>
              </Label>
              <Input
                id="latitude"
                type="number"
                step="any"
                placeholder="0.3476 (Uganda example)"
                value={latitude}
                onChange={(e) => setLatitude(e.target.value)}
                disabled={disabled || isLoading}
                className={validationError && latitude ? 'border-red-300' : ''}
              />
            </div>
            <div>
              <Label htmlFor="longitude" className="text-sm">
                Longitude <span className="text-red-500">*</span>
              </Label>
              <Input
                id="longitude"
                type="number"
                step="any"
                placeholder="32.5825 (Uganda example)"
                value={longitude}
                onChange={(e) => setLongitude(e.target.value)}
                disabled={disabled || isLoading}
                className={validationError && longitude ? 'border-red-300' : ''}
              />
            </div>
          </div>
        </div>

        {/* Address Description */}
        <div>
          <Label htmlFor="address">Address/Description (Optional)</Label>
          <Input
            id="address"
            placeholder="e.g., Near Kampala Central Market"
            value={address}
            onChange={(e) => setAddress(e.target.value)}
            disabled={disabled || isLoading}
          />
        </div>

        {/* Notes */}
        <div>
          <Label htmlFor="notes">Notes (Optional)</Label>
          <Textarea
            id="notes"
            placeholder="Additional location details or reason for manual entry"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            disabled={disabled || isLoading}
            rows={2}
          />
        </div>

        {/* Validation Error */}
        {validationError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div>
                <p className="font-medium">Invalid coordinates</p>
                <p className="text-sm">{validationError}</p>
                <p className="text-xs mt-1">
                  <strong>Valid ranges:</strong> Latitude: -90 to 90, Longitude: -180 to 180
                </p>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Accuracy Warning */}
        {!validationError && (latitude || longitude) && (
          <Alert className="border-yellow-200 bg-yellow-50">
            <Info className="h-4 w-4 text-yellow-600" />
            <AlertDescription>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium text-yellow-800">Lower accuracy expected</p>
                  <p className="text-sm text-yellow-600">Manual entry is less precise than GPS</p>
                </div>
                <Badge variant="outline" className="border-yellow-300 text-yellow-700">±1000m</Badge>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2 pt-2">
          {onCancel && (
            <Button
              variant="outline"
              onClick={onCancel}
              disabled={disabled || isLoading || isValidating}
              className="flex-1"
            >
              Cancel
            </Button>
          )}
          <Button
            onClick={handleSubmit}
            disabled={!isFormValid || disabled || isLoading || isValidating}
            className="flex-1"
          >
            {isValidating ? (
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <CheckCircle className="h-4 w-4 mr-2" />
            )}
            Use This Location
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ManualLocationEntry;
