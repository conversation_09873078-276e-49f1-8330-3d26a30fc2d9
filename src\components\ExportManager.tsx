
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Download, FileSpreadsheet, Calendar, Filter } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/useAuth';
import { Database } from '@/integrations/supabase/types';

// Type definitions for export data
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type Profile = Database['public']['Tables']['profiles']['Row'];

interface ExportManagerProps {
  dataType: 'distributions' | 'schools' | 'tasks' | 'reports';
  title: string;
}

const ExportManager = ({ dataType, title }: ExportManagerProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [exportConfig, setExportConfig] = useState({
    format: 'csv' as 'csv' | 'xlsx',
    dateRange: '30' as '7' | '30' | '90' | 'all',
    includePhotos: false,
    batchSize: 1000,
  });
  const [exporting, setExporting] = useState(false);
  const { toast } = useToast();
  const { profile } = useAuth();

  // Check if user has export permissions
  const canExport = profile?.role === 'admin' || profile?.role === 'program_officer';

  // Fetch data count for export estimation
  const { data: dataCount = 0 } = useQuery({
    queryKey: [dataType, 'count'],
    queryFn: async () => {
      const tableMap = {
        distributions: 'book_distributions',
        schools: 'schools',
        tasks: 'tasks',
        reports: 'field_reports'
      } as const;

      const tableName = tableMap[dataType];
      let query = supabase.from(tableName).select('*', { count: 'exact', head: true });

      if (exportConfig.dateRange !== 'all') {
        const days = parseInt(exportConfig.dateRange);
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        query = query.gte('created_at', cutoffDate.toISOString());
      }

      const { count, error } = await query;
      if (error) throw error;
      return count || 0;
    },
  });

  // Don't render the component if user doesn't have export permissions
  if (!canExport) {
    return null;
  }

  const exportData = async () => {
    try {
      setExporting(true);

      const tableMap = {
        distributions: 'book_distributions',
        schools: 'schools',
        tasks: 'tasks',
        reports: 'field_reports'
      } as const;

      const tableName = tableMap[dataType];

      // For large datasets, use batched export
      const totalBatches = Math.ceil(dataCount / exportConfig.batchSize);
      let allData: Record<string, unknown>[] = [];

      for (let batch = 0; batch < totalBatches; batch++) {
        const start = batch * exportConfig.batchSize;
        const end = start + exportConfig.batchSize - 1;

        let query = supabase.from(tableName).select('*').range(start, end);

        if (exportConfig.dateRange !== 'all') {
          const days = parseInt(exportConfig.dateRange);
          const cutoffDate = new Date();
          cutoffDate.setDate(cutoffDate.getDate() - days);
          query = query.gte('created_at', cutoffDate.toISOString());
        }

        const { data, error } = await query;
        if (error) throw error;
        
        allData = [...allData, ...(data || [])];
        
        // Update progress for large exports
        if (totalBatches > 1) {
          toast({
            title: "Exporting...",
            description: `Batch ${batch + 1} of ${totalBatches} complete`,
          });
        }
      }

      // Enhanced data formatting with related information
      let formattedData = allData;
      
      if (dataType === 'distributions') {
        // Fetch related school and supervisor names for distributions
        const schoolIds = [...new Set(allData.map(item => item.school_id as string))].filter(Boolean);
        const supervisorIds = [...new Set(allData.map(item => item.supervisor_id as string))].filter(Boolean);

        const { data: schools } = await supabase
          .rpc('get_schools_with_divisions');

        const { data: supervisors } = await supabase
          .from('profiles')
          .select('id, name')
          .in('id', supervisorIds);

        formattedData = allData.map(dist => ({
          ...dist,
          school_name: schools?.find((s: SchoolWithDivision) => s.id === dist.school_id)?.name || 'Unknown',
          supervisor_name: supervisors?.find((s: Profile) => s.id === dist.supervisor_id)?.name || 'Unknown',
        }));
      }

      // Generate CSV content
      if (formattedData.length === 0) {
        toast({
          title: "No Data",
          description: "No data found for the selected criteria",
          variant: "destructive",
        });
        return;
      }

      const headers = Object.keys(formattedData[0]);
      const csvContent = [
        headers.join(','),
        ...formattedData.map(row => 
          headers.map(header => {
            const value = row[header];
            return typeof value === 'string' && value.includes(',') 
              ? `"${value.replace(/"/g, '""')}"` 
              : value;
          }).join(',')
        )
      ].join('\n');

      // Download file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', `${dataType}_export_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast({
        title: "Success",
        description: `Exported ${formattedData.length} records successfully`,
      });
      
      setIsOpen(false);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Failed to export data";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setExporting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="bg-green-600 hover:bg-green-700 text-white">
          <Download className="h-4 w-4 mr-2" />
          Export {title}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Export {title}</DialogTitle>
          <DialogDescription>
            Configure export settings for {dataCount.toLocaleString()} records
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Date Range</label>
            <Select
              value={exportConfig.dateRange}
              onValueChange={(value: '7' | '30' | '90' | 'all') => setExportConfig({ ...exportConfig, dateRange: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                <SelectItem value="7">Last 7 days</SelectItem>
                <SelectItem value="30">Last 30 days</SelectItem>
                <SelectItem value="90">Last 90 days</SelectItem>
                <SelectItem value="all">All time</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Format</label>
            <Select
              value={exportConfig.format}
              onValueChange={(value: 'csv' | 'xlsx') => setExportConfig({ ...exportConfig, format: value })}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                <SelectItem value="csv">CSV (.csv)</SelectItem>
                <SelectItem value="xlsx">Excel (.xlsx)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {dataCount > 1000 && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <FileSpreadsheet className="h-4 w-4 text-orange-600" />
                  <div className="text-sm">
                    <p className="font-medium">Large Dataset Detected</p>
                    <p className="text-gray-600">
                      {dataCount.toLocaleString()} records will be exported in batches
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          <Button 
            onClick={exportData} 
            className="w-full bg-green-600 hover:bg-green-700"
            disabled={exporting}
          >
            {exporting ? 'Exporting...' : `Export ${dataCount.toLocaleString()} Records`}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ExportManager;
