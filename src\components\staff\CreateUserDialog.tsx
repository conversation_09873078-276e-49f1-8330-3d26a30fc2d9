import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  UserPlus, 
  Mail, 
  Key, 
  AlertCircle, 
  CheckCircle,
  Copy,
  Eye,
  EyeOff
} from 'lucide-react';
import { useStaffManagement } from '@/hooks/useStaffManagement';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface CreateUserDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface UserFormData {
  email: string;
  name: string;
  role: UserRole;
  division_id: string;
  phone: string;
}

const CreateUserDialog: React.FC<CreateUserDialogProps> = ({ open, onOpenChange }) => {
  const { toast } = useToast();
  const { 
    createUserInvitation, 
    createUserAccount, 
    generateSecurePassword,
    isCreatingInvitation,
    isLoading
  } = useStaffManagement();

  const [formData, setFormData] = useState<UserFormData>({
    email: '',
    name: '',
    role: 'field_staff',
    division_id: '',
    phone: ''
  });

  const [creationMethod, setCreationMethod] = useState<'invitation' | 'direct'>('invitation');
  const [generatedPassword, setGeneratedPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!open) {
      setFormData({
        email: '',
        name: '',
        role: 'field_staff',
        division_id: '',
        phone: ''
      });
      setGeneratedPassword('');
      setErrors({});
      setCreationMethod('invitation');
      setShowPassword(false);
    }
  }, [open]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }

    if (formData.phone && !/^\+?[\d\s-()]+$/.test(formData.phone)) {
      newErrors.phone = 'Invalid phone number format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof UserFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleGeneratePassword = () => {
    const password = generateSecurePassword();
    setGeneratedPassword(password);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: "Copied to clipboard",
      description: "Password has been copied to your clipboard",
    });
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    try {
      if (creationMethod === 'invitation') {
        // Create invitation
        createUserInvitation({
          email: formData.email,
          name: formData.name,
          role: formData.role,
          division_id: formData.division_id || undefined,
          phone: formData.phone || undefined
        });
      } else {
        // Create user account directly
        if (!generatedPassword) {
          setErrors({ password: 'Please generate a password first' });
          return;
        }

        await createUserAccount({
          email: formData.email,
          name: formData.name,
          role: formData.role,
          division_id: formData.division_id || undefined,
          phone: formData.phone || undefined,
          password: generatedPassword
        });

        toast({
          title: "User created successfully",
          description: `Account created for ${formData.name}. Make sure to share the password securely.`,
        });
      }

      onOpenChange(false);
    } catch (error) {
      console.error('Error creating user:', error);
    }
  };

  const isSubmitting = isCreatingInvitation || isLoading;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <UserPlus className="h-5 w-5 mr-2" />
            Create New User
          </DialogTitle>
          <DialogDescription>
            Add a new user to the system by sending an invitation or creating the account directly
          </DialogDescription>
        </DialogHeader>

        <Tabs value={creationMethod} onValueChange={(value) => setCreationMethod(value as 'invitation' | 'direct')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="invitation" className="flex items-center">
              <Mail className="h-4 w-4 mr-2" />
              Send Invitation
            </TabsTrigger>
            <TabsTrigger value="direct" className="flex items-center">
              <Key className="h-4 w-4 mr-2" />
              Create Directly
            </TabsTrigger>
          </TabsList>

          <div className="space-y-6 mt-6">
            {/* User Information Form */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">User Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="email">Email Address *</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => handleInputChange('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className={errors.email ? 'border-red-500' : ''}
                    />
                    {errors.email && (
                      <p className="text-sm text-red-500">{errors.email}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="name">Full Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder="John Doe"
                      className={errors.name ? 'border-red-500' : ''}
                    />
                    {errors.name && (
                      <p className="text-sm text-red-500">{errors.name}</p>
                    )}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="role">Role *</Label>
                    <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value as UserRole)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select role" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="field_staff">Field Staff</SelectItem>
                        <SelectItem value="program_officer">Program Officer</SelectItem>
                        <SelectItem value="admin">Admin</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="phone">Phone Number</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="+256700000000"
                      className={errors.phone ? 'border-red-500' : ''}
                    />
                    {errors.phone && (
                      <p className="text-sm text-red-500">{errors.phone}</p>
                    )}
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="division">Division (Optional)</Label>
                  <Input
                    id="division"
                    value={formData.division_id}
                    onChange={(e) => handleInputChange('division_id', e.target.value)}
                    placeholder="Leave empty if not assigned to a specific division"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Method-specific content */}
            <TabsContent value="invitation" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Mail className="h-5 w-5 mr-2" />
                    Invitation Method
                  </CardTitle>
                  <CardDescription>
                    An invitation email will be sent to the user with instructions to set up their account
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertDescription>
                      The user will receive an email invitation and can set their own password when they accept.
                      The invitation will expire in 7 days.
                    </AlertDescription>
                  </Alert>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="direct" className="mt-0">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Key className="h-5 w-5 mr-2" />
                    Direct Account Creation
                  </CardTitle>
                  <CardDescription>
                    Create the account immediately with a generated password
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      You will need to securely share the generated password with the user.
                      They will be required to change it on first login.
                    </AlertDescription>
                  </Alert>

                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Button 
                        type="button" 
                        onClick={handleGeneratePassword}
                        variant="outline"
                      >
                        Generate Secure Password
                      </Button>
                      {generatedPassword && (
                        <Badge variant="default">Password Generated</Badge>
                      )}
                    </div>

                    {generatedPassword && (
                      <div className="space-y-2">
                        <Label>Generated Password</Label>
                        <div className="flex items-center space-x-2">
                          <Input
                            type={showPassword ? 'text' : 'password'}
                            value={generatedPassword}
                            readOnly
                            className="font-mono"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                          </Button>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(generatedPassword)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                        </div>
                        <p className="text-sm text-gray-500">
                          Make sure to copy and securely share this password with the user
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleSubmit}
            disabled={isSubmitting || (creationMethod === 'direct' && !generatedPassword)}
          >
            {isSubmitting ? 'Creating...' : 
             creationMethod === 'invitation' ? 'Send Invitation' : 'Create Account'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateUserDialog;
