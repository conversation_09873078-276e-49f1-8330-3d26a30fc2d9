-- Comprehensive Attendance Tracking System
-- Migration 015: Enhanced attendance tracking with individual student records, sessions, and GPS tracking

-- Create attendance status enum
CREATE TYPE attendance_status AS ENUM ('present', 'absent', 'late', 'excused', 'partial');

-- Create session type enum  
CREATE TYPE session_type AS ENUM ('class', 'leadership_program', 'training', 'assessment', 'meeting', 'other');

-- Create check_in_status enum
CREATE TYPE check_in_status AS ENUM ('checked_in', 'checked_out', 'break', 'active');

-- Sessions table for tracking individual classes/programs
CREATE TABLE attendance_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_name VARCHAR(255) NOT NULL,
    session_type session_type NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    grade_level INTEGER,
    subject VARCHAR(100),
    teacher_name VARCHAR(100),
    facilitator_id UUID REFERENCES profiles(id),
    session_date DATE NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME,
    planned_duration_minutes INTEGER,
    actual_duration_minutes INTEGER,
    location VARCHAR(255),
    max_capacity INTEGER,
    round_tables_count INTEGER DEFAULT 0, -- For leadership programs (8 students per table)
    students_per_table INTEGER DEFAULT 8,
    session_description TEXT,
    learning_objectives TEXT[],
    materials_needed TEXT[],
    weather_condition VARCHAR(50),
    special_notes TEXT,
    is_completed BOOLEAN DEFAULT false,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual student attendance records
CREATE TABLE student_attendance (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID REFERENCES attendance_sessions(id) NOT NULL,
    student_id UUID REFERENCES students(id) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    attendance_status attendance_status NOT NULL,
    check_in_time TIMESTAMP WITH TIME ZONE,
    check_out_time TIMESTAMP WITH TIME ZONE,
    late_minutes INTEGER DEFAULT 0,
    early_departure_minutes INTEGER DEFAULT 0,
    table_number INTEGER, -- For round-table tracking in leadership programs
    participation_score INTEGER CHECK (participation_score >= 1 AND participation_score <= 5),
    behavior_notes TEXT,
    absence_reason TEXT,
    parent_notified BOOLEAN DEFAULT false,
    parent_notification_time TIMESTAMP WITH TIME ZONE,
    makeup_session_required BOOLEAN DEFAULT false,
    makeup_session_id UUID REFERENCES attendance_sessions(id),
    recorded_by UUID REFERENCES profiles(id) NOT NULL,
    recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_by UUID REFERENCES profiles(id),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- GPS-based staff check-in/out system
CREATE TABLE staff_location_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    staff_id UUID REFERENCES profiles(id) NOT NULL,
    school_id UUID REFERENCES schools(id) NOT NULL,
    session_id UUID REFERENCES attendance_sessions(id),
    check_in_status check_in_status NOT NULL,
    location_coordinates POINT NOT NULL,
    location_accuracy DECIMAL(8,2), -- GPS accuracy in meters
    address_description TEXT,
    check_in_time TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    check_out_time TIMESTAMP WITH TIME ZONE,
    total_duration_minutes INTEGER,
    distance_from_school DECIMAL(8,2), -- Distance in meters
    location_verified BOOLEAN DEFAULT false,
    verification_method VARCHAR(50), -- 'gps', 'manual', 'qr_code', etc.
    device_info JSONB DEFAULT '{}', -- Device and app info
    network_info JSONB DEFAULT '{}', -- Network connectivity info
    offline_sync BOOLEAN DEFAULT false,
    sync_timestamp TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Attendance analytics and summary tables
CREATE TABLE attendance_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_id UUID REFERENCES schools(id) NOT NULL,
    student_id UUID REFERENCES students(id),
    analysis_period VARCHAR(20) NOT NULL, -- 'daily', 'weekly', 'monthly', 'term', 'annual'
    period_start_date DATE NOT NULL,
    period_end_date DATE NOT NULL,
    total_sessions INTEGER NOT NULL,
    sessions_attended INTEGER NOT NULL,
    sessions_absent INTEGER NOT NULL,
    sessions_late INTEGER NOT NULL,
    sessions_excused INTEGER NOT NULL,
    attendance_rate DECIMAL(5,2) NOT NULL,
    punctuality_rate DECIMAL(5,2), -- Percentage of on-time attendance
    average_participation_score DECIMAL(3,2),
    consecutive_absences INTEGER DEFAULT 0,
    longest_absence_streak INTEGER DEFAULT 0,
    improvement_trend VARCHAR(20), -- 'improving', 'declining', 'stable'
    risk_level VARCHAR(20) DEFAULT 'low', -- 'low', 'medium', 'high', 'critical'
    intervention_recommended BOOLEAN DEFAULT false,
    last_calculated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Attendance notifications and alerts
CREATE TABLE attendance_notifications (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    notification_type VARCHAR(50) NOT NULL, -- 'absence_alert', 'late_alert', 'low_attendance', 'session_reminder'
    recipient_type VARCHAR(20) NOT NULL, -- 'parent', 'teacher', 'admin', 'student'
    recipient_contact VARCHAR(100),
    student_id UUID REFERENCES students(id),
    session_id UUID REFERENCES attendance_sessions(id),
    school_id UUID REFERENCES schools(id) NOT NULL,
    message_title VARCHAR(255) NOT NULL,
    message_content TEXT NOT NULL,
    notification_data JSONB DEFAULT '{}',
    sent_at TIMESTAMP WITH TIME ZONE,
    delivery_status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'sent', 'delivered', 'failed'
    delivery_method VARCHAR(20), -- 'sms', 'email', 'push', 'in_app'
    read_at TIMESTAMP WITH TIME ZONE,
    response_received BOOLEAN DEFAULT false,
    response_content TEXT,
    priority_level VARCHAR(10) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
    auto_generated BOOLEAN DEFAULT true,
    created_by UUID REFERENCES profiles(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_attendance_sessions_school_id ON attendance_sessions(school_id);
CREATE INDEX idx_attendance_sessions_date ON attendance_sessions(session_date);
CREATE INDEX idx_attendance_sessions_type ON attendance_sessions(session_type);
CREATE INDEX idx_attendance_sessions_facilitator ON attendance_sessions(facilitator_id);

CREATE INDEX idx_student_attendance_session_id ON student_attendance(session_id);
CREATE INDEX idx_student_attendance_student_id ON student_attendance(student_id);
CREATE INDEX idx_student_attendance_school_id ON student_attendance(school_id);
CREATE INDEX idx_student_attendance_status ON student_attendance(attendance_status);
CREATE INDEX idx_student_attendance_date ON student_attendance(recorded_at);

CREATE INDEX idx_staff_location_logs_staff_id ON staff_location_logs(staff_id);
CREATE INDEX idx_staff_location_logs_school_id ON staff_location_logs(school_id);
CREATE INDEX idx_staff_location_logs_session_id ON staff_location_logs(session_id);
CREATE INDEX idx_staff_location_logs_checkin_time ON staff_location_logs(check_in_time);

CREATE INDEX idx_attendance_analytics_school_id ON attendance_analytics(school_id);
CREATE INDEX idx_attendance_analytics_student_id ON attendance_analytics(student_id);
CREATE INDEX idx_attendance_analytics_period ON attendance_analytics(analysis_period, period_start_date, period_end_date);
CREATE INDEX idx_attendance_analytics_risk_level ON attendance_analytics(risk_level);

CREATE INDEX idx_attendance_notifications_student_id ON attendance_notifications(student_id);
CREATE INDEX idx_attendance_notifications_school_id ON attendance_notifications(school_id);
CREATE INDEX idx_attendance_notifications_type ON attendance_notifications(notification_type);
CREATE INDEX idx_attendance_notifications_status ON attendance_notifications(delivery_status);

-- Create triggers for updated_at columns
CREATE TRIGGER update_attendance_sessions_updated_at
    BEFORE UPDATE ON attendance_sessions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_student_attendance_updated_at
    BEFORE UPDATE ON student_attendance
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS)
ALTER TABLE attendance_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_attendance ENABLE ROW LEVEL SECURITY;
ALTER TABLE staff_location_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance_notifications ENABLE ROW LEVEL SECURITY;

-- RLS Policies for attendance_sessions
CREATE POLICY "Users can view sessions from their schools" ON attendance_sessions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        facilitator_id = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "Staff can create sessions" ON attendance_sessions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Staff can update their sessions" ON attendance_sessions
    FOR UPDATE USING (
        facilitator_id = auth.uid() OR
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- RLS Policies for student_attendance
CREATE POLICY "Users can view attendance from their schools" ON student_attendance
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        recorded_by = auth.uid() OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        ) OR
        session_id IN (
            SELECT id FROM attendance_sessions
            WHERE facilitator_id = auth.uid()
        )
    );

CREATE POLICY "Staff can record attendance" ON student_attendance
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Staff can update attendance records" ON student_attendance
    FOR UPDATE USING (
        recorded_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        session_id IN (
            SELECT id FROM attendance_sessions
            WHERE facilitator_id = auth.uid()
        )
    );

-- RLS Policies for staff_location_logs
CREATE POLICY "Users can view their own location logs" ON staff_location_logs
    FOR SELECT USING (
        staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Staff can create their own location logs" ON staff_location_logs
    FOR INSERT WITH CHECK (
        staff_id = auth.uid() AND
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

-- RLS Policies for attendance_analytics
CREATE POLICY "Users can view analytics from their schools" ON attendance_analytics
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "System can manage analytics" ON attendance_analytics
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- RLS Policies for attendance_notifications
CREATE POLICY "Users can view relevant notifications" ON attendance_notifications
    FOR SELECT USING (
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "System can manage notifications" ON attendance_notifications
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );
