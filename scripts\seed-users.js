import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

dotenv.config();

// Initialize Supabase client
const supabase = createClient(
  process.env.VITE_SUPABASE_URL,
  process.env.VITE_SUPABASE_ANON_KEY
);

async function seedUsers() {
  try {
    console.log('🌱 Starting user seeding process...');

    // Call the Edge Function
    const { data, error } = await supabase.functions.invoke('seed-users', {
      body: {}
    });

    if (error) {
      console.error('❌ Error calling seed-users function:', error);
      throw new Error(`Edge Function error: ${error.message}`);
    }

    if (!data?.success) {
      console.error('❌ Seed function returned error:', data);
      throw new Error(data?.error || 'Unknown error from seed function');
    }

    console.log('✅ User seeding completed successfully');

    // Display results
    displayResults(data);

    return data;

  } catch (error) {
    console.error('❌ Error in seedUsers:', error);
    process.exit(1);
  }
}

function displayResults(response) {
  console.log('\n📊 SEEDING RESULTS SUMMARY');
  console.log('='.repeat(60));
  console.log(`Total Users: ${response.summary.total}`);
  console.log(`✅ Completed: ${response.summary.completed}`);
  console.log(`❌ Failed: ${response.summary.failed}`);
  console.log('='.repeat(60));

  console.log('\n📋 DETAILED RESULTS:');
  response.results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.email}`);
    console.log(`   Status: ${result.status === 'complete' ? '✅ COMPLETE' : '❌ FAILED'}`);

    if (result.status === 'complete' && result.password) {
      console.log(`   Password: ${result.password}`);
      console.log(`   Note: User must change password on first login`);
    } else if (result.status === 'failed' && result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  if (response.summary.completed > 0) {
    console.log('\n🔐 PASSWORD SUMMARY FOR SHARING:');
    console.log('='.repeat(60));
    response.results
      .filter(r => r.status === 'complete')
      .forEach(result => {
        console.log(`${result.email}: ${result.password}`);
      });
    console.log('='.repeat(60));
  }

  console.log('\n⚠️  IMPORTANT NOTES:');
  console.log('- All users must change their password on first login');
  console.log('- Passwords should be shared securely with users');
  console.log('- Users are created with is_active: true');
  console.log('- Users are assigned to Uganda country by default');
}

// Run the seeding process
seedUsers();