import React from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface PageHeaderAction {
  label: string;
  onClick: () => void;
  icon?: LucideIcon;
  variant?: 'default' | 'outline' | 'secondary' | 'ghost' | 'link' | 'destructive';
  className?: string;
}

interface PageHeaderProps {
  title: string;
  description?: string;
  icon?: LucideIcon;
  actions?: PageHeaderAction[];
  children?: React.ReactNode;
  className?: string;
}

/**
 * Standardized page header component that provides consistent styling
 * for page titles, descriptions, icons, and action buttons.
 */
const PageHeader: React.FC<PageHeaderProps> = ({
  title,
  description,
  icon: Icon,
  actions = [],
  children,
  className
}) => {
  return (
    <div className={cn("flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4", className)}>
      {/* Title and Description */}
      <div className="flex items-center gap-3">
        {Icon && (
          <Icon className="h-6 w-6 text-ilead-green flex-shrink-0" />
        )}
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900 leading-tight">
            {title}
          </h1>
          {description && (
            <p className="text-gray-600 mt-1 text-sm sm:text-base">
              {description}
            </p>
          )}
        </div>
      </div>

      {/* Actions */}
      {(actions.length > 0 || children) && (
        <div className="flex items-center gap-2 flex-wrap">
          {actions.map((action, index) => {
            const ActionIcon = action.icon;
            return (
              <Button
                key={index}
                onClick={action.onClick}
                variant={action.variant || 'default'}
                className={cn(
                  action.variant === 'default' && "bg-ilead-green hover:bg-ilead-dark-green",
                  action.className
                )}
              >
                {ActionIcon && <ActionIcon className="h-4 w-4 mr-2" />}
                {action.label}
              </Button>
            );
          })}
          {children}
        </div>
      )}
    </div>
  );
};

export default PageHeader;
