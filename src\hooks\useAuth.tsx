
import { useEffect, useRef } from 'react';
import { User } from '@supabase/supabase-js';
import { useAuthState } from './auth/useAuthState';
import { useProfile } from './auth/useProfile';
import { signIn, signOut } from './auth/authUtils';

export const useAuth = () => {
  const { user, loading, setLoading } = useAuthState();
  const {
    profile,
    profileLoading,
    setProfile,
    fetchOrCreateProfile
  } = useProfile();
  
  const lastProcessedUserRef = useRef<string | null>(null);

  useEffect(() => {
    const handleUserChange = async (user: User | null) => {
      if (user) {
        // Only process if this is a different user or first time
        if (lastProcessedUserRef.current === user.id) {
          return;
        }

        lastProcessedUserRef.current = user.id;

        try {
          await fetchOrCreateProfile(user);
        } catch (error) {
          console.error('Error processing user change:', error);
        }
      } else {
        setProfile(null);
        lastProcessedUserRef.current = null;
      }
    };

    // Only process if user state has actually changed
    if (user?.id !== lastProcessedUserRef.current) {
      handleUserChange(user);
    }
  }, [user, fetchOrCreateProfile, setProfile]);

  // Simplified loading state
  const isLoading = loading || (user && profileLoading && !profile);

  return {
    user,
    profile,
    loading: isLoading,
    signIn,
    signOut,
  };
};
