
import React from 'react';

interface TaskListLoadingProps {
  className?: string;
}

const TaskListLoading: React.FC<TaskListLoadingProps> = ({ className }) => {
  return (
    <div className={`space-y-4 ${className || ''}`}>
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-20 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-32 bg-gray-100 rounded animate-pulse" />
        ))}
      </div>
    </div>
  );
};

export default TaskListLoading;
