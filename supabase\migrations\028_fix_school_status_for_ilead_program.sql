-- Fix School Status for iLead Program
-- Migration 028: Update school status to properly reflect iLead program membership

-- Add 'active' and 'inactive' values to registration_status enum
ALTER TYPE registration_status ADD VALUE IF NOT EXISTS 'active';
ALTER TYPE registration_status ADD VALUE IF NOT EXISTS 'inactive';

-- Update all existing schools to be 'active' by default (since they are in the system)
-- This assumes that schools in the database are part of the iLead program
UPDATE schools 
SET registration_status = 'active' 
WHERE registration_status = 'unregistered' OR registration_status IS NULL;

-- Update any 'registered' status to 'active' for consistency
UPDATE schools 
SET registration_status = 'active' 
WHERE registration_status = 'registered';

-- Add comment to clarify the status meanings
COMMENT ON COLUMN schools.registration_status IS 'Status in iLead program: active = participating in iLead program, inactive = not participating, pending = application pending';

-- Update the bulk update function to handle new status values
CREATE OR REPLACE FUNCTION bulk_update_school_status(
    p_school_ids UUID[],
    p_new_status registration_status
)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    updated_count INTEGER;
BEGIN
    -- Validate that the status is appropriate
    IF p_new_status NOT IN ('active', 'inactive', 'pending') THEN
        RAISE EXCEPTION 'Invalid status. Use active, inactive, or pending.';
    END IF;
    
    -- Update the schools
    UPDATE schools 
    SET registration_status = p_new_status,
        updated_at = NOW()
    WHERE id = ANY(p_school_ids);
    
    GET DIAGNOSTICS updated_count = ROW_COUNT;
    
    RETURN updated_count;
END;
$$;

-- Update the get_schools_filtered function to work with new status values
CREATE OR REPLACE FUNCTION get_schools_filtered(
    p_search_term TEXT DEFAULT NULL,
    p_school_type school_type DEFAULT NULL,
    p_registration_status registration_status DEFAULT NULL,
    p_district TEXT DEFAULT NULL,
    p_sort_by TEXT DEFAULT 'name',
    p_sort_direction TEXT DEFAULT 'asc',
    p_limit INTEGER DEFAULT 50,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    code VARCHAR(50),
    school_type school_type,
    student_count INTEGER,
    teacher_count INTEGER,
    contact_phone VARCHAR(20),
    email VARCHAR(255),
    district VARCHAR(100),
    sub_county VARCHAR(100),
    registration_status registration_status,
    classes_count INTEGER,
    streams_per_class INTEGER,
    head_teacher_name VARCHAR(255),
    deputy_head_teacher_name VARCHAR(255),
    year_established INTEGER,
    date_joined_ilead DATE,
    champion_teacher_count INTEGER,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        s.id,
        s.name,
        s.code,
        s.school_type,
        s.student_count,
        s.teacher_count,
        s.contact_phone,
        s.email,
        d.name as district,
        s.sub_county,
        s.registration_status,
        s.classes_count,
        s.streams_per_class,
        s.head_teacher_name,
        s.deputy_head_teacher_name,
        s.year_established,
        s.date_joined_ilead,
        s.champion_teacher_count,
        s.created_at,
        s.updated_at
    FROM schools s
    LEFT JOIN divisions d ON s.division_id = d.id
    WHERE 
        (p_search_term IS NULL OR 
         s.name ILIKE '%' || p_search_term || '%' OR 
         s.code ILIKE '%' || p_search_term || '%')
    AND (p_school_type IS NULL OR s.school_type = p_school_type)
    AND (p_registration_status IS NULL OR s.registration_status = p_registration_status)
    AND (p_district IS NULL OR d.name ILIKE '%' || p_district || '%')
    ORDER BY 
        CASE 
            WHEN p_sort_by = 'name' AND p_sort_direction = 'asc' THEN s.name
        END ASC,
        CASE 
            WHEN p_sort_by = 'name' AND p_sort_direction = 'desc' THEN s.name
        END DESC,
        CASE 
            WHEN p_sort_by = 'created_at' AND p_sort_direction = 'asc' THEN s.created_at::TEXT
        END ASC,
        CASE 
            WHEN p_sort_by = 'created_at' AND p_sort_direction = 'desc' THEN s.created_at::TEXT
        END DESC,
        s.name ASC
    LIMIT p_limit
    OFFSET p_offset;
END;
$$;
