
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SchoolFormData } from '@/types/school';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type AdminDivision = Database['public']['Functions']['get_admin_divisions']['Returns'][0];

interface BasicInformationSectionProps {
  formData: SchoolFormData;
  divisions: AdminDivision[];
  onFormDataChange: (data: SchoolFormData) => void;
}

const BasicInformationSection = ({ formData, divisions, onFormDataChange }: BasicInformationSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="name">School Name *</Label>
          <Input
            id="name"
            value={formData.name}
            onChange={(e) => onFormDataChange({ ...formData, name: e.target.value })}
            placeholder="Enter full school name"
            required
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="code">School Code (Optional)</Label>
          <Input
            id="code"
            value={formData.code || ''}
            onChange={(e) => onFormDataChange({ ...formData, code: e.target.value })}
            placeholder="e.g., UG-PS-001"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="school_type">School Type *</Label>
          <Select value={formData.school_type} onValueChange={(value: SchoolFormData['school_type']) => onFormDataChange({ ...formData, school_type: value })}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="primary">Primary School (P1-P7)</SelectItem>
              <SelectItem value="secondary">Secondary School (S1-S6)</SelectItem>
              <SelectItem value="tertiary">Tertiary Institution</SelectItem>
              <SelectItem value="vocational">Vocational Training</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="ownership_type">Ownership Type (Optional)</Label>
          <Select value={formData.ownership_type || 'government'} onValueChange={(value: string) => onFormDataChange({ ...formData, ownership_type: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select ownership type..." />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="government">Government School</SelectItem>
              <SelectItem value="private">Private School</SelectItem>
              <SelectItem value="community">Community School</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="year_established">Year Established (Optional)</Label>
          <Input
            id="year_established"
            type="number"
            value={formData.year_established || ''}
            onChange={(e) => onFormDataChange({ ...formData, year_established: e.target.value })}
            placeholder="e.g., 1985"
            min="1900"
            max={new Date().getFullYear()}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="division">District *</Label>
          <Select value={formData.division_id} onValueChange={(value) => onFormDataChange({ ...formData, division_id: value })}>
            <SelectTrigger>
              <SelectValue placeholder="Select district" />
            </SelectTrigger>
            <SelectContent>
              {divisions.map((division: AdminDivision) => (
                <SelectItem key={division.id} value={division.id}>
                  {division.district}, {division.sub_county}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};

export default BasicInformationSection;
