/**
 * Conflict resolution utilities for offline sync
 * Handles conflict detection, resolution strategies, and conflict management
 */

import { 
  SyncConflict, 
  ConflictData, 
  OfflineData,
  CONFLICT_RESOLUTION 
} from '@/types/offlineSync.types';
import { loadConflicts, saveConflicts } from '@/utils/offlineStorage';

/**
 * Detect conflicts between local and server data
 */
export const detectConflicts = (
  localData: Record<string, unknown>,
  serverData: Record<string, unknown>
): string[] => {
  const conflictFields: string[] = [];
  
  // Get all unique keys from both objects
  const allKeys = new Set([...Object.keys(localData), ...Object.keys(serverData)]);
  
  for (const key of allKeys) {
    const localValue = localData[key];
    const serverValue = serverData[key];
    
    // Skip timestamp fields as they're expected to differ
    if (key.includes('timestamp') || key.includes('updated_at') || key.includes('created_at')) {
      continue;
    }
    
    // Deep comparison for objects and arrays
    if (JSON.stringify(localValue) !== JSON.stringify(serverValue)) {
      conflictFields.push(key);
    }
  }
  
  return conflictFields;
};

/**
 * Create a conflict record
 */
export const createConflict = (
  id: string,
  localData: Record<string, unknown>,
  serverData: Record<string, unknown>,
  type: string = 'data_conflict'
): ConflictData => {
  const conflictFields = detectConflicts(localData, serverData);
  
  return {
    id,
    localData,
    serverData,
    conflictFields,
    timestamp: Date.now(),
    type
  };
};

/**
 * Resolve conflict using specified strategy
 */
export const resolveConflict = (
  conflict: ConflictData,
  strategy: keyof typeof CONFLICT_RESOLUTION,
  customResolution?: Record<string, unknown>
): Record<string, unknown> => {
  switch (strategy) {
    case 'CLIENT_WINS':
      return conflict.localData;
      
    case 'SERVER_WINS':
      return conflict.serverData;
      
    case 'MERGE':
      return mergeConflictData(conflict.localData, conflict.serverData);
      
    case 'MANUAL':
      if (!customResolution) {
        throw new Error('Manual resolution requires custom resolution data');
      }
      return customResolution;
      
    default:
      throw new Error(`Unknown conflict resolution strategy: ${strategy}`);
  }
};

/**
 * Intelligent merge of conflicting data
 */
export const mergeConflictData = (
  localData: Record<string, unknown>,
  serverData: Record<string, unknown>
): Record<string, unknown> => {
  const merged = { ...serverData }; // Start with server data as base
  
  // Apply local changes that don't conflict with server updates
  for (const [key, localValue] of Object.entries(localData)) {
    const serverValue = serverData[key];
    
    // If server doesn't have this field, use local value
    if (!(key in serverData)) {
      merged[key] = localValue;
      continue;
    }
    
    // For timestamps, use the most recent
    if (key.includes('timestamp') || key.includes('updated_at')) {
      const localTime = typeof localValue === 'number' ? localValue : Date.parse(String(localValue));
      const serverTime = typeof serverValue === 'number' ? serverValue : Date.parse(String(serverValue));
      merged[key] = Math.max(localTime, serverTime);
      continue;
    }
    
    // For arrays, merge unique values
    if (Array.isArray(localValue) && Array.isArray(serverValue)) {
      merged[key] = [...new Set([...serverValue, ...localValue])];
      continue;
    }
    
    // For objects, recursively merge
    if (
      typeof localValue === 'object' && 
      typeof serverValue === 'object' && 
      localValue !== null && 
      serverValue !== null
    ) {
      merged[key] = mergeConflictData(
        localValue as Record<string, unknown>,
        serverValue as Record<string, unknown>
      );
      continue;
    }
    
    // For primitive values, prefer local if it's more recent or non-empty
    if (localValue && (!serverValue || localValue !== serverValue)) {
      merged[key] = localValue;
    }
  }
  
  return merged;
};

/**
 * Add conflict to storage
 */
export const addConflict = (conflict: ConflictData): void => {
  try {
    const existingConflicts = loadConflicts();
    
    // Check if conflict already exists
    const existingIndex = existingConflicts.findIndex(c => c.id === conflict.id);
    
    if (existingIndex >= 0) {
      // Update existing conflict
      existingConflicts[existingIndex] = conflict;
    } else {
      // Add new conflict
      existingConflicts.push(conflict);
    }
    
    saveConflicts(existingConflicts);
  } catch (error) {
    console.error('Failed to add conflict:', error);
    throw new Error('Failed to save conflict data');
  }
};

/**
 * Remove conflict from storage
 */
export const removeConflict = (conflictId: string): boolean => {
  try {
    const existingConflicts = loadConflicts();
    const filteredConflicts = existingConflicts.filter(c => c.id !== conflictId);
    
    if (filteredConflicts.length === existingConflicts.length) {
      return false; // Conflict not found
    }
    
    saveConflicts(filteredConflicts);
    return true;
  } catch (error) {
    console.error('Failed to remove conflict:', error);
    return false;
  }
};

/**
 * Get all conflicts
 */
export const getAllConflicts = (): ConflictData[] => {
  return loadConflicts();
};

/**
 * Get conflicts by type
 */
export const getConflictsByType = (type: string): ConflictData[] => {
  return loadConflicts().filter(conflict => conflict.type === type);
};

/**
 * Get conflict by ID
 */
export const getConflictById = (id: string): ConflictData | null => {
  const conflicts = loadConflicts();
  return conflicts.find(conflict => conflict.id === id) || null;
};

/**
 * Clear all conflicts
 */
export const clearAllConflicts = (): void => {
  saveConflicts([]);
};

/**
 * Auto-resolve conflicts based on predefined rules
 */
export const autoResolveConflicts = (
  strategy: keyof typeof CONFLICT_RESOLUTION = 'MERGE'
): { resolved: number; failed: number } => {
  const conflicts = loadConflicts();
  let resolved = 0;
  let failed = 0;
  
  const remainingConflicts: ConflictData[] = [];
  
  for (const conflict of conflicts) {
    try {
      // Only auto-resolve if strategy is not MANUAL
      if (strategy !== 'MANUAL') {
        resolveConflict(conflict, strategy);
        resolved++;
      } else {
        remainingConflicts.push(conflict);
      }
    } catch (error) {
      console.error(`Failed to auto-resolve conflict ${conflict.id}:`, error);
      remainingConflicts.push(conflict);
      failed++;
    }
  }
  
  // Save remaining conflicts
  saveConflicts(remainingConflicts);
  
  return { resolved, failed };
};

/**
 * Validate conflict resolution data
 */
export const validateResolution = (
  conflict: ConflictData,
  resolution: Record<string, unknown>
): { valid: boolean; errors: string[] } => {
  const errors: string[] = [];
  
  // Check that all required fields from original data are present
  const requiredFields = new Set([
    ...Object.keys(conflict.localData),
    ...Object.keys(conflict.serverData)
  ]);
  
  for (const field of requiredFields) {
    if (!(field in resolution)) {
      errors.push(`Missing required field: ${field}`);
    }
  }
  
  // Validate data types match original data
  for (const [key, value] of Object.entries(resolution)) {
    const localType = typeof conflict.localData[key];
    const serverType = typeof conflict.serverData[key];
    const resolutionType = typeof value;
    
    if (localType !== 'undefined' && resolutionType !== localType) {
      errors.push(`Type mismatch for field ${key}: expected ${localType}, got ${resolutionType}`);
    }
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
};

/**
 * Get conflict statistics
 */
export const getConflictStats = (): {
  total: number;
  byType: Record<string, number>;
  oldestConflict: number | null;
  newestConflict: number | null;
} => {
  const conflicts = loadConflicts();
  
  const byType: Record<string, number> = {};
  const timestamps = conflicts.map(c => c.timestamp);
  
  for (const conflict of conflicts) {
    byType[conflict.type] = (byType[conflict.type] || 0) + 1;
  }
  
  return {
    total: conflicts.length,
    byType,
    oldestConflict: timestamps.length > 0 ? Math.min(...timestamps) : null,
    newestConflict: timestamps.length > 0 ? Math.max(...timestamps) : null
  };
};
