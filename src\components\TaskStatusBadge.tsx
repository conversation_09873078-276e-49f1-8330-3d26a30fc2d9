import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Database } from '@/integrations/supabase/types';

type TaskStatus = Database['public']['Enums']['task_status'];

interface TaskStatusBadgeProps {
  status: TaskStatus;
  className?: string;
}

const TaskStatusBadge: React.FC<TaskStatusBadgeProps> = ({ status, className }) => {
  const getStatusConfig = (status: TaskStatus) => {
    switch (status) {
      case 'pending':
        return {
          label: 'Pending',
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800 hover:bg-gray-200'
        };
      case 'in_progress':
        return {
          label: 'In Progress',
          variant: 'default' as const,
          className: 'bg-blue-100 text-blue-800 hover:bg-blue-200'
        };
      case 'completed':
        return {
          label: 'Completed',
          variant: 'default' as const,
          className: 'bg-purple-100 text-purple-800 hover:bg-purple-200'
        };
      case 'cancelled':
        return {
          label: 'Cancelled',
          variant: 'destructive' as const,
          className: 'bg-red-100 text-red-800 hover:bg-red-200'
        };
      default:
        return {
          label: 'Unknown',
          variant: 'secondary' as const,
          className: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const config = getStatusConfig(status);

  return (
    <Badge 
      variant={config.variant}
      className={`${config.className} ${className || ''}`}
    >
      {config.label}
    </Badge>
  );
};

export default TaskStatusBadge;
