import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface SeedUser {
  email: string;
  name: string;
  role: 'admin' | 'program_officer' | 'field_staff';
}

interface SeedResult {
  email: string;
  status: 'complete' | 'failed';
  password?: string;
  error?: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client with service role
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Generate secure random password (8 characters: 1 digit, 1 upper, 1 lower, rest random)
    const generatePassword = (): string => {
      const uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
      const lowercase = 'abcdefghijklmnopqrstuvwxyz';
      const numbers = '0123456789';
      const allChars = uppercase + lowercase + numbers;
      
      // Ensure at least 1 digit, 1 uppercase, 1 lowercase
      let password = '';
      password += uppercase[Math.floor(Math.random() * uppercase.length)];
      password += lowercase[Math.floor(Math.random() * lowercase.length)];
      password += numbers[Math.floor(Math.random() * numbers.length)];
      
      // Fill remaining 5 characters randomly
      for (let i = 3; i < 8; i++) {
        password += allChars[Math.floor(Math.random() * allChars.length)];
      }
      
      // Shuffle the password
      return password.split('').sort(() => Math.random() - 0.5).join('');
    };

    // Extract user name from email
    const extractNameFromEmail = (email: string): string => {
      const localPart = email.split('@')[0];
      // Convert from potential formats like 'snakanyeke' to 'S. Nakanyeke'
      return localPart.charAt(0).toUpperCase() + '. ' + 
             localPart.slice(1).charAt(0).toUpperCase() + 
             localPart.slice(2);
    };

    // Define users to seed
    const usersToSeed: SeedUser[] = [
      { email: '<EMAIL>', name: 'S. Nakanyeke', role: 'field_staff' },
      { email: '<EMAIL>', name: 'B. Mutambi', role: 'field_staff' },
      { email: '<EMAIL>', name: 'A. Ssengoba', role: 'field_staff' },
      { email: '<EMAIL>', name: 'J. Atuhierwe', role: 'field_staff' },
      { email: '<EMAIL>', name: 'A. Nkwihoreze', role: 'program_officer' },
      { email: '<EMAIL>', name: 'T. Buyinza', role: 'field_staff' }
    ];

    const results: SeedResult[] = [];

    for (const user of usersToSeed) {
      try {
        console.log(`Processing user: ${user.email}`);

        // Check if user already exists
        const { data: existingUser } = await supabaseClient.auth.admin.getUserByEmail(user.email);
        
        if (existingUser.user) {
          console.log(`User ${user.email} already exists, skipping...`);
          results.push({
            email: user.email,
            status: 'failed',
            error: 'User already exists'
          });
          continue;
        }

        // Generate password
        const password = generatePassword();
        console.log(`Generated password for ${user.email}: ${password}`);

        // Create user in Supabase Auth
        const { data: authData, error: authError } = await supabaseClient.auth.admin.createUser({
          email: user.email,
          password: password,
          email_confirm: true,
          user_metadata: {
            name: user.name,
            role: user.role
          }
        });

        if (authError) {
          console.error(`Auth error for ${user.email}:`, authError);
          results.push({
            email: user.email,
            status: 'failed',
            error: `Auth error: ${authError.message}`
          });
          continue;
        }

        if (!authData.user) {
          console.error(`No user data returned for ${user.email}`);
          results.push({
            email: user.email,
            status: 'failed',
            error: 'No user data returned from auth'
          });
          continue;
        }

        console.log(`Created auth user for ${user.email}, ID: ${authData.user.id}`);

        // Create profile
        const { error: profileError } = await supabaseClient
          .from('profiles')
          .upsert({
            id: authData.user.id,
            name: user.name,
            role: user.role,
            country: 'Uganda',
            is_active: true,
            requires_password_change: true,
            created_at: new Date().toISOString()
          });

        if (profileError) {
          console.error(`Profile error for ${user.email}:`, profileError);
          
          // Clean up auth user if profile creation fails
          await supabaseClient.auth.admin.deleteUser(authData.user.id);
          
          results.push({
            email: user.email,
            status: 'failed',
            error: `Profile error: ${profileError.message}`
          });
          continue;
        }

        console.log(`Successfully created user ${user.email}`);
        results.push({
          email: user.email,
          status: 'complete',
          password: password
        });

      } catch (error) {
        console.error(`Unexpected error for ${user.email}:`, error);
        results.push({
          email: user.email,
          status: 'failed',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return new Response(
      JSON.stringify({ 
        success: true,
        results: results,
        summary: {
          total: usersToSeed.length,
          completed: results.filter(r => r.status === 'complete').length,
          failed: results.filter(r => r.status === 'failed').length
        }
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in seed-users function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})
