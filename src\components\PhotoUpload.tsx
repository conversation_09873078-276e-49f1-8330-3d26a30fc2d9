
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Upload, X, Image } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface PhotoUploadProps {
  onPhotoUploaded: (url: string, fileName: string) => void;
  distributionId?: string;
  maxFiles?: number;
}

const PhotoUpload = ({ onPhotoUploaded, distributionId, maxFiles = 3 }: PhotoUploadProps) => {
  const [uploading, setUploading] = useState(false);
  const [uploadedPhotos, setUploadedPhotos] = useState<{ url: string; name: string }[]>([]);
  const { toast } = useToast();

  const uploadPhoto = async (file: File) => {
    try {
      setUploading(true);
      
      const fileExt = file.name.split('.').pop();
      const fileName = `${distributionId || 'temp'}_${Date.now()}.${fileExt}`;
      const filePath = `distribution-photos/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('distribution-photos')
        .upload(filePath, file);

      if (uploadError) {
        throw uploadError;
      }

      const { data: { publicUrl } } = supabase.storage
        .from('distribution-photos')
        .getPublicUrl(filePath);

      const newPhoto = { url: publicUrl, name: fileName };
      setUploadedPhotos(prev => [...prev, newPhoto]);
      onPhotoUploaded(publicUrl, fileName);

      toast({
        title: "Success",
        description: "Photo uploaded successfully",
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "Failed to upload photo";
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setUploading(false);
    }
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (!files) return;

    if (uploadedPhotos.length + files.length > maxFiles) {
      toast({
        title: "Error",
        description: `Maximum ${maxFiles} photos allowed`,
        variant: "destructive",
      });
      return;
    }

    Array.from(files).forEach(uploadPhoto);
  };

  const removePhoto = (index: number) => {
    setUploadedPhotos(prev => prev.filter((_, i) => i !== index));
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <Label htmlFor="photo-upload">Upload Photos</Label>
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <Input
            id="photo-upload"
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileChange}
            disabled={uploading || uploadedPhotos.length >= maxFiles}
            className="hidden"
          />
          <Label
            htmlFor="photo-upload"
            className="cursor-pointer flex flex-col items-center space-y-2"
          >
            <Upload className="h-8 w-8 text-gray-400" />
            <span className="text-sm text-gray-600">
              {uploading ? 'Uploading...' : `Click to upload photos (${uploadedPhotos.length}/${maxFiles})`}
            </span>
          </Label>
        </div>
      </div>

      {uploadedPhotos.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {uploadedPhotos.map((photo, index) => (
            <Card key={index} className="relative">
              <CardContent className="p-2">
                <div className="relative">
                  <img
                    src={photo.url}
                    alt={`Distribution photo ${index + 1}`}
                    className="w-full h-24 object-cover rounded"
                  />
                  <Button
                    size="sm"
                    variant="destructive"
                    className="absolute top-1 right-1 h-6 w-6 p-0"
                    onClick={() => removePhoto(index)}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </div>
                <p className="text-xs text-gray-500 mt-1 truncate">{photo.name}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default PhotoUpload;
