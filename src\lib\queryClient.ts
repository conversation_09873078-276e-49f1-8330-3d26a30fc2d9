
import { QueryClient } from '@tanstack/react-query';

// Create a client with default options
export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      retry: 2,
    },
  },
});

// Create optimized query client with performance monitoring
export const createOptimizedQueryClient = () => {
  return queryClient;
};

// Query key factories for consistency across the app
export const queryKeys = {
  all: ['queries'] as const,
  tasks: {
    all: ['tasks'] as const,
    recent: (limit: number) => ['tasks', 'recent', limit] as const,
    list: (params: {
      userId?: string;
      statusFilter?: string;
      assignedFilter?: string;
      includeComments?: boolean;
      limit?: number;
      profileId?: string;
      profileRole?: string;
    }) => ['tasks', 'list', params] as const,
    detail: (id: string) => ['tasks', 'detail', id] as const,
  },
  activities: {
    all: ['activities'] as const,
    recent: (limit: number) => ['activities', 'recent', limit] as const,
    user: (userId: string, limit: number) => ['activities', 'user', userId, limit] as const,
    stats: (days: number) => ['activities', 'stats', days] as const,
  },
  schools: {
    all: ['schools'] as const,
    list: ['schools', 'list'] as const,
    divisions: ['schools', 'divisions'] as const,
  },
  distributions: {
    all: ['distributions'] as const,
    list: ['distributions', 'list'] as const,
  },
  inventory: {
    all: ['inventory'] as const,
    books: ['inventory', 'books'] as const,
  },
  profiles: {
    all: ['profiles'] as const,
    current: ['profiles', 'current'] as const,
    detail: (id: string) => ['profiles', 'detail', id] as const,
  },
};

// Mutation key factories
export const mutationKeys = {
  tasks: {
    create: ['tasks', 'create'] as const,
    update: ['tasks', 'update'] as const,
    updateStatus: ['tasks', 'updateStatus'] as const,
    delete: ['tasks', 'delete'] as const,
  },
  schools: {
    create: ['schools', 'create'] as const,
    update: ['schools', 'update'] as const,
  },
  distributions: {
    create: ['distributions', 'create'] as const,
  },
  activities: {
    log: ['activities', 'log'] as const,
  },
};
