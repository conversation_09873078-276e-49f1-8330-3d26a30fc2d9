-- Comprehensive Book Management System Migration
-- Migration 019: Clean up existing book data and create new comprehensive schema

-- ============================================================================
-- STEP 1: DROP EXISTING BOOK-RELATED FUNCTIONS
-- ============================================================================

DROP FUNCTION IF EXISTS add_book_distribution CASCADE;
DROP FUNCTION IF EXISTS add_book_inventory CASCADE;
DROP FUNCTION IF EXISTS get_book_distributions CASCADE;
DROP FUNCTION IF EXISTS get_book_inventory CASCADE;
DROP FUNCTION IF EXISTS get_distribution_details CASCADE;
DROP FUNCTION IF EXISTS get_distribution_statistics CASCADE;
DROP FUNCTION IF EXISTS update_distribution_status CASCADE;

-- ============================================================================
-- STEP 2: DROP EXISTING BOOK-RELATED TABLES (IN CORRECT ORDER)
-- ============================================================================

-- Drop tables that reference book_distributions first
DROP TABLE IF EXISTS distribution_photos CASCADE;

-- Drop field_reports foreign key constraint to book_distributions
ALTER TABLE IF EXISTS field_reports DROP CONSTRAINT IF EXISTS field_reports_distribution_id_fkey;
ALTER TABLE IF EXISTS field_reports DROP COLUMN IF EXISTS distribution_id;

-- Drop book_distributions table
DROP TABLE IF EXISTS book_distributions CASCADE;

-- Drop book_inventory table
DROP TABLE IF EXISTS book_inventory CASCADE;

-- ============================================================================
-- STEP 3: CREATE NEW COMPREHENSIVE BOOK MANAGEMENT SCHEMA
-- ============================================================================

-- Create enums for book management
CREATE TYPE book_category AS ENUM (
    'mathematics', 'english', 'science', 'social_studies', 
    'religious_education', 'physical_education', 'art', 
    'music', 'local_language', 'life_skills', 'other'
);

CREATE TYPE book_condition AS ENUM ('new', 'good', 'fair', 'poor');

CREATE TYPE book_language AS ENUM (
    'english', 'luganda', 'runyankole', 'ateso', 'luo', 
    'lugbara', 'runyoro', 'lusoga', 'other'
);

CREATE TYPE grade_level AS ENUM (
    'p1', 'p2', 'p3', 'p4', 'p5', 'p6', 'p7',
    's1', 's2', 's3', 's4', 's5', 's6',
    'nursery', 'baby_class', 'middle_class', 'top_class'
);

CREATE TYPE distribution_status AS ENUM (
    'planned', 'in_progress', 'completed', 'cancelled', 'delayed'
);

-- ============================================================================
-- BOOKS TABLE - Master book catalog
-- ============================================================================
CREATE TABLE books (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(500) NOT NULL,
    author VARCHAR(300) NOT NULL,
    isbn VARCHAR(20),
    publication_year INTEGER,
    category book_category NOT NULL,
    grade_level grade_level NOT NULL,
    language book_language NOT NULL DEFAULT 'english',
    publisher VARCHAR(300),
    description TEXT,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT books_publication_year_check CHECK (publication_year >= 1800 AND publication_year <= EXTRACT(YEAR FROM NOW()) + 5),
    CONSTRAINT books_title_length_check CHECK (LENGTH(title) >= 2),
    CONSTRAINT books_author_length_check CHECK (LENGTH(author) >= 2)
);

-- ============================================================================
-- BOOK_INVENTORY TABLE - Inventory tracking for each book
-- ============================================================================
CREATE TABLE book_inventory (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    book_id UUID REFERENCES books(id) ON DELETE CASCADE NOT NULL,
    total_quantity INTEGER NOT NULL DEFAULT 0,
    available_quantity INTEGER NOT NULL DEFAULT 0,
    distributed_quantity INTEGER NOT NULL DEFAULT 0,
    damaged_quantity INTEGER NOT NULL DEFAULT 0,
    lost_quantity INTEGER NOT NULL DEFAULT 0,
    minimum_threshold INTEGER NOT NULL DEFAULT 10,
    condition book_condition NOT NULL DEFAULT 'good',
    storage_location VARCHAR(200),
    cost_per_unit DECIMAL(10,2),
    notes TEXT,
    last_updated_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT inventory_quantities_check CHECK (
        total_quantity >= 0 AND 
        available_quantity >= 0 AND 
        distributed_quantity >= 0 AND 
        damaged_quantity >= 0 AND 
        lost_quantity >= 0 AND
        minimum_threshold >= 0
    ),
    CONSTRAINT inventory_total_check CHECK (
        total_quantity = available_quantity + distributed_quantity + damaged_quantity + lost_quantity
    ),
    CONSTRAINT inventory_cost_check CHECK (cost_per_unit >= 0)
);

-- ============================================================================
-- BOOK_DISTRIBUTIONS TABLE - Distribution tracking
-- ============================================================================
CREATE TABLE book_distributions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    book_id UUID REFERENCES books(id) ON DELETE RESTRICT NOT NULL,
    inventory_id UUID REFERENCES book_inventory(id) ON DELETE RESTRICT NOT NULL,
    school_id UUID REFERENCES schools(id) ON DELETE RESTRICT NOT NULL,
    quantity INTEGER NOT NULL,
    planned_date DATE,
    actual_delivery_date DATE,
    status distribution_status NOT NULL DEFAULT 'planned',
    supervisor_id UUID REFERENCES profiles(id) NOT NULL,
    recipient_name VARCHAR(200),
    recipient_title VARCHAR(100),
    notes TEXT,
    delivery_confirmation BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT distribution_quantity_check CHECK (quantity > 0),
    CONSTRAINT distribution_dates_check CHECK (
        planned_date IS NULL OR actual_delivery_date IS NULL OR actual_delivery_date >= planned_date
    )
);

-- ============================================================================
-- DISTRIBUTION_PHOTOS TABLE - Photos for distributions
-- ============================================================================
CREATE TABLE distribution_photos (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    distribution_id UUID REFERENCES book_distributions(id) ON DELETE CASCADE NOT NULL,
    photo_url TEXT NOT NULL,
    caption TEXT,
    uploaded_by UUID REFERENCES profiles(id) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Books table indexes
CREATE INDEX idx_books_category ON books(category);
CREATE INDEX idx_books_grade_level ON books(grade_level);
CREATE INDEX idx_books_language ON books(language);
CREATE INDEX idx_books_title ON books USING gin(to_tsvector('english', title));
CREATE INDEX idx_books_author ON books USING gin(to_tsvector('english', author));
CREATE INDEX idx_books_created_by ON books(created_by);

-- Book inventory indexes
CREATE INDEX idx_book_inventory_book_id ON book_inventory(book_id);
CREATE INDEX idx_book_inventory_condition ON book_inventory(condition);
CREATE INDEX idx_book_inventory_low_stock ON book_inventory(available_quantity) WHERE available_quantity <= minimum_threshold;
CREATE INDEX idx_book_inventory_last_updated_by ON book_inventory(last_updated_by);

-- Book distributions indexes
CREATE INDEX idx_book_distributions_book_id ON book_distributions(book_id);
CREATE INDEX idx_book_distributions_school_id ON book_distributions(school_id);
CREATE INDEX idx_book_distributions_supervisor_id ON book_distributions(supervisor_id);
CREATE INDEX idx_book_distributions_status ON book_distributions(status);
CREATE INDEX idx_book_distributions_planned_date ON book_distributions(planned_date);
CREATE INDEX idx_book_distributions_actual_date ON book_distributions(actual_delivery_date);
CREATE INDEX idx_book_distributions_created_by ON book_distributions(created_by);

-- Distribution photos indexes
CREATE INDEX idx_distribution_photos_distribution_id ON distribution_photos(distribution_id);
CREATE INDEX idx_distribution_photos_uploaded_by ON distribution_photos(uploaded_by);

-- ============================================================================
-- TRIGGERS FOR AUTOMATIC TIMESTAMP UPDATES
-- ============================================================================

-- Update timestamp triggers
CREATE TRIGGER update_books_updated_at
    BEFORE UPDATE ON books
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_book_inventory_updated_at
    BEFORE UPDATE ON book_inventory
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_book_distributions_updated_at
    BEFORE UPDATE ON book_distributions
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ============================================================================
-- ROW LEVEL SECURITY (RLS) POLICIES
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE books ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_inventory ENABLE ROW LEVEL SECURITY;
ALTER TABLE book_distributions ENABLE ROW LEVEL SECURITY;
ALTER TABLE distribution_photos ENABLE ROW LEVEL SECURITY;

-- Books table policies
CREATE POLICY "Users can view all books" ON books
    FOR SELECT USING (true);

CREATE POLICY "Only admins can create books" ON books
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Only admins can update books" ON books
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

CREATE POLICY "Only admins can delete books" ON books
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Book inventory table policies
CREATE POLICY "Users can view inventory" ON book_inventory
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Admins and program officers can manage inventory" ON book_inventory
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Book distributions table policies
CREATE POLICY "Users can view distributions" ON book_distributions
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        supervisor_id = auth.uid() OR
        created_by = auth.uid()
    );

CREATE POLICY "Authorized users can create distributions" ON book_distributions
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

CREATE POLICY "Authorized users can update distributions" ON book_distributions
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        supervisor_id = auth.uid() OR
        created_by = auth.uid()
    );

-- Distribution photos table policies
CREATE POLICY "Users can view distribution photos" ON distribution_photos
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM book_distributions bd
            WHERE bd.id = distribution_id AND (
                EXISTS (
                    SELECT 1 FROM profiles
                    WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
                ) OR
                bd.supervisor_id = auth.uid() OR
                bd.created_by = auth.uid()
            )
        )
    );

CREATE POLICY "Authorized users can manage distribution photos" ON distribution_photos
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM book_distributions bd
            WHERE bd.id = distribution_id AND (
                EXISTS (
                    SELECT 1 FROM profiles
                    WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
                ) OR
                bd.supervisor_id = auth.uid() OR
                bd.created_by = auth.uid()
            )
        )
    );

-- ============================================================================
-- DATABASE FUNCTIONS FOR BOOK MANAGEMENT
-- ============================================================================

-- Function to add a new book with inventory
CREATE OR REPLACE FUNCTION add_book_with_inventory(
    p_title VARCHAR(500),
    p_author VARCHAR(300),
    p_isbn VARCHAR(20) DEFAULT NULL,
    p_publication_year INTEGER DEFAULT NULL,
    p_category book_category,
    p_grade_level grade_level,
    p_language book_language DEFAULT 'english',
    p_publisher VARCHAR(300) DEFAULT NULL,
    p_description TEXT DEFAULT NULL,
    p_total_quantity INTEGER DEFAULT 0,
    p_condition book_condition DEFAULT 'good',
    p_storage_location VARCHAR(200) DEFAULT NULL,
    p_cost_per_unit DECIMAL(10,2) DEFAULT NULL,
    p_minimum_threshold INTEGER DEFAULT 10,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_book_id UUID;
    v_inventory_id UUID;
BEGIN
    -- Check if user has permission to add books
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role = 'admin'
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins can add books.';
    END IF;

    -- Insert the book
    INSERT INTO books (
        title, author, isbn, publication_year, category, grade_level,
        language, publisher, description, created_by
    ) VALUES (
        p_title, p_author, p_isbn, p_publication_year, p_category, p_grade_level,
        p_language, p_publisher, p_description, auth.uid()
    ) RETURNING id INTO v_book_id;

    -- Insert the inventory record
    INSERT INTO book_inventory (
        book_id, total_quantity, available_quantity, distributed_quantity,
        damaged_quantity, lost_quantity, minimum_threshold, condition,
        storage_location, cost_per_unit, notes, last_updated_by
    ) VALUES (
        v_book_id, p_total_quantity, p_total_quantity, 0, 0, 0,
        p_minimum_threshold, p_condition, p_storage_location,
        p_cost_per_unit, p_notes, auth.uid()
    ) RETURNING id INTO v_inventory_id;

    RETURN v_book_id;
END;
$$;

-- Function to get books with inventory information
CREATE OR REPLACE FUNCTION get_books_with_inventory()
RETURNS TABLE (
    id UUID,
    title VARCHAR(500),
    author VARCHAR(300),
    isbn VARCHAR(20),
    publication_year INTEGER,
    category book_category,
    grade_level grade_level,
    language book_language,
    publisher VARCHAR(300),
    description TEXT,
    total_quantity INTEGER,
    available_quantity INTEGER,
    distributed_quantity INTEGER,
    damaged_quantity INTEGER,
    lost_quantity INTEGER,
    minimum_threshold INTEGER,
    condition book_condition,
    storage_location VARCHAR(200),
    cost_per_unit DECIMAL(10,2),
    inventory_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.id, b.title, b.author, b.isbn, b.publication_year,
        b.category, b.grade_level, b.language, b.publisher, b.description,
        bi.total_quantity, bi.available_quantity, bi.distributed_quantity,
        bi.damaged_quantity, bi.lost_quantity, bi.minimum_threshold,
        bi.condition, bi.storage_location, bi.cost_per_unit, bi.notes,
        b.created_at, b.updated_at
    FROM books b
    LEFT JOIN book_inventory bi ON b.id = bi.book_id
    ORDER BY b.title;
END;
$$;

-- Function to update inventory quantities
CREATE OR REPLACE FUNCTION update_book_inventory(
    p_inventory_id UUID,
    p_total_quantity INTEGER,
    p_available_quantity INTEGER,
    p_distributed_quantity INTEGER,
    p_damaged_quantity INTEGER,
    p_lost_quantity INTEGER,
    p_condition book_condition DEFAULT NULL,
    p_storage_location VARCHAR(200) DEFAULT NULL,
    p_cost_per_unit DECIMAL(10,2) DEFAULT NULL,
    p_minimum_threshold INTEGER DEFAULT NULL,
    p_notes TEXT DEFAULT NULL
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission to update inventory
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins and program officers can update inventory.';
    END IF;

    -- Update the inventory record
    UPDATE book_inventory SET
        total_quantity = COALESCE(p_total_quantity, total_quantity),
        available_quantity = COALESCE(p_available_quantity, available_quantity),
        distributed_quantity = COALESCE(p_distributed_quantity, distributed_quantity),
        damaged_quantity = COALESCE(p_damaged_quantity, damaged_quantity),
        lost_quantity = COALESCE(p_lost_quantity, lost_quantity),
        condition = COALESCE(p_condition, condition),
        storage_location = COALESCE(p_storage_location, storage_location),
        cost_per_unit = COALESCE(p_cost_per_unit, cost_per_unit),
        minimum_threshold = COALESCE(p_minimum_threshold, minimum_threshold),
        notes = COALESCE(p_notes, notes),
        last_updated_by = auth.uid(),
        updated_at = NOW()
    WHERE id = p_inventory_id;

    RETURN FOUND;
END;
$$;

-- Function to create book distribution
CREATE OR REPLACE FUNCTION create_book_distribution(
    p_book_id UUID,
    p_school_id UUID,
    p_quantity INTEGER,
    p_planned_date DATE DEFAULT NULL,
    p_recipient_name VARCHAR(200) DEFAULT NULL,
    p_recipient_title VARCHAR(100) DEFAULT NULL,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_distribution_id UUID;
    v_inventory_id UUID;
    v_available_qty INTEGER;
BEGIN
    -- Check if user has permission to create distributions
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Access denied. Insufficient permissions to create distributions.';
    END IF;

    -- Get inventory information
    SELECT bi.id, bi.available_quantity
    INTO v_inventory_id, v_available_qty
    FROM book_inventory bi
    WHERE bi.book_id = p_book_id;

    IF v_inventory_id IS NULL THEN
        RAISE EXCEPTION 'Book inventory not found for book ID: %', p_book_id;
    END IF;

    IF v_available_qty < p_quantity THEN
        RAISE EXCEPTION 'Insufficient inventory. Available: %, Requested: %', v_available_qty, p_quantity;
    END IF;

    -- Create the distribution record
    INSERT INTO book_distributions (
        book_id, inventory_id, school_id, quantity, planned_date,
        supervisor_id, recipient_name, recipient_title, notes, created_by
    ) VALUES (
        p_book_id, v_inventory_id, p_school_id, p_quantity, p_planned_date,
        auth.uid(), p_recipient_name, p_recipient_title, p_notes, auth.uid()
    ) RETURNING id INTO v_distribution_id;

    -- Update inventory quantities
    UPDATE book_inventory SET
        available_quantity = available_quantity - p_quantity,
        distributed_quantity = distributed_quantity + p_quantity,
        last_updated_by = auth.uid(),
        updated_at = NOW()
    WHERE id = v_inventory_id;

    RETURN v_distribution_id;
END;
$$;

-- Update legacy functions to work with new schema
-- Function to get book inventory (updated for new schema)
CREATE OR REPLACE FUNCTION get_book_inventory()
RETURNS TABLE (
    id UUID,
    book_title TEXT,
    quantity_available INTEGER,
    language TEXT,
    grade_level TEXT,
    subject TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi.id,
        b.title::TEXT as book_title,
        bi.available_quantity as quantity_available,
        COALESCE(b.language::TEXT, '') as language,
        COALESCE(b.grade_level::TEXT, '') as grade_level,
        COALESCE(b.category::TEXT, '') as subject
    FROM book_inventory bi
    LEFT JOIN books b ON bi.book_id = b.id
    WHERE bi.available_quantity > 0
    ORDER BY b.title;
END;
$$;

-- Function to get book distributions (updated for new schema)
CREATE OR REPLACE FUNCTION get_book_distributions()
RETURNS TABLE (
    id UUID,
    school_id UUID,
    school_name VARCHAR,
    book_title VARCHAR,
    quantity INTEGER,
    supervisor_id UUID,
    supervisor_name VARCHAR,
    delivery_date TEXT,
    notes TEXT,
    status VARCHAR
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bd.id,
        bd.school_id,
        s.name as school_name,
        b.title as book_title,
        bd.quantity,
        bd.supervisor_id,
        p.name as supervisor_name,
        COALESCE(bd.planned_date::TEXT, bd.actual_delivery_date::TEXT, '') as delivery_date,
        COALESCE(bd.notes, '') as notes,
        bd.status::VARCHAR
    FROM book_distributions bd
    LEFT JOIN schools s ON bd.school_id = s.id
    LEFT JOIN book_inventory bi ON bd.inventory_id = bi.id
    LEFT JOIN books b ON bi.book_id = b.id
    LEFT JOIN profiles p ON bd.supervisor_id = p.id
    ORDER BY COALESCE(bd.planned_date, bd.actual_delivery_date) DESC;
END;
$$;

-- Function to get distribution details (updated for new schema)
CREATE OR REPLACE FUNCTION get_distribution_details(p_distribution_id UUID)
RETURNS TABLE (
    id UUID,
    school_id UUID,
    school_name VARCHAR,
    book_title VARCHAR,
    quantity INTEGER,
    supervisor_id UUID,
    supervisor_name VARCHAR,
    delivery_date TEXT,
    notes TEXT,
    status VARCHAR,
    photos JSON
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bd.id,
        bd.school_id,
        s.name as school_name,
        b.title as book_title,
        bd.quantity,
        bd.supervisor_id,
        p.name as supervisor_name,
        COALESCE(bd.planned_date::TEXT, bd.actual_delivery_date::TEXT, '') as delivery_date,
        COALESCE(bd.notes, '') as notes,
        bd.status::VARCHAR,
        COALESCE(
            (SELECT json_agg(
                json_build_object(
                    'id', dp.id,
                    'photo_url', dp.photo_url,
                    'caption', dp.caption,
                    'uploaded_by', dp.uploaded_by,
                    'created_at', dp.created_at
                )
            ) FROM distribution_photos dp WHERE dp.distribution_id = bd.id),
            '[]'::json
        ) as photos
    FROM book_distributions bd
    LEFT JOIN schools s ON bd.school_id = s.id
    LEFT JOIN book_inventory bi ON bd.inventory_id = bi.id
    LEFT JOIN books b ON bi.book_id = b.id
    LEFT JOIN profiles p ON bd.supervisor_id = p.id
    WHERE bd.id = p_distribution_id;
END;
$$;

-- Function to add book distribution (legacy compatibility)
CREATE OR REPLACE FUNCTION add_book_distribution(
    p_school_id UUID,
    p_inventory_id UUID,
    p_quantity INTEGER,
    p_supervisor_id UUID,
    p_notes TEXT,
    p_book_title VARCHAR -- This parameter is kept for compatibility but not used
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    distribution_id UUID;
    available_quantity INTEGER;
    book_id_var UUID;
BEGIN
    -- Validate inputs
    IF p_quantity <= 0 THEN
        RAISE EXCEPTION 'Quantity must be greater than 0';
    END IF;

    -- Check if school exists
    IF NOT EXISTS (SELECT 1 FROM schools WHERE id = p_school_id) THEN
        RAISE EXCEPTION 'School not found';
    END IF;

    -- Check if supervisor exists
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = p_supervisor_id) THEN
        RAISE EXCEPTION 'Supervisor not found';
    END IF;

    -- Check if inventory item exists and get available quantity and book_id
    SELECT bi.available_quantity, bi.book_id
    INTO available_quantity, book_id_var
    FROM book_inventory bi
    WHERE bi.id = p_inventory_id;

    IF available_quantity IS NULL THEN
        RAISE EXCEPTION 'Book inventory item not found';
    END IF;

    IF available_quantity < p_quantity THEN
        RAISE EXCEPTION 'Insufficient inventory. Available: %, Requested: %', available_quantity, p_quantity;
    END IF;

    -- Insert distribution record using new schema
    INSERT INTO book_distributions (
        book_id,
        inventory_id,
        school_id,
        quantity,
        supervisor_id,
        notes,
        status,
        created_by
    ) VALUES (
        book_id_var,
        p_inventory_id,
        p_school_id,
        p_quantity,
        p_supervisor_id,
        COALESCE(p_notes, ''),
        'completed',
        auth.uid()
    ) RETURNING id INTO distribution_id;

    -- Update inventory quantities using new schema
    UPDATE book_inventory
    SET available_quantity = available_quantity - p_quantity,
        distributed_quantity = distributed_quantity + p_quantity,
        last_updated_by = auth.uid(),
        updated_at = NOW()
    WHERE id = p_inventory_id;

    RETURN distribution_id;
END;
$$;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION add_book_with_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION get_books_with_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION update_book_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION create_book_distribution TO authenticated;
GRANT EXECUTE ON FUNCTION get_book_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION get_book_distributions TO authenticated;
GRANT EXECUTE ON FUNCTION get_distribution_details TO authenticated;
GRANT EXECUTE ON FUNCTION add_book_distribution TO authenticated;
