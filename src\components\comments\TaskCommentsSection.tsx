import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { MessageSquare, Send } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

const commentSchema = z.object({
  comment: z.string().min(1, 'Comment cannot be empty').max(1000, 'Comment too long'),
});

type CommentFormData = z.infer<typeof commentSchema>;

interface Comment {
  id: string;
  comment: string;
  user_name: string;
  created_at: string;
}

interface TaskCommentsSectionProps {
  taskId: string;
  comments: Comment[];
}

const TaskCommentsSection: React.FC<TaskCommentsSectionProps> = ({ taskId, comments }) => {
  const [isAddingComment, setIsAddingComment] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const form = useForm<CommentFormData>({
    resolver: zodResolver(commentSchema),
    defaultValues: {
      comment: '',
    },
  });

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return 'Just now';
    } else if (diffInSeconds < 3600) {
      const minutes = Math.floor(diffInSeconds / 60);
      return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
    } else {
      const days = Math.floor(diffInSeconds / 86400);
      return `${days} day${days !== 1 ? 's' : ''} ago`;
    }
  };

  const addCommentMutation = useMutation({
    mutationFn: async (commentData: CommentFormData) => {
      console.log('💬 Adding comment to task:', taskId, 'Comment:', commentData.comment);
      
      const { data, error } = await supabase
        .rpc('add_task_comment', {
          p_task_id: taskId,
          p_comment: commentData.comment,
        });

      if (error) {
        console.error('❌ Error adding comment:', error);
        throw error;
      }
      
      console.log('✅ Comment added successfully:', data);
      return data;
    },
    onSuccess: () => {
      console.log('🔄 Invalidating queries after comment added');
      queryClient.invalidateQueries({ queryKey: ['task-details', taskId] });
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      form.reset();
      setIsAddingComment(false);
      toast({
        title: "Success",
        description: "Comment added successfully",
      });
    },
    onError: (error: Error) => {
      console.error('💥 Comment addition failed:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to add comment",
        variant: "destructive",
      });
    },
  });

  const handleAddComment = async (data: CommentFormData) => {
    console.log('📝 Submitting comment form:', data);
    await addCommentMutation.mutateAsync(data);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Comments ({comments.length})
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Existing Comments */}
        {comments.length > 0 ? (
          <div className="space-y-4 max-h-64 overflow-y-auto scrollbar-thin">
            {comments.map((comment) => (
              <div key={comment.id} className="border-l-2 border-gray-200 pl-4">
                <div className="flex items-center justify-between mb-1">
                  <span className="font-medium text-gray-900">
                    {comment.user_name}
                  </span>
                  <span className="text-xs text-gray-500">
                    {formatTimeAgo(comment.created_at)}
                  </span>
                </div>
                <p className="text-gray-600 whitespace-pre-wrap">
                  {comment.comment}
                </p>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-gray-500 text-center py-4">
            No comments yet. Be the first to add one!
          </p>
        )}

        <div className="border-t border-gray-200 my-4" />

        {/* Add Comment Form */}
        {!isAddingComment ? (
          <Button
            onClick={() => {
              console.log('📝 Opening comment form');
              setIsAddingComment(true);
            }}
            variant="outline"
            className="w-full"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Add Comment
          </Button>
        ) : (
          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleAddComment)} className="space-y-4">
              <FormField
                control={form.control}
                name="comment"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Add a comment</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Type your comment here..."
                        className="min-h-[100px]"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex gap-2">
                <Button
                  type="submit"
                  disabled={addCommentMutation.isPending}
                  size="sm"
                >
                  <Send className="h-4 w-4 mr-2" />
                  {addCommentMutation.isPending ? 'Adding...' : 'Add Comment'}
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('❌ Canceling comment form');
                    setIsAddingComment(false);
                    form.reset();
                  }}
                >
                  Cancel
                </Button>
              </div>
            </form>
          </Form>
        )}
      </CardContent>
    </Card>
  );
};

export default TaskCommentsSection;
