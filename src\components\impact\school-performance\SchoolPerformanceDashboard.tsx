import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { School, TrendingUp, Users, Building, Plus, BarChart3, Calendar, Target } from 'lucide-react';
import InfrastructureImprovements from './InfrastructureImprovements';
import AttendanceMetrics from './AttendanceMetrics';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { MetricCard } from '../shared';

interface SchoolPerformanceDashboardProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const SchoolPerformanceDashboard: React.FC<SchoolPerformanceDashboardProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  // Access control is now handled by AdminOnlyWrapper

  return (
    <PageLayout>
      <PageHeader
        title="School Performance Dashboard"
        description="Monitor infrastructure, attendance, and improvements"
        icon={School}
        actions={[
          {
            label: 'Add Performance Data',
            onClick: () => {}, // Add actual handler
            icon: Plus,
          }
        ]}
      />

      {/* Performance Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Avg Attendance Rate"
          value="87.3%"
          icon={TrendingUp}
          color="green"
        />
        <MetricCard
          title="Infrastructure Projects"
          value="23"
          icon={Building}
          color="blue"
        />
        <MetricCard
          title="Teacher-Student Ratio"
          value="1:28"
          icon={Users}
          color="purple"
        />
        <MetricCard
          title="Performance Score"
          value="4.2/5"
          icon={BarChart3}
          color="orange"
        />
      </div>

      {/* Detailed Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center space-x-2">
            <BarChart3 className="h-4 w-4" />
            <span>Overview</span>
          </TabsTrigger>
          <TabsTrigger value="infrastructure" className="flex items-center space-x-2">
            <Building className="h-4 w-4" />
            <span>Infrastructure</span>
          </TabsTrigger>
          <TabsTrigger value="attendance" className="flex items-center space-x-2">
            <Calendar className="h-4 w-4" />
            <span>Attendance</span>
          </TabsTrigger>
          <TabsTrigger value="comparative" className="flex items-center space-x-2">
            <Target className="h-4 w-4" />
            <span>Comparative</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Overview</CardTitle>
              <CardDescription>
                High-level view of school performance across all metrics
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <h4 className="font-semibold text-green-800">Overall Rating</h4>
                  <p className="text-3xl font-bold text-green-600">4.2/5</p>
                  <p className="text-sm text-green-700">Excellent Performance</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-semibold text-blue-800">Improvement Trend</h4>
                  <p className="text-3xl font-bold text-blue-600">+15%</p>
                  <p className="text-sm text-blue-700">Year over year</p>
                </div>
                <div className="text-center p-4 bg-purple-50 rounded-lg">
                  <h4 className="font-semibold text-purple-800">Target Achievement</h4>
                  <p className="text-3xl font-bold text-purple-600">87%</p>
                  <p className="text-sm text-purple-700">Goals met</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="infrastructure">
          <InfrastructureImprovements
            schoolId={schoolId}
            dateRange={dateRange}
          />
        </TabsContent>

        <TabsContent value="attendance">
          <AttendanceMetrics
            schoolId={schoolId}
            dateRange={dateRange}
          />
        </TabsContent>

        <TabsContent value="comparative" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Comparative Analysis</CardTitle>
              <CardDescription>
                Compare performance across schools and regions
              </CardDescription>
            </CardHeader>
            <CardContent className="text-center py-12">
              <div className="bg-gray-100 p-4 rounded-lg inline-block mb-4">
                <Target className="h-12 w-12 text-gray-400" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                Comparative Analysis Coming Soon
              </h3>
              <p className="text-gray-600 mb-4">
                This section will include school-to-school comparisons, regional benchmarking,
                and performance ranking systems.
              </p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default SchoolPerformanceDashboard;
