
import React from 'react';
import { ActivityType, EntityType } from './types';
import { useRecentActivities } from './queries';

// Hook for filtered activities
export const useFilteredActivities = (options: {
  activityType?: ActivityType | 'all';
  entityType?: EntityType;
  limit?: number;
  offset?: number;
} = {}) => {
  const { activityType, entityType, limit = 20, offset = 0 } = options;
  const { data: allActivities = [], ...queryResult } = useRecentActivities(limit * 2, offset); // Get more to filter
  
  // Filter activities on client side
  const filteredActivities = React.useMemo(() => {
    let filtered = allActivities;
    
    if (activityType && activityType !== 'all') {
      filtered = filtered.filter(activity => activity.activity_type === activityType);
    }
    
    if (entityType) {
      filtered = filtered.filter(activity => activity.entity_type === entityType);
    }
    
    // Limit results after filtering
    return filtered.slice(0, limit);
  }, [allActivities, activityType, entityType, limit]);
  
  return {
    ...queryResult,
    data: filteredActivities,
  };
};
