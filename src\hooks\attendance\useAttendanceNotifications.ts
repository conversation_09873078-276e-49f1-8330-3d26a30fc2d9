import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

type NotificationType = 'absence_alert' | 'late_alert' | 'low_attendance' | 'session_reminder' | 'check_in_confirmation' | 'check_out_confirmation' | 'attendance_summary';
type RecipientType = 'parent' | 'teacher' | 'admin' | 'student';
type DeliveryStatus = 'pending' | 'sent' | 'delivered' | 'failed';
type DeliveryMethod = 'sms' | 'email' | 'push' | 'in_app';
type PriorityLevel = 'low' | 'normal' | 'high' | 'urgent';

interface AttendanceNotification {
  id: string;
  notification_type: NotificationType;
  recipient_type: RecipientType;
  recipient_contact?: string;
  student_id?: string;
  session_id?: string;
  school_id: string;
  message_title: string;
  message_content: string;
  notification_data: Record<string, unknown>;
  sent_at?: string;
  delivery_status: DeliveryStatus;
  delivery_method?: DeliveryMethod;
  read_at?: string;
  response_received: boolean;
  response_content?: string;
  priority_level: PriorityLevel;
  auto_generated: boolean;
  created_by?: string;
  created_at: string;
  student?: {
    first_name: string;
    last_name: string;
    student_number: string | null;
  };
  session?: {
    session_name: string;
    session_date: string;
  };
  school?: {
    name: string;
  };
}

interface CreateNotificationData {
  notification_type: NotificationType;
  recipient_type: RecipientType;
  recipient_contact?: string;
  student_id?: string;
  session_id?: string;
  school_id: string;
  message_title: string;
  message_content: string;
  notification_data?: Record<string, unknown>;
  delivery_method?: DeliveryMethod;
  priority_level?: PriorityLevel;
  auto_generated?: boolean;
}

interface NotificationTemplate {
  type: NotificationType;
  title: string;
  content: string;
  priority: PriorityLevel;
  delivery_method: DeliveryMethod;
}

// Notification templates
const NOTIFICATION_TEMPLATES: Record<NotificationType, NotificationTemplate> = {
  absence_alert: {
    type: 'absence_alert',
    title: 'Student Absence Alert',
    content: 'Your child {student_name} was absent from {session_name} on {date}. Please contact the school if this was unexpected.',
    priority: 'normal',
    delivery_method: 'sms',
  },
  late_alert: {
    type: 'late_alert',
    title: 'Student Late Arrival',
    content: 'Your child {student_name} arrived {minutes} minutes late to {session_name} on {date}.',
    priority: 'low',
    delivery_method: 'sms',
  },
  low_attendance: {
    type: 'low_attendance',
    title: 'Low Attendance Warning',
    content: 'Your child {student_name} has an attendance rate of {attendance_rate}% which is below the required minimum. Please schedule a meeting with the school.',
    priority: 'high',
    delivery_method: 'sms',
  },
  session_reminder: {
    type: 'session_reminder',
    title: 'Session Reminder',
    content: 'Reminder: {session_name} is scheduled for {date} at {time}. Location: {location}',
    priority: 'normal',
    delivery_method: 'push',
  },
  check_in_confirmation: {
    type: 'check_in_confirmation',
    title: 'Check-in Confirmed',
    content: 'Successfully checked in at {school_name} at {time}. Session: {session_name}',
    priority: 'low',
    delivery_method: 'in_app',
  },
  check_out_confirmation: {
    type: 'check_out_confirmation',
    title: 'Check-out Confirmed',
    content: 'Successfully checked out from {school_name} at {time}. Total duration: {duration}',
    priority: 'low',
    delivery_method: 'in_app',
  },
  attendance_summary: {
    type: 'attendance_summary',
    title: 'Weekly Attendance Summary',
    content: 'Weekly attendance summary for {student_name}: {sessions_attended}/{total_sessions} sessions attended ({attendance_rate}%)',
    priority: 'normal',
    delivery_method: 'email',
  },
};

// Hook to fetch notifications
export const useAttendanceNotifications = (
  schoolId?: string,
  studentId?: string,
  unreadOnly: boolean = false
) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['attendance-notifications', schoolId, studentId, unreadOnly],
    queryFn: async () => {
      let query = supabase
        .from('attendance_notifications')
        .select(`
          *,
          student:students(first_name, last_name, student_number),
          session:attendance_sessions(session_name, session_date),
          school:schools(name)
        `)
        .order('created_at', { ascending: false });

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      if (studentId) {
        query = query.eq('student_id', studentId);
      }

      if (unreadOnly) {
        query = query.is('read_at', null);
      }

      // Filter based on user role
      if (profile?.role === 'field_staff') {
        query = query.or(`created_by.eq.${profile.id},recipient_type.eq.teacher`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching attendance notifications:', error);
        throw error;
      }

      return data as AttendanceNotification[];
    },
    enabled: !!profile?.id,
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });
};

// Hook to create notification
export const useCreateAttendanceNotification = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (notificationData: CreateNotificationData) => {
      const { data, error } = await supabase
        .from('attendance_notifications')
        .insert({
          ...notificationData,
          notification_data: notificationData.notification_data || {},
          delivery_status: 'pending' as DeliveryStatus,
          priority_level: notificationData.priority_level || 'normal',
          auto_generated: notificationData.auto_generated ?? true,
          created_by: notificationData.auto_generated ? null : undefined,
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating notification:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-notifications'] });
      toast({
        title: 'Success',
        description: 'Notification created successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to create notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to create notification. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to mark notification as read
export const useMarkNotificationRead = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (notificationId: string) => {
      const { data, error } = await supabase
        .from('attendance_notifications')
        .update({ read_at: new Date().toISOString() })
        .eq('id', notificationId)
        .select()
        .single();

      if (error) {
        console.error('Error marking notification as read:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-notifications'] });
    },
  });
};

// Hook to send notification
export const useSendNotification = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (notificationId: string) => {
      // In a real implementation, this would trigger the actual sending
      // For now, we'll just update the status
      const { data, error } = await supabase
        .from('attendance_notifications')
        .update({ 
          delivery_status: 'sent' as DeliveryStatus,
          sent_at: new Date().toISOString()
        })
        .eq('id', notificationId)
        .select()
        .single();

      if (error) {
        console.error('Error sending notification:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-notifications'] });
      toast({
        title: 'Success',
        description: 'Notification sent successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to send notification:', error);
      toast({
        title: 'Error',
        description: 'Failed to send notification. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Utility function to create absence alert
export const createAbsenceAlert = async (
  studentId: string,
  sessionId: string,
  schoolId: string,
  guardianContact?: string
) => {
  const template = NOTIFICATION_TEMPLATES.absence_alert;
  
  // Get student and session info for template variables
  const { data: student } = await supabase
    .from('students')
    .select('first_name, last_name, guardian_contact')
    .eq('id', studentId)
    .single();

  const { data: session } = await supabase
    .from('attendance_sessions')
    .select('session_name, session_date')
    .eq('id', sessionId)
    .single();

  if (!student || !session) return null;

  const messageContent = template.content
    .replace('{student_name}', `${student.first_name} ${student.last_name}`)
    .replace('{session_name}', session.session_name)
    .replace('{date}', new Date(session.session_date).toLocaleDateString());

  return {
    notification_type: template.type,
    recipient_type: 'parent' as RecipientType,
    recipient_contact: guardianContact || student.guardian_contact,
    student_id: studentId,
    session_id: sessionId,
    school_id: schoolId,
    message_title: template.title,
    message_content: messageContent,
    delivery_method: template.delivery_method,
    priority_level: template.priority,
    auto_generated: true,
    notification_data: {
      student_name: `${student.first_name} ${student.last_name}`,
      session_name: session.session_name,
      session_date: session.session_date,
    },
  };
};

// Utility function to create low attendance warning
export const createLowAttendanceWarning = async (
  studentId: string,
  schoolId: string,
  attendanceRate: number,
  guardianContact?: string
) => {
  const template = NOTIFICATION_TEMPLATES.low_attendance;
  
  const { data: student } = await supabase
    .from('students')
    .select('first_name, last_name, guardian_contact')
    .eq('id', studentId)
    .single();

  if (!student) return null;

  const messageContent = template.content
    .replace('{student_name}', `${student.first_name} ${student.last_name}`)
    .replace('{attendance_rate}', attendanceRate.toFixed(1));

  return {
    notification_type: template.type,
    recipient_type: 'parent' as RecipientType,
    recipient_contact: guardianContact || student.guardian_contact,
    student_id: studentId,
    school_id: schoolId,
    message_title: template.title,
    message_content: messageContent,
    delivery_method: template.delivery_method,
    priority_level: template.priority,
    auto_generated: true,
    notification_data: {
      student_name: `${student.first_name} ${student.last_name}`,
      attendance_rate: attendanceRate,
      threshold: 75, // Minimum required attendance rate
    },
  };
};

// Utility function to create session reminder
export const createSessionReminder = async (
  sessionId: string,
  schoolId: string,
  recipientType: RecipientType = 'teacher'
) => {
  const template = NOTIFICATION_TEMPLATES.session_reminder;
  
  const { data: session } = await supabase
    .from('attendance_sessions')
    .select(`
      session_name,
      session_date,
      start_time,
      location,
      school:schools(name)
    `)
    .eq('id', sessionId)
    .single();

  if (!session) return null;

  const messageContent = template.content
    .replace('{session_name}', session.session_name)
    .replace('{date}', new Date(session.session_date).toLocaleDateString())
    .replace('{time}', session.start_time)
    .replace('{location}', session.location || session.school?.name || 'TBD');

  return {
    notification_type: template.type,
    recipient_type: recipientType,
    session_id: sessionId,
    school_id: schoolId,
    message_title: template.title,
    message_content: messageContent,
    delivery_method: template.delivery_method,
    priority_level: template.priority,
    auto_generated: true,
    notification_data: {
      session_name: session.session_name,
      session_date: session.session_date,
      start_time: session.start_time,
      location: session.location,
      school_name: session.school?.name,
    },
  };
};

// Hook to get notification statistics
export const useNotificationStats = (schoolId?: string) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['notification-stats', schoolId],
    queryFn: async () => {
      let query = supabase
        .from('attendance_notifications')
        .select('notification_type, delivery_status, priority_level, created_at');

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      // Filter based on user role
      if (profile?.role === 'field_staff') {
        query = query.or(`created_by.eq.${profile.id},recipient_type.eq.teacher`);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching notification stats:', error);
        throw error;
      }

      // Calculate statistics
      const stats = {
        total: data?.length || 0,
        by_type: {} as Record<string, number>,
        by_status: {} as Record<string, number>,
        by_priority: {} as Record<string, number>,
        recent_24h: 0,
        pending: 0,
        failed: 0,
      };

      const now = new Date();
      const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);

      data?.forEach(notification => {
        // Count by type
        stats.by_type[notification.notification_type] = 
          (stats.by_type[notification.notification_type] || 0) + 1;

        // Count by status
        stats.by_status[notification.delivery_status] = 
          (stats.by_status[notification.delivery_status] || 0) + 1;

        // Count by priority
        stats.by_priority[notification.priority_level] = 
          (stats.by_priority[notification.priority_level] || 0) + 1;

        // Count recent notifications
        if (new Date(notification.created_at) > yesterday) {
          stats.recent_24h++;
        }

        // Count pending and failed
        if (notification.delivery_status === 'pending') {
          stats.pending++;
        } else if (notification.delivery_status === 'failed') {
          stats.failed++;
        }
      });

      return stats;
    },
    enabled: !!profile?.id,
  });
};
