import React from 'react';
import { Card, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  BookOpen, 
  Users, 
  Award, 
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  Target,
  ArrowRight
} from 'lucide-react';
import { DashboardMetrics, StaffPerformance } from '@/hooks/dashboard/useDashboardMetrics';

interface ImpactIndicatorsProps {
  metrics: DashboardMetrics;
  staffPerformance: StaffPerformance[];
  isLoading?: boolean;
  onViewDetails?: (section: string) => void;
}

interface ProgressCardProps {
  title: string;
  current: number;
  target: number;
  unit: string;
  color: 'green' | 'blue' | 'purple' | 'orange';
  icon: React.ElementType;
  trend?: number;
}

const ProgressCard: React.FC<ProgressCardProps> = ({
  title,
  current,
  target,
  unit,
  color,
  icon: Icon,
  trend
}) => {
  const percentage = Math.min(100, (current / target) * 100);
  const isOnTrack = percentage >= 80;
  
  const colorClasses = {
    green: 'text-green-600',
    blue: 'text-blue-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600',
  };

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <Icon className={`h-5 w-5 ${colorClasses[color]}`} />
            <h3 className="font-medium text-gray-900">{title}</h3>
          </div>
          {isOnTrack ? (
            <CheckCircle className="h-5 w-5 text-green-500" />
          ) : (
            <AlertCircle className="h-5 w-5 text-yellow-500" />
          )}
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">Progress</span>
            <span className="font-medium">
              {current.toLocaleString()} / {target.toLocaleString()} {unit}
            </span>
          </div>
          <Progress value={percentage} className="h-2" />
          <div className="flex justify-between items-center">
            <span className="text-sm text-gray-500">{percentage.toFixed(1)}% complete</span>
            {trend && (
              <div className="flex items-center space-x-1">
                <TrendingUp className="h-3 w-3 text-green-500" />
                <span className="text-xs text-green-600">+{trend}%</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

const TopPerformerCard: React.FC<{
  staff: StaffPerformance;
  rank: number;
}> = ({ staff, rank }) => {
  const rankColors = {
    1: 'bg-yellow-100 text-yellow-800',
    2: 'bg-gray-100 text-gray-800',
    3: 'bg-orange-100 text-orange-800',
  };

  const defaultColor = 'bg-blue-100 text-blue-800';
  const rankColor = rankColors[rank as keyof typeof rankColors] || defaultColor;

  return (
    <div className="flex items-center justify-between p-3 bg-white border rounded-lg">
      <div className="flex items-center space-x-3">
        <Badge className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${rankColor}`}>
          {rank}
        </Badge>
        <div>
          <p className="font-medium text-gray-900">{staff.name}</p>
          <p className="text-sm text-gray-500">
            {staff.studentsReached} students • {staff.schoolsVisited} schools
          </p>
        </div>
      </div>
      <div className="text-right">
        <p className="text-lg font-bold text-gray-900">{staff.performanceScore.toFixed(0)}</p>
        <p className="text-xs text-gray-500">score</p>
      </div>
    </div>
  );
};

const QualityMetricCard: React.FC<{
  title: string;
  value: number;
  unit: string;
  target: number;
  icon: React.ElementType;
}> = ({ title, value, unit, target, icon: Icon }) => {
  const isGood = value >= target;
  
  return (
    <div className="p-4 bg-gray-50 rounded-lg">
      <div className="flex items-center justify-between mb-2">
        <Icon className={`h-5 w-5 ${isGood ? 'text-green-600' : 'text-yellow-600'}`} />
        <Badge variant={isGood ? 'default' : 'secondary'}>
          {isGood ? 'On Track' : 'Needs Attention'}
        </Badge>
      </div>
      <h4 className="font-medium text-gray-900 mb-1">{title}</h4>
      <p className="text-2xl font-bold text-gray-900">
        {value}{unit}
      </p>
      <p className="text-sm text-gray-500">Target: {target}{unit}</p>
    </div>
  );
};

export const ImpactIndicators: React.FC<ImpactIndicatorsProps> = ({
  metrics,
  staffPerformance,
  isLoading,
  onViewDetails
}) => {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Impact Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-32 bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const progressTargets = [
    {
      title: 'Book Distribution',
      current: 8500,
      target: 10000,
      unit: 'books',
      color: 'green' as const,
      icon: BookOpen,
      trend: 12,
    },
    {
      title: 'Student Engagement',
      current: metrics.programReach.totalStudentsReached,
      target: 15000,
      unit: 'students',
      color: 'blue' as const,
      icon: Users,
      trend: 8,
    },
    {
      title: 'Session Quality',
      current: Math.round(metrics.quality.studentEngagementScore * 20),
      target: 90,
      unit: '%',
      color: 'purple' as const,
      icon: Award,
      trend: 5,
    },
    {
      title: 'Task Completion',
      current: Math.round(metrics.operational.taskCompletionRate),
      target: 95,
      unit: '%',
      color: 'orange' as const,
      icon: Target,
      trend: 3,
    },
  ];

  const qualityMetrics = [
    {
      title: 'Attendance Rate',
      value: Math.round(metrics.programReach.averageAttendancePerSession),
      unit: '%',
      target: 85,
      icon: Users,
    },
    {
      title: 'Report Quality',
      value: Math.round(metrics.operational.reportQualityScore),
      unit: '%',
      target: 90,
      icon: Award,
    },
    {
      title: 'Response Time',
      value: metrics.quality.challengeResolutionTime,
      unit: 'd',
      target: 2,
      icon: Clock,
    },
  ];

  const topPerformers = staffPerformance
    .sort((a, b) => b.performanceScore - a.performanceScore)
    .slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Progress Tracking */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Impact Progress</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDetails?.('progress')}
            >
              View Details
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {progressTargets.map((target, index) => (
              <ProgressCard key={index} {...target} />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quality Indicators */}
      <Card>
        <CardHeader>
          <CardTitle>Quality Indicators</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {qualityMetrics.map((metric, index) => (
              <QualityMetricCard key={index} {...metric} />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Top Performers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Top Performers This Week</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onViewDetails?.('staff')}
            >
              View All Staff
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {topPerformers.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Users className="h-8 w-8 mx-auto mb-2" />
                <p>No performance data available</p>
              </div>
            ) : (
              topPerformers.map((staff, index) => (
                <TopPerformerCard
                  key={staff.id}
                  staff={staff}
                  rank={index + 1}
                />
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
