import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface ContentCardProps {
  title?: string;
  description?: string;
  icon?: LucideIcon;
  children: React.ReactNode;
  className?: string;
  headerClassName?: string;
  contentClassName?: string;
  noPadding?: boolean;
}

/**
 * Standardized content card component that provides consistent styling
 * for main content areas across all pages.
 */
const ContentCard: React.FC<ContentCardProps> = ({
  title,
  description,
  icon: Icon,
  children,
  className,
  headerClassName,
  contentClassName,
  noPadding = false
}) => {
  return (
    <Card className={cn("border-0 shadow-sm", className)}>
      {(title || description || Icon) && (
        <CardHeader className={cn("pb-4", headerClassName)}>
          {title && (
            <CardTitle className="flex items-center gap-2">
              {Icon && <Icon className="h-5 w-5 text-ilead-green" />}
              {title}
            </CardTitle>
          )}
          {description && (
            <CardDescription>{description}</CardDescription>
          )}
        </CardHeader>
      )}
      <CardContent className={cn(
        !noPadding && "pt-0",
        noPadding && "p-0",
        contentClassName
      )}>
        {children}
      </CardContent>
    </Card>
  );
};

export default ContentCard;
