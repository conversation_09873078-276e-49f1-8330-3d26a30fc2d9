-- Enhanced School Management Fields Migration
-- Migration 018: Add new fields for comprehensive school management

-- Add new enum value to school_type for university
ALTER TYPE school_type ADD VALUE IF NOT EXISTS 'university';

-- Create school category enum
CREATE TYPE school_category AS ENUM ('day', 'boarding', 'both');

-- Add new columns to schools table
ALTER TABLE schools 
ADD COLUMN IF NOT EXISTS school_category school_category DEFAULT 'day',
ADD COLUMN IF NOT EXISTS date_joined_ilead DATE,
ADD COLUMN IF NOT EXISTS location_coordinates POINT,
ADD COLUMN IF NOT EXISTS is_partner_managed BOOLEAN DEFAULT false,
ADD COLUMN IF NOT EXISTS partner_name <PERSON><PERSON><PERSON><PERSON>(255),
ADD COLUMN IF NOT EXISTS champion_teacher_count INTEGER;

-- Create champion teachers contact table
CREATE TABLE IF NOT EXISTS school_champion_teachers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    school_id UUID REFERENCES schools(id) ON DELETE CASCADE NOT NULL,
    contact_type VARCHAR(50) NOT NULL CHECK (contact_type IN ('champion', 'assistant_champion')),
    name VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    email VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_school_champion_teachers_school_id ON school_champion_teachers(school_id);
CREATE INDEX IF NOT EXISTS idx_school_champion_teachers_type ON school_champion_teachers(contact_type);
CREATE INDEX IF NOT EXISTS idx_school_champion_teachers_active ON school_champion_teachers(is_active);
CREATE INDEX IF NOT EXISTS idx_schools_category ON schools(school_category);
CREATE INDEX IF NOT EXISTS idx_schools_partner_managed ON schools(is_partner_managed);
CREATE INDEX IF NOT EXISTS idx_schools_date_joined ON schools(date_joined_ilead);

-- Enable RLS on the new table
ALTER TABLE school_champion_teachers ENABLE ROW LEVEL SECURITY;

-- RLS Policies for school_champion_teachers table
CREATE POLICY "Users can view champion teachers from accessible schools" ON school_champion_teachers
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        ) OR
        school_id IN (
            SELECT school_id FROM tasks
            WHERE assigned_to = auth.uid() OR created_by = auth.uid()
        )
    );

CREATE POLICY "Admins and program officers can insert champion teachers" ON school_champion_teachers
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Admins and program officers can update champion teachers" ON school_champion_teachers
    FOR UPDATE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Only admins can delete champion teachers" ON school_champion_teachers
    FOR DELETE USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role = 'admin'
        )
    );

-- Update the add_school function to handle new fields
CREATE OR REPLACE FUNCTION add_school_enhanced(
    p_name VARCHAR(255),
    p_code VARCHAR(50) DEFAULT NULL,
    p_school_type school_type,
    p_school_category school_category DEFAULT 'day',
    p_student_count INTEGER DEFAULT NULL,
    p_teacher_count INTEGER DEFAULT NULL,
    p_contact_phone VARCHAR(50) DEFAULT NULL,
    p_email VARCHAR(255) DEFAULT NULL,
    p_division_id UUID,
    p_classes_count INTEGER DEFAULT NULL,
    p_streams_per_class INTEGER DEFAULT NULL,
    p_head_teacher_name VARCHAR(255) DEFAULT NULL,
    p_deputy_head_teacher_name VARCHAR(255) DEFAULT NULL,
    p_date_joined_ilead DATE DEFAULT NULL,
    p_ownership_type VARCHAR(50) DEFAULT NULL,
    p_location_coordinates POINT DEFAULT NULL,
    p_nearest_health_center VARCHAR(255) DEFAULT NULL,
    p_distance_to_main_road VARCHAR(255) DEFAULT NULL,
    p_infrastructure_notes TEXT DEFAULT NULL,
    p_is_partner_managed BOOLEAN DEFAULT false,
    p_partner_name VARCHAR(255) DEFAULT NULL,
    p_champion_teacher_count INTEGER DEFAULT NULL,
    p_champion_teachers JSONB DEFAULT '[]',
    p_assistant_champion_teachers JSONB DEFAULT '[]'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_school_id UUID;
    v_champion_teacher JSONB;
    v_assistant_teacher JSONB;
BEGIN
    -- Check if user has permission to add schools
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins and program officers can add schools.';
    END IF;

    -- Insert the school
    INSERT INTO schools (
        name, code, school_type, school_category, student_count, teacher_count,
        contact_phone, email, division_id, classes_count, streams_per_class,
        head_teacher_name, deputy_head_teacher_name, date_joined_ilead,
        ownership_type, location_coordinates, nearest_health_center,
        distance_to_main_road, infrastructure_notes, is_partner_managed,
        partner_name, champion_teacher_count, created_by
    ) VALUES (
        p_name, p_code, p_school_type, p_school_category, p_student_count, p_teacher_count,
        p_contact_phone, p_email, p_division_id, p_classes_count, p_streams_per_class,
        p_head_teacher_name, p_deputy_head_teacher_name, p_date_joined_ilead,
        p_ownership_type, p_location_coordinates, p_nearest_health_center,
        p_distance_to_main_road, p_infrastructure_notes, p_is_partner_managed,
        p_partner_name, p_champion_teacher_count, auth.uid()
    ) RETURNING id INTO v_school_id;

    -- Insert champion teachers
    FOR v_champion_teacher IN SELECT * FROM jsonb_array_elements(p_champion_teachers)
    LOOP
        IF v_champion_teacher->>'name' IS NOT NULL AND trim(v_champion_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'champion',
                v_champion_teacher->>'name',
                v_champion_teacher->>'phone',
                v_champion_teacher->>'email'
            );
        END IF;
    END LOOP;

    -- Insert assistant champion teachers
    FOR v_assistant_teacher IN SELECT * FROM jsonb_array_elements(p_assistant_champion_teachers)
    LOOP
        IF v_assistant_teacher->>'name' IS NOT NULL AND trim(v_assistant_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'assistant_champion',
                v_assistant_teacher->>'name',
                v_assistant_teacher->>'phone',
                v_assistant_teacher->>'email'
            );
        END IF;
    END LOOP;

    RETURN v_school_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION add_school_enhanced TO authenticated;

-- Create function to get school with champion teachers
CREATE OR REPLACE FUNCTION get_school_with_contacts(p_school_id UUID)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_school JSONB;
    v_champion_teachers JSONB;
    v_assistant_teachers JSONB;
BEGIN
    -- Get school data
    SELECT to_jsonb(s.*) INTO v_school
    FROM schools s
    WHERE s.id = p_school_id;

    -- Get champion teachers
    SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
            'id', id,
            'name', name,
            'phone', phone,
            'email', email
        )
    ), '[]'::jsonb) INTO v_champion_teachers
    FROM school_champion_teachers
    WHERE school_id = p_school_id AND contact_type = 'champion' AND is_active = true;

    -- Get assistant champion teachers
    SELECT COALESCE(jsonb_agg(
        jsonb_build_object(
            'id', id,
            'name', name,
            'phone', phone,
            'email', email
        )
    ), '[]'::jsonb) INTO v_assistant_teachers
    FROM school_champion_teachers
    WHERE school_id = p_school_id AND contact_type = 'assistant_champion' AND is_active = true;

    -- Combine all data
    RETURN v_school || jsonb_build_object(
        'champion_teachers', v_champion_teachers,
        'assistant_champion_teachers', v_assistant_teachers
    );
END;
$$;

-- Grant execute permission
GRANT EXECUTE ON FUNCTION get_school_with_contacts TO authenticated;
