import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { 
  HardDrive, 
  AlertTriangle, 
  CheckCircle, 
  XCircle, 
  Trash2, 
  RefreshCw,
  Database,
  FileText,
  Camera,
  Clock
} from 'lucide-react';
import { storageManager, StorageUsage, CleanupReport } from '@/utils/storageManager';
import { useOfflineSync } from '@/hooks/field-staff/useOfflineSync';
import { toast } from 'sonner';

interface StorageMonitorProps {
  showDetails?: boolean;
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export const StorageMonitor: React.FC<StorageMonitorProps> = ({
  showDetails = true,
  autoRefresh = true,
  refreshInterval = 30000, // 30 seconds
}) => {
  const [storageUsage, setStorageUsage] = useState<StorageUsage | null>(null);
  const [storageHealth, setStorageHealth] = useState<{
    status: 'healthy' | 'warning' | 'critical';
    usage: StorageUsage;
    recommendations: string[];
  } | null>(null);
  const [cleanupHistory, setCleanupHistory] = useState<CleanupReport[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCleaningUp, setIsCleaningUp] = useState(false);
  
  const { syncStatus, getStorageStats, performComprehensiveCleanup } = useOfflineSync();

  const refreshStorageData = useCallback(async () => {
    try {
      setIsLoading(true);
      const [usage, health, history, offlineStats] = await Promise.all([
        storageManager.getStorageUsage(),
        storageManager.getStorageHealth(),
        Promise.resolve(storageManager.getCleanupHistory()),
        getStorageStats(),
      ]);

      setStorageUsage(usage);
      setStorageHealth(health);
      setCleanupHistory(history);
    } catch (error) {
      console.error('Error refreshing storage data:', error);
      toast.error('Failed to refresh storage information');
    } finally {
      setIsLoading(false);
    }
  }, [getStorageStats]);

  const handleCleanup = async () => {
    try {
      setIsCleaningUp(true);
      toast.info('Starting storage cleanup...');
      
      const report = await storageManager.performCleanup();
      
      if (report.errors.length > 0) {
        toast.warning(`Cleanup completed with ${report.errors.length} warnings`);
      } else {
        toast.success(`Cleanup completed: ${report.itemsRemoved} items removed, ${formatBytes(report.sizeFreed)} freed`);
      }
      
      // Refresh data after cleanup
      await refreshStorageData();
    } catch (error) {
      console.error('Error during cleanup:', error);
      toast.error('Storage cleanup failed');
    } finally {
      setIsCleaningUp(false);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStatusColor = (percentage: number) => {
    if (percentage > 90) return 'text-red-600';
    if (percentage > 80) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <HardDrive className="h-4 w-4" />;
    }
  };

  useEffect(() => {
    refreshStorageData();
  }, [refreshStorageData]);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(refreshStorageData, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval, refreshStorageData]);

  // Show storage alerts based on usage
  useEffect(() => {
    if (storageHealth) {
      if (storageHealth.status === 'critical') {
        toast.error('Storage is critically full! Please clean up offline data.');
      } else if (storageHealth.status === 'warning') {
        toast.warning('Storage usage is high. Consider cleaning up offline data.');
      }
    }
  }, [storageHealth]);

  if (isLoading && !storageUsage) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HardDrive className="h-5 w-5" />
            Storage Monitor
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center p-4">
            <RefreshCw className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading storage information...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Main Storage Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <HardDrive className="h-5 w-5" />
              Storage Monitor
            </div>
            <div className="flex items-center gap-2">
              {storageHealth && getStatusIcon(storageHealth.status)}
              <Badge variant={storageHealth?.status === 'critical' ? 'destructive' : 
                             storageHealth?.status === 'warning' ? 'secondary' : 'default'}>
                {storageHealth?.status || 'Unknown'}
              </Badge>
            </div>
          </CardTitle>
          <CardDescription>
            Monitor offline storage usage and manage cleanup
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {storageUsage && (
            <>
              {/* Overall Usage */}
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Total Storage Usage</span>
                  <span className={`text-sm font-bold ${getStatusColor(storageUsage.percentage)}`}>
                    {storageUsage.percentage.toFixed(1)}%
                  </span>
                </div>
                <Progress value={storageUsage.percentage} className="h-2" />
                <div className="flex justify-between text-xs text-muted-foreground">
                  <span>{formatBytes(storageUsage.total)} used</span>
                  <span>{formatBytes(60 * 1024 * 1024)} total</span>
                </div>
              </div>

              {/* Storage Breakdown */}
              {showDetails && (
                <div className="grid grid-cols-2 gap-4 pt-4 border-t">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Database className="h-4 w-4" />
                      <span className="text-sm">Offline Data</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatBytes(storageUsage.localStorage)}
                    </div>
                  </div>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Camera className="h-4 w-4" />
                      <span className="text-sm">Photos</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {formatBytes(storageUsage.indexedDB)}
                    </div>
                  </div>
                </div>
              )}

              {/* Sync Status */}
              <div className="grid grid-cols-3 gap-4 pt-4 border-t">
                <div className="text-center">
                  <div className="text-lg font-bold">{syncStatus.pendingItems}</div>
                  <div className="text-xs text-muted-foreground">Pending</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold">{syncStatus.failedItems}</div>
                  <div className="text-xs text-muted-foreground">Failed</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold">{syncStatus.conflictItems}</div>
                  <div className="text-xs text-muted-foreground">Conflicts</div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex gap-2 pt-4 border-t">
                <Button
                  onClick={handleCleanup}
                  disabled={isCleaningUp}
                  variant="outline"
                  size="sm"
                  className="flex-1"
                >
                  {isCleaningUp ? (
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <Trash2 className="h-4 w-4 mr-2" />
                  )}
                  {isCleaningUp ? 'Cleaning...' : 'Clean Up'}
                </Button>
                <Button
                  onClick={refreshStorageData}
                  disabled={isLoading}
                  variant="outline"
                  size="sm"
                >
                  <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Storage Health Alerts */}
      {storageHealth && storageHealth.recommendations.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <div className="font-medium">Storage Recommendations:</div>
              <ul className="list-disc list-inside text-sm space-y-1">
                {storageHealth.recommendations.map((rec: string, index: number) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Recent Cleanup History */}
      {showDetails && cleanupHistory.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Recent Cleanup History
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {cleanupHistory.slice(-3).reverse().map((report, index) => (
                <div key={index} className="flex justify-between items-center p-2 bg-muted rounded">
                  <div className="text-sm">
                    <div>{new Date(report.timestamp).toLocaleString()}</div>
                    <div className="text-xs text-muted-foreground">
                      {report.itemsRemoved} items • {formatBytes(report.sizeFreed)} freed
                    </div>
                  </div>
                  {report.errors.length > 0 && (
                    <Badge variant="secondary" className="text-xs">
                      {report.errors.length} warnings
                    </Badge>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default StorageMonitor;
