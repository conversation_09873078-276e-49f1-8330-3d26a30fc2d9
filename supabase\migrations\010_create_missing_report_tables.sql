-- Create Missing Report Tables for iLEAD Field Tracker Impact Measurement
-- This migration creates the missing database tables for NGO funder reporting requirements

-- First, extend the activity_type enum to include report submission types
ALTER TYPE activity_type ADD VALUE IF NOT EXISTS 'field_report_submitted';
ALTER TYPE activity_type ADD VALUE IF NOT EXISTS 'activity_report_submitted';
ALTER TYPE activity_type ADD VALUE IF NOT EXISTS 'monthly_report_generated';
ALTER TYPE activity_type ADD VALUE IF NOT EXISTS 'baseline_questionnaire_completed';

-- Create report type enum for field reports
CREATE TYPE field_report_type AS ENUM (
    'arrival', 
    'departure', 
    'activity', 
    'incident',
    'inspection',
    'training',
    'distribution'
);

-- Create activity report type enum
CREATE TYPE activity_report_type AS ENUM (
    'leadership_training',
    'school_visit',
    'community_engagement',
    'capacity_building',
    'monitoring_evaluation'
);

-- Create report status enum
CREATE TYPE report_status AS ENUM (
    'draft',
    'submitted',
    'reviewed',
    'approved',
    'published'
);

-- Create gender enum for participant tracking
CREATE TYPE gender_type AS ENUM ('male', 'female', 'other', 'prefer_not_to_say');

-- Create leadership skill enum for baseline questionnaires
CREATE TYPE leadership_skill AS ENUM (
    'communication',
    'teamwork',
    'problem_solving',
    'critical_thinking',
    'public_speaking',
    'decision_making',
    'conflict_resolution',
    'project_management',
    'time_management',
    'emotional_intelligence'
);

-- 1. Create field_reports table (fixing the missing table referenced in types)
CREATE TABLE field_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_type field_report_type NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    findings TEXT,
    recommendations TEXT,
    school_id UUID REFERENCES schools(id),
    distribution_id UUID, -- References book_distributions if exists
    reported_by UUID REFERENCES profiles(id),
    gps_coordinates POINT, -- PostGIS point for GPS coordinates
    photos JSONB DEFAULT '[]', -- Array of photo URLs
    metadata JSONB DEFAULT '{}', -- Additional flexible data
    status report_status DEFAULT 'draft',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 2. Create activity_reports table for structured NGO activity reporting
CREATE TABLE activity_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    activity_name VARCHAR(255) NOT NULL,
    activity_type activity_report_type NOT NULL,
    venue VARCHAR(255),
    activity_date DATE NOT NULL,
    start_time TIME,
    end_time TIME,
    duration_hours DECIMAL(4,2),
    
    -- Facilitator Information
    primary_facilitator VARCHAR(255),
    co_facilitators TEXT[], -- Array of co-facilitator names
    facilitator_organization VARCHAR(255),
    
    -- School and Participant Information
    school_id UUID REFERENCES schools(id),
    school_name VARCHAR(255), -- Denormalized for reporting
    school_district VARCHAR(255),
    school_region VARCHAR(255),
    
    -- Participant Demographics
    total_participants INTEGER DEFAULT 0,
    male_participants INTEGER DEFAULT 0,
    female_participants INTEGER DEFAULT 0,
    grade_levels INTEGER[], -- Array of grade levels represented
    
    -- Activity Details
    topics_covered TEXT[], -- Array of topics/modules covered
    learning_objectives TEXT[], -- Array of learning objectives
    activities_conducted TEXT[], -- Array of specific activities
    materials_used TEXT[], -- Array of materials/resources used
    
    -- Observations and Feedback
    participant_engagement_level INTEGER CHECK (participant_engagement_level >= 1 AND participant_engagement_level <= 5),
    key_observations TEXT,
    participant_feedback TEXT,
    challenges_encountered TEXT,
    solutions_implemented TEXT,
    
    -- Outcomes and Impact
    lessons_learned TEXT,
    recommendations TEXT,
    follow_up_actions TEXT,
    next_steps TEXT,
    
    -- Media and Documentation
    photos JSONB DEFAULT '[]', -- Array of photo URLs
    videos JSONB DEFAULT '[]', -- Array of video URLs
    documents JSONB DEFAULT '[]', -- Array of document URLs
    
    -- Administrative
    created_by UUID REFERENCES profiles(id) NOT NULL,
    reviewed_by UUID REFERENCES profiles(id),
    approved_by UUID REFERENCES profiles(id),
    status report_status DEFAULT 'draft',
    submission_date TIMESTAMP WITH TIME ZONE,
    review_date TIMESTAMP WITH TIME ZONE,
    approval_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 3. Create monthly_reports table for automated monthly compilations
CREATE TABLE monthly_reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_month INTEGER NOT NULL CHECK (report_month >= 1 AND report_month <= 12),
    report_year INTEGER NOT NULL,
    report_title VARCHAR(255) NOT NULL,
    
    -- Summary Statistics
    total_activities INTEGER DEFAULT 0,
    total_participants INTEGER DEFAULT 0,
    total_schools_reached INTEGER DEFAULT 0,
    total_facilitators INTEGER DEFAULT 0,
    
    -- Activity Breakdown
    leadership_training_sessions INTEGER DEFAULT 0,
    school_visits INTEGER DEFAULT 0,
    community_engagements INTEGER DEFAULT 0,
    capacity_building_sessions INTEGER DEFAULT 0,
    
    -- Participant Demographics Summary
    total_male_participants INTEGER DEFAULT 0,
    total_female_participants INTEGER DEFAULT 0,
    participant_age_distribution JSONB DEFAULT '{}',
    grade_level_distribution JSONB DEFAULT '{}',
    
    -- Geographic Coverage
    regions_covered TEXT[],
    districts_covered TEXT[],
    schools_list JSONB DEFAULT '[]', -- Array of school objects with details
    
    -- Performance Metrics
    planned_activities INTEGER DEFAULT 0,
    achieved_activities INTEGER DEFAULT 0,
    achievement_rate DECIMAL(5,2), -- Percentage
    average_participant_satisfaction DECIMAL(3,2),
    
    -- Challenges and Solutions
    key_challenges TEXT,
    solutions_implemented TEXT,
    lessons_learned TEXT,
    
    -- Forward Planning
    next_month_targets TEXT,
    resource_requirements TEXT,
    recommendations TEXT,
    
    -- Linked Activity Reports
    activity_report_ids UUID[], -- Array of activity report IDs included
    
    -- Administrative
    generated_by UUID REFERENCES profiles(id) NOT NULL,
    reviewed_by UUID REFERENCES profiles(id),
    approved_by UUID REFERENCES profiles(id),
    status report_status DEFAULT 'draft',
    generation_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    submission_date TIMESTAMP WITH TIME ZONE,
    review_date TIMESTAMP WITH TIME ZONE,
    approval_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure unique monthly reports
    UNIQUE(report_month, report_year)
);

-- 4. Create baseline_questionnaires table for participant assessments
CREATE TABLE baseline_questionnaires (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    questionnaire_title VARCHAR(255) NOT NULL,
    description TEXT,
    version VARCHAR(50) DEFAULT '1.0',

    -- Target Information
    target_program_type leadership_program_type,
    target_grade_levels INTEGER[],
    target_age_range VARCHAR(50),

    -- Questionnaire Structure
    demographic_questions JSONB DEFAULT '{}', -- Structured demographic questions
    leadership_skills_matrix JSONB DEFAULT '{}', -- 1-5 scale skills assessment
    open_ended_questions JSONB DEFAULT '{}', -- Learning objectives and aspirations
    custom_questions JSONB DEFAULT '{}', -- Additional flexible questions

    -- Scoring Configuration
    scoring_criteria JSONB DEFAULT '{}', -- How to calculate scores
    skill_weights JSONB DEFAULT '{}', -- Weights for different skills

    -- Administrative
    created_by UUID REFERENCES profiles(id) NOT NULL,
    is_active BOOLEAN DEFAULT true,
    version_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 5. Create participant_responses table for questionnaire data storage
CREATE TABLE participant_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    questionnaire_id UUID REFERENCES baseline_questionnaires(id) NOT NULL,

    -- Participant Information
    participant_name VARCHAR(255) NOT NULL,
    participant_age INTEGER,
    participant_gender gender_type,
    grade_level INTEGER,
    school_id UUID REFERENCES schools(id),
    school_name VARCHAR(255), -- Denormalized for reporting

    -- Contact Information
    guardian_name VARCHAR(255),
    guardian_contact VARCHAR(100),
    participant_contact VARCHAR(100),

    -- Leadership Program Context
    leadership_program_id UUID REFERENCES student_leadership_programs(id),
    program_name VARCHAR(255),
    assessment_type assessment_type DEFAULT 'baseline',

    -- Response Data
    demographic_responses JSONB DEFAULT '{}', -- Responses to demographic questions
    leadership_skill_scores JSONB DEFAULT '{}', -- 1-5 scale scores for each skill
    open_ended_responses JSONB DEFAULT '{}', -- Text responses to open questions
    custom_responses JSONB DEFAULT '{}', -- Additional responses

    -- Calculated Scores
    overall_leadership_score DECIMAL(4,2), -- Calculated overall score
    skill_category_scores JSONB DEFAULT '{}', -- Scores by skill category
    improvement_areas TEXT[], -- Identified areas for development
    strengths TEXT[], -- Identified strengths

    -- Administrative
    response_date TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_by UUID REFERENCES profiles(id), -- Staff member who administered
    completion_time_minutes INTEGER, -- Time taken to complete
    response_quality_score INTEGER CHECK (response_quality_score >= 1 AND response_quality_score <= 5),
    notes TEXT, -- Additional observations

    -- Follow-up Tracking
    follow_up_required BOOLEAN DEFAULT false,
    follow_up_date DATE,
    follow_up_notes TEXT,

    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_field_reports_school_id ON field_reports(school_id);
CREATE INDEX idx_field_reports_reported_by ON field_reports(reported_by);
CREATE INDEX idx_field_reports_report_type ON field_reports(report_type);
CREATE INDEX idx_field_reports_status ON field_reports(status);
CREATE INDEX idx_field_reports_created_at ON field_reports(created_at DESC);

CREATE INDEX idx_activity_reports_school_id ON activity_reports(school_id);
CREATE INDEX idx_activity_reports_activity_type ON activity_reports(activity_type);
CREATE INDEX idx_activity_reports_activity_date ON activity_reports(activity_date);
CREATE INDEX idx_activity_reports_created_by ON activity_reports(created_by);
CREATE INDEX idx_activity_reports_status ON activity_reports(status);

CREATE INDEX idx_monthly_reports_month_year ON monthly_reports(report_year, report_month);
CREATE INDEX idx_monthly_reports_generated_by ON monthly_reports(generated_by);
CREATE INDEX idx_monthly_reports_status ON monthly_reports(status);

CREATE INDEX idx_baseline_questionnaires_active ON baseline_questionnaires(is_active);
CREATE INDEX idx_baseline_questionnaires_program_type ON baseline_questionnaires(target_program_type);

CREATE INDEX idx_participant_responses_questionnaire_id ON participant_responses(questionnaire_id);
CREATE INDEX idx_participant_responses_school_id ON participant_responses(school_id);
CREATE INDEX idx_participant_responses_program_id ON participant_responses(leadership_program_id);
CREATE INDEX idx_participant_responses_assessment_type ON participant_responses(assessment_type);
CREATE INDEX idx_participant_responses_response_date ON participant_responses(response_date);

-- Create updated_at triggers for all tables
CREATE TRIGGER trigger_field_reports_updated_at
    BEFORE UPDATE ON field_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_activity_reports_updated_at
    BEFORE UPDATE ON activity_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_monthly_reports_updated_at
    BEFORE UPDATE ON monthly_reports
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_baseline_questionnaires_updated_at
    BEFORE UPDATE ON baseline_questionnaires
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_participant_responses_updated_at
    BEFORE UPDATE ON participant_responses
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security (RLS) for all new tables
ALTER TABLE field_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE activity_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE monthly_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE baseline_questionnaires ENABLE ROW LEVEL SECURITY;
ALTER TABLE participant_responses ENABLE ROW LEVEL SECURITY;

-- RLS Policies for field_reports
CREATE POLICY "Users can view field reports from their organization" ON field_reports
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND (p.role = 'admin' OR reported_by = auth.uid())
        )
    );

CREATE POLICY "Users can insert their own field reports" ON field_reports
    FOR INSERT WITH CHECK (reported_by = auth.uid());

CREATE POLICY "Users can update their own field reports" ON field_reports
    FOR UPDATE USING (reported_by = auth.uid());

-- RLS Policies for activity_reports
CREATE POLICY "Users can view activity reports based on role" ON activity_reports
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND (p.role = 'admin' OR p.role = 'program_officer' OR created_by = auth.uid())
        )
    );

CREATE POLICY "Users can insert activity reports" ON activity_reports
    FOR INSERT WITH CHECK (created_by = auth.uid());

CREATE POLICY "Users can update their own activity reports or admins can update any" ON activity_reports
    FOR UPDATE USING (
        created_by = auth.uid() OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- RLS Policies for monthly_reports
CREATE POLICY "Admins and program officers can view monthly reports" ON monthly_reports
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND (p.role = 'admin' OR p.role = 'program_officer')
        )
    );

CREATE POLICY "Admins and program officers can manage monthly reports" ON monthly_reports
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND (p.role = 'admin' OR p.role = 'program_officer')
        )
    );

-- RLS Policies for baseline_questionnaires
CREATE POLICY "All authenticated users can view active questionnaires" ON baseline_questionnaires
    FOR SELECT USING (is_active = true AND auth.uid() IS NOT NULL);

CREATE POLICY "Admins and program officers can manage questionnaires" ON baseline_questionnaires
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND (p.role = 'admin' OR p.role = 'program_officer')
        )
    );

-- RLS Policies for participant_responses
CREATE POLICY "Users can view participant responses based on role" ON participant_responses
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles p
            WHERE p.id = auth.uid()
            AND (p.role = 'admin' OR p.role = 'program_officer' OR completed_by = auth.uid())
        )
    );

CREATE POLICY "Users can insert participant responses" ON participant_responses
    FOR INSERT WITH CHECK (completed_by = auth.uid());

CREATE POLICY "Users can update their own responses or admins can update any" ON participant_responses
    FOR UPDATE USING (
        completed_by = auth.uid() OR
        EXISTS (SELECT 1 FROM profiles WHERE id = auth.uid() AND role = 'admin')
    );

-- Add activity logging triggers for the new tables
CREATE TRIGGER trigger_log_field_report_activities
    AFTER INSERT ON field_reports
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();

CREATE TRIGGER trigger_log_activity_report_activities
    AFTER INSERT ON activity_reports
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();

CREATE TRIGGER trigger_log_monthly_report_activities
    AFTER INSERT ON monthly_reports
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();

CREATE TRIGGER trigger_log_baseline_questionnaire_activities
    AFTER INSERT ON participant_responses
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();
