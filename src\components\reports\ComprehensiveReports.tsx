import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  BarChart3, 
  TrendingUp,
  Download,
  Calendar,
  Users,
  School,
  Award,
  BookOpen,
  Target,
  MessageSquare
} from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';

// Import existing report components
import Reports from '@/components/Reports';
import Analytics from './Analytics';
import Performance from './Performance';
import PerformanceTest from '../PerformanceTest';
import CustomReports from './CustomReports';
import AttendanceReportsHub from '@/components/attendance/reports/AttendanceReportsHub';
import ImpactReports from '@/components/impact/analytics/ImpactReports';

interface ComprehensiveReportsProps {
  defaultTab?: string;
}

const ComprehensiveReports: React.FC<ComprehensiveReportsProps> = ({ 
  defaultTab = 'overview' 
}) => {
  const [activeTab, setActiveTab] = useState(defaultTab);

  const reportCategories = [
    {
      id: 'overview',
      title: 'Reports Overview',
      description: 'Comprehensive view of all available reports',
      icon: BarChart3,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      id: 'impact',
      title: 'Impact Reports',
      description: 'Student outcomes, school performance, and long-term impact',
      icon: Award,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      id: 'attendance',
      title: 'Attendance Reports',
      description: 'Student attendance, session reports, and participation analytics',
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      id: 'performance',
      title: 'Performance Analytics',
      description: 'System performance, trends, and operational metrics',
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
    },
    {
      id: 'custom',
      title: 'Custom Reports',
      description: 'Build and generate custom reports with flexible parameters',
      icon: FileText,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-100',
    },
    {
      id: 'analytics',
      title: 'Advanced Analytics',
      description: 'Deep dive analytics and data visualization',
      icon: BarChart3,
      color: 'text-teal-600',
      bgColor: 'bg-teal-100',
    }
  ];

  const quickActions = [
    {
      label: 'Export All Data',
      icon: Download,
      action: () => console.log('Export all data'),
    },
    {
      label: 'Schedule Reports',
      icon: Calendar,
      action: () => console.log('Schedule reports'),
    },
    {
      label: 'Generate Summary',
      icon: FileText,
      action: () => console.log('Generate summary'),
    }
  ];

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return (
          <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <div>
                      <p className="text-sm font-medium">Total Reports</p>
                      <p className="text-2xl font-bold">24</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Calendar className="h-5 w-5 text-green-600" />
                    <div>
                      <p className="text-sm font-medium">Scheduled</p>
                      <p className="text-2xl font-bold">8</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <Download className="h-5 w-5 text-purple-600" />
                    <div>
                      <p className="text-sm font-medium">Downloads</p>
                      <p className="text-2xl font-bold">156</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-orange-600" />
                    <div>
                      <p className="text-sm font-medium">Active</p>
                      <p className="text-2xl font-bold">12</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Report Categories */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {reportCategories.slice(1).map((category) => {
                const Icon = category.icon;
                return (
                  <Card
                    key={category.id}
                    className="cursor-pointer hover:shadow-md transition-shadow border-l-4 border-l-ilead-green"
                    onClick={() => setActiveTab(category.id)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start space-x-3">
                        <div className={`${category.bgColor} p-2 rounded-lg`}>
                          <Icon className={`h-5 w-5 ${category.color}`} />
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-gray-900">{category.title}</h3>
                          <p className="text-sm text-gray-500 mt-1">{category.description}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                );
              })}
            </div>

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common reporting tasks and utilities</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={action.action}
                      >
                        <Icon className="h-4 w-4 mr-2" />
                        {action.label}
                      </Button>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </div>
        );
      
      case 'impact':
        return (
          <ImpactReports
            schoolId={null}
            dateRange={{ start: new Date(new Date().getFullYear(), 0, 1), end: new Date() }}
            canViewAllData={true}
          />
        );
      
      case 'attendance':
        return <AttendanceReportsHub />;
      
      case 'performance':
        return <Performance />;
      
      case 'custom':
        return <CustomReports />;
      
      case 'analytics':
        return <Analytics />;
      
      default:
        return <Reports />;
    }
  };

  return (
    <PageLayout>
      <PageHeader
        title="Reports"
        description="Comprehensive reporting across all application modules"
        icon={FileText}
      />

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-6">
          {reportCategories.map((category) => (
            <TabsTrigger key={category.id} value={category.id} className="text-xs">
              {category.title.split(' ')[0]}
            </TabsTrigger>
          ))}
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          {renderTabContent()}
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default ComprehensiveReports;
