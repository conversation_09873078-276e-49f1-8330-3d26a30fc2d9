
export const getPerformanceRating = (duration: number): 'excellent' | 'good' | 'moderate' | 'slow' => {
  if (duration < 100) return 'excellent';
  if (duration < 250) return 'good';
  if (duration < 500) return 'moderate';
  return 'slow';
};

export const getPerformanceColor = (rating: string): string => {
  switch (rating) {
    case 'excellent': return 'text-green-600';
    case 'good': return 'text-blue-600';
    case 'moderate': return 'text-yellow-600';
    case 'slow': return 'text-red-600';
    default: return 'text-gray-600';
  }
};

export const getPerformanceIcon = (rating: string): string => {
  switch (rating) {
    case 'excellent': return '🚀';
    case 'good': return '✅';
    case 'moderate': return '⚡';
    case 'slow': return '🐌';
    default: return '❓';
  }
};
