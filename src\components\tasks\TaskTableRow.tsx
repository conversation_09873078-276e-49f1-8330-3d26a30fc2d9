import React from 'react';
import { TableCell, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, MessageSquare, User, School, MoreHorizontal } from 'lucide-react';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import TaskStatusBadge from '../TaskStatusBadge';
import TaskPriorityBadge from '../TaskPriorityBadge';
import { Database } from '@/integrations/supabase/types';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

interface TaskTableRowProps {
  task: Task;
  currentUserId?: string;
  onViewDetails: (taskId: string) => void;
  onUpdateStatus: (taskId: string, status: Database['public']['Enums']['task_status']) => void;
  onEdit?: (taskId: string) => void;
}

const TaskTableRow: React.FC<TaskTableRowProps> = ({
  task,
  currentUserId,
  onViewDetails,
  onUpdateStatus,
  onEdit
}) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return '-';
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const handleStatusChange = (taskId: string, newStatus: Database['public']['Enums']['task_status']) => {
    onUpdateStatus(taskId, newStatus);
  };

  const isOverdue = task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed';
  const canEdit = currentUserId === task.created_by || currentUserId === task.assigned_to;

  return (
    <TableRow 
      className={`cursor-pointer hover:bg-gray-50 ${isOverdue ? 'bg-red-50' : ''}`}
      onClick={() => onViewDetails(task.id)}
    >
      <TableCell>
        <div>
          <div className="font-medium text-gray-900 hover:text-ilead-green">
            {task.title}
          </div>
          {task.description && (
            <div className="text-sm text-gray-600 line-clamp-1">
              {task.description}
            </div>
          )}
        </div>
      </TableCell>
      <TableCell>
        <TaskStatusBadge status={task.status} />
      </TableCell>
      <TableCell>
        <TaskPriorityBadge priority={task.priority} />
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          {task.assigned_to_name ? (
            <>
              <User className="h-4 w-4 text-gray-400" />
              <span className="text-sm">{task.assigned_to_name}</span>
            </>
          ) : (
            <span className="text-sm text-gray-400">Unassigned</span>
          )}
        </div>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          {task.school_name ? (
            <>
              <School className="h-4 w-4 text-gray-400" />
              <span className="text-sm">{task.school_name}</span>
            </>
          ) : (
            <span className="text-sm text-gray-400">-</span>
          )}
        </div>
      </TableCell>
      <TableCell>
        <div className={`flex items-center gap-2 ${isOverdue ? 'text-red-600 font-medium' : ''}`}>
          <Calendar className="h-4 w-4 text-gray-400" />
          <span className="text-sm">{formatDate(task.due_date)}</span>
          {isOverdue && <Badge variant="destructive" className="text-xs">Overdue</Badge>}
        </div>
      </TableCell>
      <TableCell>
        {task.comment_count > 0 && (
          <div className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4 text-gray-400" />
            <span className="text-sm">{task.comment_count}</span>
          </div>
        )}
      </TableCell>
      <TableCell>
        <DropdownMenu>
          <DropdownMenuTrigger asChild onClick={(e) => e.stopPropagation()}>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onViewDetails(task.id); }}>
              View Details
            </DropdownMenuItem>
            {canEdit && task.status !== 'completed' && (
              <>
                {task.status === 'pending' && (
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange(task.id, 'in_progress'); }}>
                    Start Task
                  </DropdownMenuItem>
                )}
                {task.status === 'in_progress' && (
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); handleStatusChange(task.id, 'completed'); }}>
                    Mark Complete
                  </DropdownMenuItem>
                )}
                {onEdit && (
                  <DropdownMenuItem onClick={(e) => { e.stopPropagation(); onEdit(task.id); }}>
                    Edit Task
                  </DropdownMenuItem>
                )}
              </>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
};

export default TaskTableRow;
