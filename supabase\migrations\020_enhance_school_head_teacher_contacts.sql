-- Enhanced School Head Teacher Contact Information Migration
-- Migration 020: Add head teacher and deputy head teacher contact fields and field staff assignment

-- Add new columns to schools table for head teacher contact information
ALTER TABLE schools 
ADD COLUMN IF NOT EXISTS head_teacher_phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS head_teacher_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS deputy_head_teacher_phone VARCHAR(50),
ADD COLUMN IF NOT EXISTS deputy_head_teacher_email VARCHAR(255),
ADD COLUMN IF NOT EXISTS field_staff_id UUID REFERENCES profiles(id);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_schools_field_staff_id ON schools(field_staff_id);
CREATE INDEX IF NOT EXISTS idx_schools_head_teacher_email ON schools(head_teacher_email);
CREATE INDEX IF NOT EXISTS idx_schools_deputy_head_teacher_email ON schools(deputy_head_teacher_email);

-- Add constraint to ensure field_staff_id references a user with field_staff role
ALTER TABLE schools 
ADD CONSTRAINT check_field_staff_role 
CHECK (
    field_staff_id IS NULL OR 
    EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = field_staff_id AND role = 'field_staff'
    )
);

-- Update RLS policies to include field staff access
CREATE POLICY "Field staff can view their assigned schools" ON schools
    FOR SELECT USING (
        field_staff_id = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Create function to get field staff members for dropdown
CREATE OR REPLACE FUNCTION get_field_staff_members()
RETURNS TABLE (
    id UUID,
    name VARCHAR(255),
    phone VARCHAR(50),
    division_name VARCHAR(255)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission to view field staff
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins and program officers can view field staff members.';
    END IF;

    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.phone,
        COALESCE(ad.name, 'Not Assigned') as division_name
    FROM profiles p
    LEFT JOIN administrative_divisions ad ON p.division_id = ad.id
    WHERE p.role = 'field_staff' 
    AND p.is_active = true
    ORDER BY p.name;
END;
$$;

-- Create function to get schools assigned to field staff
CREATE OR REPLACE FUNCTION get_field_staff_schools(p_staff_id UUID DEFAULT NULL)
RETURNS TABLE (
    school_id UUID,
    school_name VARCHAR(255),
    school_type school_type,
    division_name VARCHAR(255),
    field_staff_name VARCHAR(255)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check permissions
    IF NOT EXISTS (
        SELECT 1 FROM profiles 
        WHERE id = auth.uid() AND (
            role IN ('admin', 'program_officer') OR 
            (role = 'field_staff' AND (p_staff_id IS NULL OR id = p_staff_id))
        )
    ) THEN
        RAISE EXCEPTION 'Access denied.';
    END IF;

    RETURN QUERY
    SELECT 
        s.id as school_id,
        s.name as school_name,
        s.school_type,
        COALESCE(ad.name, 'Unknown') as division_name,
        COALESCE(p.name, 'Unassigned') as field_staff_name
    FROM schools s
    LEFT JOIN administrative_divisions ad ON s.division_id = ad.id
    LEFT JOIN profiles p ON s.field_staff_id = p.id
    WHERE (p_staff_id IS NULL OR s.field_staff_id = p_staff_id)
    ORDER BY s.name;
END;
$$;

-- Update the add_school_enhanced function to handle new fields
CREATE OR REPLACE FUNCTION add_school_enhanced(
    p_name VARCHAR(255),
    p_code VARCHAR(50) DEFAULT NULL,
    p_school_type school_type,
    p_school_category school_category DEFAULT 'day',
    p_student_count INTEGER DEFAULT NULL,
    p_teacher_count INTEGER DEFAULT NULL,
    p_contact_phone VARCHAR(50) DEFAULT NULL,
    p_email VARCHAR(255) DEFAULT NULL,
    p_division_id UUID,
    p_classes_count INTEGER DEFAULT NULL,
    p_streams_per_class INTEGER DEFAULT NULL,
    p_head_teacher_name VARCHAR(255) DEFAULT NULL,
    p_head_teacher_phone VARCHAR(50) DEFAULT NULL,
    p_head_teacher_email VARCHAR(255) DEFAULT NULL,
    p_deputy_head_teacher_name VARCHAR(255) DEFAULT NULL,
    p_deputy_head_teacher_phone VARCHAR(50) DEFAULT NULL,
    p_deputy_head_teacher_email VARCHAR(255) DEFAULT NULL,
    p_date_joined_ilead DATE DEFAULT NULL,
    p_ownership_type VARCHAR(50) DEFAULT NULL,
    p_location_coordinates POINT DEFAULT NULL,
    p_nearest_health_center VARCHAR(255) DEFAULT NULL,
    p_distance_to_main_road VARCHAR(255) DEFAULT NULL,
    p_infrastructure_notes TEXT DEFAULT NULL,
    p_is_partner_managed BOOLEAN DEFAULT false,
    p_partner_name VARCHAR(255) DEFAULT NULL,
    p_champion_teacher_count INTEGER DEFAULT NULL,
    p_field_staff_id UUID DEFAULT NULL,
    p_champion_teachers JSONB DEFAULT '[]',
    p_assistant_champion_teachers JSONB DEFAULT '[]'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_school_id UUID;
    v_champion_teacher JSONB;
    v_assistant_teacher JSONB;
BEGIN
    -- Check if user has permission to add schools
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Access denied. Only admins and program officers can add schools.';
    END IF;

    -- Validate field staff assignment if provided
    IF p_field_staff_id IS NOT NULL THEN
        IF NOT EXISTS (
            SELECT 1 FROM profiles
            WHERE id = p_field_staff_id AND role = 'field_staff' AND is_active = true
        ) THEN
            RAISE EXCEPTION 'Invalid field staff assignment. User must be an active field staff member.';
        END IF;
    END IF;

    -- Insert the school
    INSERT INTO schools (
        name, code, school_type, school_category, student_count, teacher_count,
        contact_phone, email, division_id, classes_count, streams_per_class,
        head_teacher_name, head_teacher_phone, head_teacher_email,
        deputy_head_teacher_name, deputy_head_teacher_phone, deputy_head_teacher_email,
        date_joined_ilead, ownership_type, location_coordinates, nearest_health_center,
        distance_to_main_road, infrastructure_notes, is_partner_managed,
        partner_name, champion_teacher_count, field_staff_id, created_by
    ) VALUES (
        p_name, p_code, p_school_type, p_school_category, p_student_count, p_teacher_count,
        p_contact_phone, p_email, p_division_id, p_classes_count, p_streams_per_class,
        p_head_teacher_name, p_head_teacher_phone, p_head_teacher_email,
        p_deputy_head_teacher_name, p_deputy_head_teacher_phone, p_deputy_head_teacher_email,
        p_date_joined_ilead, p_ownership_type, p_location_coordinates, p_nearest_health_center,
        p_distance_to_main_road, p_infrastructure_notes, p_is_partner_managed,
        p_partner_name, p_champion_teacher_count, p_field_staff_id, auth.uid()
    ) RETURNING id INTO v_school_id;

    -- Insert champion teachers
    FOR v_champion_teacher IN SELECT * FROM jsonb_array_elements(p_champion_teachers)
    LOOP
        IF v_champion_teacher->>'name' IS NOT NULL AND trim(v_champion_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'champion',
                v_champion_teacher->>'name',
                v_champion_teacher->>'phone',
                v_champion_teacher->>'email'
            );
        END IF;
    END LOOP;

    -- Insert assistant champion teachers
    FOR v_assistant_teacher IN SELECT * FROM jsonb_array_elements(p_assistant_champion_teachers)
    LOOP
        IF v_assistant_teacher->>'name' IS NOT NULL AND trim(v_assistant_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'assistant_champion',
                v_assistant_teacher->>'name',
                v_assistant_teacher->>'phone',
                v_assistant_teacher->>'email'
            );
        END IF;
    END LOOP;

    RETURN v_school_id;
END;
$$;
