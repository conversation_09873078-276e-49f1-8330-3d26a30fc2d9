import { renderHook, act, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useOfflineSync } from '../useOfflineSync';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';

// Mock dependencies
jest.mock('@/integrations/supabase/client');
jest.mock('sonner');

const mockSupabase = supabase as jest.Mocked<typeof supabase>;
const mockToast = toast as jest.Mocked<typeof toast>;

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Test wrapper with QueryClient
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  );
};

describe('useOfflineSync', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
    Object.defineProperty(global.navigator, 'onLine', {
      value: true,
      writable: true,
    });
  });

  it('should initialize with correct default state', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    expect(result.current.syncStatus).toEqual({
      isOnline: true,
      pendingItems: 0,
      lastSyncTime: null,
      isSyncing: false,
      syncProgress: 0,
      failedItems: 0,
      conflictItems: 0,
    });
    expect(result.current.conflicts).toEqual([]);
  });

  it('should add item to offline queue when offline', () => {
    Object.defineProperty(global.navigator, 'onLine', {
      value: false,
      writable: true,
    });

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    const testData = {
      school_id: 'test-school',
      latitude: 40.7128,
      longitude: -74.0060,
      accuracy: 15,
    };

    act(() => {
      result.current.addToOfflineQueue('check_in', testData, 'HIGH');
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'field_staff_offline_data',
      expect.stringContaining('"type":"check_in"')
    );
    expect(mockToast.info).toHaveBeenCalledWith(
      'Data saved offline. Will sync when connection is restored.'
    );
  });

  it('should sync offline data when coming back online', async () => {
    const mockOfflineData = [
      {
        id: 'test-1',
        type: 'check_in',
        data: {
          school_id: 'test-school',
          latitude: 40.7128,
          longitude: -74.0060,
          accuracy: 15,
        },
        timestamp: Date.now(),
        retryCount: 0,
        maxRetries: 5,
        priority: 'HIGH',
        checksum: 'test-checksum',
      },
    ];

    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockOfflineData));
    mockSupabase.rpc.mockResolvedValue({ data: null, error: null });

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.syncOfflineData();
    });

    expect(mockSupabase.rpc).toHaveBeenCalledWith('field_staff_checkin', {
      p_school_id: 'test-school',
      p_latitude: 40.7128,
      p_longitude: -74.0060,
      p_accuracy: 15,
      p_address: undefined,
      p_verification_method: undefined,
      p_device_info: {},
      p_network_info: {},
      p_offline_sync: true,
    });

    expect(mockToast.success).toHaveBeenCalledWith(
      expect.stringContaining('Synced check_in')
    );
  });

  it('should handle sync conflicts correctly', async () => {
    const mockOfflineData = [
      {
        id: 'test-1',
        type: 'check_in',
        data: {
          school_id: 'test-school',
          latitude: 40.7128,
          longitude: -74.0060,
        },
        timestamp: Date.now() - 60000, // 1 minute ago
        retryCount: 0,
        maxRetries: 5,
        priority: 'HIGH',
        checksum: 'test-checksum',
        conflictResolution: 'MANUAL',
      },
    ];

    // Mock server data that conflicts with local data
    const mockServerData = {
      id: 'test-1',
      school_id: 'different-school',
      latitude: 40.7500,
      longitude: -73.9857,
      updated_at: new Date().toISOString(), // More recent than local
    };

    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockOfflineData));
    
    // Mock the conflict detection query
    mockSupabase.from.mockReturnValue({
      select: jest.fn().mockReturnThis(),
      eq: jest.fn().mockReturnThis(),
      maybeSingle: jest.fn().mockResolvedValue({
        data: mockServerData,
        error: null,
      }),
    } as {
      select: jest.Mock;
      eq: jest.Mock;
      maybeSingle: jest.Mock;
    });

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.syncOfflineData();
    });

    expect(mockToast.warning).toHaveBeenCalledWith(
      expect.stringContaining('sync conflicts require manual resolution')
    );
    expect(result.current.conflicts).toHaveLength(1);
  });

  it('should retry failed sync attempts with exponential backoff', async () => {
    const mockOfflineData = [
      {
        id: 'test-1',
        type: 'check_in',
        data: { school_id: 'test-school' },
        timestamp: Date.now(),
        retryCount: 2,
        maxRetries: 5,
        priority: 'HIGH',
        checksum: 'test-checksum',
      },
    ];

    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockOfflineData));
    mockSupabase.rpc.mockRejectedValue(new Error('Network error'));

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.syncOfflineData();
    });

    // Should increment retry count
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'field_staff_offline_data',
      expect.stringContaining('"retryCount":3')
    );
  });

  it('should perform cleanup when storage is full', async () => {
    const oldItems = Array.from({ length: 100 }, (_, i) => ({
      id: `old-${i}`,
      type: 'check_in',
      data: { school_id: 'test' },
      timestamp: Date.now() - (i + 1) * 86400000, // Days ago
      retryCount: 5, // Max retries reached
      maxRetries: 5,
      priority: 'LOW',
    }));

    localStorageMock.getItem.mockReturnValue(JSON.stringify(oldItems));

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      await result.current.performComprehensiveCleanup();
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'field_staff_offline_data',
      expect.not.stringContaining('old-99') // Oldest items should be removed
    );
  });

  it('should handle storage quota exceeded error', async () => {
    const quotaError = new Error('QuotaExceededError');
    quotaError.name = 'QuotaExceededError';
    
    localStorageMock.setItem.mockImplementation(() => {
      throw quotaError;
    });

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.addToOfflineQueue('check_in', { school_id: 'test' });
    });

    expect(mockToast.warning).toHaveBeenCalledWith(
      'Storage space low. Performed emergency cleanup.'
    );
  });

  it('should resolve conflicts correctly', () => {
    const mockConflict = {
      id: 'conflict-1',
      localData: { school_id: 'local-school', latitude: 40.7128 },
      serverData: { school_id: 'server-school', latitude: 40.7500 },
      conflictFields: ['school_id', 'latitude'],
      timestamp: Date.now(),
    };

    localStorageMock.getItem.mockReturnValue(JSON.stringify([mockConflict]));

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    act(() => {
      result.current.resolveConflict('conflict-1', 'CLIENT');
    });

    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      'field_staff_sync_conflicts',
      '[]' // Conflict should be removed
    );
    expect(mockToast.success).toHaveBeenCalledWith('Conflict resolved successfully');
  });

  it('should calculate storage statistics correctly', async () => {
    const mockData = Array.from({ length: 50 }, (_, i) => ({
      id: `item-${i}`,
      type: 'check_in',
      data: { school_id: 'test' },
      timestamp: Date.now(),
      retryCount: 0,
      maxRetries: 5,
      priority: 'MEDIUM',
    }));

    localStorageMock.getItem.mockReturnValue(JSON.stringify(mockData));

    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    await act(async () => {
      const stats = await result.current.getStorageStats();
      expect(stats.totalItems).toBe(50);
      expect(stats.pendingItems).toBe(50);
      expect(stats.failedItems).toBe(0);
      expect(stats.storageUsagePercent).toBeGreaterThan(0);
    });
  });

  it('should handle network state changes', () => {
    const { result } = renderHook(() => useOfflineSync(), {
      wrapper: createWrapper(),
    });

    // Simulate going offline
    act(() => {
      Object.defineProperty(global.navigator, 'onLine', {
        value: false,
        writable: true,
      });
      window.dispatchEvent(new Event('offline'));
    });

    expect(result.current.syncStatus.isOnline).toBe(false);

    // Simulate coming back online
    act(() => {
      Object.defineProperty(global.navigator, 'onLine', {
        value: true,
        writable: true,
      });
      window.dispatchEvent(new Event('online'));
    });

    expect(result.current.syncStatus.isOnline).toBe(true);
  });
});
