# Field Visits Testing Documentation

## Overview

This document outlines the comprehensive testing strategy for the Field Visits feature, covering GPS tracking, offline sync, error scenarios, and user workflows.

## Test Structure

### Unit Tests

#### GPS Tracking (`src/hooks/attendance/__tests__/useGPSTracking.test.ts`)
- **GPS Position Acquisition**: Tests successful GPS position retrieval with accurate coordinates
- **Permission Handling**: Tests GPS permission denied scenarios with appropriate error messages
- **Timeout Retry Logic**: Tests exponential backoff retry mechanism for GPS timeouts
- **Check-in Workflow**: Tests complete GPS check-in process with location validation
- **Check-out Workflow**: Tests GPS check-out with session lookup and validation
- **Error Handling**: Tests various GPS error scenarios (permission denied, unavailable, timeout)
- **Accuracy Validation**: Tests GPS accuracy thresholds and warnings for poor signal

#### Offline Sync (`src/hooks/field-staff/__tests__/useOfflineSync.test.ts`)
- **Queue Management**: Tests adding items to offline queue when network is unavailable
- **Sync Process**: Tests automatic sync when network connection is restored
- **Conflict Resolution**: Tests handling of data conflicts between local and server data
- **Retry Logic**: Tests exponential backoff for failed sync attempts
- **Storage Management**: Tests cleanup of old/failed items when storage is full
- **Batch Processing**: Tests processing items in configurable batch sizes
- **Storage Statistics**: Tests calculation of storage usage and item counts
- **Network State**: Tests handling of online/offline state changes

#### Adaptive GPS (`src/hooks/field-staff/__tests__/useAdaptiveGPS.test.ts`)
- **Movement Detection**: Tests Haversine distance calculation for movement detection
- **Battery Optimization**: Tests adaptive polling intervals based on movement state
- **Background Mode**: Tests reduced polling when app is in background
- **Low Battery Mode**: Tests aggressive battery optimization when battery is low
- **Stationary Detection**: Tests detection of stationary state and interval adjustment
- **Statistics Tracking**: Tests accuracy of distance, position count, and battery savings
- **Configuration Updates**: Tests dynamic configuration changes during tracking
- **Error Recovery**: Tests graceful handling of GPS errors with retry mechanisms

### Integration Tests

#### Field Visits Component (`src/components/attendance/__tests__/UnifiedFieldVisits.integration.test.tsx`)
- **Role-based Interface**: Tests field staff vs admin interface differences
- **GPS Check-in Flow**: Tests complete check-in workflow with location acquisition
- **Offline Mode**: Tests graceful degradation when network is unavailable
- **Error Scenarios**: Tests handling of GPS errors, network failures, and permission issues
- **Session Management**: Tests display and interaction with assigned sessions
- **Attendance Tracking**: Tests student attendance recording and display
- **Battery Settings**: Tests battery optimization toggle and behavior
- **Empty States**: Tests appropriate messaging when no data is available
- **Check-out Flow**: Tests complete check-out workflow with session validation

## Test Coverage Goals

### Critical Paths (100% Coverage Required)
- GPS position acquisition and error handling
- Offline data queuing and sync processes
- Check-in/check-out workflows
- Data conflict resolution
- Battery optimization logic

### Important Features (90% Coverage Target)
- Movement detection algorithms
- Storage management and cleanup
- User interface interactions
- Session and attendance management
- Error messaging and user feedback

### Supporting Features (80% Coverage Target)
- Statistics calculation
- Configuration management
- Background processing
- Performance optimizations

## Running Tests

### All Tests
```bash
npm test
```

### Watch Mode (Development)
```bash
npm run test:watch
```

### Coverage Report
```bash
npm run test:coverage
```

### Field Visits Specific Tests
```bash
npm run test:field-visits
```

## Test Data and Mocking

### GPS Mocking
- Mock `navigator.geolocation` with configurable responses
- Simulate various GPS error conditions (permission denied, timeout, unavailable)
- Test with different accuracy levels and movement patterns

### Network Mocking
- Mock `navigator.onLine` for offline/online state simulation
- Mock Supabase client for database operations
- Simulate network failures and recovery scenarios

### Battery API Mocking
- Mock `navigator.getBattery()` for battery level simulation
- Test low battery scenarios and charging state changes

### Storage Mocking
- Mock `localStorage` for offline data persistence
- Simulate storage quota exceeded scenarios
- Test cleanup and data migration processes

## Performance Testing

### GPS Performance
- Test GPS acquisition time under various conditions
- Measure battery usage with different polling intervals
- Validate movement detection accuracy and responsiveness

### Offline Sync Performance
- Test sync performance with large datasets (up to 300 items)
- Measure storage usage and cleanup efficiency
- Validate batch processing performance

### Memory Management
- Test for memory leaks in long-running GPS tracking
- Validate cleanup of event listeners and timers
- Monitor storage growth and cleanup effectiveness

## Error Scenarios

### GPS Errors
- Permission denied by user
- GPS signal unavailable (indoor/underground)
- GPS timeout in poor signal conditions
- Device GPS disabled
- Browser doesn't support geolocation

### Network Errors
- Complete network loss during operations
- Intermittent connectivity issues
- Server errors during sync operations
- Database constraint violations
- Authentication failures

### Storage Errors
- Storage quota exceeded
- Corrupted offline data
- Concurrent access conflicts
- Browser storage disabled

## Accessibility Testing

### Screen Reader Support
- Test GPS status announcements
- Validate error message accessibility
- Ensure button and form accessibility

### Keyboard Navigation
- Test tab order through GPS controls
- Validate keyboard shortcuts for common actions
- Ensure focus management during state changes

### Visual Accessibility
- Test color contrast for status indicators
- Validate text size and readability
- Ensure proper visual hierarchy

## Browser Compatibility

### Supported Browsers
- Chrome 90+ (primary target)
- Firefox 88+ (secondary)
- Safari 14+ (mobile primary)
- Edge 90+ (enterprise)

### Feature Detection
- Test graceful degradation when GPS unavailable
- Validate fallback mechanisms for unsupported features
- Ensure progressive enhancement approach

## Continuous Integration

### Pre-commit Hooks
- Run linting and type checking
- Execute unit tests for changed files
- Validate test coverage thresholds

### CI Pipeline
- Run full test suite on all pull requests
- Generate coverage reports and trends
- Validate performance benchmarks
- Test across multiple browser environments

## Debugging and Troubleshooting

### Common Issues
- GPS permission prompts in different browsers
- Offline sync conflicts and resolution
- Battery optimization not activating
- Movement detection sensitivity

### Debug Tools
- GPS tracking console logs
- Offline sync status indicators
- Performance monitoring hooks
- Error boundary reporting

## Future Testing Considerations

### Planned Enhancements
- End-to-end testing with Playwright
- Visual regression testing
- Load testing for concurrent users
- Real device testing on mobile

### Monitoring and Analytics
- GPS accuracy tracking in production
- Battery usage analytics
- Offline sync success rates
- User workflow completion rates
