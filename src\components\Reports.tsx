
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Download, FileSpreadsheet, Filter } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type BookDistribution = Database['public']['Functions']['get_book_distributions']['Returns'][0];

const Reports = () => {
  const [filterSchool, setFilterSchool] = useState('all');
  const [filterMonth, setFilterMonth] = useState('all');

  // Fetch schools using RPC function
  const { data: schools = [] } = useQuery({
    queryKey: ['schools'],
    queryFn: async (): Promise<SchoolWithDivision[]> => {
      const { data, error } = await supabase
        .rpc('get_schools_with_divisions');

      if (error) throw error;
      return data || [];
    },
  });

  // Fetch distributions using RPC function
  const { data: distributions = [] } = useQuery({
    queryKey: ['distributions'],
    queryFn: async (): Promise<BookDistribution[]> => {
      const { data, error } = await supabase
        .rpc('get_book_distributions');

      if (error) throw error;
      return data || [];
    },
  });

  const exportToExcel = () => {
    let filteredData = distributions;

    if (filterSchool !== 'all') {
      filteredData = filteredData.filter((d: BookDistribution) => d.school_id === filterSchool);
    }

    if (filterMonth !== 'all') {
      filteredData = filteredData.filter((d: BookDistribution) => {
        const distDate = new Date(d.delivery_date);
        return distDate.getMonth() === parseInt(filterMonth);
      });
    }

    // Create CSV content
    const headers = ['School Name', 'Book Title', 'Quantity', 'Date', 'Supervisor', 'Notes'];
    const csvContent = [
      headers.join(','),
      ...filteredData.map((dist: BookDistribution) => {
        return [
          dist.school_name || 'Unknown School',
          dist.book_title || 'Unknown Book',
          dist.quantity,
          dist.delivery_date,
          dist.supervisor_name || 'Unknown Supervisor',
          dist.notes || ''
        ].map(field => `"${field}"`).join(',');
      })
    ].join('\n');

    // Download CSV
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `ilead_book_distributions_${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const filteredDistributions = distributions.filter((dist: BookDistribution) => {
    if (filterSchool !== 'all' && dist.school_id !== filterSchool) return false;
    if (filterMonth !== 'all' && new Date(dist.delivery_date).getMonth() !== parseInt(filterMonth)) return false;
    return true;
  });

  const totalBooks = filteredDistributions.reduce((sum: number, dist: BookDistribution) => sum + (dist.quantity || 0), 0);
  const uniqueSchools = new Set(filteredDistributions.map((dist: BookDistribution) => dist.school_id)).size;

  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  return (
    <div className="p-6 space-y-6">
      <div>
        <h1 className="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
        <p className="text-gray-600 mt-2">Generate and export distribution reports</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Filter className="h-5 w-5 mr-2" />
            Filter Reports
          </CardTitle>
          <CardDescription>Apply filters to customize your report data</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">School</label>
              <Select value={filterSchool} onValueChange={setFilterSchool}>
                <SelectTrigger>
                  <SelectValue placeholder="All Schools" />
                </SelectTrigger>
                <SelectContent className="bg-white z-50">
                  <SelectItem value="all">All Schools</SelectItem>
                  {schools.map((school: SchoolWithDivision) => (
                    <SelectItem key={school.id} value={school.id}>
                      {school.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Month</label>
              <Select value={filterMonth} onValueChange={setFilterMonth}>
                <SelectTrigger>
                  <SelectValue placeholder="All Months" />
                </SelectTrigger>
                <SelectContent className="bg-white z-50">
                  <SelectItem value="all">All Months</SelectItem>
                  {months.map((month, index) => (
                    <SelectItem key={index} value={index.toString()}>
                      {month}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={exportToExcel} className="w-full bg-green-600 hover:bg-green-700">
                <Download className="h-4 w-4 mr-2" />
                Export to Excel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileSpreadsheet className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Distributions</p>
                <p className="text-2xl font-bold text-gray-900">{filteredDistributions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileSpreadsheet className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Books Distributed</p>
                <p className="text-2xl font-bold text-gray-900">{totalBooks.toLocaleString()}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <FileSpreadsheet className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Schools Served</p>
                <p className="text-2xl font-bold text-gray-900">{uniqueSchools}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Distribution Summary</CardTitle>
          <CardDescription>Detailed breakdown of filtered distributions</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredDistributions.slice(-10).reverse().map((distribution: BookDistribution) => (
              <div key={distribution.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">{distribution.school_name || 'Unknown School'}</p>
                  <p className="text-sm text-gray-600">
                    {distribution.book_title || 'Unknown Book'} - {distribution.quantity} copies
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-500">{distribution.delivery_date}</p>
                  <p className="text-xs text-gray-400">by {distribution.supervisor_name || 'Unknown'}</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default Reports;
