import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { X, Save, Plus, Trash2 } from 'lucide-react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { toast } from 'sonner';
import { AssessmentType, SubjectType } from '@/types/impact';

const assessmentSchema = z.object({
  assessment_type: z.enum(['baseline', 'midterm', 'endline', 'quarterly', 'annual']),
  subject: z.enum(['literacy', 'numeracy', 'english', 'mathematics', 'science', 'social_studies', 'life_skills']),
  grade_level: z.number().min(1).max(12),
  school_id: z.string().uuid(),
  assessment_date: z.string(),
  academic_year: z.string(),
  assessor_name: z.string().optional(),
  notes: z.string().optional(),
  students: z.array(z.object({
    student_number: z.string().optional(),
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    pre_assessment_score: z.number().min(0).max(100).optional(),
    post_assessment_score: z.number().min(0).max(100).optional()
  })).min(1, 'At least one student is required')
});

type AssessmentFormData = z.infer<typeof assessmentSchema>;

interface AssessmentFormProps {
  onClose: () => void;
  schoolId?: string | null;
}

const AssessmentForm: React.FC<AssessmentFormProps> = ({ onClose, schoolId }) => {
  const { profile } = useAuth();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Fetch schools for selection
  const { data: schools } = useQuery({
    queryKey: ['schools'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('schools')
        .select('id, name')
        .order('name');
      
      if (error) throw error;
      return data;
    }
  });

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<AssessmentFormData>({
    resolver: zodResolver(assessmentSchema),
    defaultValues: {
      school_id: schoolId || '',
      assessment_date: new Date().toISOString().split('T')[0],
      academic_year: new Date().getFullYear().toString(),
      grade_level: 1,
      students: [{ first_name: '', last_name: '' }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'students'
  });

  const selectedAssessmentType = watch('assessment_type');
  const selectedSubject = watch('subject');

  // Create assessment mutation
  const createAssessmentMutation = useMutation({
    mutationFn: async (data: AssessmentFormData) => {
      // First, create or get students
      const studentsData = [];
      
      for (const student of data.students) {
        // Check if student exists
        let studentRecord;
        if (student.student_number) {
          const { data: existingStudent } = await supabase
            .from('students')
            .select('id')
            .eq('student_number', student.student_number)
            .eq('school_id', data.school_id)
            .single();
          
          studentRecord = existingStudent;
        }

        if (!studentRecord) {
          // Create new student
          const { data: newStudent, error: studentError } = await supabase
            .from('students')
            .insert({
              student_number: student.student_number,
              first_name: student.first_name,
              last_name: student.last_name,
              grade_level: data.grade_level,
              school_id: data.school_id,
              enrollment_date: data.assessment_date,
              created_by: profile?.id
            })
            .select('id')
            .single();

          if (studentError) throw studentError;
          studentRecord = newStudent;
        }

        studentsData.push({
          student_id: studentRecord.id,
          pre_assessment_score: student.pre_assessment_score,
          post_assessment_score: student.post_assessment_score
        });
      }

      // Create assessments for each student
      const assessments = studentsData.map(student => ({
        student_id: student.student_id,
        school_id: data.school_id,
        assessment_type: data.assessment_type,
        subject: data.subject,
        grade_level: data.grade_level,
        pre_assessment_score: student.pre_assessment_score,
        post_assessment_score: student.post_assessment_score,
        improvement_percentage: student.pre_assessment_score && student.post_assessment_score 
          ? ((student.post_assessment_score - student.pre_assessment_score) / student.pre_assessment_score) * 100
          : null,
        assessment_date: data.assessment_date,
        academic_year: data.academic_year,
        assessor_name: data.assessor_name,
        notes: data.notes,
        created_by: profile?.id
      }));

      const { data: result, error } = await supabase
        .from('student_assessments')
        .insert(assessments)
        .select();

      if (error) throw error;
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['student-learning-outcomes'] });
      toast.success('Assessment data saved successfully');
      onClose();
    },
    onError: (error) => {
      console.error('Error saving assessment:', error);
      toast.error('Failed to save assessment data');
    }
  });

  const onSubmit = async (data: AssessmentFormData) => {
    setIsSubmitting(true);
    try {
      await createAssessmentMutation.mutateAsync(data);
    } finally {
      setIsSubmitting(false);
    }
  };

  const addStudent = () => {
    append({ first_name: '', last_name: '' });
  };

  const removeStudent = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <div className="bg-blue-100 p-2 rounded-lg">
              <Plus className="h-5 w-5 text-blue-600" />
            </div>
            <span>New Student Assessment</span>
          </DialogTitle>
          <DialogDescription>
            Record student learning outcomes and assessment scores
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Assessment Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Assessment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="school_id">School *</Label>
                  <Select 
                    value={watch('school_id')} 
                    onValueChange={(value) => setValue('school_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select school" />
                    </SelectTrigger>
                    <SelectContent>
                      {schools?.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.school_id && (
                    <p className="text-sm text-red-600 mt-1">{errors.school_id.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="assessment_type">Assessment Type *</Label>
                  <Select 
                    value={watch('assessment_type')} 
                    onValueChange={(value) => setValue('assessment_type', value as AssessmentType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select assessment type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="baseline">Baseline</SelectItem>
                      <SelectItem value="midterm">Midterm</SelectItem>
                      <SelectItem value="endline">Endline</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="annual">Annual</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.assessment_type && (
                    <p className="text-sm text-red-600 mt-1">{errors.assessment_type.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="subject">Subject *</Label>
                  <Select 
                    value={watch('subject')} 
                    onValueChange={(value) => setValue('subject', value as SubjectType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select subject" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="literacy">Literacy</SelectItem>
                      <SelectItem value="numeracy">Numeracy</SelectItem>
                      <SelectItem value="english">English</SelectItem>
                      <SelectItem value="mathematics">Mathematics</SelectItem>
                      <SelectItem value="science">Science</SelectItem>
                      <SelectItem value="social_studies">Social Studies</SelectItem>
                      <SelectItem value="life_skills">Life Skills</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.subject && (
                    <p className="text-sm text-red-600 mt-1">{errors.subject.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="grade_level">Grade Level *</Label>
                  <Input
                    type="number"
                    min="1"
                    max="12"
                    {...register('grade_level', { valueAsNumber: true })}
                  />
                  {errors.grade_level && (
                    <p className="text-sm text-red-600 mt-1">{errors.grade_level.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="assessment_date">Assessment Date *</Label>
                  <Input
                    type="date"
                    {...register('assessment_date')}
                  />
                  {errors.assessment_date && (
                    <p className="text-sm text-red-600 mt-1">{errors.assessment_date.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="academic_year">Academic Year *</Label>
                  <Input
                    placeholder="e.g., 2024"
                    {...register('academic_year')}
                  />
                  {errors.academic_year && (
                    <p className="text-sm text-red-600 mt-1">{errors.academic_year.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="assessor_name">Assessor Name</Label>
                <Input
                  placeholder="Name of person conducting assessment"
                  {...register('assessor_name')}
                />
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  placeholder="Additional notes about the assessment"
                  {...register('notes')}
                />
              </div>
            </CardContent>
          </Card>

          {/* Students */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Students</CardTitle>
                <Button type="button" onClick={addStudent} variant="outline" size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Student
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {fields.map((field, index) => (
                  <div key={field.id} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <h4 className="font-medium">Student {index + 1}</h4>
                      {fields.length > 1 && (
                        <Button
                          type="button"
                          onClick={() => removeStudent(index)}
                          variant="outline"
                          size="sm"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                      <div>
                        <Label>Student Number</Label>
                        <Input
                          placeholder="Optional"
                          {...register(`students.${index}.student_number`)}
                        />
                      </div>
                      
                      <div>
                        <Label>First Name *</Label>
                        <Input
                          {...register(`students.${index}.first_name`)}
                        />
                        {errors.students?.[index]?.first_name && (
                          <p className="text-sm text-red-600 mt-1">
                            {errors.students[index]?.first_name?.message}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Last Name *</Label>
                        <Input
                          {...register(`students.${index}.last_name`)}
                        />
                        {errors.students?.[index]?.last_name && (
                          <p className="text-sm text-red-600 mt-1">
                            {errors.students[index]?.last_name?.message}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <Label>Pre-Assessment Score (%)</Label>
                        <Input
                          type="number"
                          min="0"
                          max="100"
                          step="0.1"
                          {...register(`students.${index}.pre_assessment_score`, { valueAsNumber: true })}
                        />
                      </div>
                      
                      {selectedAssessmentType !== 'baseline' && (
                        <div className="md:col-span-2 lg:col-span-1">
                          <Label>Post-Assessment Score (%)</Label>
                          <Input
                            type="number"
                            min="0"
                            max="100"
                            step="0.1"
                            {...register(`students.${index}.post_assessment_score`, { valueAsNumber: true })}
                          />
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isSubmitting}
              className="bg-ilead-green hover:bg-ilead-dark-green"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Save Assessment
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AssessmentForm;
