/**
 * Test login using direct HTTP request to Supabase auth endpoint
 */

import fetch from 'node-fetch';

const supabaseUrl = "https://bygrspebofyofymivmib.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzIxODgsImV4cCI6MjA2NDYwODE4OH0.xxfeix-6F42NmVWaQHE19nnDCxZmiMDs1_fyLb0-lgE";

async function testLogin(email, password) {
  try {
    console.log(`Testing login for: ${email}`);
    
    const response = await fetch(`${supabaseUrl}/auth/v1/token?grant_type=password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`
      },
      body: JSON.stringify({
        email: email,
        password: password
      })
    });
    
    const result = await response.json();
    
    if (response.ok) {
      console.log('✅ Login successful!');
      console.log('User ID:', result.user?.id);
      console.log('Email:', result.user?.email);
      return true;
    } else {
      console.log('❌ Login failed!');
      console.log('Error:', result.error_description || result.msg || result.message);
      console.log('Full response:', result);
      return false;
    }
    
  } catch (error) {
    console.error('Request failed:', error);
    return false;
  }
}

async function main() {
  console.log('=== Direct HTTP Login Test ===');
  
  const success = await testLogin('<EMAIL>', 'Xzt4q87m');
  
  if (success) {
    console.log('\n✅ Password reset was successful!');
    console.log('Anita can now log in with the new password.');
  } else {
    console.log('\n❌ Login test failed.');
  }
}

main().catch(console.error);
