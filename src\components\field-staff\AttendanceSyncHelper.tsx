import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle, 
  XCircle,
  Info
} from 'lucide-react';
import { useRefreshAttendanceData, useVerifyCheckInStatus, useCurrentAttendance } from '@/hooks/field-staff/useFieldStaffAttendance';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { toast } from 'sonner';

interface AttendanceSyncHelperProps {
  onClose?: () => void;
}

/**
 * Component to help users diagnose and recover from attendance sync issues
 */
export const AttendanceSyncHelper: React.FC<AttendanceSyncHelperProps> = ({ onClose }) => {
  const refreshData = useRefreshAttendanceData();
  const { data: currentAttendance, isLoading: attendanceLoading } = useCurrentAttendance();
  const { data: unifiedStatus, isLoading: unifiedLoading } = useUnifiedCheckInStatus();
  const { data: verificationData, refetch: verifyStatus, isLoading: verifyLoading } = useVerifyCheckInStatus();

  const handleRefreshAll = () => {
    refreshData();
    // Also refresh the verification
    setTimeout(() => {
      verifyStatus();
    }, 1000);
  };

  const handleVerifyStatus = () => {
    verifyStatus();
  };

  const isLoading = attendanceLoading || unifiedLoading || verifyLoading;

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-yellow-500" />
          Attendance Sync Helper
        </CardTitle>
        <CardDescription>
          Diagnose and resolve attendance synchronization issues
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Status Overview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium">Frontend Status</h4>
            <div className="space-y-1">
              <div className="flex items-center gap-2">
                <span className="text-sm">Unified Status:</span>
                <Badge variant={unifiedStatus?.isCheckedIn ? "default" : "secondary"}>
                  {unifiedStatus?.isCheckedIn ? "Checked In" : "Not Checked In"}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm">Current Attendance:</span>
                <Badge variant={currentAttendance ? "default" : "secondary"}>
                  {currentAttendance ? "Found" : "None"}
                </Badge>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Database Status</h4>
            <div className="space-y-1">
              {verificationData ? (
                <div className="flex items-center gap-2">
                  <span className="text-sm">Actually Checked In:</span>
                  <Badge variant={verificationData.isActuallyCheckedIn ? "default" : "destructive"}>
                    {verificationData.isActuallyCheckedIn ? "Yes" : "No"}
                  </Badge>
                </div>
              ) : (
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={handleVerifyStatus}
                  disabled={isLoading}
                >
                  Check Database Status
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Sync Issue Detection */}
        {verificationData && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <strong>Database says:</strong> {verificationData.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Sync Mismatch Warning */}
        {verificationData && unifiedStatus && (
          verificationData.isActuallyCheckedIn !== unifiedStatus.isCheckedIn && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                <strong>Sync Issue Detected!</strong> Frontend thinks you are {unifiedStatus.isCheckedIn ? "checked in" : "not checked in"}, 
                but database shows you are {verificationData.isActuallyCheckedIn ? "checked in" : "not checked in"}.
              </AlertDescription>
            </Alert>
          )
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button 
            onClick={handleRefreshAll}
            disabled={isLoading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            Refresh All Data
          </Button>

          <Button 
            variant="outline"
            onClick={handleVerifyStatus}
            disabled={isLoading}
          >
            Verify Database Status
          </Button>

          {onClose && (
            <Button variant="ghost" onClick={onClose}>
              Close
            </Button>
          )}
        </div>

        {/* Recovery Instructions */}
        <div className="space-y-2">
          <h4 className="font-medium">Recovery Steps:</h4>
          <ol className="list-decimal list-inside space-y-1 text-sm text-muted-foreground">
            <li>Click "Refresh All Data" to clear cached data</li>
            <li>If sync issue persists, refresh the entire page (F5)</li>
            <li>If you need to check out but aren't checked in, check in first</li>
            <li>If you're stuck in a checked-in state, contact support</li>
          </ol>
        </div>

        {/* Technical Details */}
        {(currentAttendance || verificationData?.attendanceRecord) && (
          <details className="text-xs">
            <summary className="cursor-pointer font-medium">Technical Details</summary>
            <div className="mt-2 space-y-2">
              {currentAttendance && (
                <div>
                  <strong>Frontend Attendance ID:</strong> {currentAttendance.id}
                </div>
              )}
              {verificationData?.attendanceRecord && (
                <div>
                  <strong>Database Attendance ID:</strong> {verificationData.attendanceRecord.id}
                </div>
              )}
            </div>
          </details>
        )}
      </CardContent>
    </Card>
  );
};

export default AttendanceSyncHelper;
