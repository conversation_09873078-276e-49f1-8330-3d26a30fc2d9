// Temporary type definitions until Supabase types are regenerated

export type BookCondition = 'new' | 'good' | 'fair' | 'poor';

export type BookLanguage =
  | 'english'
  | 'luganda'
  | 'runyankole'
  | 'ateso'
  | 'luo'
  | 'lugbara'
  | 'runyoro'
  | 'lusoga'
  | 'other';



export type DistributionStatus =
  | 'planned'
  | 'in_progress'
  | 'completed'
  | 'cancelled'
  | 'delayed';

export interface Book {
  id: string;
  title: string;
  author: string;
  isbn?: string;
  publication_year?: number;
  language: BookLanguage;
  publisher?: string;
  description?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface BookInventory {
  id: string;
  book_id: string;
  total_quantity: number;
  available_quantity: number;
  distributed_quantity: number;
  damaged_quantity: number;
  lost_quantity: number;
  minimum_threshold: number;
  condition: BookCondition;
  storage_location?: string;
  cost_per_unit?: number;
  notes?: string;
  last_updated_by: string;
  created_at: string;
  updated_at: string;
}

export interface BookWithInventory extends Book {
  total_quantity: number;
  available_quantity: number;
  distributed_quantity: number;
  damaged_quantity: number;
  lost_quantity: number;
  minimum_threshold: number;
  condition: BookCondition;
  storage_location?: string;
  cost_per_unit?: number;
  inventory_notes?: string;
}

export interface BookDistribution {
  id: string;
  book_id: string;
  inventory_id: string;
  school_id: string;
  quantity: number;
  planned_date?: string;
  actual_delivery_date?: string;
  status: DistributionStatus;
  supervisor_id: string;
  recipient_name?: string;
  recipient_title?: string;
  notes?: string;
  delivery_confirmation: boolean;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface DistributionPhoto {
  id: string;
  distribution_id: string;
  photo_url: string;
  caption?: string;
  uploaded_by: string;
  created_at: string;
}

export interface BookFormData {
  title: string;
  author: string;
  isbn?: string;
  publication_year?: number;
  language: BookLanguage;
  publisher?: string;
  description?: string;
  total_quantity: number;
  condition: BookCondition;
  storage_location?: string;
  cost_per_unit?: number;
  minimum_threshold: number;
  notes?: string;
}

export interface BookDistributionFormData {
  book_id: string;
  school_id: string;
  quantity: number;
  planned_date?: string;
  recipient_name?: string;
  recipient_title?: string;
  notes?: string;
}

// Helper functions for display

export const formatBookLanguage = (language: BookLanguage): string => {
  const languageMap: Record<BookLanguage, string> = {
    english: 'English',
    luganda: 'Luganda',
    runyankole: 'Runyankole',
    ateso: 'Ateso',
    luo: 'Luo',
    lugbara: 'Lugbara',
    runyoro: 'Runyoro',
    lusoga: 'Lusoga',
    other: 'Other',
  };
  return languageMap[language] || language;
};

export const formatBookCondition = (condition: BookCondition): string => {
  const conditionMap: Record<BookCondition, string> = {
    new: 'New',
    good: 'Good',
    fair: 'Fair',
    poor: 'Poor',
  };
  return conditionMap[condition] || condition;
};

export const formatDistributionStatus = (status: DistributionStatus): string => {
  const statusMap: Record<DistributionStatus, string> = {
    planned: 'Planned',
    in_progress: 'In Progress',
    completed: 'Completed',
    cancelled: 'Cancelled',
    delayed: 'Delayed',
  };
  return statusMap[status] || status;
};
