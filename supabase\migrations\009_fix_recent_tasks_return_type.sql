
-- Fix the return type mismatch in get_recent_tasks function
DROP FUNCTION IF EXISTS get_recent_tasks(INTEGER);

CREATE OR REPLACE FUNCTION get_recent_tasks(
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    description TEXT,
    priority task_priority,
    status task_status,
    due_date TIMESTAMP WITH TIME ZONE,
    assigned_to UUID,
    assigned_to_name TEXT,
    created_by UUI<PERSON>,
    created_by_name TEXT,
    school_id UUID,
    school_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.title,
        t.description,
        t.priority,
        t.status,
        t.due_date,
        t.assigned_to,
        p_assigned.name as assigned_to_name,
        t.created_by,
        p_created.name as created_by_name,
        t.school_id,
        s.name as school_name,
        t.created_at,
        t.updated_at
    FROM tasks t
    LEFT JOIN profiles p_assigned ON t.assigned_to = p_assigned.id
    LEFT JOIN profiles p_created ON t.created_by = p_created.id
    LEFT JOIN schools s ON t.school_id = s.id
    WHERE 
        -- Apply RLS: user can see tasks they're assigned to, created, or if they're admin/program_officer
        (t.assigned_to = auth.uid() OR 
         t.created_by = auth.uid() OR
         EXISTS (
             SELECT 1 FROM profiles 
             WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
         ))
    ORDER BY 
        CASE t.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        t.due_date ASC NULLS LAST,
        t.created_at DESC
    LIMIT p_limit;
END;
$$;
