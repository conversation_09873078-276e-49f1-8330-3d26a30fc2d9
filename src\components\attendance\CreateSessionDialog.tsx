import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Plus, Clock, Users, MapPin, BookOpen, Target } from 'lucide-react';
import { useCreateAttendanceSession, CreateSessionData } from '@/hooks/attendance/useAttendanceSessions';
import { useSchools } from '@/hooks/useSchools';
import { Database } from '@/integrations/supabase/types';

type SessionType = Database['public']['Enums']['session_type'];

interface CreateSessionDialogProps {
  trigger?: React.ReactNode;
  onSessionCreated?: (sessionId: string) => void;
}

const CreateSessionDialog: React.FC<CreateSessionDialogProps> = ({
  trigger,
  onSessionCreated,
}) => {
  const [open, setOpen] = useState(false);
  const [formData, setFormData] = useState<CreateSessionData>({
    session_name: '',
    session_type: 'class' as SessionType,
    school_id: 'not_selected',
    session_date: new Date().toISOString().split('T')[0],
    start_time: '09:00',
    end_time: '10:00',
    planned_duration_minutes: 60,
    location: '',
    max_capacity: 30,
    round_tables_count: 0,
    session_description: '',
    learning_objectives: [],
    materials_needed: [],
  });

  const [objectiveInput, setObjectiveInput] = useState('');
  const [materialInput, setMaterialInput] = useState('');

  const { data: schools } = useSchools();
  const createSession = useCreateAttendanceSession();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const sessionId = await createSession.mutateAsync(formData);
      setOpen(false);
      resetForm();
      onSessionCreated?.(sessionId);
    } catch (error) {
      console.error('Failed to create session:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      session_name: '',
      session_type: 'class' as SessionType,
      school_id: 'not_selected',
      session_date: new Date().toISOString().split('T')[0],
      start_time: '09:00',
      end_time: '10:00',
      planned_duration_minutes: 60,
      location: '',
      max_capacity: 30,
      round_tables_count: 0,
      session_description: '',
      learning_objectives: [],
      materials_needed: [],
    });
    setObjectiveInput('');
    setMaterialInput('');
  };

  const addObjective = () => {
    if (objectiveInput.trim()) {
      setFormData(prev => ({
        ...prev,
        learning_objectives: [...(prev.learning_objectives || []), objectiveInput.trim()]
      }));
      setObjectiveInput('');
    }
  };

  const removeObjective = (index: number) => {
    setFormData(prev => ({
      ...prev,
      learning_objectives: prev.learning_objectives?.filter((_, i) => i !== index) || []
    }));
  };

  const addMaterial = () => {
    if (materialInput.trim()) {
      setFormData(prev => ({
        ...prev,
        materials_needed: [...(prev.materials_needed || []), materialInput.trim()]
      }));
      setMaterialInput('');
    }
  };

  const removeMaterial = (index: number) => {
    setFormData(prev => ({
      ...prev,
      materials_needed: prev.materials_needed?.filter((_, i) => i !== index) || []
    }));
  };

  const updateFormData = <K extends keyof CreateSessionData>(field: K, value: CreateSessionData[K]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const calculateDuration = React.useCallback(() => {
    if (formData.start_time && formData.end_time) {
      const start = new Date(`2000-01-01T${formData.start_time}`);
      const end = new Date(`2000-01-01T${formData.end_time}`);
      const diffMs = end.getTime() - start.getTime();
      const diffMinutes = Math.floor(diffMs / (1000 * 60));
      updateFormData('planned_duration_minutes', diffMinutes > 0 ? diffMinutes : 60);
    }
  }, [formData.start_time, formData.end_time]);

  React.useEffect(() => {
    calculateDuration();
  }, [calculateDuration]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Create Session
          </Button>
        )}
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create Attendance Session</DialogTitle>
          <DialogDescription>
            Set up a new session for tracking student attendance
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="session_name">Session Name *</Label>
                  <Input
                    id="session_name"
                    value={formData.session_name}
                    onChange={(e) => updateFormData('session_name', e.target.value)}
                    placeholder="e.g., Math Class - Algebra"
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="session_type">Session Type *</Label>
                  <Select
                    value={formData.session_type}
                    onValueChange={(value: SessionType) => updateFormData('session_type', value)}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="class">Class</SelectItem>
                      <SelectItem value="leadership_program">Leadership Program</SelectItem>
                      <SelectItem value="training">Training</SelectItem>
                      <SelectItem value="assessment">Assessment</SelectItem>
                      <SelectItem value="meeting">Meeting</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="school_id">School *</Label>
                  <Select
                    value={formData.school_id}
                    onValueChange={(value) => updateFormData('school_id', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select school" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="not_selected">Select school</SelectItem>
                      {schools?.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="grade_level">Grade Level</Label>
                  <Input
                    id="grade_level"
                    type="number"
                    min="1"
                    max="12"
                    value={formData.grade_level || ''}
                    onChange={(e) => updateFormData('grade_level', e.target.value ? parseInt(e.target.value) : undefined)}
                    placeholder="e.g., 5"
                  />
                </div>

                <div>
                  <Label htmlFor="subject">Subject</Label>
                  <Input
                    id="subject"
                    value={formData.subject || ''}
                    onChange={(e) => updateFormData('subject', e.target.value)}
                    placeholder="e.g., Mathematics"
                  />
                </div>

                <div>
                  <Label htmlFor="teacher_name">Teacher/Facilitator</Label>
                  <Input
                    id="teacher_name"
                    value={formData.teacher_name || ''}
                    onChange={(e) => updateFormData('teacher_name', e.target.value)}
                    placeholder="Teacher's name"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Schedule & Location */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Schedule & Location
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <Label htmlFor="session_date">Date *</Label>
                  <Input
                    id="session_date"
                    type="date"
                    value={formData.session_date}
                    onChange={(e) => updateFormData('session_date', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="start_time">Start Time *</Label>
                  <Input
                    id="start_time"
                    type="time"
                    value={formData.start_time}
                    onChange={(e) => updateFormData('start_time', e.target.value)}
                    required
                  />
                </div>

                <div>
                  <Label htmlFor="end_time">End Time</Label>
                  <Input
                    id="end_time"
                    type="time"
                    value={formData.end_time || ''}
                    onChange={(e) => updateFormData('end_time', e.target.value)}
                  />
                </div>

                <div>
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <Input
                    id="duration"
                    type="number"
                    min="15"
                    value={formData.planned_duration_minutes || ''}
                    onChange={(e) => updateFormData('planned_duration_minutes', parseInt(e.target.value) || 60)}
                    readOnly
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={formData.location || ''}
                    onChange={(e) => updateFormData('location', e.target.value)}
                    placeholder="e.g., Classroom A, Library"
                  />
                </div>

                <div>
                  <Label htmlFor="max_capacity">Max Capacity</Label>
                  <Input
                    id="max_capacity"
                    type="number"
                    min="1"
                    value={formData.max_capacity || ''}
                    onChange={(e) => updateFormData('max_capacity', parseInt(e.target.value) || 30)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Leadership Program Settings */}
          {formData.session_type === 'leadership_program' && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Round Table Setup
                </CardTitle>
                <CardDescription>
                  Configure round tables for leadership program sessions
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="round_tables_count">Number of Tables</Label>
                    <Input
                      id="round_tables_count"
                      type="number"
                      min="0"
                      max="20"
                      value={formData.round_tables_count || 0}
                      onChange={(e) => updateFormData('round_tables_count', parseInt(e.target.value) || 0)}
                    />
                  </div>
                  <div className="flex items-end">
                    <div className="text-sm text-gray-600">
                      8 students per table (standard)
                      <br />
                      Total capacity: {(formData.round_tables_count || 0) * 8} students
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Session Details */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Session Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="session_description">Description</Label>
                <Textarea
                  id="session_description"
                  value={formData.session_description || ''}
                  onChange={(e) => updateFormData('session_description', e.target.value)}
                  placeholder="Brief description of the session..."
                  rows={3}
                />
              </div>

              {/* Learning Objectives */}
              <div>
                <Label>Learning Objectives</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={objectiveInput}
                    onChange={(e) => setObjectiveInput(e.target.value)}
                    placeholder="Add learning objective..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addObjective())}
                  />
                  <Button type="button" onClick={addObjective} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.learning_objectives?.map((objective, index) => (
                    <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeObjective(index)}>
                      {objective} ×
                    </Badge>
                  ))}
                </div>
              </div>

              {/* Materials Needed */}
              <div>
                <Label>Materials Needed</Label>
                <div className="flex gap-2 mb-2">
                  <Input
                    value={materialInput}
                    onChange={(e) => setMaterialInput(e.target.value)}
                    placeholder="Add material..."
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addMaterial())}
                  />
                  <Button type="button" onClick={addMaterial} size="sm">
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.materials_needed?.map((material, index) => (
                    <Badge key={index} variant="outline" className="cursor-pointer" onClick={() => removeMaterial(index)}>
                      {material} ×
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Form Actions */}
          <div className="flex justify-end gap-3">
            <Button type="button" variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={createSession.isPending}>
              {createSession.isPending ? 'Creating...' : 'Create Session'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateSessionDialog;
