import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { BarChart3, BookOpen, Calendar, School, TrendingUp, Users } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface DistributionStats {
  total_distributions: number;
  total_books_distributed: number;
  distributions_this_month: number;
  books_this_month: number;
  active_distributions: number;
  schools_served: number;
  top_books: Array<{
    book_title: string;
    total_distributed: number;
  }>;
}

const DistributionStatistics = () => {
  const { data: stats, isLoading, error } = useQuery({
    queryKey: ['distribution-statistics'],
    queryFn: async (): Promise<DistributionStats> => {
      const { data, error } = await supabase
        .rpc('get_distribution_statistics');

      if (error) {
        console.error('Error fetching distribution statistics:', error);
        throw error;
      }
      
      const result = data?.[0];
      if (!result) {
        return {
          total_distributions: 0,
          total_books_distributed: 0,
          distributions_this_month: 0,
          books_this_month: 0,
          active_distributions: 0,
          schools_served: 0,
          top_books: []
        };
      }

      // Handle top_books Json type conversion
      let topBooks: Array<{ book_title: string; total_distributed: number }> = [];
      try {
        if (result.top_books) {
          topBooks = Array.isArray(result.top_books)
            ? result.top_books
            : JSON.parse(result.top_books as string);
        }
      } catch (err) {
        console.error('Error parsing top_books:', err);
        topBooks = [];
      }

      return {
        total_distributions: result.total_distributions,
        total_books_distributed: result.total_books_distributed,
        distributions_this_month: result.distributions_this_month,
        books_this_month: result.books_this_month,
        active_distributions: result.active_distributions,
        schools_served: result.schools_served,
        top_books: topBooks
      };
    },
  });

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded mb-2"></div>
              <div className="h-8 bg-gray-200 rounded"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-red-600">
            <p>Error loading distribution statistics</p>
            <p className="text-sm text-gray-500">{error.message}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const statCards = [
    {
      title: 'Total Distributions',
      value: stats?.total_distributions || 0,
      icon: BookOpen,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Books Distributed',
      value: stats?.total_books_distributed || 0,
      icon: BarChart3,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'This Month',
      value: stats?.distributions_this_month || 0,
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
    },
    {
      title: 'Books This Month',
      value: stats?.books_this_month || 0,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
    {
      title: 'Active Distributions',
      value: stats?.active_distributions || 0,
      icon: Users,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
    },
    {
      title: 'Schools Served',
      value: stats?.schools_served || 0,
      icon: School,
      color: 'text-teal-600',
      bgColor: 'bg-teal-50',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
        {statCards.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value.toLocaleString()}</p>
                  </div>
                  <div className={`p-3 rounded-full ${stat.bgColor}`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Top Books Chart */}
      {stats?.top_books && stats.top_books.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Most Distributed Books
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.top_books.map((book, index) => {
                const maxValue = Math.max(...stats.top_books.map(b => b.total_distributed));
                const percentage = (book.total_distributed / maxValue) * 100;
                
                return (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline" className="text-xs">
                          #{index + 1}
                        </Badge>
                        <span className="font-medium text-sm">{book.book_title}</span>
                      </div>
                      <span className="text-sm font-semibold text-gray-600">
                        {book.total_distributed} books
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      ></div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Quick Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Distribution Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                <span className="text-sm font-medium">Average per Distribution</span>
                <span className="font-semibold text-blue-600">
                  {stats?.total_distributions ? 
                    Math.round((stats.total_books_distributed || 0) / stats.total_distributions) : 0
                  } books
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                <span className="text-sm font-medium">Books per School</span>
                <span className="font-semibold text-green-600">
                  {stats?.schools_served ? 
                    Math.round((stats.total_books_distributed || 0) / stats.schools_served) : 0
                  } books
                </span>
              </div>
              <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                <span className="text-sm font-medium">Monthly Growth</span>
                <span className="font-semibold text-purple-600">
                  {stats?.total_distributions ? 
                    Math.round(((stats.distributions_this_month || 0) / stats.total_distributions) * 100) : 0
                  }%
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Activity Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Completed Distributions</span>
                <Badge variant="default" className="bg-green-100 text-green-800">
                  {(stats?.total_distributions || 0) - (stats?.active_distributions || 0)}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Active/Planned</span>
                <Badge variant="outline" className="text-blue-600 border-blue-200">
                  {stats?.active_distributions || 0}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Schools Reached</span>
                <Badge variant="secondary">
                  {stats?.schools_served || 0}
                </Badge>
              </div>
              <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                <p className="text-xs text-gray-600 text-center">
                  {stats?.total_distributions === 0 
                    ? "No distributions recorded yet. Start by logging your first distribution!"
                    : `Great work! You've distributed ${stats?.total_books_distributed} books across ${stats?.schools_served} schools.`
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default DistributionStatistics;
