import React, { useState, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import {
  Upload,
  X,
  Image as ImageIcon,
  Loader2,
  CheckCircle,
  AlertCircle,
  Minimize2,
  Wifi,
  WifiOff
} from 'lucide-react';
import { toast } from 'sonner';
import { 
  compressImage, 
  progressiveCompress, 
  batchCompressImages,
  compressedImageToFile,
  CompressionOptions,
  CompressedImageResult,
  DEFAULT_COMPRESSION_OPTIONS
} from '@/utils/imageCompression';
import { useOfflineSync } from '@/hooks/field-staff/useOfflineSync';
import { usePhotoUploadQueue } from '@/hooks/field-staff/usePhotoUploadQueue';
import { supabase } from '@/integrations/supabase/client';

export interface PhotoUploadItem {
  id: string;
  originalFile: File;
  compressedResult?: CompressedImageResult;
  uploadStatus: 'pending' | 'compressing' | 'compressed' | 'uploading' | 'uploaded' | 'failed';
  uploadUrl?: string;
  error?: string;
  progress?: number;
}

interface OptimizedPhotoUploadProps {
  onPhotosChanged: (photos: PhotoUploadItem[]) => void;
  maxFiles?: number;
  compressionOptions?: Partial<CompressionOptions>;
  fieldReportId?: string;
  disabled?: boolean;
}

const OptimizedPhotoUpload: React.FC<OptimizedPhotoUploadProps> = ({
  onPhotosChanged,
  maxFiles = 5,
  compressionOptions = {},
  fieldReportId,
  disabled = false
}) => {
  const [photos, setPhotos] = useState<PhotoUploadItem[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [compressionProgress, setCompressionProgress] = useState(0);
  const { addToOfflineQueue, syncStatus } = useOfflineSync();
  const { addToQueue: addToPhotoQueue, isProcessing: photoQueueProcessing } = usePhotoUploadQueue();

  const finalCompressionOptions: CompressionOptions = {
    ...DEFAULT_COMPRESSION_OPTIONS,
    ...compressionOptions
  };

  // Generate unique ID for photo items
  const generatePhotoId = () => `photo_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // Update photos and notify parent
  const updatePhotos = useCallback((newPhotos: PhotoUploadItem[]) => {
    setPhotos(newPhotos);
    onPhotosChanged(newPhotos);
  }, [onPhotosChanged]);

  // Handle file selection and compression
  const handleFileSelection = async (files: FileList) => {
    if (photos.length + files.length > maxFiles) {
      toast.error(`Maximum ${maxFiles} photos allowed`);
      return;
    }

    setIsProcessing(true);
    setCompressionProgress(0);

    try {
      const fileArray = Array.from(files);
      
      // Create initial photo items
      const newPhotoItems: PhotoUploadItem[] = fileArray.map(file => ({
        id: generatePhotoId(),
        originalFile: file,
        uploadStatus: 'pending'
      }));

      // Add to photos list immediately
      const updatedPhotos = [...photos, ...newPhotoItems];
      updatePhotos(updatedPhotos);

      // Compress images with progress tracking
      const compressionResults = await batchCompressImages(
        fileArray,
        finalCompressionOptions,
        (completed, total) => {
          setCompressionProgress((completed / total) * 100);
        }
      );

      // Update photo items with compression results
      const compressedPhotos = updatedPhotos.map((photo, index) => {
        if (newPhotoItems.includes(photo)) {
          const resultIndex = newPhotoItems.indexOf(photo);
          return {
            ...photo,
            compressedResult: compressionResults[resultIndex],
            uploadStatus: 'compressed' as const
          };
        }
        return photo;
      });

      updatePhotos(compressedPhotos);

      // Show compression summary
      const totalOriginalSize = compressionResults.reduce((sum, result) => sum + result.originalSize, 0);
      const totalCompressedSize = compressionResults.reduce((sum, result) => sum + result.compressedSize, 0);
      const overallRatio = (totalOriginalSize - totalCompressedSize) / totalOriginalSize;

      toast.success(
        `Images compressed successfully! Reduced size by ${Math.round(overallRatio * 100)}% ` +
        `(${Math.round(totalOriginalSize / 1024)}KB → ${Math.round(totalCompressedSize / 1024)}KB)`
      );

      // Queue photos for dedicated photo upload queue (works both online and offline)
      compressedPhotos
        .filter(p => p.uploadStatus === 'compressed')
        .forEach(photo => queuePhotoForUpload(photo));

    } catch (error) {
      console.error('Photo compression failed:', error);
      toast.error('Failed to process photos. Please try again.');
    } finally {
      setIsProcessing(false);
      setCompressionProgress(0);
    }
  };

  // Upload compressed photos to Supabase
  const uploadCompressedPhotos = async (photosToUpload: PhotoUploadItem[]) => {
    for (const photo of photosToUpload) {
      if (!photo.compressedResult) continue;

      try {
        // Update status to uploading
        updatePhotoStatus(photo.id, 'uploading');

        // Convert compressed result to File
        const compressedFile = compressedImageToFile(
          photo.compressedResult,
          photo.originalFile.name
        );

        // Upload to Supabase Storage
        const uploadUrl = await uploadToSupabase(compressedFile, fieldReportId);
        
        // Update status to uploaded
        updatePhotoStatus(photo.id, 'uploaded', uploadUrl);

      } catch (error) {
        console.error('Photo upload failed:', error);
        updatePhotoStatus(photo.id, 'failed', undefined, error.message);
        
        // Queue for dedicated photo upload queue as fallback
        queuePhotoForUpload(photo);
      }
    }
  };

  // Queue photo for dedicated photo upload queue
  const queuePhotoForUpload = (photo: PhotoUploadItem) => {
    if (!photo.compressedResult) return;

    const queueId = addToPhotoQueue(
      photo.id,
      photo.originalFile.name,
      photo.compressedResult.blob,
      {
        fieldReportId,
        priority: 'MEDIUM',
        metadata: {
          originalSize: photo.compressedResult.originalSize,
          compressedSize: photo.compressedResult.compressedSize,
          compressionRatio: photo.compressedResult.compressionRatio
        }
      }
    );

    updatePhotoStatus(photo.id, 'uploaded'); // Mark as uploaded for UI purposes
    toast.info('Photo added to upload queue');

    return queueId;
  };

  // Upload to Supabase Storage
  const uploadToSupabase = async (file: File, reportId?: string): Promise<string> => {
    const fileName = `${reportId || 'temp'}_${Date.now()}_${file.name}`;
    let bucket = 'field-report-photos';
    let filePath = `${bucket}/${fileName}`;

    try {
      // Try uploading to field-report-photos bucket first
      const { error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(filePath, file);

      // If bucket doesn't exist, fallback to general-files bucket
      if (uploadError && uploadError.message?.includes('Bucket not found')) {
        console.warn('field-report-photos bucket not found, falling back to general-files bucket');
        bucket = 'general-files';
        filePath = `${bucket}/${fileName}`;

        const { error: fallbackError } = await supabase.storage
          .from(bucket)
          .upload(filePath, file);

        if (fallbackError) {
          throw fallbackError;
        }
      } else if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      return publicUrl;
    } catch (error) {
      console.error('Upload failed:', error);
      throw error;
    }
  };

  // Update individual photo status
  const updatePhotoStatus = (
    photoId: string, 
    status: PhotoUploadItem['uploadStatus'], 
    uploadUrl?: string, 
    error?: string
  ) => {
    setPhotos(prev => prev.map(photo => 
      photo.id === photoId 
        ? { ...photo, uploadStatus: status, uploadUrl, error }
        : photo
    ));
  };

  // Remove photo
  const removePhoto = (photoId: string) => {
    const updatedPhotos = photos.filter(photo => photo.id !== photoId);
    updatePhotos(updatedPhotos);
  };

  // Get status icon for photo
  const getStatusIcon = (photo: PhotoUploadItem) => {
    switch (photo.uploadStatus) {
      case 'compressing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'compressed':
        return <Minimize2 className="h-4 w-4 text-green-500" />;
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'uploaded':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <ImageIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  // Get status badge
  const getStatusBadge = (photo: PhotoUploadItem) => {
    const variants = {
      pending: 'secondary',
      compressing: 'default',
      compressed: 'secondary',
      uploading: 'default',
      uploaded: 'default',
      failed: 'destructive'
    } as const;

    return (
      <Badge variant={variants[photo.uploadStatus]} className="text-xs">
        {photo.uploadStatus}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {/* Upload Area */}
      <div className="space-y-2">
        <Label className="flex items-center gap-2">
          <ImageIcon className="h-4 w-4" />
          Field Photos
          {!syncStatus.isOnline && (
            <Badge variant="outline" className="text-xs">
              <WifiOff className="h-3 w-3 mr-1" />
              Offline Mode
            </Badge>
          )}
        </Label>
        
        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
          <Input
            type="file"
            accept="image/*"
            multiple
            onChange={(e) => e.target.files && handleFileSelection(e.target.files)}
            disabled={disabled || isProcessing || photos.length >= maxFiles}
            className="hidden"
            id="photo-upload"
          />
          <Label
            htmlFor="photo-upload"
            className="cursor-pointer flex flex-col items-center space-y-2"
          >
            {isProcessing ? (
              <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
            ) : (
              <Upload className="h-8 w-8 text-gray-400" />
            )}
            <span className="text-sm text-gray-600">
              {isProcessing 
                ? `Processing photos... ${Math.round(compressionProgress)}%`
                : `Click to upload photos (${photos.length}/${maxFiles})`
              }
            </span>
          </Label>
        </div>

        {/* Compression Progress */}
        {isProcessing && (
          <div className="space-y-2">
            <Progress value={compressionProgress} className="w-full" />
            <p className="text-xs text-gray-500 text-center">
              Compressing images for optimal storage and upload...
            </p>
          </div>
        )}
      </div>

      {/* Photo List */}
      {photos.length > 0 && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Uploaded Photos</Label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
            {photos.map((photo) => (
              <Card key={photo.id} className="relative">
                <CardContent className="p-3">
                  <div className="aspect-square bg-gray-100 rounded-lg mb-2 relative overflow-hidden">
                    {photo.compressedResult ? (
                      <img
                        src={photo.compressedResult.dataUrl}
                        alt={photo.originalFile.name}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center">
                        <ImageIcon className="h-8 w-8 text-gray-400" />
                      </div>
                    )}
                    
                    {/* Remove button */}
                    <Button
                      variant="destructive"
                      size="sm"
                      className="absolute top-1 right-1 h-6 w-6 p-0"
                      onClick={() => removePhoto(photo.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                  
                  <div className="space-y-1">
                    <div className="flex items-center justify-between">
                      {getStatusIcon(photo)}
                      {getStatusBadge(photo)}
                    </div>
                    
                    <p className="text-xs text-gray-600 truncate">
                      {photo.originalFile.name}
                    </p>
                    
                    {photo.compressedResult && (
                      <p className="text-xs text-gray-500">
                        {Math.round(photo.compressedResult.compressedSize / 1024)}KB
                        {photo.compressedResult.compressionRatio > 0 && (
                          <span className="text-green-600 ml-1">
                            (-{Math.round(photo.compressedResult.compressionRatio * 100)}%)
                          </span>
                        )}
                      </p>
                    )}
                    
                    {photo.error && (
                      <p className="text-xs text-red-600">{photo.error}</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default OptimizedPhotoUpload;
