
import React from 'react';
import { Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import TaskViewModeToggle from './TaskViewModeToggle';

interface TaskListHeaderProps {
  title: string;
  description?: string;
  taskCount: number;
  viewMode: 'cards' | 'list';
  onViewModeChange: (mode: 'cards' | 'list') => void;
}

const TaskListHeader: React.FC<TaskListHeaderProps> = ({
  title,
  description,
  taskCount,
  viewMode,
  onViewModeChange
}) => {
  return (
    <CardHeader>
      <div className="flex items-center justify-between">
        <div>
          <CardTitle>{title}</CardTitle>
          {description && <p className="text-gray-600 mt-1">{description}</p>}
        </div>
        <div className="flex items-center gap-3">
          <Badge variant="outline">{taskCount} tasks</Badge>
          <TaskViewModeToggle
            viewMode={viewMode}
            onViewModeChange={onViewModeChange}
          />
        </div>
      </div>
    </CardHeader>
  );
};

export default TaskListHeader;
