<?xml version="1.0" encoding="UTF-8"?>
<svg id="Layer_1" xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 147.2 75.8">
  <!-- Generator: Adobe Illustrator 29.3.0, SVG Export Plug-In . SVG Version: 2.1.0 Build 146)  -->
  <defs>
    <style>
      .st0 {
        opacity: .5;
      }

      .st0, .st1 {
        isolation: isolate;
      }

      .st0, .st1, .st2 {
        fill: #b3895f;
      }

      .st3 {
        fill: none;
      }

      .st1 {
        opacity: .2;
      }

      .st4 {
        fill: #36256d;
      }
    </style>
  </defs>
  <g>
    <path class="st2" d="M3.1,68.7c.4,0,.8,0,1.1.2s.5.4.7.6.2.6.2,1,0,.7-.2,1-.4.5-.7.6-.6.2-1.1.2h-1.4v2.3h-.7v-5.9h2.1ZM3,71.7c.5,0,.8-.1,1-.3s.3-.5.3-.8-.1-.6-.3-.8c-.2-.2-.6-.3-1-.3h-1.3v2.3h1.3Z"/>
    <path class="st2" d="M8.7,68.7c.6,0,1.1.2,1.5.5s.5.7.5,1.3-.2.9-.5,1.2-.9.4-1.5.4h0c0,0-1.5,0-1.5,0v2.5h-.7v-5.9h2.3,0ZM8.7,71.5c.4,0,.8,0,.9-.3s.3-.4.3-.8-.1-.6-.3-.8c-.2-.2-.5-.3-.9-.3h-1.5v2.1h1.5ZM9.1,71.7l2,3h-.9l-1.7-2.6.6-.4h0Z"/>
    <path class="st2" d="M12.5,74.7v-5.9h4.2v.7h-3.5v1.9h2.7v.7h-2.7v2h3.6v.7h-4.3Z"/>
    <path class="st2" d="M20.3,68.7c.4,0,.8,0,1.1.2s.5.4.7.6.2.6.2,1,0,.7-.2,1-.4.5-.7.6-.6.2-1.1.2h-1.4v2.3h-.7v-5.9h2.1ZM20.3,71.7c.5,0,.8-.1,1-.3s.3-.5.3-.8-.1-.6-.3-.8c-.2-.2-.6-.3-1-.3h-1.3v2.3h1.3Z"/>
    <path class="st2" d="M28.5,74.7h-.8l-.6-1.6h-2.7l-.6,1.6h-.8l2.3-5.9h.9l2.3,5.9h0ZM24.7,72.3h2.2l-1.1-2.9-1.1,2.9h0Z"/>
    <path class="st2" d="M32,68.7c.6,0,1.1.2,1.5.5s.5.7.5,1.3-.2.9-.5,1.2-.9.4-1.5.4h0c0,0-1.5,0-1.5,0v2.5h-.7v-5.9h2.2ZM32,71.5c.4,0,.8,0,.9-.3s.3-.4.3-.8-.1-.6-.3-.8c-.2-.2-.5-.3-.9-.3h-1.5v2.1h1.5ZM32.4,71.7l2,3h-.9l-1.7-2.6.6-.4h0Z"/>
    <path class="st2" d="M36.4,68.7v5.9h-.7v-5.9h.7Z"/>
    <path class="st2" d="M43,68.7v5.9h-.9l-2.5-4-.6-1.1h0v.9s0,4.2,0,4.2h-.7v-5.9h.9l2.5,4,.6,1.1h0v-.9s0-4.2,0-4.2h.7,0Z"/>
    <path class="st2" d="M47.4,74.8c-.6,0-1.1-.1-1.5-.4s-.7-.6-.9-1.1-.3-1-.3-1.6.1-1.2.4-1.6.6-.8,1-1.1.9-.4,1.5-.4,1.1.1,1.4.3c.4.2.7.6.9,1l-.6.4c-.1-.4-.4-.6-.6-.8-.3-.2-.6-.3-1.1-.3s-.8.1-1.1.3c-.3.2-.5.5-.7.8s-.3.8-.3,1.3,0,.9.2,1.3c.2.4.4.6.7.8.3.2.7.3,1.1.3s.5,0,.7,0,.4-.2.6-.3.3-.3.4-.5.1-.4.1-.7v-.2h-1.9v-.7h2.6v3h-.6v-1.1s.1,0,.1,0c-.2.4-.4.7-.7.9-.3.2-.8.3-1.2.3h0Z"/>
    <path class="st2" d="M59.3,68.7v5.9h-.9l-2.5-4-.6-1.1h0v.9s0,4.2,0,4.2h-.7v-5.9h.9l2.5,4,.6,1.1h0v-.9s0-4.2,0-4.2h.7,0Z"/>
    <path class="st2" d="M61.2,74.7v-5.9h4.2v.7h-3.5v1.9h2.7v.7h-2.7v2h3.6v.7h-4.3Z"/>
    <path class="st2" d="M71.2,74.7h-.8l-1.6-2.5-1.6,2.5h-.8l2.1-3-1.9-2.9h.8l1.5,2.3,1.5-2.3h.8l-1.9,2.9,2,3.1h0Z"/>
    <path class="st2" d="M76.6,68.7v.7h-2.1v5.3h-.7v-5.3h-2.1v-.7h4.9Z"/>
    <path class="st2" d="M83.4,74.8c-.6,0-1.1-.1-1.5-.4s-.7-.6-.9-1.1-.3-1-.3-1.6.1-1.2.4-1.6c.2-.5.6-.8,1-1.1s.9-.4,1.5-.4,1.1.1,1.4.3c.4.2.7.6.9,1l-.6.4c-.1-.4-.4-.6-.6-.8-.3-.2-.6-.3-1.1-.3s-.8.1-1.1.3-.5.5-.7.8-.3.8-.3,1.3,0,.9.2,1.3c.2.4.4.6.7.8.3.2.7.3,1.1.3s.5,0,.7,0,.4-.2.6-.3c.2-.1.3-.3.4-.5,0-.2.1-.4.1-.7v-.2h-1.9v-.7h2.6v3h-.6v-1.1s.1,0,.1,0c-.2.4-.4.7-.7.9s-.8.3-1.2.3h0Z"/>
    <path class="st2" d="M87.5,74.7v-5.9h4.2v.7h-3.5v1.9h2.7v.7h-2.7v2h3.6v.7h-4.3Z"/>
    <path class="st2" d="M97.9,68.7v5.9h-.9l-2.5-4-.6-1.1h0v.9s0,4.2,0,4.2h-.7v-5.9h.9l2.5,4,.6,1.1h0v-.9s0-4.2,0-4.2h.7,0Z"/>
    <path class="st2" d="M103.7,74h3.4v.7h-4.1v-5.9h.7v5.3h0Z"/>
    <path class="st2" d="M108.3,74.7v-5.9h4.2v.7h-3.5v1.9h2.7v.7h-2.7v2h3.6v.7h-4.3Z"/>
    <path class="st2" d="M118.9,74.7h-.8l-.6-1.6h-2.7l-.6,1.6h-.8l2.3-5.9h.9l2.3,5.9h0ZM115,72.3h2.2l-1.1-2.9-1.1,2.9Z"/>
    <path class="st2" d="M122,68.7c.9,0,1.7.3,2.2.8.5.5.8,1.2.8,2.2s-.3,1.7-.8,2.2-1.3.8-2.2.8h-1.9v-5.9h1.9ZM122.1,74c.7,0,1.2-.2,1.6-.6.4-.4.6-1,.6-1.7s-.2-1.3-.6-1.7-.9-.6-1.6-.6h-1.3v4.6h1.3Z"/>
    <path class="st2" d="M126.6,74.7v-5.9h4.2v.7h-3.5v1.9h2.7v.7h-2.7v2h3.6v.7h-4.3Z"/>
    <path class="st2" d="M134.6,68.7c.6,0,1.1.2,1.5.5.4.3.5.7.5,1.3s-.2.9-.5,1.2-.9.4-1.5.4h0c0,0-1.5,0-1.5,0v2.5h-.7v-5.9h2.3,0ZM134.6,71.5c.4,0,.8,0,.9-.3s.3-.4.3-.8-.1-.6-.3-.8c-.2-.2-.5-.3-.9-.3h-1.5v2.1h1.5ZM135,71.7l2,3h-.9l-1.7-2.6.6-.4h0Z"/>
    <path class="st2" d="M140.2,68.6c.5,0,1,.1,1.4.3s.7.5.9.9l-.6.5c-.2-.4-.5-.6-.8-.8s-.6-.2-1-.2-.6,0-.8.1c-.2,0-.4.2-.5.4s-.1.3-.1.5,0,.4.2.6.4.3.8.4l1.3.3c.6.1,1,.3,1.2.6.2.2.3.6.3.9s0,.6-.3.9c-.2.3-.5.5-.8.6-.4.1-.8.2-1.2.2s-.8,0-1.1-.2-.6-.3-.8-.5-.4-.4-.6-.7l.6-.5c.1.2.3.4.4.6s.4.3.6.4c.3.1.5.1.9.1s.6,0,.8-.1.4-.2.5-.3.2-.3.2-.5,0-.4-.2-.5c-.1-.2-.4-.3-.7-.4l-1.4-.3c-.4,0-.7-.2-.9-.4-.2-.1-.4-.3-.5-.5-.1-.2-.1-.4-.1-.6s0-.6.3-.9.4-.5.8-.6.7-.2,1.2-.2h0Z"/>
  </g>
  <path class="st4" d="M60.4,62.2l10.5-.2c2.6-.2,4.5-.3,5.7-2.9s.6-2.7.9-4.2.3-1.2.9-.9l.2.3-.2,8.2c2.2-.5,4.6.1,5.2-2.7l11.6-32.6.3.3,12.2,33c.3.5.5,1.1,1,1.4s1.9.4,2.4.4c.7,0,2.9-.2,3.5-.4s1-.6,1.1-1.2v-30.4c0-2.3-2.1-1.2-3.4-1.6s-.7-.6-.2-.9h13.4c9.1-.1,16.6,3.5,18.6,12.9s-3.6,21.4-15.4,22.2-19.3-.2-28.5,0c-.6,0-4.1.6-4-.4s5.1,0,5.8-1.3l-3-9.4-10.7-.2h-.3c-.3,1.2-.7,2.1-1.1,3.2s-2.1,5.7-1.9,6.5c.2,1.3,3.7.5,4.5,1.1s.3.2,0,.4c-.3.5-3.5.3-4.2.3-1.7,0-3.3-.2-5-.2s-1.3.4-2,0v.5c0,0-6.4-.3-6.4-.3h-20.6c0-.1,0-.6,0-.6,1.1-.4,3.8.3,4-1.4v-31.8c-.6-1.1-3.6-.2-4-1l.2-.3,25.4-.3.7,9.1c0,.3-.6.3-.8,0-.4-.5-.8-2.8-1.1-3.7-1.5-3.8-3.8-3.9-7.5-4.1s-5.1,0-7.7,0v14.9c2.1,0,4.6,0,6.7-.4s2-.6,2.6-2,.7-4,.9-4.3.5-.2.7,0c.4.3,0,3.4,0,4.1,0,2.5,0,5.2,0,7.8s.5,2.8,0,3.1c-.2,0-.4.1-.6,0-.4-.2-.6-3.3-.8-3.9-1.1-4.7-5.9-3-9.5-3.4v17.2ZM121.1,61.5c6.2,1.9,13,.4,16-5.7s2.4-13.8.7-18.8-8-8.3-14.6-7.8-1.7.2-2.1.7-.4,1.8-.4,2.3c-.5,8.9.4,18.4,0,27.4l.4,2ZM88.5,50.7h10.4l-5.3-14.2-5.1,14.2Z"/>
  <path class="st4" d="M11.6,63.1l-1.7-19.4-.4,19.4c-.2.2-4.8-.1-5.6,0s-.6.3-1.1.4c-.7,0-1.2-.2-.8-1s3.7-.3,4.6-.7.5-1.2.5-1.6c.6-8.9-.5-18.7,0-27.7-.1-.3,0-1.1-.4-1.2-.8-.3-3,.1-4,0s-1-1,0-1.1c4.6-.7,8.7-.7,12.2-4.5l2.8-4c-.6,2.8-2,5.4-4,7.4v30.6c.3.5.2,1.3.3,1.7.4,1.2,4.9.6,5.3,1.1s.2.7-.1.9c-.9.6-6.1-.5-7.5-.1Z"/>
  <path class="st4" d="M31.9,62.2h6.4c2.9-.4,7.5,0,9-3s.8-3.4,1.1-5.1h.8c.2.2.1.4.2.5.2,1.9.1,6,0,7.9s-.1,1-.3,1.1l-26.3-.5c0-1.1,1.5-.7,2.2-.8,1.7-.3,1.8-.5,1.9-2.2.6-9.6-.5-19.9,0-29.6,0-2.4-2.5-1.2-4-1.7-.7-1.1,2.1-.8,2.7-.8,3.3.1,6.6.2,9.9,0l.5.3c.3.4-.5.6-.8.6-.8.1-3.1-.3-3.1,1.1v32.1Z"/>
  <rect class="st3" y="1.9" width="145.4" height="62"/>
  <rect class="st3" y="1.9" width="145.4" height="62"/>
  <polygon class="st2" points="15.4 16.3 22.3 13.7 22.3 20.9 20.4 16.5 15.4 16.3"/>
  <polygon class="st0" points="19.1 11.3 26.1 8.8 26.1 16 24.1 11.6 19.1 11.3"/>
  <polygon class="st1" points="22.9 6.4 29.9 3.9 29.9 11.1 27.9 6.7 22.9 6.4"/>
  <ellipse class="st2" cx="9" cy="21.9" rx="3.1" ry="3.5" transform="translate(-7.4 4.8) rotate(-21.4)"/>
</svg>