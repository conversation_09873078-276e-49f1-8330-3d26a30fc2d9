/**
 * Progressive upload system for field report photos
 * Handles chunked uploads, resume functionality, and offline queue integration
 */

import { supabase } from '@/integrations/supabase/client';

export interface UploadChunk {
  id: string;
  data: Blob;
  index: number;
  size: number;
  uploaded: boolean;
}

export interface ProgressiveUploadSession {
  id: string;
  fileName: string;
  totalSize: number;
  chunks: UploadChunk[];
  uploadedBytes: number;
  status: 'pending' | 'uploading' | 'paused' | 'completed' | 'failed';
  resumeToken?: string;
  error?: string;
  startTime: number;
  lastActivity: number;
}

export interface UploadProgress {
  sessionId: string;
  uploadedBytes: number;
  totalBytes: number;
  percentage: number;
  speed: number; // bytes per second
  estimatedTimeRemaining: number; // seconds
  currentChunk: number;
  totalChunks: number;
}

const CHUNK_SIZE = 1024 * 1024; // 1MB chunks for optimal mobile performance
const MAX_CONCURRENT_UPLOADS = 2; // Limit concurrent uploads to preserve bandwidth
const UPLOAD_TIMEOUT = 30000; // 30 seconds timeout per chunk
const MAX_RETRY_ATTEMPTS = 3;

class ProgressiveUploadManager {
  private sessions: Map<string, ProgressiveUploadSession> = new Map();
  private activeUploads: Set<string> = new Set();
  private uploadQueue: string[] = [];

  /**
   * Create a new upload session for a file
   */
  createSession(file: Blob, fileName: string): ProgressiveUploadSession {
    const sessionId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const chunks = this.createChunks(file, sessionId);
    
    const session: ProgressiveUploadSession = {
      id: sessionId,
      fileName,
      totalSize: file.size,
      chunks,
      uploadedBytes: 0,
      status: 'pending',
      startTime: Date.now(),
      lastActivity: Date.now(),
    };

    this.sessions.set(sessionId, session);
    return session;
  }

  /**
   * Create chunks from a file blob
   */
  private createChunks(file: Blob, sessionId: string): UploadChunk[] {
    const chunks: UploadChunk[] = [];
    const totalChunks = Math.ceil(file.size / CHUNK_SIZE);

    for (let i = 0; i < totalChunks; i++) {
      const start = i * CHUNK_SIZE;
      const end = Math.min(start + CHUNK_SIZE, file.size);
      const chunkBlob = file.slice(start, end);

      chunks.push({
        id: `${sessionId}_chunk_${i}`,
        data: chunkBlob,
        index: i,
        size: chunkBlob.size,
        uploaded: false,
      });
    }

    return chunks;
  }

  /**
   * Start uploading a session
   */
  async startUpload(
    sessionId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Upload session not found');
    }

    if (this.activeUploads.size >= MAX_CONCURRENT_UPLOADS) {
      this.uploadQueue.push(sessionId);
      return new Promise((resolve, reject) => {
        // Will be resolved when upload starts from queue
        session.status = 'pending';
      });
    }

    return this.executeUpload(sessionId, onProgress);
  }

  /**
   * Execute the actual upload process
   */
  private async executeUpload(
    sessionId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Upload session not found');
    }

    this.activeUploads.add(sessionId);
    session.status = 'uploading';
    session.lastActivity = Date.now();

    try {
      // Initialize multipart upload
      const uploadId = await this.initializeMultipartUpload(session.fileName);
      session.resumeToken = uploadId;

      const uploadedParts: { ETag: string; PartNumber: number }[] = [];

      // Upload chunks sequentially for better reliability on mobile
      for (let i = 0; i < session.chunks.length; i++) {
        const chunk = session.chunks[i];
        
        if (chunk.uploaded) {
          continue; // Skip already uploaded chunks (for resume functionality)
        }

        let retryCount = 0;
        let chunkUploaded = false;

        while (retryCount < MAX_RETRY_ATTEMPTS && !chunkUploaded) {
          try {
            const etag = await this.uploadChunk(
              uploadId,
              chunk,
              i + 1 // Part numbers start from 1
            );

            uploadedParts.push({ ETag: etag, PartNumber: i + 1 });
            chunk.uploaded = true;
            session.uploadedBytes += chunk.size;
            session.lastActivity = Date.now();
            chunkUploaded = true;

            // Report progress
            if (onProgress) {
              const progress = this.calculateProgress(session);
              onProgress(progress);
            }

          } catch (error) {
            retryCount++;
            console.warn(`Chunk upload failed (attempt ${retryCount}):`, error);
            
            if (retryCount >= MAX_RETRY_ATTEMPTS) {
              throw new Error(`Failed to upload chunk ${i} after ${MAX_RETRY_ATTEMPTS} attempts`);
            }

            // Exponential backoff
            await this.delay(Math.pow(2, retryCount) * 1000);
          }
        }
      }

      // Complete multipart upload
      const finalUrl = await this.completeMultipartUpload(uploadId, uploadedParts);
      
      session.status = 'completed';
      this.activeUploads.delete(sessionId);
      
      // Process next item in queue
      this.processQueue();

      return finalUrl;

    } catch (error) {
      session.status = 'failed';
      session.error = error.message;
      this.activeUploads.delete(sessionId);
      
      // Process next item in queue
      this.processQueue();
      
      throw error;
    }
  }

  /**
   * Initialize multipart upload with Supabase
   */
  private async initializeMultipartUpload(fileName: string): Promise<string> {
    // This would integrate with Supabase's multipart upload API
    // For now, returning a mock upload ID
    const uploadId = `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // In real implementation:
    // Try field-report-photos bucket first, fallback to general-files
    // const { data, error } = await supabase.storage
    //   .from('field-report-photos')
    //   .createSignedUploadUrl(fileName);
    // If error and bucket not found, try general-files bucket
    
    return uploadId;
  }

  /**
   * Upload a single chunk
   */
  private async uploadChunk(
    uploadId: string,
    chunk: UploadChunk,
    partNumber: number
  ): Promise<string> {
    // Create a timeout promise
    const timeoutPromise = new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error('Chunk upload timeout')), UPLOAD_TIMEOUT);
    });

    // Create upload promise
    const uploadPromise = this.performChunkUpload(uploadId, chunk, partNumber);

    // Race between upload and timeout
    return Promise.race([uploadPromise, timeoutPromise]);
  }

  /**
   * Perform the actual chunk upload
   */
  private async performChunkUpload(
    uploadId: string,
    chunk: UploadChunk,
    partNumber: number
  ): Promise<string> {
    // This would integrate with Supabase's chunk upload API
    // For now, simulating upload with delay
    await this.delay(100 + Math.random() * 500); // Simulate network delay
    
    // In real implementation:
    // Try field-report-photos bucket first, fallback to general-files
    // const { data, error } = await supabase.storage
    //   .from('field-report-photos')
    //   .uploadPart(uploadId, partNumber, chunk.data);
    
    return `etag_${partNumber}_${Date.now()}`;
  }

  /**
   * Complete multipart upload
   */
  private async completeMultipartUpload(
    uploadId: string,
    parts: { ETag: string; PartNumber: number }[]
  ): Promise<string> {
    // This would integrate with Supabase's complete multipart upload API
    // For now, returning a mock URL
    const finalUrl = `https://your-supabase-storage.com/field-report-photos/${uploadId}`;
    
    // In real implementation:
    // Try field-report-photos bucket first, fallback to general-files
    // const { data, error } = await supabase.storage
    //   .from('field-report-photos')
    //   .completeMultipartUpload(uploadId, parts);
    
    return finalUrl;
  }

  /**
   * Calculate upload progress
   */
  private calculateProgress(session: ProgressiveUploadSession): UploadProgress {
    const percentage = (session.uploadedBytes / session.totalSize) * 100;
    const elapsedTime = (Date.now() - session.startTime) / 1000; // seconds
    const speed = session.uploadedBytes / elapsedTime; // bytes per second
    const remainingBytes = session.totalSize - session.uploadedBytes;
    const estimatedTimeRemaining = speed > 0 ? remainingBytes / speed : 0;

    const uploadedChunks = session.chunks.filter(chunk => chunk.uploaded).length;

    return {
      sessionId: session.id,
      uploadedBytes: session.uploadedBytes,
      totalBytes: session.totalSize,
      percentage,
      speed,
      estimatedTimeRemaining,
      currentChunk: uploadedChunks,
      totalChunks: session.chunks.length,
    };
  }

  /**
   * Pause an upload session
   */
  pauseUpload(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session && session.status === 'uploading') {
      session.status = 'paused';
      this.activeUploads.delete(sessionId);
      this.processQueue();
    }
  }

  /**
   * Resume a paused upload session
   */
  async resumeUpload(
    sessionId: string,
    onProgress?: (progress: UploadProgress) => void
  ): Promise<string> {
    const session = this.sessions.get(sessionId);
    if (!session) {
      throw new Error('Upload session not found');
    }

    if (session.status !== 'paused') {
      throw new Error('Upload session is not paused');
    }

    return this.startUpload(sessionId, onProgress);
  }

  /**
   * Cancel an upload session
   */
  cancelUpload(sessionId: string): void {
    const session = this.sessions.get(sessionId);
    if (session) {
      session.status = 'failed';
      session.error = 'Upload cancelled by user';
      this.activeUploads.delete(sessionId);
      this.sessions.delete(sessionId);
      this.processQueue();
    }
  }

  /**
   * Get session status
   */
  getSession(sessionId: string): ProgressiveUploadSession | undefined {
    return this.sessions.get(sessionId);
  }

  /**
   * Process upload queue
   */
  private processQueue(): void {
    if (this.uploadQueue.length > 0 && this.activeUploads.size < MAX_CONCURRENT_UPLOADS) {
      const nextSessionId = this.uploadQueue.shift();
      if (nextSessionId) {
        this.executeUpload(nextSessionId);
      }
    }
  }

  /**
   * Utility delay function
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Clean up completed or failed sessions
   */
  cleanup(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [sessionId, session] of this.sessions.entries()) {
      if (
        (session.status === 'completed' || session.status === 'failed') &&
        (now - session.lastActivity) > maxAge
      ) {
        this.sessions.delete(sessionId);
      }
    }
  }
}

// Export singleton instance
export const progressiveUploadManager = new ProgressiveUploadManager();

// Cleanup old sessions periodically
setInterval(() => {
  progressiveUploadManager.cleanup();
}, 60 * 60 * 1000); // Every hour
