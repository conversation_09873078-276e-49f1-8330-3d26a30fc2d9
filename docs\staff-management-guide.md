# Staff Management System - User Guide

## Overview

The Staff Management System is a comprehensive admin-only feature that allows administrators to manage user accounts, roles, and permissions within the iLead Field Track application. This system provides tools for creating users, managing invitations, tracking audit logs, and maintaining user data.

## Access Requirements

- **Role Required**: Admin only
- **Navigation**: Settings → Staff Management
- **Permissions**: Full CRUD operations on user accounts

## Main Features

### 1. Staff Dashboard

The main dashboard provides an overview of your organization's staff:

- **Total Staff**: Shows the total number of registered users
- **Active Users**: Number of currently active users
- **Inactive Users**: Number of deactivated users
- **Pending Invitations**: Number of outstanding invitations

### 2. Staff Management

#### Viewing Staff Members

The staff table displays:
- User name and email
- Role (Admin, Program Officer, Field Staff)
- Status (Active/Inactive)
- Division assignment
- Contact information
- Last activity

#### Filtering and Search

- **Search**: Filter by name or email
- **Role Filter**: Filter by user role
- **Status Filter**: Filter by active/inactive status
- **Division Filter**: Filter by division assignment

### 3. User Creation

#### Individual User Creation

1. Click **"Add User"** button
2. Choose creation method:
   - **Send Invitation**: User receives email to set up account
   - **Create Directly**: Account created immediately with generated password

3. Fill in user information:
   - Email address (required)
   - Full name (required)
   - Role (required)
   - Phone number (optional)
   - Division assignment (optional)

4. For direct creation:
   - Generate secure password
   - Copy password to share securely with user
   - User must change password on first login

#### Bulk User Creation

1. Click **"Bulk Import"** button
2. Choose import method:
   - **CSV Upload**: Upload a CSV file with user data
   - **Manual Entry**: Add multiple users through forms

3. For CSV upload:
   - Download the template file
   - Fill in user data following the format
   - Upload completed file
   - Review validation results
   - Confirm creation

4. CSV Format:
   ```csv
   email,name,role,division_id,phone
   <EMAIL>,John Doe,field_staff,,+************
   <EMAIL>,Jane Smith,program_officer,division-uuid,+************
   ```

### 4. User Management Actions

#### Role Management

1. Locate user in staff table
2. Click the role badge or action menu
3. Select new role:
   - **Admin**: Full system access
   - **Program Officer**: Management and reporting access
   - **Field Staff**: Field operations access

#### Status Management

1. Find user in staff table
2. Use the status toggle or action menu
3. Confirm status change:
   - **Active**: User can log in and use system
   - **Inactive**: User cannot log in

#### User Information Updates

1. Click on user row or action menu
2. Select "Edit User"
3. Update available fields:
   - Name
   - Phone number
   - Division assignment
4. Save changes

### 5. Invitation Management

#### Viewing Pending Invitations

1. Click **"Pending Invitations"** tab
2. View invitation details:
   - Email address
   - Name
   - Role
   - Status
   - Expiration date

#### Resending Invitations

1. Find invitation in pending list
2. Click **"Resend"** button
3. Invitation email will be sent again
4. Expiration date extends by 7 days

#### Managing Failed Invitations

- Failed invitations are marked with error status
- Review failure reason in invitation details
- Correct issues and resend invitation
- Consider creating account directly if email issues persist

### 6. Data Export

#### Staff Data Export

1. Apply desired filters
2. Click **"Export CSV"** button
3. File downloads with current filtered data
4. Includes all user information and metadata

#### Export Contents

- User names and emails
- Roles and status
- Division assignments
- Contact information
- Account creation dates
- Last activity information

### 7. Audit Logging

#### Viewing Audit Logs

1. Click **"Audit Log"** button
2. Review all staff management actions:
   - User creation/updates/deletion
   - Role changes
   - Status changes
   - Invitation activities
   - Bulk operations

#### Filtering Audit Logs

- **Action Type**: Filter by specific actions
- **Date Range**: Filter by time period
- **User**: Filter by who performed action
- **Target**: Filter by affected user

#### Audit Log Export

1. Apply desired filters
2. Click **"Export CSV"** in audit viewer
3. Download filtered audit data for compliance

## User Roles Explained

### Admin
- Full system access
- Can manage all users and settings
- Access to all features and data
- Can assign any role to users

### Program Officer
- Management and reporting access
- Can view and manage field operations
- Access to analytics and reports
- Cannot manage other admin users

### Field Staff
- Field operations access only
- Can check in/out of visits
- Can log session data
- Limited to assigned schools/areas

## Security Features

### Password Requirements

All passwords must contain:
- Minimum 8 characters
- At least one uppercase letter
- At least one lowercase letter
- At least one number
- At least one special character

### Account Security

- Users must change generated passwords on first login
- Failed login attempts are tracked
- Inactive accounts cannot access system
- All actions are logged for audit purposes

### Data Protection

- All user data is encrypted
- Access is restricted by role
- Audit logs track all changes
- Regular security monitoring

## Best Practices

### User Creation

1. **Use invitations** for new users when possible
2. **Verify email addresses** before creating accounts
3. **Assign appropriate roles** based on job function
4. **Set division assignments** for field staff
5. **Document role assignments** for compliance

### Account Management

1. **Regularly review** user accounts and roles
2. **Deactivate accounts** for departed staff immediately
3. **Update contact information** as needed
4. **Monitor audit logs** for unusual activity
5. **Export data regularly** for backup purposes

### Security Management

1. **Use strong passwords** for all accounts
2. **Force password changes** for compromised accounts
3. **Review role assignments** quarterly
4. **Monitor failed login attempts**
5. **Keep audit logs** for compliance requirements

## Troubleshooting

### Common Issues

#### Invitation Not Received
- Check spam/junk folders
- Verify email address spelling
- Resend invitation
- Check invitation expiration date

#### CSV Import Errors
- Verify CSV format matches template
- Check for required fields
- Ensure email addresses are valid
- Review role names for typos

#### Role Assignment Issues
- Verify admin permissions
- Check user account status
- Review audit logs for conflicts
- Contact system administrator

#### Account Access Problems
- Verify account is active
- Check role assignments
- Review password requirements
- Check for system maintenance

### Getting Help

For additional support:
1. Check audit logs for error details
2. Review user account status
3. Verify role and permission settings
4. Contact system administrator
5. Submit support ticket with error details

## Compliance and Reporting

### Audit Requirements

- All user management actions are logged
- Audit logs include timestamps and user details
- Export capabilities for compliance reporting
- Role change tracking for security audits

### Data Retention

- User accounts: Retained per organizational policy
- Audit logs: Retained for compliance requirements
- Invitation records: Retained for 90 days after expiration
- Export data: Managed per data governance policy

### Privacy Considerations

- User data is protected per privacy policy
- Access is restricted to authorized administrators
- Data export should follow data handling procedures
- Account deactivation removes system access immediately

## Technical Implementation

### Database Schema

The staff management system uses the following database tables:

#### user_invitations
- Tracks invitation status and metadata
- Includes expiration dates and retry counts
- Links to user profiles upon acceptance

#### staff_audit_logs
- Records all staff management actions
- Includes IP addresses and user agents
- Provides comprehensive audit trail

#### bulk_operations
- Tracks bulk user creation operations
- Records success/failure counts
- Links to individual operation results

### API Functions

#### Database Functions
- `create_user_invitation()`: Creates new user invitation
- `bulk_create_invitations()`: Handles bulk invitation creation
- `get_all_staff()`: Retrieves staff with role filtering
- `update_user_role()`: Updates user role with audit logging
- `toggle_user_status()`: Activates/deactivates user accounts

#### Security Features
- Row Level Security (RLS) policies
- Admin-only access control
- Input validation and sanitization
- Rate limiting for bulk operations

### Component Architecture

#### Main Components
- `StaffManagement`: Main dashboard component
- `StaffTable`: User listing and management
- `CreateUserDialog`: Individual user creation
- `BulkUserCreation`: Bulk import functionality
- `AuditLogViewer`: Audit log display and filtering

#### Hooks and Services
- `useStaffManagement`: React hook for all staff operations
- `EmailService`: Handles invitation and welcome emails
- `CSVService`: CSV import/export functionality

### Error Handling

The system implements comprehensive error handling:
- Database operation failures
- Email delivery issues
- CSV validation errors
- Network connectivity problems
- Permission and authentication errors
