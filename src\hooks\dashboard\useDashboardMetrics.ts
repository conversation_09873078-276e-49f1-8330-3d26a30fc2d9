import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';

export interface DashboardMetrics {
  // Field Staff Performance
  fieldStaff: {
    totalStaff: number;
    activeStaff: number;
    checkedInToday: number;
    averageHoursPerDay: number;
    checkInComplianceRate: number;
    reportSubmissionRate: number;
  };
  
  // Program Reach & Impact
  programReach: {
    totalStudentsReached: number;
    schoolsCovered: number;
    totalSchools: number;
    sessionCompletionRate: number;
    averageAttendancePerSession: number;
    bookDistributionRate: number;
  };
  
  // Operational Efficiency
  operational: {
    taskCompletionRate: number;
    averageTaskCompletionTime: number;
    reportQualityScore: number;
    resourceUtilization: number;
    offlineSyncSuccessRate: number;
  };
  
  // Quality Indicators
  quality: {
    sessionAttendanceTrend: number;
    studentEngagementScore: number;
    followUpCompletionRate: number;
    challengeResolutionTime: number;
    feedbackSentiment: number;
  };
}

export interface ActivitySummary {
  todayActivities: {
    checkIns: number;
    sessions: number;
    reports: number;
    visits: number;
  };
  weeklyTrend: {
    date: string;
    activities: number;
    students: number;
    hours: number;
  }[];
  criticalAlerts: {
    id: string;
    type: 'error' | 'warning' | 'info';
    message: string;
    timestamp: string;
    priority: 'high' | 'medium' | 'low';
  }[];
}

export interface StaffPerformance {
  id: string;
  name: string;
  role: string;
  isCheckedIn: boolean;
  currentSchool?: string;
  todayHours: number;
  weeklyHours: number;
  studentsReached: number;
  schoolsVisited: number;
  reportsSubmitted: number;
  performanceScore: number;
  lastActivity: string;
}

export const useDashboardMetrics = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['dashboard-metrics', profile?.id],
    queryFn: async (): Promise<DashboardMetrics> => {
      // For now, we'll use existing data and calculate metrics
      // Later we can create dedicated RPC functions for better performance
      
      // Fetch field staff data
      const { data: profiles } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'field_staff');
      
      // Fetch today's attendance
      const today = new Date().toISOString().split('T')[0];
      const { data: todayAttendance } = await supabase
        .from('field_staff_attendance')
        .select('*')
        .eq('attendance_date', today);
      
      // Fetch timesheets for this week
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());
      const { data: weeklyTimesheets } = await supabase
        .from('daily_timesheets')
        .select('*')
        .gte('timesheet_date', weekStart.toISOString().split('T')[0]);
      
      // Fetch schools
      const { data: schools } = await supabase
        .from('schools')
        .select('*');
      
      // Fetch field reports for this month
      const monthStart = new Date();
      monthStart.setDate(1);
      const { data: monthlyReports } = await supabase
        .from('field_reports')
        .select('*')
        .gte('report_date', monthStart.toISOString().split('T')[0]);
      
      // Fetch tasks
      const { data: tasks } = await supabase
        .from('tasks')
        .select('*');
      
      // Calculate metrics
      const totalStaff = profiles?.length || 0;
      const activeStaff = profiles?.filter(p => p.is_active)?.length || 0;
      const checkedInToday = todayAttendance?.length || 0;
      
      const totalHours = weeklyTimesheets?.reduce((sum, ts) => sum + (ts.total_work_hours || 0), 0) || 0;
      const averageHoursPerDay = totalStaff > 0 ? totalHours / (totalStaff * 7) : 0;
      
      const totalStudentsReached = monthlyReports?.reduce((sum, report) => sum + (report.total_students_attended || 0), 0) || 0;
      const schoolsCovered = new Set(monthlyReports?.map(r => r.school_id)).size;
      const totalSchools = schools?.length || 0;
      
      const completedTasks = tasks?.filter(t => t.status === 'completed')?.length || 0;
      const taskCompletionRate = tasks?.length ? (completedTasks / tasks.length) * 100 : 0;
      
      return {
        fieldStaff: {
          totalStaff,
          activeStaff,
          checkedInToday,
          averageHoursPerDay,
          checkInComplianceRate: totalStaff > 0 ? (checkedInToday / totalStaff) * 100 : 0,
          reportSubmissionRate: 85, // Placeholder - calculate from actual data
        },
        programReach: {
          totalStudentsReached,
          schoolsCovered,
          totalSchools,
          sessionCompletionRate: 92, // Placeholder
          averageAttendancePerSession: monthlyReports?.length ? totalStudentsReached / monthlyReports.length : 0,
          bookDistributionRate: 78, // Placeholder
        },
        operational: {
          taskCompletionRate,
          averageTaskCompletionTime: 2.5, // Placeholder - days
          reportQualityScore: 88, // Placeholder
          resourceUtilization: 76, // Placeholder
          offlineSyncSuccessRate: 94, // Placeholder
        },
        quality: {
          sessionAttendanceTrend: 5.2, // Placeholder - percentage increase
          studentEngagementScore: 4.3, // Placeholder - out of 5
          followUpCompletionRate: 82, // Placeholder
          challengeResolutionTime: 1.8, // Placeholder - days
          feedbackSentiment: 4.1, // Placeholder - out of 5
        },
      };
    },
    enabled: !!profile?.id,
    staleTime: 60000, // 1 minute
    refetchInterval: 300000, // 5 minutes
  });
};

export const useActivitySummary = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['activity-summary', profile?.id],
    queryFn: async (): Promise<ActivitySummary> => {
      const today = new Date().toISOString().split('T')[0];
      
      // Fetch today's activities
      const { data: todayAttendance } = await supabase
        .from('field_staff_attendance')
        .select('*')
        .eq('attendance_date', today);
      
      const { data: todayReports } = await supabase
        .from('field_reports')
        .select('*')
        .eq('report_date', today);
      
      // Fetch weekly trend data
      const weeklyTrend = [];
      for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dateStr = date.toISOString().split('T')[0];
        
        const { data: dayAttendance } = await supabase
          .from('field_staff_attendance')
          .select('*')
          .eq('attendance_date', dateStr);
        
        const { data: dayTimesheets } = await supabase
          .from('daily_timesheets')
          .select('*')
          .eq('timesheet_date', dateStr);
        
        weeklyTrend.push({
          date: dateStr,
          activities: (dayAttendance?.length || 0),
          students: dayTimesheets?.reduce((sum, ts) => sum + (ts.total_students_reached || 0), 0) || 0,
          hours: dayTimesheets?.reduce((sum, ts) => sum + (ts.total_work_hours || 0), 0) || 0,
        });
      }
      
      // Generate sample critical alerts
      const criticalAlerts = [
        {
          id: '1',
          type: 'warning' as const,
          message: 'GPS verification failed for 2 staff members',
          timestamp: new Date().toISOString(),
          priority: 'medium' as const,
        },
        {
          id: '2',
          type: 'error' as const,
          message: '3 overdue field reports require attention',
          timestamp: new Date().toISOString(),
          priority: 'high' as const,
        },
      ];
      
      return {
        todayActivities: {
          checkIns: todayAttendance?.length || 0,
          sessions: todayReports?.reduce((sum, r) => sum + (r.round_table_sessions_count || 0), 0) || 0,
          reports: todayReports?.length || 0,
          visits: todayAttendance?.length || 0,
        },
        weeklyTrend,
        criticalAlerts,
      };
    },
    enabled: !!profile?.id,
    staleTime: 30000, // 30 seconds
    refetchInterval: 60000, // 1 minute for real-time updates
  });
};

export const useStaffPerformance = () => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: ['staff-performance', profile?.id],
    queryFn: async (): Promise<StaffPerformance[]> => {
      const { data: fieldStaff } = await supabase
        .from('profiles')
        .select('*')
        .eq('role', 'field_staff');
      
      if (!fieldStaff) return [];
      
      const today = new Date().toISOString().split('T')[0];
      const weekStart = new Date();
      weekStart.setDate(weekStart.getDate() - weekStart.getDay());
      
      const staffPerformance = await Promise.all(
        fieldStaff.map(async (staff) => {
          // Check if checked in today
          const { data: todayAttendance } = await supabase
            .from('field_staff_attendance')
            .select('*, schools(name)')
            .eq('staff_id', staff.id)
            .eq('attendance_date', today)
            .is('check_out_time', null)
            .single();
          
          // Get weekly timesheets
          const { data: weeklyTimesheets } = await supabase
            .from('daily_timesheets')
            .select('*')
            .eq('staff_id', staff.id)
            .gte('timesheet_date', weekStart.toISOString().split('T')[0]);
          
          const weeklyHours = weeklyTimesheets?.reduce((sum, ts) => sum + (ts.total_work_hours || 0), 0) || 0;
          const studentsReached = weeklyTimesheets?.reduce((sum, ts) => sum + (ts.total_students_reached || 0), 0) || 0;
          const schoolsVisited = weeklyTimesheets?.reduce((sum, ts) => sum + (ts.total_schools_visited || 0), 0) || 0;
          
          return {
            id: staff.id,
            name: staff.full_name || 'Unknown',
            role: staff.role,
            isCheckedIn: !!todayAttendance,
            currentSchool: todayAttendance?.schools?.name,
            todayHours: 0, // Calculate from today's timesheet
            weeklyHours,
            studentsReached,
            schoolsVisited,
            reportsSubmitted: 0, // Calculate from field reports
            performanceScore: Math.min(100, (weeklyHours * 10) + (studentsReached * 0.1)), // Simple calculation
            lastActivity: staff.updated_at || staff.created_at,
          };
        })
      );
      
      return staffPerformance;
    },
    enabled: !!profile?.id && (profile?.role === 'admin' || profile?.role === 'program_officer'),
    staleTime: 120000, // 2 minutes
    refetchInterval: 300000, // 5 minutes
  });
};
