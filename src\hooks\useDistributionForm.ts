
import { useState } from 'react';

// Enhanced interface to match the new database schema
interface DistributionFormData {
  school_id: string;
  inventory_id: string;
  quantity: string;
  notes: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  distribution_date: string;
}

// Interface for the API call (matches RPC function signature)
export interface DistributionSubmissionData {
  school_id: string;
  inventory_id: string;
  quantity: number;
  supervisor_id: string;
  notes?: string;
  book_title: string; // Still needed for compatibility with existing RPC function
  status?: 'planned' | 'in_progress' | 'completed' | 'cancelled'; // New optional status field
  distribution_date?: string; // New optional distribution date field
}

export const useDistributionForm = () => {
  const [formData, setFormData] = useState<DistributionFormData>({
    school_id: '',
    inventory_id: '',
    quantity: '',
    notes: '',
    status: 'completed', // Default to completed for immediate distributions
    distribution_date: new Date().toISOString().split('T')[0], // Default to today's date
  });

  const resetForm = () => {
    setFormData({
      school_id: '',
      inventory_id: '',
      quantity: '',
      notes: '',
      status: 'completed',
      distribution_date: new Date().toISOString().split('T')[0],
    });
  };

  const updateField = (field: keyof DistributionFormData, value: DistributionFormData[keyof DistributionFormData]) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };



  // Validation function
  const validateForm = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!formData.school_id) {
      errors.push('Please select a school');
    }

    if (!formData.inventory_id) {
      errors.push('Please select a book from inventory');
    }

    if (!formData.quantity || parseInt(formData.quantity) <= 0) {
      errors.push('Please enter a valid quantity greater than 0');
    }

    if (!formData.status) {
      errors.push('Please select a distribution status');
    }

    if (!formData.distribution_date) {
      errors.push('Please select a distribution date');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  return {
    formData,
    setFormData,
    resetForm,
    updateField,
    validateForm,
  };
};
