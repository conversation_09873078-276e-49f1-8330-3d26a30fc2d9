/**
 * Storage cleanup and maintenance utilities
 * Handles storage optimization, cleanup operations, and maintenance tasks
 */

import { 
  CleanupResult, 
  StorageStats,
  DEFAULT_CONFIG,
  STORAGE_KEYS 
} from '@/types/offlineSync.types';
import { 
  loadOfflineData, 
  saveOfflineData, 
  loadConflicts, 
  saveConflicts,
  calculateStorageStats 
} from '@/utils/offlineStorage';

/**
 * Perform comprehensive storage cleanup
 */
export const performComprehensiveCleanup = async (forceCleanup = false): Promise<CleanupResult> => {
  const now = Date.now();
  const stats = await calculateStorageStats();

  // Check if cleanup is needed
  if (!forceCleanup && stats.storageUsagePercent < (DEFAULT_CONFIG.cleanupThreshold * 100)) {
    return { itemsRemoved: 0, sizeFreed: 0, conflictsRemoved: 0, photosRemoved: 0 };
  }

  let itemsRemoved = 0;
  let sizeFreed = 0;
  let conflictsRemoved = 0;
  let photosRemoved = 0;

  try {
    // 1. Clean up old successful items
    const offlineData = loadOfflineData();
    const itemsToKeep = offlineData.filter(item => {
      const age = now - item.timestamp;
      const maxAge = DEFAULT_CONFIG.maxRetentionDays * 24 * 60 * 60 * 1000;

      // Keep items that are still pending or uploading
      if (item.retryCount < item.maxRetries) {
        return true;
      }

      // Keep recent items
      if (age < maxAge) {
        return true;
      }

      // Remove old completed items
      const itemSize = JSON.stringify(item).length;
      sizeFreed += itemSize;
      itemsRemoved++;
      return false;
    });

    saveOfflineData(itemsToKeep);

    // 2. Clean up old conflicts
    const conflicts = loadConflicts();
    const conflictsToKeep = conflicts.filter(conflict => {
      const age = now - conflict.timestamp;
      const maxConflictAge = 7 * 24 * 60 * 60 * 1000; // 7 days

      if (age > maxConflictAge) {
        const conflictSize = JSON.stringify(conflict).length;
        sizeFreed += conflictSize;
        conflictsRemoved++;
        return false;
      }
      return true;
    });

    saveConflicts(conflictsToKeep);

    // 3. Clean up photo cache
    try {
      const { photoCacheManager } = await import('@/utils/photoCacheManager');
      const photoCleanupResult = await photoCacheManager.performCleanup();
      photosRemoved = photoCleanupResult.itemsRemoved;
      sizeFreed += photoCleanupResult.sizeFreed;
    } catch (error) {
      console.warn('Could not perform photo cache cleanup:', error);
    }

    // 4. Update cleanup timestamp
    localStorage.setItem(`${STORAGE_KEYS.STORAGE_STATS}_last_cleanup`, now.toString());

    console.log(`Cleanup completed: ${itemsRemoved} items, ${conflictsRemoved} conflicts, ${photosRemoved} photos removed. ${sizeFreed} bytes freed.`);

    return { itemsRemoved, sizeFreed, conflictsRemoved, photosRemoved };
  } catch (error) {
    console.error('Error during cleanup:', error);
    throw new Error('Cleanup operation failed');
  }
};

/**
 * Clean up failed items that have exceeded max retries
 */
export const cleanupFailedItems = (): CleanupResult => {
  try {
    const offlineData = loadOfflineData();
    const initialSize = JSON.stringify(offlineData).length;
    
    const itemsToKeep = offlineData.filter(item => item.retryCount < item.maxRetries);
    const itemsRemoved = offlineData.length - itemsToKeep.length;
    
    saveOfflineData(itemsToKeep);
    
    const finalSize = JSON.stringify(itemsToKeep).length;
    const sizeFreed = initialSize - finalSize;
    
    return { itemsRemoved, sizeFreed, conflictsRemoved: 0, photosRemoved: 0 };
  } catch (error) {
    console.error('Error cleaning up failed items:', error);
    return { itemsRemoved: 0, sizeFreed: 0, conflictsRemoved: 0, photosRemoved: 0 };
  }
};

/**
 * Clean up old conflicts
 */
export const cleanupOldConflicts = (maxAgeHours: number = 168): CleanupResult => { // 7 days default
  try {
    const conflicts = loadConflicts();
    const cutoffTime = Date.now() - (maxAgeHours * 60 * 60 * 1000);
    const initialSize = JSON.stringify(conflicts).length;
    
    const conflictsToKeep = conflicts.filter(conflict => conflict.timestamp > cutoffTime);
    const conflictsRemoved = conflicts.length - conflictsToKeep.length;
    
    saveConflicts(conflictsToKeep);
    
    const finalSize = JSON.stringify(conflictsToKeep).length;
    const sizeFreed = initialSize - finalSize;
    
    return { itemsRemoved: 0, sizeFreed, conflictsRemoved, photosRemoved: 0 };
  } catch (error) {
    console.error('Error cleaning up old conflicts:', error);
    return { itemsRemoved: 0, sizeFreed: 0, conflictsRemoved: 0, photosRemoved: 0 };
  }
};

/**
 * Optimize storage by compressing data
 */
export const optimizeStorage = async (): Promise<{ originalSize: number; optimizedSize: number; savings: number }> => {
  try {
    const offlineData = loadOfflineData();
    const conflicts = loadConflicts();
    
    const originalOfflineSize = JSON.stringify(offlineData).length;
    const originalConflictSize = JSON.stringify(conflicts).length;
    const originalSize = originalOfflineSize + originalConflictSize;
    
    // Remove redundant data and optimize structure
    const optimizedOfflineData = offlineData.map(item => ({
      ...item,
      // Remove redundant checksum if data hasn't changed
      checksum: item.version === 1 ? undefined : item.checksum,
      // Compress large data objects
      data: compressDataObject(item.data)
    }));
    
    const optimizedConflicts = conflicts.map(conflict => ({
      ...conflict,
      // Only keep essential conflict fields
      localData: compressDataObject(conflict.localData),
      serverData: compressDataObject(conflict.serverData)
    }));
    
    saveOfflineData(optimizedOfflineData);
    saveConflicts(optimizedConflicts);
    
    const optimizedOfflineSize = JSON.stringify(optimizedOfflineData).length;
    const optimizedConflictSize = JSON.stringify(optimizedConflicts).length;
    const optimizedSize = optimizedOfflineSize + optimizedConflictSize;
    
    const savings = originalSize - optimizedSize;
    
    return { originalSize, optimizedSize, savings };
  } catch (error) {
    console.error('Error optimizing storage:', error);
    return { originalSize: 0, optimizedSize: 0, savings: 0 };
  }
};

/**
 * Compress data object by removing unnecessary fields
 */
const compressDataObject = (data: Record<string, unknown>): Record<string, unknown> => {
  const compressed = { ...data };
  
  // Remove null and undefined values
  Object.keys(compressed).forEach(key => {
    if (compressed[key] === null || compressed[key] === undefined) {
      delete compressed[key];
    }
  });
  
  // Truncate long strings (keep first 1000 chars for text fields)
  Object.keys(compressed).forEach(key => {
    if (typeof compressed[key] === 'string' && (compressed[key] as string).length > 1000) {
      if (key.includes('note') || key.includes('comment') || key.includes('description')) {
        compressed[key] = (compressed[key] as string).substring(0, 1000) + '...';
      }
    }
  });
  
  return compressed;
};

/**
 * Check if cleanup is needed based on storage usage
 */
export const isCleanupNeeded = async (): Promise<boolean> => {
  try {
    const stats = await calculateStorageStats();
    return stats.storageUsagePercent > (DEFAULT_CONFIG.cleanupThreshold * 100);
  } catch (error) {
    console.error('Error checking cleanup status:', error);
    return false;
  }
};

/**
 * Get cleanup recommendations
 */
export const getCleanupRecommendations = async (): Promise<{
  shouldCleanup: boolean;
  recommendations: string[];
  potentialSavings: number;
}> => {
  try {
    const stats = await calculateStorageStats();
    const offlineData = loadOfflineData();
    const conflicts = loadConflicts();
    const now = Date.now();
    
    const recommendations: string[] = [];
    let potentialSavings = 0;
    
    // Check for old items
    const oldItems = offlineData.filter(item => {
      const age = now - item.timestamp;
      return age > (DEFAULT_CONFIG.maxRetentionDays * 24 * 60 * 60 * 1000);
    });
    
    if (oldItems.length > 0) {
      const oldItemsSize = JSON.stringify(oldItems).length;
      recommendations.push(`Remove ${oldItems.length} old items (${Math.round(oldItemsSize / 1024)}KB)`);
      potentialSavings += oldItemsSize;
    }
    
    // Check for failed items
    const failedItems = offlineData.filter(item => item.retryCount >= item.maxRetries);
    if (failedItems.length > 0) {
      const failedItemsSize = JSON.stringify(failedItems).length;
      recommendations.push(`Remove ${failedItems.length} failed items (${Math.round(failedItemsSize / 1024)}KB)`);
      potentialSavings += failedItemsSize;
    }
    
    // Check for old conflicts
    const oldConflicts = conflicts.filter(conflict => {
      const age = now - conflict.timestamp;
      return age > (7 * 24 * 60 * 60 * 1000); // 7 days
    });
    
    if (oldConflicts.length > 0) {
      const oldConflictsSize = JSON.stringify(oldConflicts).length;
      recommendations.push(`Remove ${oldConflicts.length} old conflicts (${Math.round(oldConflictsSize / 1024)}KB)`);
      potentialSavings += oldConflictsSize;
    }
    
    // Check storage usage
    const shouldCleanup = stats.storageUsagePercent > (DEFAULT_CONFIG.cleanupThreshold * 100);
    
    if (shouldCleanup && recommendations.length === 0) {
      recommendations.push('Consider optimizing storage structure');
    }
    
    return {
      shouldCleanup,
      recommendations,
      potentialSavings
    };
  } catch (error) {
    console.error('Error getting cleanup recommendations:', error);
    return {
      shouldCleanup: false,
      recommendations: [],
      potentialSavings: 0
    };
  }
};

/**
 * Schedule automatic cleanup
 */
export const scheduleAutomaticCleanup = (): void => {
  // Check every hour if cleanup is needed
  setInterval(async () => {
    try {
      const needed = await isCleanupNeeded();
      if (needed) {
        console.log('Automatic cleanup triggered');
        await performComprehensiveCleanup();
      }
    } catch (error) {
      console.error('Error in automatic cleanup:', error);
    }
  }, 60 * 60 * 1000); // 1 hour
};
