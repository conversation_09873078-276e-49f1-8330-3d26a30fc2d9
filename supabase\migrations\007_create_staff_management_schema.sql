-- Staff Management System Database Schema
-- This migration creates the necessary tables and functions for the staff management system

-- Create audit_action enum for audit logging
CREATE TYPE audit_action AS ENUM (
  'user_created',
  'user_updated', 
  'user_deleted',
  'role_changed',
  'status_changed',
  'password_reset',
  'login_attempt',
  'permission_granted',
  'permission_revoked'
);

-- Create user_invitations table
CREATE TABLE user_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT NOT NULL UNIQUE,
  name TEXT NOT NULL,
  role user_role NOT NULL,
  division_id UUID REFERENCES divisions(id),
  phone TEXT,
  invitation_token TEXT NOT NULL UNIQUE,
  expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
  accepted_at TIMESTAMP WITH TIME ZONE,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> audit_logs table
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action audit_action NOT NULL,
  target_user_id UUID REFERENCES auth.users(id),
  target_user_email TEXT,
  performed_by UUID NOT NULL REFERENCES auth.users(id),
  performed_by_name TEXT NOT NULL,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on new tables
ALTER TABLE user_invitations ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS policies for user_invitations (admin and program officers only)
CREATE POLICY "Admin and program officers can manage invitations" ON user_invitations
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'program_officer')
    )
  );

-- RLS policies for audit_logs (admin only)
CREATE POLICY "Admin can view all audit logs" ON audit_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role = 'admin'
    )
  );

CREATE POLICY "Admin can insert audit logs" ON audit_logs
  FOR INSERT WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND role IN ('admin', 'program_officer')
    )
  );

-- Create indexes for performance
CREATE INDEX idx_user_invitations_email ON user_invitations(email);
CREATE INDEX idx_user_invitations_token ON user_invitations(invitation_token);
CREATE INDEX idx_user_invitations_expires_at ON user_invitations(expires_at);
CREATE INDEX idx_audit_logs_action ON audit_logs(action);
CREATE INDEX idx_audit_logs_target_user ON audit_logs(target_user_id);
CREATE INDEX idx_audit_logs_performed_by ON audit_logs(performed_by);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- Function to create user invitation
CREATE OR REPLACE FUNCTION create_user_invitation(
  p_email TEXT,
  p_name TEXT,
  p_role user_role,
  p_division_id UUID DEFAULT NULL,
  p_phone TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  invitation_record user_invitations%ROWTYPE;
  current_user_role user_role;
  invitation_token TEXT;
BEGIN
  -- Check if current user has permission
  SELECT role INTO current_user_role 
  FROM profiles 
  WHERE id = auth.uid();
  
  IF current_user_role NOT IN ('admin', 'program_officer') THEN
    RAISE EXCEPTION 'Only administrators and program officers can create user invitations';
  END IF;
  
  -- Check if email already exists in auth.users
  IF EXISTS (SELECT 1 FROM auth.users WHERE email = p_email) THEN
    RAISE EXCEPTION 'User with email % already exists', p_email;
  END IF;
  
  -- Check if invitation already exists
  IF EXISTS (SELECT 1 FROM user_invitations WHERE email = p_email AND accepted_at IS NULL) THEN
    RAISE EXCEPTION 'Pending invitation already exists for email %', p_email;
  END IF;
  
  -- Generate invitation token
  invitation_token := encode(gen_random_bytes(32), 'base64');
  
  -- Create invitation record
  INSERT INTO user_invitations (
    email,
    name,
    role,
    division_id,
    phone,
    invitation_token,
    expires_at,
    created_by
  ) VALUES (
    p_email,
    p_name,
    p_role,
    p_division_id,
    p_phone,
    invitation_token,
    NOW() + INTERVAL '7 days',
    auth.uid()
  ) RETURNING * INTO invitation_record;
  
  -- Log the action
  INSERT INTO audit_logs (
    action,
    target_user_email,
    performed_by,
    performed_by_name,
    details
  ) VALUES (
    'user_created',
    p_email,
    auth.uid(),
    (SELECT name FROM profiles WHERE id = auth.uid()),
    jsonb_build_object(
      'invitation_id', invitation_record.id,
      'role', p_role,
      'division_id', p_division_id
    )
  );
  
  RETURN json_build_object(
    'id', invitation_record.id,
    'email', invitation_record.email,
    'invitation_token', invitation_record.invitation_token,
    'expires_at', invitation_record.expires_at
  );
END;
$$;

-- Function to get audit logs with pagination
CREATE OR REPLACE FUNCTION get_audit_logs(
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0,
  p_action audit_action DEFAULT NULL,
  p_performed_by UUID DEFAULT NULL,
  p_target_user UUID DEFAULT NULL,
  p_date_from TIMESTAMP WITH TIME ZONE DEFAULT NULL,
  p_date_to TIMESTAMP WITH TIME ZONE DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  action audit_action,
  target_user_id UUID,
  target_user_email TEXT,
  performed_by UUID,
  performed_by_name TEXT,
  details JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_role user_role;
BEGIN
  -- Check if current user has permission (admin only)
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid();

  IF current_user_role != 'admin' THEN
    RAISE EXCEPTION 'Only administrators can view audit logs';
  END IF;

  RETURN QUERY
  SELECT
    al.id,
    al.action,
    al.target_user_id,
    al.target_user_email,
    al.performed_by,
    al.performed_by_name,
    al.details,
    al.ip_address,
    al.user_agent,
    al.created_at
  FROM audit_logs al
  WHERE
    (p_action IS NULL OR al.action = p_action)
    AND (p_performed_by IS NULL OR al.performed_by = p_performed_by)
    AND (p_target_user IS NULL OR al.target_user_id = p_target_user)
    AND (p_date_from IS NULL OR al.created_at >= p_date_from)
    AND (p_date_to IS NULL OR al.created_at <= p_date_to)
  ORDER BY al.created_at DESC
  LIMIT p_limit
  OFFSET p_offset;
END;
$$;

-- Function to get staff members with enhanced details
CREATE OR REPLACE FUNCTION get_staff_members(
  p_limit INTEGER DEFAULT 50,
  p_offset INTEGER DEFAULT 0,
  p_search TEXT DEFAULT NULL,
  p_role user_role DEFAULT NULL,
  p_division_id UUID DEFAULT NULL,
  p_is_active BOOLEAN DEFAULT NULL
)
RETURNS TABLE (
  id UUID,
  name TEXT,
  email TEXT,
  role user_role,
  division_id UUID,
  division_name TEXT,
  phone TEXT,
  country TEXT,
  is_active BOOLEAN,
  requires_password_change BOOLEAN,
  last_password_change TIMESTAMP WITH TIME ZONE,
  invitation_accepted_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_role user_role;
BEGIN
  -- Check if current user has permission
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid();

  IF current_user_role NOT IN ('admin', 'program_officer') THEN
    RAISE EXCEPTION 'Only administrators and program officers can view staff members';
  END IF;

  RETURN QUERY
  SELECT
    p.id,
    p.name,
    u.email,
    p.role,
    p.division_id,
    d.name as division_name,
    p.phone,
    p.country,
    p.is_active,
    p.requires_password_change,
    p.last_password_change,
    p.invitation_accepted_at,
    p.created_at
  FROM profiles p
  JOIN auth.users u ON p.id = u.id
  LEFT JOIN divisions d ON p.division_id = d.id
  WHERE
    (p_search IS NULL OR
     p.name ILIKE '%' || p_search || '%' OR
     u.email ILIKE '%' || p_search || '%')
    AND (p_role IS NULL OR p.role = p_role)
    AND (p_division_id IS NULL OR p.division_id = p_division_id)
    AND (p_is_active IS NULL OR p.is_active = p_is_active)
  ORDER BY p.name
  LIMIT p_limit
  OFFSET p_offset;
END;
$$;

-- Function for bulk user creation
CREATE OR REPLACE FUNCTION create_bulk_user_invitations(
  p_users JSONB
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  user_data JSONB;
  invitation_record user_invitations%ROWTYPE;
  current_user_role user_role;
  invitation_token TEXT;
  results JSONB := '[]'::JSONB;
  success_count INTEGER := 0;
  error_count INTEGER := 0;
  errors JSONB := '[]'::JSONB;
BEGIN
  -- Check if current user has permission
  SELECT role INTO current_user_role
  FROM profiles
  WHERE id = auth.uid();

  IF current_user_role NOT IN ('admin', 'program_officer') THEN
    RAISE EXCEPTION 'Only administrators and program officers can create bulk user invitations';
  END IF;

  -- Process each user
  FOR user_data IN SELECT * FROM jsonb_array_elements(p_users)
  LOOP
    BEGIN
      -- Validate required fields
      IF user_data->>'email' IS NULL OR user_data->>'name' IS NULL OR user_data->>'role' IS NULL THEN
        error_count := error_count + 1;
        errors := errors || jsonb_build_object(
          'email', COALESCE(user_data->>'email', 'unknown'),
          'error', 'Missing required fields (email, name, role)'
        );
        CONTINUE;
      END IF;

      -- Check if email already exists
      IF EXISTS (SELECT 1 FROM auth.users WHERE email = user_data->>'email') THEN
        error_count := error_count + 1;
        errors := errors || jsonb_build_object(
          'email', user_data->>'email',
          'error', 'User already exists'
        );
        CONTINUE;
      END IF;

      -- Check if invitation already exists
      IF EXISTS (SELECT 1 FROM user_invitations WHERE email = user_data->>'email' AND accepted_at IS NULL) THEN
        error_count := error_count + 1;
        errors := errors || jsonb_build_object(
          'email', user_data->>'email',
          'error', 'Pending invitation already exists'
        );
        CONTINUE;
      END IF;

      -- Generate invitation token
      invitation_token := encode(gen_random_bytes(32), 'base64');

      -- Create invitation record
      INSERT INTO user_invitations (
        email,
        name,
        role,
        division_id,
        phone,
        invitation_token,
        expires_at,
        created_by
      ) VALUES (
        user_data->>'email',
        user_data->>'name',
        (user_data->>'role')::user_role,
        CASE WHEN user_data->>'division_id' != '' THEN (user_data->>'division_id')::UUID ELSE NULL END,
        NULLIF(user_data->>'phone', ''),
        invitation_token,
        NOW() + INTERVAL '7 days',
        auth.uid()
      ) RETURNING * INTO invitation_record;

      -- Log the action
      INSERT INTO audit_logs (
        action,
        target_user_email,
        performed_by,
        performed_by_name,
        details
      ) VALUES (
        'user_created',
        user_data->>'email',
        auth.uid(),
        (SELECT name FROM profiles WHERE id = auth.uid()),
        jsonb_build_object(
          'invitation_id', invitation_record.id,
          'role', user_data->>'role',
          'division_id', user_data->>'division_id',
          'bulk_operation', true
        )
      );

      success_count := success_count + 1;
      results := results || jsonb_build_object(
        'email', invitation_record.email,
        'invitation_token', invitation_record.invitation_token,
        'status', 'success'
      );

    EXCEPTION WHEN OTHERS THEN
      error_count := error_count + 1;
      errors := errors || jsonb_build_object(
        'email', COALESCE(user_data->>'email', 'unknown'),
        'error', SQLERRM
      );
    END;
  END LOOP;

  RETURN json_build_object(
    'success_count', success_count,
    'error_count', error_count,
    'results', results,
    'errors', errors
  );
END;
$$;
