import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { 
  Building, 
  Plus, 
  Calendar, 
  DollarSign, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  Camera,
  FileText,
  TrendingUp
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { ImprovementType } from '@/types/impact';

const infrastructureSchema = z.object({
  improvement_type: z.enum(['infrastructure', 'equipment', 'resources', 'facilities', 'technology', 'furniture', 'utilities']),
  title: z.string().min(1, 'Title is required'),
  description: z.string().min(1, 'Description is required'),
  before_condition: z.string().optional(),
  after_condition: z.string().optional(),
  improvement_date: z.string(),
  completion_date: z.string().optional(),
  cost_estimate: z.number().min(0).optional(),
  actual_cost: z.number().min(0).optional(),
  funding_source: z.string().optional(),
  contractor_details: z.string().optional(),
  impact_description: z.string().optional()
});

type InfrastructureFormData = z.infer<typeof infrastructureSchema>;

interface InfrastructureImprovementsProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
}

const InfrastructureImprovements: React.FC<InfrastructureImprovementsProps> = ({
  schoolId,
  dateRange
}) => {
  const [showForm, setShowForm] = useState(false);
  const [selectedImprovement, setSelectedImprovement] = useState<string | null>(null);

  // Mock data for demonstration
  const improvements = [
    {
      id: '1',
      improvement_type: 'infrastructure' as ImprovementType,
      title: 'New Classroom Block Construction',
      description: 'Construction of 4 new classrooms to accommodate growing student population',
      before_condition: 'Students were learning under trees due to lack of classroom space',
      after_condition: 'All students now have proper classroom facilities with desks and blackboards',
      improvement_date: '2024-01-15',
      completion_date: '2024-03-20',
      cost_estimate: 25000,
      actual_cost: 27500,
      funding_source: 'World Bank Education Grant',
      contractor_details: 'ABC Construction Ltd.',
      impact_description: 'Increased enrollment by 30% and improved learning conditions',
      status: 'completed'
    },
    {
      id: '2',
      improvement_type: 'utilities' as ImprovementType,
      title: 'Solar Power Installation',
      description: 'Installation of solar panels to provide electricity for lighting and computer lab',
      before_condition: 'No electricity, limiting evening study and computer access',
      after_condition: 'Reliable electricity enabling extended study hours and digital learning',
      improvement_date: '2024-02-01',
      completion_date: '2024-02-28',
      cost_estimate: 15000,
      actual_cost: 14200,
      funding_source: 'Green Energy Initiative',
      contractor_details: 'Solar Solutions Kenya',
      impact_description: 'Extended study hours and introduced computer literacy program',
      status: 'completed'
    },
    {
      id: '3',
      improvement_type: 'facilities' as ImprovementType,
      title: 'Water Well and Sanitation',
      description: 'Drilling of borehole and construction of improved sanitation facilities',
      before_condition: 'Students walked 2km to fetch water, poor sanitation facilities',
      after_condition: 'Clean water on campus, modern toilet facilities with handwashing stations',
      improvement_date: '2024-03-01',
      completion_date: null,
      cost_estimate: 18000,
      actual_cost: null,
      funding_source: 'UNICEF WASH Program',
      contractor_details: 'Water Works Ltd.',
      impact_description: 'Expected to improve health outcomes and reduce absenteeism',
      status: 'in_progress'
    }
  ];

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    reset,
    formState: { errors }
  } = useForm<InfrastructureFormData>({
    resolver: zodResolver(infrastructureSchema),
    defaultValues: {
      improvement_date: new Date().toISOString().split('T')[0]
    }
  });

  const onSubmit = (data: InfrastructureFormData) => {
    console.log('Infrastructure improvement data:', data);
    // In real implementation, this would save to database
    setShowForm(false);
    reset();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case 'in_progress':
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case 'planned':
        return <Badge className="bg-yellow-100 text-yellow-800">Planned</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  const getImprovementIcon = (type: ImprovementType) => {
    switch (type) {
      case 'infrastructure':
        return <Building className="h-5 w-5" />;
      case 'utilities':
        return <TrendingUp className="h-5 w-5" />;
      case 'facilities':
        return <Building className="h-5 w-5" />;
      default:
        return <Building className="h-5 w-5" />;
    }
  };

  const totalInvestment = improvements.reduce((sum, imp) => sum + (imp.actual_cost || imp.cost_estimate || 0), 0);
  const completedProjects = improvements.filter(imp => imp.status === 'completed').length;
  const inProgressProjects = improvements.filter(imp => imp.status === 'in_progress').length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="bg-blue-100 p-3 rounded-lg">
            <Building className="h-6 w-6 text-blue-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Infrastructure Improvements</h2>
            <p className="text-gray-600">Track facility upgrades and their impact on education</p>
          </div>
        </div>
        
        <Button 
          onClick={() => setShowForm(true)}
          className="bg-ilead-green hover:bg-ilead-dark-green"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Improvement
        </Button>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-green-100 p-2 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{completedProjects}</p>
                <p className="text-sm text-gray-600">Completed Projects</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Clock className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{inProgressProjects}</p>
                <p className="text-sm text-gray-600">In Progress</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-purple-100 p-2 rounded-lg">
                <DollarSign className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">${totalInvestment.toLocaleString()}</p>
                <p className="text-sm text-gray-600">Total Investment</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-orange-100 p-2 rounded-lg">
                <TrendingUp className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">85%</p>
                <p className="text-sm text-gray-600">Success Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Improvements List */}
      <Card>
        <CardHeader>
          <CardTitle>Infrastructure Projects</CardTitle>
          <CardDescription>
            Detailed view of all infrastructure improvements and their impact
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {improvements.map((improvement) => (
              <div key={improvement.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="bg-blue-100 p-2 rounded-lg">
                      {getImprovementIcon(improvement.improvement_type)}
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{improvement.title}</h3>
                      <p className="text-gray-600 capitalize">{improvement.improvement_type}</p>
                    </div>
                  </div>
                  {getStatusBadge(improvement.status)}
                </div>
                
                <p className="text-gray-700 mb-3">{improvement.description}</p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                  <div>
                    <h4 className="font-medium text-sm text-gray-800 mb-1">Before Condition</h4>
                    <p className="text-sm text-gray-600">{improvement.before_condition}</p>
                  </div>
                  <div>
                    <h4 className="font-medium text-sm text-gray-800 mb-1">After Condition</h4>
                    <p className="text-sm text-gray-600">{improvement.after_condition}</p>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Start Date:</span>
                    <p className="text-gray-600">{new Date(improvement.improvement_date).toLocaleDateString()}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Completion:</span>
                    <p className="text-gray-600">
                      {improvement.completion_date 
                        ? new Date(improvement.completion_date).toLocaleDateString()
                        : 'In Progress'
                      }
                    </p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Budget:</span>
                    <p className="text-gray-600">${improvement.cost_estimate?.toLocaleString()}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Actual Cost:</span>
                    <p className="text-gray-600">
                      {improvement.actual_cost 
                        ? `$${improvement.actual_cost.toLocaleString()}`
                        : 'TBD'
                      }
                    </p>
                  </div>
                </div>
                
                {improvement.impact_description && (
                  <div className="mt-3 p-3 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-800 mb-1">Impact</h4>
                    <p className="text-sm text-green-700">{improvement.impact_description}</p>
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Add Improvement Form */}
      {showForm && (
        <Dialog open={showForm} onOpenChange={setShowForm}>
          <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>Add Infrastructure Improvement</span>
              </DialogTitle>
              <DialogDescription>
                Record details about infrastructure improvements and their impact
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="improvement_type">Improvement Type *</Label>
                  <Select 
                    value={watch('improvement_type')} 
                    onValueChange={(value) => setValue('improvement_type', value as ImprovementType)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="infrastructure">Infrastructure</SelectItem>
                      <SelectItem value="equipment">Equipment</SelectItem>
                      <SelectItem value="resources">Resources</SelectItem>
                      <SelectItem value="facilities">Facilities</SelectItem>
                      <SelectItem value="technology">Technology</SelectItem>
                      <SelectItem value="furniture">Furniture</SelectItem>
                      <SelectItem value="utilities">Utilities</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.improvement_type && (
                    <p className="text-sm text-red-600 mt-1">{errors.improvement_type.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="title">Project Title *</Label>
                  <Input {...register('title')} />
                  {errors.title && (
                    <p className="text-sm text-red-600 mt-1">{errors.title.message}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea {...register('description')} />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1">{errors.description.message}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="before_condition">Before Condition</Label>
                  <Textarea {...register('before_condition')} />
                </div>

                <div>
                  <Label htmlFor="after_condition">After Condition</Label>
                  <Textarea {...register('after_condition')} />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="improvement_date">Start Date *</Label>
                  <Input type="date" {...register('improvement_date')} />
                  {errors.improvement_date && (
                    <p className="text-sm text-red-600 mt-1">{errors.improvement_date.message}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="completion_date">Completion Date</Label>
                  <Input type="date" {...register('completion_date')} />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="cost_estimate">Estimated Cost ($)</Label>
                  <Input 
                    type="number" 
                    step="0.01" 
                    {...register('cost_estimate', { valueAsNumber: true })} 
                  />
                </div>

                <div>
                  <Label htmlFor="actual_cost">Actual Cost ($)</Label>
                  <Input 
                    type="number" 
                    step="0.01" 
                    {...register('actual_cost', { valueAsNumber: true })} 
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="funding_source">Funding Source</Label>
                <Input {...register('funding_source')} />
              </div>

              <div>
                <Label htmlFor="contractor_details">Contractor Details</Label>
                <Textarea {...register('contractor_details')} />
              </div>

              <div>
                <Label htmlFor="impact_description">Impact Description</Label>
                <Textarea 
                  {...register('impact_description')} 
                  placeholder="Describe the impact this improvement has had on education..."
                />
              </div>

              <div className="flex justify-end space-x-2">
                <Button type="button" variant="outline" onClick={() => setShowForm(false)}>
                  Cancel
                </Button>
                <Button type="submit" className="bg-ilead-green hover:bg-ilead-dark-green">
                  Save Improvement
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
};

export default InfrastructureImprovements;
