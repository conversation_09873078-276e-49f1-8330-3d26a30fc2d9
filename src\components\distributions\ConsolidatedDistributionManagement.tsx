import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Package, 
  Plus, 
  Calendar, 
  CheckCircle, 
  Clock, 
  AlertTriangle,
  BookOpen,
  Search,
  Filter,
  Download,
  Eye,
  Truck
} from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import { useDistributions } from '@/hooks/useDistributions';
import { DistributionSubmissionData } from '@/hooks/useDistributionForm';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import CreateDistributionDialog from './CreateDistributionDialog';
import DistributionList from './DistributionList';
import { Database } from '@/integrations/supabase/types';

type BookDistribution = Database['public']['Functions']['get_book_distributions']['Returns'][0];

const ConsolidatedDistributionManagement = () => {
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState('active');
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [statusFilter, setStatusFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const {
    distributions,
    schools,
    inventory,
    isLoading,
    addDistribution,
    isAdding,
  } = useDistributions();

  // Filter distributions by status and search
  const getFilteredDistributions = (status?: string) => {
    if (!distributions) return [];
    
    let filtered = distributions;

    // Filter by status
    if (status === 'active') {
      const today = new Date();
      const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());
      
      filtered = distributions.filter((dist: BookDistribution) => {
        const distDate = new Date(dist.delivery_date);
        const distDateOnly = new Date(distDate.getFullYear(), distDate.getMonth(), distDate.getDate());
        return (distDateOnly.getTime() === todayOnly.getTime()) || (dist.status === 'in_progress');
      });
    } else if (status === 'planned') {
      filtered = distributions.filter((dist: BookDistribution) => dist.status === 'planned');
    } else if (status === 'completed') {
      filtered = distributions.filter((dist: BookDistribution) => dist.status === 'completed');
    }

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter((dist: BookDistribution) => 
        dist.book_title?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        dist.school_name?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  };

  const activeDistributions = getFilteredDistributions('active');
  const plannedDistributions = getFilteredDistributions('planned');
  const completedDistributions = getFilteredDistributions('completed');
  const allDistributions = getFilteredDistributions();

  const handleCreateDistribution = (data: DistributionSubmissionData) => {
    addDistribution(data);
    setCreateDialogOpen(false);
  };

  // Calculate summary stats
  const getSummaryStats = () => {
    const totalDistributions = distributions?.length || 0;
    const activeCount = activeDistributions.length;
    const plannedCount = plannedDistributions.length;
    const completedCount = completedDistributions.length;
    const totalBooks = distributions?.reduce((sum, dist) => sum + (dist.quantity || 0), 0) || 0;

    return {
      totalDistributions,
      activeCount,
      plannedCount,
      completedCount,
      totalBooks
    };
  };

  const stats = getSummaryStats();

  // Tab configuration
  const availableTabs = [
    {
      id: 'active',
      label: 'Active',
      icon: Truck,
      count: stats.activeCount,
      description: "Today's distributions and in-progress deliveries"
    },
    {
      id: 'planned',
      label: 'Planned',
      icon: Calendar,
      count: stats.plannedCount,
      description: 'Scheduled future distributions'
    },
    {
      id: 'completed',
      label: 'Completed',
      icon: CheckCircle,
      count: stats.completedCount,
      description: 'Successfully delivered distributions'
    },
    {
      id: 'all',
      label: 'All',
      icon: Package,
      count: stats.totalDistributions,
      description: 'Complete distribution history'
    }
  ];

  if (isLoading) {
    return (
      <div className="animate-pulse space-y-4">
        <div className="h-8 bg-gray-200 rounded w-1/3"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        <div className="space-y-3">
          <div className="h-32 bg-gray-200 rounded"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <>
      <PageHeader
        title="Distribution Management"
        description="Manage book distributions across all schools and track delivery status"
        icon={Package}
      >
        <div className="flex items-center gap-2">
          <Badge variant={stats.activeCount > 0 ? "default" : "secondary"}>
            {stats.activeCount} Active
          </Badge>
          {stats.plannedCount > 0 && (
            <Badge variant="outline">
              {stats.plannedCount} Planned
            </Badge>
          )}
        </div>
      </PageHeader>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Package className="h-4 w-4" />
              Total Distributions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalDistributions}</div>
            <p className="text-xs text-muted-foreground">
              All time
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Truck className="h-4 w-4" />
              Active Today
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeCount}</div>
            <p className="text-xs text-muted-foreground">
              In progress
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              Planned
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.plannedCount}</div>
            <p className="text-xs text-muted-foreground">
              Future deliveries
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Books Distributed
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalBooks}</div>
            <p className="text-xs text-muted-foreground">
              Total copies
            </p>
          </CardContent>
        </Card>
      </div>

      <ContentCard>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          {/* Tab Navigation */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <TabsList className="grid w-full grid-cols-4 sm:w-auto">
              {availableTabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <TabsTrigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                    <Icon className="h-4 w-4" />
                    <span className="hidden sm:inline">{tab.label}</span>
                    {tab.count > 0 && (
                      <Badge variant="secondary" className="ml-1">
                        {tab.count}
                      </Badge>
                    )}
                  </TabsTrigger>
                );
              })}
            </TabsList>

            {/* Search and Filters */}
            <div className="flex items-center gap-2">
              <div className="relative">
                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search distributions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-8 w-64"
                />
              </div>

              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                Export
              </Button>
            </div>
          </div>

          {/* Tab Content */}
          {availableTabs.map((tab) => (
            <TabsContent key={tab.id} value={tab.id} className="mt-0">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold">{tab.label} Distributions</h3>
                    <p className="text-sm text-muted-foreground">
                      {tab.description} ({tab.count} items)
                    </p>
                  </div>
                  {tab.id === 'active' && (
                    <Button
                      onClick={() => setCreateDialogOpen(true)}
                      size="sm"
                      className="bg-purple-600 hover:bg-purple-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Log Distribution
                    </Button>
                  )}
                </div>
                
                {/* Show alert for active distributions */}
                {tab.id === 'active' && stats.activeCount > 0 && (
                  <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-center text-blue-800">
                      <Clock className="h-5 w-5 mr-2" />
                      <span className="font-medium">
                        {stats.activeCount} distribution(s) require attention today
                      </span>
                    </div>
                  </div>
                )}
                
                {/* Distribution List */}
                <DistributionList
                  distributions={getFilteredDistributions(tab.id === 'all' ? undefined : tab.id)}
                  currentUser={profile}
                  onCreateClick={() => setCreateDialogOpen(true)}
                />
              </div>
            </TabsContent>
          ))}
        </Tabs>
      </ContentCard>

      {/* Create Distribution Dialog */}
      <CreateDistributionDialog
        schools={schools}
        inventory={inventory}
        currentUser={profile}
        onSubmit={handleCreateDistribution}
        isSubmitting={isAdding}
        open={createDialogOpen}
        onOpenChange={setCreateDialogOpen}
      />
    </>
  );
};

export default ConsolidatedDistributionManagement;
