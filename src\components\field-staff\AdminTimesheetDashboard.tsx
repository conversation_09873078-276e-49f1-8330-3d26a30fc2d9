import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Calendar,
  Clock,
  Users,
  School,
  Target,
  TrendingUp,
  MapPin,
  FileText,
  Download,
  Filter,
  Search,
  Eye
} from 'lucide-react';
import { useFieldStaffTimesheets, useFieldStaffAttendanceStatus } from '@/hooks/field-staff/useFieldStaffAttendance';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { format } from 'date-fns';

/**
 * Admin Timesheet Dashboard
 *
 * Note: GPS tracking has been simplified to check-in only verification.
 * - Check-in location is required for school arrival verification
 * - Check-out location is optional to support real-world field staff workflows
 * - Staff can check out from any location after completing their field activities
 */

interface TimesheetData {
  timesheet_id: string;
  staff_id: string;
  staff_name: string;
  staff_role: string;
  timesheet_date: string;
  total_work_hours: number;
  total_schools_visited: number;
  total_students_reached: number;
  total_sessions_conducted: number;
  schools_visited: Record<string, unknown>;
  key_achievements: string;
  main_challenges: string;
  timesheet_status: string;
  productivity_score: number;
  submitted_at: string;
}

interface AttendanceStatusData {
  staff_id: string;
  staff_name: string;
  attendance_date: string;
  total_check_ins: number;
  current_status: string;
  current_school_name: string;
  check_in_time: string;
  hours_worked: number;
  schools_visited_today: number;
}

const AdminTimesheetDashboard: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  const { data: timesheets, isLoading: timesheetsLoading } = useFieldStaffTimesheets(selectedDate, selectedStaff === 'all' ? undefined : selectedStaff);
  const { data: attendanceStatus, isLoading: statusLoading } = useFieldStaffAttendanceStatus(selectedDate, selectedStaff === 'all' ? undefined : selectedStaff);

  // Filter timesheets based on search and status
  const filteredTimesheets = timesheets?.filter(timesheet => {
    const matchesSearch = timesheet.staff_name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || timesheet.timesheet_status === statusFilter;
    return matchesSearch && matchesStatus;
  }) || [];

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      draft: { color: 'bg-gray-100 text-gray-800', label: 'Draft' },
      submitted: { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },
      approved: { color: 'bg-green-100 text-green-800', label: 'Approved' },
      rejected: { color: 'bg-red-100 text-red-800', label: 'Rejected' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.draft;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getCurrentStatusBadge = (status: string) => {
    const statusConfig = {
      not_checked_in: { color: 'bg-gray-100 text-gray-800', label: 'Not Checked In' },
      active: { color: 'bg-green-100 text-green-800', label: 'Active' },
      completed: { color: 'bg-blue-100 text-blue-800', label: 'Completed' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.not_checked_in;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const calculateTotalStats = () => {
    if (!filteredTimesheets.length) return { hours: 0, schools: 0, students: 0, sessions: 0 };
    
    return filteredTimesheets.reduce((acc, timesheet) => ({
      hours: acc.hours + (timesheet.total_work_hours || 0),
      schools: acc.schools + (timesheet.total_schools_visited || 0),
      students: acc.students + (timesheet.total_students_reached || 0),
      sessions: acc.sessions + (timesheet.total_sessions_conducted || 0),
    }), { hours: 0, schools: 0, students: 0, sessions: 0 });
  };

  const totalStats = calculateTotalStats();

  return (
    <PageLayout>
      <PageHeader
        title="Field Staff Timesheets"
        description="Monitor daily activities and performance of field staff members"
      />

      {/* Filters and Controls */}
      <ContentCard>
        <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4" />
              <Input
                type="date"
                value={selectedDate}
                onChange={(e) => setSelectedDate(e.target.value)}
                className="w-auto"
              />
            </div>

            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <Input
                placeholder="Search staff..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48"
              />
            </div>

            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-40">
                <SelectValue placeholder="All Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="submitted">Submitted</SelectItem>
                <SelectItem value="approved">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" size="sm">
              <Filter className="h-4 w-4 mr-2" />
              Advanced Filters
            </Button>
          </div>
        </div>
      </ContentCard>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-500" />
              <div>
                <div className="text-2xl font-bold">{totalStats.hours.toFixed(1)}</div>
                <div className="text-sm text-gray-600">Total Hours</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <School className="h-5 w-5 text-green-500" />
              <div>
                <div className="text-2xl font-bold">{totalStats.schools}</div>
                <div className="text-sm text-gray-600">Schools Visited</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-purple-500" />
              <div>
                <div className="text-2xl font-bold">{totalStats.students}</div>
                <div className="text-sm text-gray-600">Students Reached</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Target className="h-5 w-5 text-orange-500" />
              <div>
                <div className="text-2xl font-bold">{totalStats.sessions}</div>
                <div className="text-sm text-gray-600">Sessions Conducted</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Current Status Overview */}
      {attendanceStatus && attendanceStatus.length > 0 && (
        <ContentCard>
          <CardHeader>
            <CardTitle>Current Status Overview</CardTitle>
            <CardDescription>Real-time status of field staff for {format(new Date(selectedDate), 'MMMM d, yyyy')}</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {attendanceStatus.map((status) => (
                <div key={status.staff_id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <div className="font-medium">{status.staff_name}</div>
                    {getCurrentStatusBadge(status.current_status)}
                  </div>
                  
                  <div className="space-y-1 text-sm text-gray-600">
                    {status.current_school_name && (
                      <div className="flex items-center gap-1">
                        <MapPin className="h-3 w-3" />
                        {status.current_school_name}
                      </div>
                    )}
                    <div>Hours: {status.hours_worked.toFixed(1)}</div>
                    <div>Schools: {status.schools_visited_today}</div>
                    <div>Check-ins: {status.total_check_ins}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </ContentCard>
      )}

      {/* Timesheets List */}
      <ContentCard>
        <CardHeader>
          <CardTitle>Daily Timesheets</CardTitle>
          <CardDescription>
            Detailed timesheets for {format(new Date(selectedDate), 'MMMM d, yyyy')}
          </CardDescription>
        </CardHeader>
        <CardContent>
          {timesheetsLoading ? (
            <div className="text-center py-8">Loading timesheets...</div>
          ) : filteredTimesheets.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              No timesheets found for the selected criteria
            </div>
          ) : (
            <div className="space-y-4">
              {filteredTimesheets.map((timesheet) => (
                <Card key={timesheet.timesheet_id} className="border-l-4 border-l-blue-500">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="font-medium text-lg">{timesheet.staff_name}</div>
                        <div className="text-sm text-gray-600">{timesheet.staff_role}</div>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusBadge(timesheet.timesheet_status)}
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div>
                        <div className="text-sm text-gray-600">Work Hours</div>
                        <div className="font-medium">{timesheet.total_work_hours?.toFixed(1) || 0}h</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Schools Visited</div>
                        <div className="font-medium">{timesheet.total_schools_visited || 0}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Students Reached</div>
                        <div className="font-medium">{timesheet.total_students_reached || 0}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Sessions</div>
                        <div className="font-medium">{timesheet.total_sessions_conducted || 0}</div>
                      </div>
                    </div>

                    {timesheet.key_achievements && (
                      <div className="mb-2">
                        <div className="text-sm font-medium text-green-700">Key Achievements:</div>
                        <div className="text-sm text-gray-700">{timesheet.key_achievements}</div>
                      </div>
                    )}

                    {timesheet.main_challenges && (
                      <div className="mb-2">
                        <div className="text-sm font-medium text-orange-700">Main Challenges:</div>
                        <div className="text-sm text-gray-700">{timesheet.main_challenges}</div>
                      </div>
                    )}

                    {timesheet.submitted_at && (
                      <div className="text-xs text-gray-500 mt-2">
                        Submitted: {new Date(timesheet.submitted_at).toLocaleString()}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </ContentCard>
    </PageLayout>
  );
};

export default AdminTimesheetDashboard;
