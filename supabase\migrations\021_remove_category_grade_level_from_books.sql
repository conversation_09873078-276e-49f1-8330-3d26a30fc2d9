-- Remove category and grade_level from books table and related functions
-- Migration 021: Simplify book management by removing category and grade_level

-- ============================================================================
-- STEP 1: UPDATE FUNCTIONS TO REMOVE CATEGORY AND GRADE_LEVEL
-- ============================================================================

-- Update add_book_with_inventory function to remove category and grade_level parameters
DROP FUNCTION IF EXISTS add_book_with_inventory CASCADE;

CREATE OR REPLACE FUNCTION add_book_with_inventory(
    p_title VARCHAR(500),
    p_author VARCHAR(300) DEFAULT 'iLead Program',
    p_isbn VARCHAR(20) DEFAULT NULL,
    p_publication_year INTEGER DEFAULT NULL,
    p_language book_language DEFAULT 'english',
    p_publisher VARCHAR(300) DEFAULT NULL,
    p_description TEXT DEFAULT NULL,
    p_total_quantity INTEGER DEFAULT 0,
    p_condition book_condition DEFAULT 'good',
    p_storage_location VARCHAR(200) DEFAULT NULL,
    p_cost_per_unit DECIMAL(10,2) DEFAULT NULL,
    p_minimum_threshold INTEGER DEFAULT 10,
    p_notes TEXT DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_book_id UUID;
    v_inventory_id UUID;
    v_user_id UUID;
BEGIN
    -- Get current user ID
    v_user_id := auth.uid();
    
    IF v_user_id IS NULL THEN
        RAISE EXCEPTION 'User must be authenticated';
    END IF;

    -- Insert book record (without category and grade_level)
    INSERT INTO books (
        title, author, isbn, publication_year, language, 
        publisher, description, created_by
    ) VALUES (
        p_title, p_author, p_isbn, p_publication_year, p_language,
        p_publisher, p_description, v_user_id
    ) RETURNING id INTO v_book_id;

    -- Insert inventory record
    INSERT INTO book_inventory (
        book_id, total_quantity, available_quantity, 
        minimum_threshold, condition, storage_location, 
        cost_per_unit, notes, last_updated_by
    ) VALUES (
        v_book_id, p_total_quantity, p_total_quantity,
        p_minimum_threshold, p_condition, p_storage_location,
        p_cost_per_unit, p_notes, v_user_id
    ) RETURNING id INTO v_inventory_id;

    RETURN v_book_id;
END;
$$;

-- Update get_books_with_inventory function to remove category and grade_level
DROP FUNCTION IF EXISTS get_books_with_inventory CASCADE;

CREATE OR REPLACE FUNCTION get_books_with_inventory()
RETURNS TABLE (
    id UUID,
    title VARCHAR(500),
    author VARCHAR(300),
    isbn VARCHAR(20),
    publication_year INTEGER,
    language book_language,
    publisher VARCHAR(300),
    description TEXT,
    total_quantity INTEGER,
    available_quantity INTEGER,
    distributed_quantity INTEGER,
    damaged_quantity INTEGER,
    lost_quantity INTEGER,
    minimum_threshold INTEGER,
    condition book_condition,
    storage_location VARCHAR(200),
    cost_per_unit DECIMAL(10,2),
    inventory_notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        b.id, b.title, b.author, b.isbn, b.publication_year,
        b.language, b.publisher, b.description,
        bi.total_quantity, bi.available_quantity, bi.distributed_quantity,
        bi.damaged_quantity, bi.lost_quantity, bi.minimum_threshold,
        bi.condition, bi.storage_location, bi.cost_per_unit, bi.notes,
        b.created_at, b.updated_at
    FROM books b
    LEFT JOIN book_inventory bi ON b.id = bi.book_id
    ORDER BY b.title;
END;
$$;

-- Update get_book_inventory function to remove grade_level and category references
DROP FUNCTION IF EXISTS get_book_inventory CASCADE;

CREATE OR REPLACE FUNCTION get_book_inventory()
RETURNS TABLE (
    id UUID,
    book_title TEXT,
    quantity_available INTEGER,
    language TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi.id,
        b.title::TEXT as book_title,
        bi.available_quantity as quantity_available,
        COALESCE(b.language::TEXT, '') as language
    FROM book_inventory bi
    LEFT JOIN books b ON bi.book_id = b.id
    WHERE bi.available_quantity > 0
    ORDER BY b.title;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION add_book_with_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION get_books_with_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION get_book_inventory TO authenticated;

-- ============================================================================
-- STEP 2: DROP INDEXES ON CATEGORY AND GRADE_LEVEL
-- ============================================================================

DROP INDEX IF EXISTS idx_books_category;
DROP INDEX IF EXISTS idx_books_grade_level;

-- ============================================================================
-- STEP 3: REMOVE COLUMNS FROM BOOKS TABLE
-- ============================================================================

-- Remove category and grade_level columns from books table
ALTER TABLE books DROP COLUMN IF EXISTS category CASCADE;
ALTER TABLE books DROP COLUMN IF EXISTS grade_level CASCADE;

-- ============================================================================
-- STEP 4: DROP UNUSED ENUM TYPES (OPTIONAL - ONLY IF NOT USED ELSEWHERE)
-- ============================================================================

-- Note: We're keeping the enum types in case they're used elsewhere
-- If you want to remove them completely, uncomment the lines below:
-- DROP TYPE IF EXISTS book_category CASCADE;
-- DROP TYPE IF EXISTS grade_level CASCADE;
