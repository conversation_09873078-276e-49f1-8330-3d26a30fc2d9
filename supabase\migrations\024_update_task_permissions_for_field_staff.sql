-- Update task creation permissions to allow field staff to create tasks for themselves
-- Migration 024: Allow field staff to create tasks automatically assigned to themselves

-- Update the create_task function to allow field staff to create tasks for themselves
CREATE OR REPLACE FUNCTION create_task(
    p_title VARCHAR,
    p_description TEXT DEFAULT NULL,
    p_priority task_priority DEFAULT 'medium',
    p_due_date TIMESTAMP WITH TIME ZONE DEFAULT NULL,
    p_assigned_to UUID DEFAULT NULL,
    p_school_id UUID DEFAULT NULL
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    task_id UUID;
    user_role TEXT;
    final_assigned_to UUID;
BEGIN
    -- Get the current user's role
    SELECT role INTO user_role
    FROM profiles 
    WHERE id = auth.uid();

    -- Check if user has permission to create tasks
    IF user_role NOT IN ('admin', 'program_officer', 'field_staff') THEN
        RAISE EXCEPTION 'Insufficient permissions to create tasks';
    END IF;

    -- Determine final assignment based on user role and permissions
    IF user_role = 'field_staff' THEN
        -- Field staff can only create tasks assigned to themselves
        final_assigned_to := auth.uid();
        
        -- Validate that field staff is not trying to assign to someone else
        IF p_assigned_to IS NOT NULL AND p_assigned_to != auth.uid() THEN
            RAISE EXCEPTION 'Field staff can only create tasks assigned to themselves';
        END IF;
    ELSE
        -- Admin and program officers can assign to anyone (including themselves)
        final_assigned_to := p_assigned_to;
    END IF;

    -- Insert new task
    INSERT INTO tasks (title, description, priority, due_date, assigned_to, created_by, school_id)
    VALUES (p_title, p_description, p_priority, p_due_date, final_assigned_to, auth.uid(), p_school_id)
    RETURNING id INTO task_id;

    RETURN task_id;
END;
$$;

-- Update the get_tasks_optimized function to ensure field staff can see their own tasks
CREATE OR REPLACE FUNCTION get_tasks_optimized(
    p_user_id UUID DEFAULT NULL,
    p_status_filter task_status DEFAULT NULL,
    p_assigned_filter UUID DEFAULT NULL,
    p_include_comments BOOLEAN DEFAULT FALSE,
    p_limit INTEGER DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    description TEXT,
    priority task_priority,
    status task_status,
    due_date TIMESTAMP WITH TIME ZONE,
    assigned_to UUID,
    assigned_to_name VARCHAR,
    created_by UUID,
    created_by_name VARCHAR,
    school_id UUID,
    school_name VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    comment_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    user_role TEXT;
BEGIN
    -- Get the current user's role
    SELECT role INTO user_role
    FROM profiles 
    WHERE id = auth.uid();

    -- Check permissions
    IF user_role NOT IN ('admin', 'program_officer', 'field_staff') THEN
        RAISE EXCEPTION 'Insufficient permissions to view tasks';
    END IF;

    RETURN QUERY
    SELECT 
        t.id,
        t.title,
        t.description,
        t.priority,
        t.status,
        t.due_date,
        t.assigned_to,
        assigned_profile.name as assigned_to_name,
        t.created_by,
        creator_profile.name as created_by_name,
        t.school_id,
        s.name as school_name,
        t.created_at,
        t.updated_at,
        CASE 
            WHEN p_include_comments THEN 
                COALESCE((
                    SELECT COUNT(*) 
                    FROM task_comments tc 
                    WHERE tc.task_id = t.id
                ), 0)
            ELSE 0
        END as comment_count
    FROM tasks t
    LEFT JOIN profiles assigned_profile ON t.assigned_to = assigned_profile.id
    LEFT JOIN profiles creator_profile ON t.created_by = creator_profile.id
    LEFT JOIN schools s ON t.school_id = s.id
    WHERE 
        -- Role-based filtering
        CASE 
            WHEN user_role = 'admin' THEN TRUE  -- Admins see all tasks
            WHEN user_role = 'program_officer' THEN TRUE  -- Program officers see all tasks
            WHEN user_role = 'field_staff' THEN 
                (t.assigned_to = auth.uid() OR t.created_by = auth.uid())  -- Field staff see only their tasks
            ELSE FALSE
        END
        -- Additional filters
        AND (p_user_id IS NULL OR t.assigned_to = p_user_id OR t.created_by = p_user_id)
        AND (p_status_filter IS NULL OR t.status = p_status_filter)
        AND (p_assigned_filter IS NULL OR t.assigned_to = p_assigned_filter OR t.created_by = p_assigned_filter)
    ORDER BY 
        CASE t.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        t.due_date ASC NULLS LAST,
        t.created_at DESC
    LIMIT p_limit;
END;
$$;

-- Update RLS policies for tasks table to allow field staff to create and view their own tasks
DROP POLICY IF EXISTS "Users can view tasks they are assigned to or created" ON tasks;
DROP POLICY IF EXISTS "Admins and program officers can create tasks" ON tasks;
DROP POLICY IF EXISTS "Users can update tasks they are assigned to or created" ON tasks;

-- Create new RLS policies
CREATE POLICY "Users can view their own tasks" ON tasks
    FOR SELECT USING (
        assigned_to = auth.uid() OR 
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

CREATE POLICY "Authorized users can create tasks" ON tasks
    FOR INSERT WITH CHECK (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        ) AND
        -- Field staff can only create tasks assigned to themselves
        (
            (SELECT role FROM profiles WHERE id = auth.uid()) IN ('admin', 'program_officer') OR
            (
                (SELECT role FROM profiles WHERE id = auth.uid()) = 'field_staff' AND
                assigned_to = auth.uid()
            )
        )
    );

CREATE POLICY "Users can update their own tasks" ON tasks
    FOR UPDATE USING (
        assigned_to = auth.uid() OR 
        created_by = auth.uid() OR
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Add comment explaining the changes
COMMENT ON FUNCTION create_task IS 'Updated to allow field staff to create tasks automatically assigned to themselves, while maintaining admin/program officer ability to assign tasks to others';
COMMENT ON FUNCTION get_tasks_optimized IS 'Updated to include role-based filtering for field staff to see only their own tasks';
