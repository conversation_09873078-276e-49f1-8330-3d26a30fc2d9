import { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import {
  Bell,
  Settings,
  User,
  LogOut,
  Menu,
  Search,
  HelpCircle,
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useAuth } from '@/hooks/useAuth';

interface HeaderProps {
  onMenuToggle?: () => void;
  onViewChange?: (view: string) => void;
}

const Header = ({ onMenuToggle, onViewChange }: HeaderProps) => {
  const { profile, user, signOut } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrator';
      case 'program_officer':
        return 'Program Officer';
      case 'field_staff':
        return 'Field Staff';
      default:
        return 'User';
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-purple-100 text-purple-800';
      case 'program_officer':
        return 'bg-blue-100 text-blue-800';
      case 'field_staff':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm">
      <div className="flex items-center justify-between px-4 py-3">
        {/* Left Section - Mobile Menu & Logo */}
        <div className="flex items-center space-x-4 flex-shrink-0">
          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="icon"
            className="lg:hidden"
            onClick={onMenuToggle}
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Mobile Logo */}
          <div className="md:hidden">
            <img
              src="/ilead-logo.svg"
              alt="iLEAD Uganda Logo"
              className="h-11 w-auto"
              onError={(e) => {
                // Fallback to text if logo fails to load
                e.currentTarget.style.display = 'none';
                const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                if (fallback) fallback.style.display = 'block';
              }}
            />
            {/* Mobile Fallback text (hidden by default) */}
            <div className="hidden">
              <span className="text-lg font-semibold text-gray-900">iLEAD</span>
            </div>
          </div>

          {/* App Logo - 10% larger as per user preference */}
          <div className="hidden md:flex items-center mr-10">
            <div className="flex flex-col items-center">
              <img
                src="/ilead-logo.svg"
                alt="iLEAD Uganda Logo"
                className="h-14 w-auto" // Increased from h-12 to h-14 (10% larger)
                onError={(e) => {
                  // Fallback to text if logo fails to load
                  e.currentTarget.style.display = 'none';
                  const fallback = e.currentTarget.nextElementSibling as HTMLElement;
                  if (fallback) fallback.style.display = 'block';
                }}
              />
              {/* Fallback text (hidden by default) */}
              <div className="hidden">
                <h1 className="text-xl font-semibold text-gray-900">
                  iLEAD Field Tracker
                </h1>
              </div>
              {/* Platform subtitle */}
              <div className="text-sm text-gray-600 mt-1 text-center leading-tight">
                Field Management & Monitoring Platform
              </div>
            </div>
          </div>
        </div>

          {/* Search Bar - Expanded spacing as per user preference */}
          <div className="hidden lg:block relative flex-1 max-w-3xl mx-6">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder="Search schools, tasks, distributions..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-full bg-gray-50 border-gray-200 focus:bg-white focus:border-ilead-green"
            />
          </div>

        {/* Right Section - Notifications & User Menu */}
        <div className="flex items-center space-x-3 flex-shrink-0">
          {/* Search Button for Mobile */}
          <Button variant="ghost" size="icon" className="lg:hidden h-10 w-10 touch-manipulation">
            <Search className="h-5 w-5" />
          </Button>

          {/* Notifications */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="icon" className="relative h-10 w-10 touch-manipulation">
                <Bell className="h-5 w-5" />
                <Badge className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs bg-red-500">
                  3
                </Badge>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-80">
              <DropdownMenuLabel>Notifications</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="flex flex-col items-start p-3">
                <div className="font-medium">New task assigned</div>
                <div className="text-sm text-gray-500">
                  You have been assigned a new book distribution task
                </div>
                <div className="text-xs text-gray-400 mt-1">2 hours ago</div>
              </DropdownMenuItem>
              <DropdownMenuItem className="flex flex-col items-start p-3">
                <div className="font-medium">Distribution completed</div>
                <div className="text-sm text-gray-500">
                  Kampala Primary School distribution marked as complete
                </div>
                <div className="text-xs text-gray-400 mt-1">1 day ago</div>
              </DropdownMenuItem>
              <DropdownMenuItem className="flex flex-col items-start p-3">
                <div className="font-medium">Report due soon</div>
                <div className="text-sm text-gray-500">
                  Monthly field report is due in 2 days
                </div>
                <div className="text-xs text-gray-400 mt-1">2 days ago</div>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem className="text-center text-sm text-blue-600">
                View all notifications
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          {/* Help */}
          <Button
            variant="ghost"
            size="icon"
            className="h-10 w-10 touch-manipulation"
            onClick={() => onViewChange?.('help')}
          >
            <HelpCircle className="h-5 w-5" />
          </Button>

          {/* User Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="flex items-center space-x-2 px-3">
                <Avatar className="h-8 w-8">
                  <AvatarImage src="" />
                  <AvatarFallback className="bg-ilead-green text-white">
                    {profile?.name ? getInitials(profile.name) : 'U'}
                  </AvatarFallback>
                </Avatar>
                <div className="hidden md:block text-left">
                  <div className="text-sm font-medium">{profile?.name || 'User'}</div>
                  <div className="text-xs text-gray-500">
                    {profile?.role ? getRoleDisplayName(profile.role) : 'User'}
                  </div>
                </div>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-56">
              <DropdownMenuLabel>
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium">{profile?.name || 'User'}</p>
                  <p className="text-xs text-gray-500">{user?.email || 'No email'}</p>
                  <Badge className={`text-xs w-fit ${getRoleBadgeColor(profile?.role || '')}`}>
                    {profile?.role ? getRoleDisplayName(profile.role) : 'User'}
                  </Badge>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />

              {/* Show all menu items for admin and program officers */}
              {profile?.role !== 'field_staff' && (
                <>
                  <DropdownMenuItem onClick={() => onViewChange?.('settings-profile')}>
                    <User className="mr-2 h-4 w-4" />
                    Profile Settings
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onViewChange?.('settings-notifications')}>
                    <Bell className="mr-2 h-4 w-4" />
                    Notifications
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onViewChange?.('settings-preferences')}>
                    <Settings className="mr-2 h-4 w-4" />
                    Preferences
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                </>
              )}

              {/* Help & Support - visible to all users */}
              <DropdownMenuItem onClick={() => onViewChange?.('help')}>
                <HelpCircle className="mr-2 h-4 w-4" />
                Help & Support
              </DropdownMenuItem>
              <DropdownMenuSeparator />

              {/* Logout - visible to all users */}
              <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
                <LogOut className="mr-2 h-4 w-4" />
                Sign Out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
};

export default Header;