-- Remove Redundant Book Management Functions
-- Migration 020: Clean up unused database functions to reduce backend complexity
-- This migration removes functions that are not called by the frontend

-- ============================================================================
-- REMOVE UNUSED BOOK MANAGEMENT FUNCTIONS
-- ============================================================================

-- 1. Remove create_book_distribution (not used - frontend uses add_book_distribution)
DROP FUNCTION IF EXISTS create_book_distribution(UUID, UUID, INTEGER, DATE, VARCHAR, VARCHAR, TEXT) CASCADE;

-- 2. Remove get_distribution_details (not used by frontend)
DROP FUNCTION IF EXISTS get_distribution_details(UUID) CASCADE;

-- 3. Remove legacy get_book_inventory function (not used - frontend uses get_books_with_inventory)
-- Note: Keep the newer version that's actually used by distribution system
DROP FUNCTION IF EXISTS get_book_inventory() CASCADE;

-- 4. Remove legacy add_book_inventory function (not used - frontend uses add_book_with_inventory)
DROP FUNCTION IF EXISTS add_book_inventory(VARCHAR, INTEGER, VARCHAR, VARCHAR, VARCHAR, VARCHAR, VARCHAR, INTEGER, DECIMAL, TEXT) CASCADE;

-- 5. Remove update_inventory_quantity function (not used - frontend uses update_book_inventory)
DROP FUNCTION IF EXISTS update_inventory_quantity(UUID, INTEGER, VARCHAR) CASCADE;

-- ============================================================================
-- RECREATE ONLY THE get_book_inventory FUNCTION THAT'S ACTUALLY USED
-- ============================================================================

-- This version is used by the distribution system and should be kept
CREATE OR REPLACE FUNCTION get_book_inventory()
RETURNS TABLE (
    id UUID,
    book_title TEXT,
    quantity_available INTEGER,
    language TEXT,
    grade_level TEXT,
    subject TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi.id,
        b.title::TEXT as book_title,
        bi.available_quantity as quantity_available,
        COALESCE(b.language::TEXT, '') as language,
        COALESCE(b.grade_level::TEXT, '') as grade_level,
        COALESCE(b.category::TEXT, '') as subject
    FROM book_inventory bi
    LEFT JOIN books b ON bi.book_id = b.id
    WHERE bi.available_quantity > 0
    ORDER BY b.title;
END;
$$;

-- ============================================================================
-- UPDATE PERMISSIONS FOR REMAINING FUNCTIONS
-- ============================================================================

-- Grant execute permissions only for functions that are actually used
GRANT EXECUTE ON FUNCTION add_book_with_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION get_books_with_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION update_book_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION get_book_inventory TO authenticated;
GRANT EXECUTE ON FUNCTION get_book_distributions TO authenticated;
GRANT EXECUTE ON FUNCTION add_book_distribution TO authenticated;

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- List all remaining book-related functions for verification
DO $$
BEGIN
    RAISE NOTICE 'Remaining book management functions:';
    RAISE NOTICE '1. add_book_with_inventory - Used by frontend for adding books';
    RAISE NOTICE '2. get_books_with_inventory - Used by frontend for fetching books';
    RAISE NOTICE '3. update_book_inventory - Used by frontend for inventory updates';
    RAISE NOTICE '4. get_book_inventory - Used by distribution system';
    RAISE NOTICE '5. get_book_distributions - Used by frontend for distribution reports';
    RAISE NOTICE '6. add_book_distribution - Used by frontend for creating distributions';
    RAISE NOTICE '';
    RAISE NOTICE 'Removed redundant functions:';
    RAISE NOTICE '- create_book_distribution (unused)';
    RAISE NOTICE '- get_distribution_details (unused)';
    RAISE NOTICE '- legacy add_book_inventory (unused)';
    RAISE NOTICE '- update_inventory_quantity (unused)';
END $$;
