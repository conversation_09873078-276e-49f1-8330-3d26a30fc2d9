/**
 * useOfflineQueue - Focused hook for managing offline data queue
 * Handles adding, removing, and querying offline data items
 */

import { useCallback, useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  OfflineData,
  SyncStats
} from '@/types/offlineSync.types';
import { 
  loadOfflineData, 
  addToOfflineQueue as addToQueue,
  removeFromOfflineQueue as removeFromQueue
} from '@/utils/offlineStorage';
import { getAllConflicts } from '@/utils/conflictResolution';

export const useOfflineQueue = () => {
  const [queueStats, setQueueStats] = useState<SyncStats>({
    totalPending: 0,
    highPriority: 0,
    failed: 0,
    conflicts: 0,
    oldestItem: null,
  });

  // Update queue statistics
  const updateQueueStats = useCallback(() => {
    try {
      const offlineData = loadOfflineData();
      const conflictData = getAllConflicts();

      setQueueStats({
        totalPending: offlineData.length,
        highPriority: offlineData.filter(item => 
          item.priority === 'HIGH' || item.priority === 'CRITICAL'
        ).length,
        failed: offlineData.filter(item => 
          item.retryCount >= item.maxRetries
        ).length,
        conflicts: conflictData.length,
        oldestItem: offlineData.length > 0 
          ? Math.min(...offlineData.map(item => item.timestamp)) 
          : null,
      });
    } catch (error) {
      console.error('Failed to update queue stats:', error);
    }
  }, []);

  // Add item to offline queue
  const addToOfflineQueue = useCallback((
    type: OfflineData['type'],
    data: Record<string, unknown>,
    priority: OfflineData['priority'] = 'MEDIUM'
  ): string => {
    try {
      const id = addToQueue(type, data, priority);
      updateQueueStats();
      toast.success('Data saved offline');
      return id;
    } catch (error) {
      console.error('Failed to add to offline queue:', error);
      toast.error('Failed to save data offline');
      throw error;
    }
  }, [updateQueueStats]);

  // Remove item from offline queue
  const removeFromOfflineQueue = useCallback((id: string): boolean => {
    try {
      const success = removeFromQueue(id);
      if (success) {
        updateQueueStats();
      }
      return success;
    } catch (error) {
      console.error('Failed to remove from offline queue:', error);
      return false;
    }
  }, [updateQueueStats]);

  // Get queue data
  const getQueueData = useCallback((): OfflineData[] => {
    return loadOfflineData();
  }, []);

  // Get high priority items
  const getHighPriorityItems = useCallback((): OfflineData[] => {
    const data = loadOfflineData();
    return data.filter(item => 
      item.priority === 'HIGH' || item.priority === 'CRITICAL'
    );
  }, []);

  // Get failed items
  const getFailedItems = useCallback((): OfflineData[] => {
    const data = loadOfflineData();
    return data.filter(item => item.retryCount >= item.maxRetries);
  }, []);

  // Initialize stats
  useEffect(() => {
    updateQueueStats();
  }, [updateQueueStats]);

  return {
    queueStats,
    addToOfflineQueue,
    removeFromOfflineQueue,
    getQueueData,
    getHighPriorityItems,
    getFailedItems,
    updateQueueStats,
  };
};
