import { supabase } from '@/integrations/supabase/client';
import {
  createAbsenceAlert,
  createLowAttendanceWarning,
  createSessionReminder
} from '@/hooks/attendance/useAttendanceNotifications';

// Type definitions for student and attendance records
interface StudentRecord {
  id: string;
  first_name: string;
  last_name: string;
  school_id: string;
  guardian_contact?: string;
}

interface AttendanceRecord {
  student_id: string;
  attendance_status: string;
  student: StudentRecord;
}

// Trigger absence alert when student is marked absent
export const triggerAbsenceAlert = async (
  studentId: string,
  sessionId: string,
  schoolId: string
) => {
  try {
    // Get student info to check if guardian contact exists
    const { data: student } = await supabase
      .from('students')
      .select('guardian_contact, guardian_name')
      .eq('id', studentId)
      .single();

    if (!student?.guardian_contact) {
      console.log('No guardian contact found for student, skipping absence alert');
      return;
    }

    // Create absence alert notification
    const notificationData = await createAbsenceAlert(
      studentId,
      sessionId,
      schoolId,
      student.guardian_contact
    );

    if (notificationData) {
      const { error } = await supabase
        .from('attendance_notifications')
        .insert(notificationData);

      if (error) {
        console.error('Error creating absence alert:', error);
      } else {
        console.log('Absence alert created successfully');
      }
    }
  } catch (error) {
    console.error('Error triggering absence alert:', error);
  }
};

// Trigger late arrival alert when student is marked late
export const triggerLateAlert = async (
  studentId: string,
  sessionId: string,
  schoolId: string,
  lateMinutes: number
) => {
  try {
    // Only send alert if student is more than 15 minutes late
    if (lateMinutes < 15) return;

    const { data: student } = await supabase
      .from('students')
      .select('first_name, last_name, guardian_contact')
      .eq('id', studentId)
      .single();

    const { data: session } = await supabase
      .from('attendance_sessions')
      .select('session_name, session_date')
      .eq('id', sessionId)
      .single();

    if (!student?.guardian_contact || !session) return;

    const messageContent = `Your child ${student.first_name} ${student.last_name} arrived ${lateMinutes} minutes late to ${session.session_name} on ${new Date(session.session_date).toLocaleDateString()}.`;

    const notificationData = {
      notification_type: 'late_alert' as const,
      recipient_type: 'parent' as const,
      recipient_contact: student.guardian_contact,
      student_id: studentId,
      session_id: sessionId,
      school_id: schoolId,
      message_title: 'Student Late Arrival',
      message_content: messageContent,
      delivery_method: 'sms' as const,
      priority_level: 'low' as const,
      auto_generated: true,
      notification_data: {
        student_name: `${student.first_name} ${student.last_name}`,
        session_name: session.session_name,
        session_date: session.session_date,
        late_minutes: lateMinutes,
      },
    };

    const { error } = await supabase
      .from('attendance_notifications')
      .insert(notificationData);

    if (error) {
      console.error('Error creating late alert:', error);
    } else {
      console.log('Late alert created successfully');
    }
  } catch (error) {
    console.error('Error triggering late alert:', error);
  }
};

// Check for low attendance and trigger warnings
export const checkAndTriggerLowAttendanceWarnings = async (schoolId?: string) => {
  try {
    // Calculate attendance rates for all students in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    let query = supabase
      .from('student_attendance')
      .select(`
        student_id,
        attendance_status,
        student:students(
          first_name,
          last_name,
          guardian_contact,
          school_id
        )
      `)
      .gte('recorded_at', thirtyDaysAgo.toISOString());

    if (schoolId) {
      query = query.eq('school_id', schoolId);
    }

    const { data: attendanceRecords, error } = await query;

    if (error) {
      console.error('Error fetching attendance records:', error);
      return;
    }

    // Group by student and calculate attendance rates
    const studentStats = new Map<string, {
      student: StudentRecord;
      total: number;
      attended: number;
      rate: number;
    }>();

    attendanceRecords?.forEach(record => {
      const studentId = record.student_id;
      if (!studentStats.has(studentId)) {
        studentStats.set(studentId, {
          student: record.student,
          total: 0,
          attended: 0,
          rate: 0,
        });
      }

      const stats = studentStats.get(studentId)!;
      stats.total++;
      if (['present', 'late'].includes(record.attendance_status)) {
        stats.attended++;
      }
    });

    // Calculate rates and identify students with low attendance
    const lowAttendanceStudents: Array<{
      studentId: string;
      student: StudentRecord;
      rate: number;
    }> = [];

    studentStats.forEach((stats, studentId) => {
      stats.rate = stats.total > 0 ? (stats.attended / stats.total) * 100 : 0;
      
      // Flag students with attendance rate below 75%
      if (stats.rate < 75 && stats.total >= 5) { // At least 5 sessions to be meaningful
        lowAttendanceStudents.push({
          studentId,
          student: stats.student,
          rate: stats.rate,
        });
      }
    });

    // Create notifications for low attendance students
    for (const { studentId, student, rate } of lowAttendanceStudents) {
      if (!student?.guardian_contact) continue;

      // Check if we've already sent a low attendance warning in the last 7 days
      const sevenDaysAgo = new Date();
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

      const { data: recentNotifications } = await supabase
        .from('attendance_notifications')
        .select('id')
        .eq('student_id', studentId)
        .eq('notification_type', 'low_attendance')
        .gte('created_at', sevenDaysAgo.toISOString());

      if (recentNotifications && recentNotifications.length > 0) {
        continue; // Skip if already notified recently
      }

      const notificationData = await createLowAttendanceWarning(
        studentId,
        student.school_id,
        rate,
        student.guardian_contact
      );

      if (notificationData) {
        const { error } = await supabase
          .from('attendance_notifications')
          .insert(notificationData);

        if (error) {
          console.error('Error creating low attendance warning:', error);
        } else {
          console.log(`Low attendance warning created for student ${studentId}`);
        }
      }
    }

    console.log(`Processed ${lowAttendanceStudents.length} low attendance warnings`);
  } catch (error) {
    console.error('Error checking low attendance:', error);
  }
};

// Trigger session reminders for upcoming sessions
export const triggerSessionReminders = async (schoolId?: string) => {
  try {
    // Get sessions scheduled for tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const tomorrowStr = tomorrow.toISOString().split('T')[0];

    let query = supabase
      .from('attendance_sessions')
      .select(`
        id,
        session_name,
        session_date,
        start_time,
        location,
        facilitator_id,
        school_id,
        school:schools(name)
      `)
      .eq('session_date', tomorrowStr)
      .eq('is_completed', false);

    if (schoolId) {
      query = query.eq('school_id', schoolId);
    }

    const { data: sessions, error } = await query;

    if (error) {
      console.error('Error fetching upcoming sessions:', error);
      return;
    }

    // Create reminders for each session
    for (const session of sessions || []) {
      // Check if reminder already sent
      const { data: existingReminders } = await supabase
        .from('attendance_notifications')
        .select('id')
        .eq('session_id', session.id)
        .eq('notification_type', 'session_reminder');

      if (existingReminders && existingReminders.length > 0) {
        continue; // Skip if reminder already sent
      }

      // Create reminder for facilitator
      if (session.facilitator_id) {
        const notificationData = await createSessionReminder(
          session.id,
          session.school_id,
          'teacher'
        );

        if (notificationData) {
          const { error } = await supabase
            .from('attendance_notifications')
            .insert({
              ...notificationData,
              recipient_contact: null, // Will be sent as in-app notification
            });

          if (error) {
            console.error('Error creating session reminder:', error);
          } else {
            console.log(`Session reminder created for session ${session.id}`);
          }
        }
      }

      // Create reminder for admin/program officers
      const { data: adminUsers } = await supabase
        .from('profiles')
        .select('id')
        .in('role', ['admin', 'program_officer']);

      for (const admin of adminUsers || []) {
        const notificationData = await createSessionReminder(
          session.id,
          session.school_id,
          'admin'
        );

        if (notificationData) {
          const { error } = await supabase
            .from('attendance_notifications')
            .insert({
              ...notificationData,
              recipient_contact: null,
              created_by: admin.id,
            });

          if (error) {
            console.error('Error creating admin session reminder:', error);
          }
        }
      }
    }

    console.log(`Processed ${sessions?.length || 0} session reminders`);
  } catch (error) {
    console.error('Error triggering session reminders:', error);
  }
};

// Trigger check-in confirmation
export const triggerCheckInConfirmation = async (
  staffId: string,
  schoolId: string,
  sessionId?: string
) => {
  try {
    const { data: school } = await supabase
      .from('schools')
      .select('name')
      .eq('id', schoolId)
      .single();

    const { data: session } = sessionId ? await supabase
      .from('attendance_sessions')
      .select('session_name')
      .eq('id', sessionId)
      .single() : null;

    const messageContent = `Successfully checked in at ${school?.name || 'school'} at ${new Date().toLocaleTimeString()}${session ? `. Session: ${session.session_name}` : ''}`;

    const notificationData = {
      notification_type: 'check_in_confirmation' as const,
      recipient_type: 'teacher' as const,
      session_id: sessionId,
      school_id: schoolId,
      message_title: 'Check-in Confirmed',
      message_content: messageContent,
      delivery_method: 'in_app' as const,
      priority_level: 'low' as const,
      auto_generated: true,
      created_by: staffId,
      notification_data: {
        school_name: school?.name,
        session_name: session?.session_name,
        check_in_time: new Date().toISOString(),
      },
    };

    const { error } = await supabase
      .from('attendance_notifications')
      .insert(notificationData);

    if (error) {
      console.error('Error creating check-in confirmation:', error);
    } else {
      console.log('Check-in confirmation created successfully');
    }
  } catch (error) {
    console.error('Error triggering check-in confirmation:', error);
  }
};

// Trigger check-out confirmation
export const triggerCheckOutConfirmation = async (
  staffId: string,
  schoolId: string,
  duration: number,
  sessionId?: string
) => {
  try {
    const { data: school } = await supabase
      .from('schools')
      .select('name')
      .eq('id', schoolId)
      .single();

    const hours = Math.floor(duration / 60);
    const minutes = duration % 60;
    const durationStr = hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`;

    const messageContent = `Successfully checked out from ${school?.name || 'school'} at ${new Date().toLocaleTimeString()}. Total duration: ${durationStr}`;

    const notificationData = {
      notification_type: 'check_out_confirmation' as const,
      recipient_type: 'teacher' as const,
      session_id: sessionId,
      school_id: schoolId,
      message_title: 'Check-out Confirmed',
      message_content: messageContent,
      delivery_method: 'in_app' as const,
      priority_level: 'low' as const,
      auto_generated: true,
      created_by: staffId,
      notification_data: {
        school_name: school?.name,
        check_out_time: new Date().toISOString(),
        duration_minutes: duration,
        duration_formatted: durationStr,
      },
    };

    const { error } = await supabase
      .from('attendance_notifications')
      .insert(notificationData);

    if (error) {
      console.error('Error creating check-out confirmation:', error);
    } else {
      console.log('Check-out confirmation created successfully');
    }
  } catch (error) {
    console.error('Error triggering check-out confirmation:', error);
  }
};

// Generate weekly attendance summary
export const generateWeeklyAttendanceSummary = async (schoolId?: string) => {
  try {
    // Get attendance data for the past week
    const oneWeekAgo = new Date();
    oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

    let query = supabase
      .from('student_attendance')
      .select(`
        student_id,
        attendance_status,
        student:students(
          first_name,
          last_name,
          guardian_contact,
          school_id
        )
      `)
      .gte('recorded_at', oneWeekAgo.toISOString());

    if (schoolId) {
      query = query.eq('school_id', schoolId);
    }

    const { data: attendanceRecords, error } = await query;

    if (error) {
      console.error('Error fetching weekly attendance:', error);
      return;
    }

    // Group by student and calculate weekly stats
    const studentWeeklyStats = new Map<string, {
      student: StudentRecord;
      total: number;
      attended: number;
      rate: number;
    }>();

    attendanceRecords?.forEach(record => {
      const studentId = record.student_id;
      if (!studentWeeklyStats.has(studentId)) {
        studentWeeklyStats.set(studentId, {
          student: record.student,
          total: 0,
          attended: 0,
          rate: 0,
        });
      }

      const stats = studentWeeklyStats.get(studentId)!;
      stats.total++;
      if (['present', 'late'].includes(record.attendance_status)) {
        stats.attended++;
      }
    });

    // Create weekly summary notifications for parents
    for (const [studentId, stats] of studentWeeklyStats) {
      if (!stats.student?.guardian_contact || stats.total === 0) continue;

      stats.rate = (stats.attended / stats.total) * 100;

      const messageContent = `Weekly attendance summary for ${stats.student.first_name} ${stats.student.last_name}: ${stats.attended}/${stats.total} sessions attended (${stats.rate.toFixed(1)}%)`;

      const notificationData = {
        notification_type: 'attendance_summary' as const,
        recipient_type: 'parent' as const,
        recipient_contact: stats.student.guardian_contact,
        student_id: studentId,
        school_id: stats.student.school_id,
        message_title: 'Weekly Attendance Summary',
        message_content: messageContent,
        delivery_method: 'email' as const,
        priority_level: 'normal' as const,
        auto_generated: true,
        notification_data: {
          student_name: `${stats.student.first_name} ${stats.student.last_name}`,
          sessions_attended: stats.attended,
          total_sessions: stats.total,
          attendance_rate: stats.rate,
          week_start: oneWeekAgo.toISOString().split('T')[0],
          week_end: new Date().toISOString().split('T')[0],
        },
      };

      const { error } = await supabase
        .from('attendance_notifications')
        .insert(notificationData);

      if (error) {
        console.error('Error creating weekly summary:', error);
      } else {
        console.log(`Weekly summary created for student ${studentId}`);
      }
    }

    console.log(`Generated ${studentWeeklyStats.size} weekly attendance summaries`);
  } catch (error) {
    console.error('Error generating weekly attendance summary:', error);
  }
};
