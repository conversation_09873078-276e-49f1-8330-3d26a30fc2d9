-- Fix get_distribution_details function type mismatch
-- This migration fixes the type mismatch error in get_distribution_details function
-- The issue is that school_name is defined as VARCHAR but schools.name is TEXT

-- Drop and recreate the function with correct types
DROP FUNCTION IF EXISTS get_distribution_details(UUID);

CREATE OR REPLACE FUNCTION get_distribution_details(p_distribution_id UUID)
RETURNS TABLE (
    id UUID,
    school_id UUID,
    school_name TEXT,
    book_title TEXT,
    quantity INTEGER,
    supervisor_id UUID,
    supervisor_name TEXT,
    delivery_date TEXT,
    notes TEXT,
    status TEXT,
    photos JSON
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bd.id,
        bd.school_id,
        s.name as school_name,
        bi.book_title::TEXT,
        bd.quantity,
        bd.supervisor_id,
        p.name as supervisor_name,
        bd.delivery_date::TEXT,
        COALESCE(bd.notes, '') as notes,
        bd.status::TEXT,
        COALESCE(
            (SELECT json_agg(
                json_build_object(
                    'id', dp.id,
                    'photo_url', dp.photo_url,
                    'caption', dp.caption,
                    'uploaded_by', dp.uploaded_by,
                    'created_at', dp.created_at
                )
            ) FROM distribution_photos dp WHERE dp.distribution_id = bd.id),
            '[]'::json
        ) as photos
    FROM book_distributions bd
    LEFT JOIN schools s ON bd.school_id = s.id
    LEFT JOIN book_inventory bi ON bd.inventory_id = bi.id  
    LEFT JOIN profiles p ON bd.supervisor_id = p.id
    WHERE bd.id = p_distribution_id;
END;
$$;

-- Also fix the get_book_distributions function for consistency
DROP FUNCTION IF EXISTS get_book_distributions();

CREATE OR REPLACE FUNCTION get_book_distributions()
RETURNS TABLE (
    id UUID,
    school_id UUID,
    school_name TEXT,
    book_title TEXT,
    quantity INTEGER,
    supervisor_id UUID,
    supervisor_name TEXT,
    delivery_date TEXT,
    notes TEXT,
    status TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        bd.id,
        bd.school_id,
        s.name as school_name,
        bi.book_title::TEXT,
        bd.quantity,
        bd.supervisor_id,
        p.name as supervisor_name,
        bd.delivery_date::TEXT,
        COALESCE(bd.notes, '') as notes,
        bd.status::TEXT
    FROM book_distributions bd
    LEFT JOIN schools s ON bd.school_id = s.id
    LEFT JOIN book_inventory bi ON bd.inventory_id = bi.id  
    LEFT JOIN profiles p ON bd.supervisor_id = p.id
    ORDER BY bd.delivery_date DESC;
END;
$$;

-- Fix get_book_inventory function for consistency
DROP FUNCTION IF EXISTS get_book_inventory();

CREATE OR REPLACE FUNCTION get_book_inventory()
RETURNS TABLE (
    id UUID,
    book_title TEXT,
    quantity_available INTEGER,
    language TEXT,
    grade_level TEXT,
    subject TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT
        bi.id,
        bi.book_title::TEXT,
        bi.quantity_available,
        bi.language::TEXT,
        bi.grade_level::TEXT,
        bi.subject::TEXT
    FROM book_inventory bi
    WHERE bi.quantity_available > 0
    ORDER BY bi.book_title;
END;
$$;

-- Fix add_book_distribution function to support planned distributions
DROP FUNCTION IF EXISTS add_book_distribution(UUID, UUID, INTEGER, UUID, TEXT, VARCHAR);

CREATE OR REPLACE FUNCTION add_book_distribution(
    p_school_id UUID,
    p_inventory_id UUID,
    p_quantity INTEGER,
    p_supervisor_id UUID,
    p_notes TEXT,
    p_book_title VARCHAR, -- This parameter is kept for compatibility but not used
    p_status TEXT DEFAULT 'completed' -- New parameter for status
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    distribution_id UUID;
    available_quantity INTEGER;
    book_title_var VARCHAR;
BEGIN
    -- Validate inputs
    IF p_quantity <= 0 THEN
        RAISE EXCEPTION 'Quantity must be greater than 0';
    END IF;

    -- Validate status
    IF p_status NOT IN ('planned', 'in_progress', 'completed', 'cancelled') THEN
        RAISE EXCEPTION 'Invalid status. Must be one of: planned, in_progress, completed, cancelled';
    END IF;

    -- Check if school exists
    IF NOT EXISTS (SELECT 1 FROM schools WHERE id = p_school_id) THEN
        RAISE EXCEPTION 'School not found';
    END IF;

    -- Check if supervisor exists
    IF NOT EXISTS (SELECT 1 FROM profiles WHERE id = p_supervisor_id) THEN
        RAISE EXCEPTION 'Supervisor not found';
    END IF;

    -- Check if inventory item exists and get available quantity
    SELECT quantity_available, book_title
    INTO available_quantity, book_title_var
    FROM book_inventory
    WHERE id = p_inventory_id;

    IF available_quantity IS NULL THEN
        RAISE EXCEPTION 'Book inventory item not found';
    END IF;

    -- Only check inventory for completed distributions (not planned ones)
    IF p_status = 'completed' AND available_quantity < p_quantity THEN
        RAISE EXCEPTION 'Insufficient inventory. Available: %, Requested: %', available_quantity, p_quantity;
    END IF;

    -- Insert distribution record
    INSERT INTO book_distributions (
        school_id,
        inventory_id,
        quantity,
        supervisor_id,
        notes,
        status
    ) VALUES (
        p_school_id,
        p_inventory_id,
        p_quantity,
        p_supervisor_id,
        COALESCE(p_notes, ''),
        p_status
    ) RETURNING id INTO distribution_id;

    -- Only update inventory for completed distributions
    IF p_status = 'completed' THEN
        UPDATE book_inventory
        SET quantity_available = quantity_available - p_quantity,
            updated_at = NOW()
        WHERE id = p_inventory_id;
    END IF;

    RETURN distribution_id;
END;
$$;
