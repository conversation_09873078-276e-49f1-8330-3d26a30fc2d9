/**
 * Centralized storage management system for offline sync
 * Handles cleanup, monitoring, and maintenance of all storage systems
 */

import { OfflineData, ConflictData } from '@/types/offlineSync.types';

export interface StorageQuota {
  localStorage: number;
  indexedDB: number;
  total: number;
}

export interface StorageUsage {
  localStorage: number;
  indexedDB: number;
  total: number;
  percentage: number;
}

export interface CleanupConfig {
  maxAge: number;
  maxItems: number;
  cleanupThreshold: number;
  retentionPolicies: {
    successful: number;
    failed: number;
    conflicts: number;
    photos: number;
  };
}

export interface CleanupReport {
  timestamp: number;
  itemsRemoved: number;
  sizeFreed: number;
  storageBeforeCleanup: StorageUsage;
  storageAfterCleanup: StorageUsage;
  errors: string[];
}

const DEFAULT_CONFIG: CleanupConfig = {
  maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
  maxItems: 500,
  cleanupThreshold: 0.8, // 80%
  retentionPolicies: {
    successful: 24 * 60 * 60 * 1000, // 24 hours
    failed: 7 * 24 * 60 * 60 * 1000, // 7 days
    conflicts: 30 * 24 * 60 * 60 * 1000, // 30 days
    photos: 7 * 24 * 60 * 60 * 1000, // 7 days
  },
};

const STORAGE_QUOTA: StorageQuota = {
  localStorage: 10 * 1024 * 1024, // 10MB
  indexedDB: 50 * 1024 * 1024, // 50MB
  total: 60 * 1024 * 1024, // 60MB total
};

class StorageManager {
  private config: CleanupConfig;
  private cleanupHistory: CleanupReport[] = [];
  private isCleanupRunning = false;

  constructor(config: Partial<CleanupConfig> = {}) {
    this.config = { ...DEFAULT_CONFIG, ...config };
    this.loadCleanupHistory();
    this.startPeriodicCleanup();
  }

  /**
   * Get current storage usage across all systems
   */
  async getStorageUsage(): Promise<StorageUsage> {
    try {
      // Calculate localStorage usage
      let localStorageSize = 0;
      for (const key in localStorage) {
        if (Object.prototype.hasOwnProperty.call(localStorage, key)) {
          localStorageSize += localStorage[key].length + key.length;
        }
      }

      // Calculate IndexedDB usage (estimate)
      let indexedDBSize = 0;
      try {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
          const estimate = await navigator.storage.estimate();
          indexedDBSize = estimate.usage || 0;
        }
      } catch (error) {
        console.warn('Could not estimate IndexedDB usage:', error);
      }

      const total = localStorageSize + indexedDBSize;
      const percentage = (total / STORAGE_QUOTA.total) * 100;

      return {
        localStorage: localStorageSize,
        indexedDB: indexedDBSize,
        total,
        percentage,
      };
    } catch (error) {
      console.error('Error calculating storage usage:', error);
      return {
        localStorage: 0,
        indexedDB: 0,
        total: 0,
        percentage: 0,
      };
    }
  }

  /**
   * Check if cleanup is needed based on current usage
   */
  async shouldCleanup(): Promise<boolean> {
    const usage = await this.getStorageUsage();
    return usage.percentage > (this.config.cleanupThreshold * 100);
  }

  /**
   * Perform comprehensive cleanup across all storage systems
   */
  async performCleanup(force = false): Promise<CleanupReport> {
    if (this.isCleanupRunning && !force) {
      throw new Error('Cleanup already in progress');
    }

    this.isCleanupRunning = true;
    const startTime = Date.now();
    const errors: string[] = [];
    let totalItemsRemoved = 0;
    let totalSizeFreed = 0;

    try {
      const storageBeforeCleanup = await this.getStorageUsage();

      // 1. Clean up offline sync data
      try {
        const offlineResult = await this.cleanupOfflineData();
        totalItemsRemoved += offlineResult.itemsRemoved;
        totalSizeFreed += offlineResult.sizeFreed;
      } catch (error) {
        errors.push(`Offline data cleanup failed: ${error.message}`);
      }

      // 2. Clean up photo cache
      try {
        const photoResult = await this.cleanupPhotoCache();
        totalItemsRemoved += photoResult.itemsRemoved;
        totalSizeFreed += photoResult.sizeFreed;
      } catch (error) {
        errors.push(`Photo cache cleanup failed: ${error.message}`);
      }

      // 3. Clean up conflicts
      try {
        const conflictResult = await this.cleanupConflicts();
        totalItemsRemoved += conflictResult.itemsRemoved;
        totalSizeFreed += conflictResult.sizeFreed;
      } catch (error) {
        errors.push(`Conflict cleanup failed: ${error.message}`);
      }

      // 4. Clean up old logs and temporary data
      try {
        const logResult = await this.cleanupLogs();
        totalItemsRemoved += logResult.itemsRemoved;
        totalSizeFreed += logResult.sizeFreed;
      } catch (error) {
        errors.push(`Log cleanup failed: ${error.message}`);
      }

      const storageAfterCleanup = await this.getStorageUsage();

      const report: CleanupReport = {
        timestamp: startTime,
        itemsRemoved: totalItemsRemoved,
        sizeFreed: totalSizeFreed,
        storageBeforeCleanup,
        storageAfterCleanup,
        errors,
      };

      this.addCleanupReport(report);
      return report;
    } finally {
      this.isCleanupRunning = false;
    }
  }

  /**
   * Clean up offline sync data
   */
  private async cleanupOfflineData(): Promise<{ itemsRemoved: number; sizeFreed: number }> {
    const OFFLINE_STORAGE_KEY = 'field_staff_offline_data';
    const now = Date.now();
    let itemsRemoved = 0;
    let sizeFreed = 0;

    try {
      const dataStr = localStorage.getItem(OFFLINE_STORAGE_KEY);
      if (!dataStr) return { itemsRemoved: 0, sizeFreed: 0 };

      const originalSize = dataStr.length;
      const data = JSON.parse(dataStr);
      
      const filteredData = data.filter((item: OfflineData) => {
        const age = now - item.timestamp;
        
        // Keep items that are still pending
        if (item.retryCount < item.maxRetries) return true;
        
        // Remove old successful items
        if (item.retryCount === 0 && age > this.config.retentionPolicies.successful) {
          itemsRemoved++;
          return false;
        }
        
        // Remove old failed items
        if (item.retryCount >= item.maxRetries && age > this.config.retentionPolicies.failed) {
          itemsRemoved++;
          return false;
        }
        
        return true;
      });

      // Limit total items
      if (filteredData.length > this.config.maxItems) {
        const sortedData = filteredData.sort((a: OfflineData, b: OfflineData) => b.timestamp - a.timestamp);
        const limitedData = sortedData.slice(0, this.config.maxItems);
        itemsRemoved += filteredData.length - limitedData.length;
        filteredData.splice(0, filteredData.length, ...limitedData);
      }

      const newDataStr = JSON.stringify(filteredData);
      localStorage.setItem(OFFLINE_STORAGE_KEY, newDataStr);
      sizeFreed = originalSize - newDataStr.length;

      return { itemsRemoved, sizeFreed };
    } catch (error) {
      console.error('Error cleaning offline data:', error);
      return { itemsRemoved: 0, sizeFreed: 0 };
    }
  }

  /**
   * Clean up photo cache using the photo cache manager
   */
  private async cleanupPhotoCache(): Promise<{ itemsRemoved: number; sizeFreed: number }> {
    try {
      const { photoCacheManager } = await import('@/utils/photoCacheManager');
      const beforeStats = await photoCacheManager.getCacheStats();
      await photoCacheManager.performCleanup();
      const afterStats = await photoCacheManager.getCacheStats();

      return {
        itemsRemoved: beforeStats.totalItems - afterStats.totalItems,
        sizeFreed: beforeStats.totalSize - afterStats.totalSize,
      };
    } catch (error) {
      console.error('Error cleaning photo cache:', error);
      return { itemsRemoved: 0, sizeFreed: 0 };
    }
  }

  /**
   * Clean up sync conflicts
   */
  private async cleanupConflicts(): Promise<{ itemsRemoved: number; sizeFreed: number }> {
    const CONFLICTS_STORAGE_KEY = 'field_staff_sync_conflicts';
    const now = Date.now();
    let itemsRemoved = 0;
    let sizeFreed = 0;

    try {
      const dataStr = localStorage.getItem(CONFLICTS_STORAGE_KEY);
      if (!dataStr) return { itemsRemoved: 0, sizeFreed: 0 };

      const originalSize = dataStr.length;
      const conflicts = JSON.parse(dataStr);
      
      const filteredConflicts = conflicts.filter((conflict: ConflictData) => {
        const age = now - conflict.timestamp;
        if (age > this.config.retentionPolicies.conflicts) {
          itemsRemoved++;
          return false;
        }
        return true;
      });

      const newDataStr = JSON.stringify(filteredConflicts);
      localStorage.setItem(CONFLICTS_STORAGE_KEY, newDataStr);
      sizeFreed = originalSize - newDataStr.length;

      return { itemsRemoved, sizeFreed };
    } catch (error) {
      console.error('Error cleaning conflicts:', error);
      return { itemsRemoved: 0, sizeFreed: 0 };
    }
  }

  /**
   * Clean up old logs and temporary data
   */
  private async cleanupLogs(): Promise<{ itemsRemoved: number; sizeFreed: number }> {
    let itemsRemoved = 0;
    let sizeFreed = 0;
    const now = Date.now();
    const maxAge = 7 * 24 * 60 * 60 * 1000; // 7 days

    try {
      // Clean up performance metrics
      const keysToCheck = [
        'performance_metrics',
        'error_logs',
        'debug_logs',
        'temp_data',
        'cache_metadata',
      ];

      for (const keyPrefix of keysToCheck) {
        for (const key in localStorage) {
          if (key.startsWith(keyPrefix)) {
            try {
              const data = JSON.parse(localStorage[key]);
              if (data.timestamp && (now - data.timestamp) > maxAge) {
                const size = localStorage[key].length;
                localStorage.removeItem(key);
                itemsRemoved++;
                sizeFreed += size;
              }
            } catch (error) {
              // If it's not JSON or doesn't have timestamp, skip
              continue;
            }
          }
        }
      }

      return { itemsRemoved, sizeFreed };
    } catch (error) {
      console.error('Error cleaning logs:', error);
      return { itemsRemoved: 0, sizeFreed: 0 };
    }
  }

  /**
   * Start periodic cleanup task
   */
  private startPeriodicCleanup(): void {
    // Run cleanup every 30 minutes
    setInterval(async () => {
      try {
        const shouldClean = await this.shouldCleanup();
        if (shouldClean) {
          console.log('Starting scheduled storage cleanup...');
          const report = await this.performCleanup();
          console.log('Scheduled cleanup completed:', report);
        }
      } catch (error) {
        console.error('Error during scheduled cleanup:', error);
      }
    }, 30 * 60 * 1000);
  }

  /**
   * Load cleanup history from localStorage
   */
  private loadCleanupHistory(): void {
    try {
      const historyStr = localStorage.getItem('storage_cleanup_history');
      if (historyStr) {
        this.cleanupHistory = JSON.parse(historyStr);
      }
    } catch (error) {
      console.error('Error loading cleanup history:', error);
      this.cleanupHistory = [];
    }
  }

  /**
   * Add cleanup report to history
   */
  private addCleanupReport(report: CleanupReport): void {
    this.cleanupHistory.push(report);
    
    // Keep only last 10 reports
    if (this.cleanupHistory.length > 10) {
      this.cleanupHistory = this.cleanupHistory.slice(-10);
    }

    try {
      localStorage.setItem('storage_cleanup_history', JSON.stringify(this.cleanupHistory));
    } catch (error) {
      console.error('Error saving cleanup history:', error);
    }
  }

  /**
   * Get cleanup history
   */
  getCleanupHistory(): CleanupReport[] {
    return [...this.cleanupHistory];
  }

  /**
   * Get storage health status
   */
  async getStorageHealth(): Promise<{
    status: 'healthy' | 'warning' | 'critical';
    usage: StorageUsage;
    recommendations: string[];
  }> {
    const usage = await this.getStorageUsage();
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (usage.percentage > 90) {
      status = 'critical';
      recommendations.push('Storage is critically full. Immediate cleanup required.');
      recommendations.push('Consider reducing offline data retention periods.');
    } else if (usage.percentage > 80) {
      status = 'warning';
      recommendations.push('Storage usage is high. Cleanup recommended.');
      recommendations.push('Review and clear old offline data.');
    }

    if (usage.percentage > 70) {
      recommendations.push('Consider clearing photo cache if not needed.');
      recommendations.push('Review sync conflicts and resolve them.');
    }

    return { status, usage, recommendations };
  }
}

// Export singleton instance
export const storageManager = new StorageManager();

// Initialize on module load
storageManager.getStorageUsage().catch(console.error);
