import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Wifi,
  WifiOff,
  Loader2,
  LogOut
} from 'lucide-react';
import { useFieldStaffCheckOut, useCurrentAttendance } from '@/hooks/field-staff/useFieldStaffAttendance';
import { useGPSLocation } from '@/hooks/field-staff/useGPSLocation';
import { getDeviceInfo, getNetworkInfo } from '@/utils/deviceInfo';
import FieldReportForm from './FieldReportForm';
import { toast } from 'sonner';
import { format } from 'date-fns';

interface FieldStaffCheckOutModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface FieldReportData {
  activity_type: string;
  round_table_sessions_count: number;
  total_students_attended: number;
  students_per_session: number;
  activities_conducted: string[];
  topics_covered: string[];
  challenges_encountered: string;
  wins_achieved: string;
  lessons_learned: string;
  follow_up_required: boolean;
  follow_up_actions: string;
}

const FieldStaffCheckOutModal: React.FC<FieldStaffCheckOutModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [showReportForm, setShowReportForm] = useState(false);
  
  const { data: currentCheckIn, isLoading: attendanceLoading } = useCurrentAttendance();
  const { location, getCurrentLocation } = useGPSLocation();
  const checkOutMutation = useFieldStaffCheckOut();

  const isOnline = navigator.onLine;
  const isOfflineMode = !isOnline;
  const isCheckedIn = !!currentCheckIn;

  const handleSubmitReport = async (reportData: FieldReportData) => {
    if (!currentCheckIn) {
      toast.error('No active check-in session found');
      return;
    }

    try {
      // Location is now optional for check-out
      // Try to get current location but don't fail if unavailable
      let currentLocation = location;
      if (!currentLocation) {
        try {
          currentLocation = await getCurrentLocation({
            enableHighAccuracy: false, // Use lower accuracy for optional location
            timeout: 5000, // Short timeout since it's optional
            maximumAge: 600000, // 10 minutes cache is acceptable
          });
        } catch (gpsError) {
          // GPS failure is acceptable for check-out
          console.log('GPS not available for check-out, proceeding without location');
        }
      }

      await checkOutMutation.mutateAsync({
        attendance_id: currentCheckIn.id,
        // Location parameters are now optional
        latitude: currentLocation?.latitude,
        longitude: currentLocation?.longitude,
        accuracy: currentLocation?.accuracy,
        offline_sync: isOfflineMode,
        // Map field names from FieldReportForm to CheckOutData interface
        activity_type: reportData.activity_type,
        round_table_sessions: reportData.round_table_sessions_count,
        total_students: reportData.total_students_attended,
        students_per_session: reportData.students_per_session,
        activities_conducted: reportData.activities_conducted,
        topics_covered: reportData.topics_covered,
        challenges: reportData.challenges_encountered,
        wins: reportData.wins_achieved,
        lessons_learned: reportData.lessons_learned,
        follow_up_required: reportData.follow_up_required,
        follow_up_actions: reportData.follow_up_actions,
      });

      setShowReportForm(false);
      onClose();
      toast.success('Successfully checked out and report submitted!');
    } catch (error: unknown) {
      console.error('Check-out failed:', error);
      toast.error('Check-out failed. Please try again.');
    }
  };

  const handleClose = () => {
    setShowReportForm(false);
    onClose();
  };

  if (attendanceLoading) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[600px]">
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-3">Loading attendance status...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (!isCheckedIn) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              Not Checked In
            </DialogTitle>
            <DialogDescription>
              You need to check in first before you can check out.
            </DialogDescription>
          </DialogHeader>
          <div className="text-center py-6">
            <p className="text-gray-600 mb-4">
              Please check in to a school location first to start your field work session.
            </p>
            <Button onClick={handleClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <LogOut className="h-5 w-5 text-red-600" />
            Field Work Check-Out
          </DialogTitle>
          <DialogDescription>
            Complete your field work session and submit your daily report.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Connection Status */}
          <Alert className={isOnline ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}>
            <div className="flex items-center gap-2">
              {isOnline ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
              <AlertDescription>
                {isOnline ? 'Online - Data will sync immediately' : 'Offline - Data will sync when connection is restored'}
              </AlertDescription>
            </div>
          </Alert>

          {/* Current Session Info */}
          {currentCheckIn && (
            <Card className="border-green-200 bg-green-50">
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2 text-green-800">
                  <MapPin className="h-4 w-4" />
                  Current Field Session
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-green-700 font-medium">Check-in Time:</span>
                    <div className="text-green-800">
                      {format(new Date(currentCheckIn.check_in_time), 'MMM d, yyyy h:mm a')}
                    </div>
                  </div>
                  <div>
                    <span className="text-green-700 font-medium">Duration:</span>
                    <div className="text-green-800">
                      {(() => {
                        const checkInTime = new Date(currentCheckIn.check_in_time);
                        const now = new Date();
                        const diffMs = now.getTime() - checkInTime.getTime();
                        const hours = Math.floor(diffMs / (1000 * 60 * 60));
                        const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                        return `${hours}h ${minutes}m`;
                      })()}
                    </div>
                  </div>
                  <div>
                    <span className="text-green-700 font-medium">Location:</span>
                    <div className="text-green-800">
                      {currentCheckIn.check_in_address || 'GPS Location Recorded'}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Field Report Form or Submit Button */}
          {!showReportForm ? (
            <div className="text-center py-6">
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Ready to Check Out?
              </h3>
              <p className="text-gray-600 mb-6">
                Please submit your field report before checking out to complete your session.
              </p>
              <Button
                onClick={() => setShowReportForm(true)}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Submit Field Report & Check Out
              </Button>
            </div>
          ) : (
            <div>
              <h3 className="text-lg font-medium text-gray-900 mb-4">
                Field Work Report
              </h3>
              <FieldReportForm
                onSubmit={handleSubmitReport}
                isSubmitting={checkOutMutation.isPending}
              />
            </div>
          )}

          {/* Action Buttons */}
          {showReportForm && (
            <div className="flex justify-end space-x-3 pt-4 border-t">
              <Button variant="outline" onClick={() => setShowReportForm(false)}>
                Back
              </Button>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FieldStaffCheckOutModal;
