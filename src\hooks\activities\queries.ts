
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '../useAuth';
import { performanceMonitor } from '@/utils/performance';
import { queryKeys } from '@/lib/queryClient';
import { Activity } from './types';

// Hook for recent activities with improved error handling
export const useRecentActivities = (limit: number = 20, offset: number = 0) => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: queryKeys.activities.recent(limit),
    queryFn: async () => {
      const endTimer = performanceMonitor.startTimer('get_recent_activities');
      
      try {
        const { data, error } = await supabase.rpc('get_recent_activities', {
          p_limit: limit,
          p_offset: offset,
        });

        if (error) {
          console.error('Error fetching activities:', error);
          endTimer(false, error.message);
          throw error;
        }

        const result = (data || []) as Activity[];
        endTimer(true);
        return result;
      } catch (err) {
        console.error('Activity feed error:', err);
        endTimer(false, (err as Error).message);
        throw err;
      }
    },
    enabled: !!profile?.id,
    retry: 3, // Retry up to 3 times on failure
    staleTime: 30 * 1000, // 30 seconds for activity feed
  });
};

// Hook for user-specific activities
export const useUserActivities = (userId: string, limit: number = 20) => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: queryKeys.activities.user(userId, limit),
    queryFn: async () => {
      const endTimer = performanceMonitor.startTimer('get_user_activities');
      
      try {
        const { data, error } = await supabase.rpc('get_user_activities', {
          p_user_id: userId,
          p_limit: limit,
        });

        if (error) {
          console.error('Error fetching user activities:', error);
          endTimer(false, error.message);
          throw error;
        }

        const result = (data || []) as Activity[];
        endTimer(true);
        return result;
      } catch (err) {
        console.error('User activities error:', err);
        endTimer(false, (err as Error).message);
        throw err;
      }
    },
    enabled: !!profile?.id && !!userId,
    retry: 3,
  });
};

// Hook for activity statistics
export const useActivityStats = (days: number = 30) => {
  const { profile } = useAuth();
  
  return useQuery({
    queryKey: queryKeys.activities.stats(days),
    queryFn: async () => {
      const endTimer = performanceMonitor.startTimer('get_activity_stats');
      
      try {
        const { data, error } = await supabase.rpc('get_activity_stats', {
          p_days: days,
        });

        if (error) {
          endTimer(false, error.message);
          throw error;
        }

        const result = data || [];
        endTimer(true);
        return result;
      } catch (err) {
        endTimer(false, (err as Error).message);
        throw err;
      }
    },
    enabled: !!profile?.id,
    staleTime: 5 * 60 * 1000, // 5 minutes for stats
  });
};
