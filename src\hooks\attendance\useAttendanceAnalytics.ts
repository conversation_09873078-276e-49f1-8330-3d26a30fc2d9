import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

interface AttendanceAnalytics {
  id: string;
  school_id: string;
  student_id?: string;
  analysis_period: string;
  period_start_date: string;
  period_end_date: string;
  total_sessions: number;
  sessions_attended: number;
  sessions_absent: number;
  sessions_late: number;
  sessions_excused: number;
  attendance_rate: number;
  punctuality_rate?: number;
  average_participation_score?: number;
  consecutive_absences: number;
  longest_absence_streak: number;
  improvement_trend?: string;
  risk_level: string;
  intervention_recommended: boolean;
  last_calculated: string;
}

export interface StudentAtRiskData extends AttendanceAnalytics {
  student?: {
    id: string;
    first_name: string;
    last_name: string;
    student_number: string | null;
    grade_level: number;
    guardian_name: string | null;
    guardian_contact: string | null;
  };
  school?: {
    name: string;
  };
}

interface AttendanceStatsParams {
  schoolId?: string;
  studentId?: string;
  period?: 'daily' | 'weekly' | 'monthly' | 'term' | 'annual';
  startDate?: Date;
  endDate?: Date;
}

interface TrendData {
  date: string;
  attendance_rate: number;
  total_sessions: number;
  present: number;
  absent: number;
  late: number;
}

// Hook to fetch attendance analytics
export const useAttendanceAnalytics = (params: AttendanceStatsParams) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['attendance-analytics', params],
    queryFn: async () => {
      let query = supabase
        .from('attendance_analytics')
        .select(`
          *,
          school:schools(name),
          student:students(first_name, last_name, student_number)
        `)
        .order('last_calculated', { ascending: false });

      if (params.schoolId) {
        query = query.eq('school_id', params.schoolId);
      }

      if (params.studentId) {
        query = query.eq('student_id', params.studentId);
      }

      if (params.period) {
        query = query.eq('analysis_period', params.period);
      }

      if (params.startDate) {
        query = query.gte('period_start_date', params.startDate.toISOString().split('T')[0]);
      }

      if (params.endDate) {
        query = query.lte('period_end_date', params.endDate.toISOString().split('T')[0]);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching attendance analytics:', error);
        throw error;
      }

      return data as AttendanceAnalytics[];
    },
    enabled: !!profile?.id,
  });
};

// Hook to calculate attendance analytics
export const useCalculateAttendanceAnalytics = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({
      schoolId,
      studentId,
      periodStart,
      periodEnd,
      analysisPeriod = 'monthly',
    }: {
      schoolId: string;
      studentId?: string;
      periodStart: Date;
      periodEnd: Date;
      analysisPeriod?: string;
    }) => {
      const { data, error } = await supabase.rpc('calculate_attendance_analytics', {
        p_school_id: schoolId,
        p_student_id: studentId,
        p_period_start: periodStart.toISOString().split('T')[0],
        p_period_end: periodEnd.toISOString().split('T')[0],
        p_analysis_period: analysisPeriod,
      });

      if (error) {
        console.error('Error calculating attendance analytics:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['attendance-analytics'] });
      toast({
        title: 'Success',
        description: 'Attendance analytics calculated successfully',
      });
    },
    onError: (error: Error) => {
      console.error('Failed to calculate analytics:', error);
      toast({
        title: 'Error',
        description: 'Failed to calculate attendance analytics. Please try again.',
        variant: 'destructive',
      });
    },
  });
};

// Hook to get attendance trends
export const useAttendanceTrends = (
  schoolId?: string,
  studentId?: string,
  period: 'daily' | 'weekly' | 'monthly' = 'daily',
  days: number = 30
) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['attendance-trends', schoolId, studentId, period, days],
    queryFn: async () => {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - days);

      let query = supabase
        .from('student_attendance')
        .select(`
          recorded_at,
          attendance_status,
          session:attendance_sessions(
            session_date,
            school_id
          )
        `)
        .gte('recorded_at', startDate.toISOString())
        .lte('recorded_at', endDate.toISOString())
        .order('recorded_at', { ascending: true });

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      if (studentId) {
        query = query.eq('student_id', studentId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching attendance trends:', error);
        throw error;
      }

      // Group data by period
      const groupedData: Record<string, TrendData> = {};
      
      data?.forEach(record => {
        if (!record.session?.session_date) return;
        
        let groupKey: string;
        const date = new Date(record.session.session_date);
        
        switch (period) {
          case 'daily':
            groupKey = date.toISOString().split('T')[0];
            break;
          case 'weekly': {
            const weekStart = new Date(date);
            weekStart.setDate(date.getDate() - date.getDay());
            groupKey = weekStart.toISOString().split('T')[0];
            break;
          }
          case 'monthly':
            groupKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            break;
          default:
            groupKey = date.toISOString().split('T')[0];
        }

        if (!groupedData[groupKey]) {
          groupedData[groupKey] = {
            date: groupKey,
            attendance_rate: 0,
            total_sessions: 0,
            present: 0,
            absent: 0,
            late: 0,
          };
        }

        const group = groupedData[groupKey];
        group.total_sessions++;

        switch (record.attendance_status) {
          case 'present':
            group.present++;
            break;
          case 'absent':
            group.absent++;
            break;
          case 'late':
            group.late++;
            break;
        }

        // Calculate attendance rate (present + late = attended)
        const attended = group.present + group.late;
        group.attendance_rate = group.total_sessions > 0 
          ? (attended / group.total_sessions) * 100 
          : 0;
      });

      return Object.values(groupedData).sort((a, b) => a.date.localeCompare(b.date));
    },
    enabled: !!profile?.id,
  });
};

// Hook to get school-level attendance summary
export const useSchoolAttendanceSummary = (schoolId?: string, dateRange?: { start: Date; end: Date }) => {
  const { profile } = useAuth();

  return useQuery({
    queryKey: ['school-attendance-summary', schoolId, dateRange],
    queryFn: async () => {
      let query = supabase
        .from('student_attendance')
        .select(`
          attendance_status,
          session:attendance_sessions(
            session_date,
            session_type,
            school_id
          ),
          student:students(
            grade_level,
            gender
          )
        `);

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      if (dateRange) {
        query = query
          .gte('recorded_at', dateRange.start.toISOString())
          .lte('recorded_at', dateRange.end.toISOString());
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching school attendance summary:', error);
        throw error;
      }

      // Calculate summary statistics
      const summary = {
        total_records: data?.length || 0,
        present: data?.filter(r => r.attendance_status === 'present').length || 0,
        absent: data?.filter(r => r.attendance_status === 'absent').length || 0,
        late: data?.filter(r => r.attendance_status === 'late').length || 0,
        excused: data?.filter(r => r.attendance_status === 'excused').length || 0,
        attendance_rate: 0,
        punctuality_rate: 0,
        by_grade: {} as Record<number, { total: number; present: number; rate: number }>,
        by_gender: {} as Record<string, { total: number; present: number; rate: number }>,
        by_session_type: {} as Record<string, { total: number; present: number; rate: number }>,
      };

      if (summary.total_records > 0) {
        const attended = summary.present + summary.late;
        summary.attendance_rate = (attended / summary.total_records) * 100;
        summary.punctuality_rate = attended > 0 ? (summary.present / attended) * 100 : 0;

        // Group by grade
        data?.forEach(record => {
          if (record.student?.grade_level) {
            const grade = record.student.grade_level;
            if (!summary.by_grade[grade]) {
              summary.by_grade[grade] = { total: 0, present: 0, rate: 0 };
            }
            summary.by_grade[grade].total++;
            if (['present', 'late'].includes(record.attendance_status)) {
              summary.by_grade[grade].present++;
            }
          }
        });

        // Calculate rates for grades
        Object.keys(summary.by_grade).forEach(grade => {
          const gradeData = summary.by_grade[parseInt(grade)];
          gradeData.rate = gradeData.total > 0 ? (gradeData.present / gradeData.total) * 100 : 0;
        });

        // Group by gender
        data?.forEach(record => {
          if (record.student?.gender) {
            const gender = record.student.gender;
            if (!summary.by_gender[gender]) {
              summary.by_gender[gender] = { total: 0, present: 0, rate: 0 };
            }
            summary.by_gender[gender].total++;
            if (['present', 'late'].includes(record.attendance_status)) {
              summary.by_gender[gender].present++;
            }
          }
        });

        // Calculate rates for genders
        Object.keys(summary.by_gender).forEach(gender => {
          const genderData = summary.by_gender[gender];
          genderData.rate = genderData.total > 0 ? (genderData.present / genderData.total) * 100 : 0;
        });

        // Group by session type
        data?.forEach(record => {
          if (record.session?.session_type) {
            const sessionType = record.session.session_type;
            if (!summary.by_session_type[sessionType]) {
              summary.by_session_type[sessionType] = { total: 0, present: 0, rate: 0 };
            }
            summary.by_session_type[sessionType].total++;
            if (['present', 'late'].includes(record.attendance_status)) {
              summary.by_session_type[sessionType].present++;
            }
          }
        });

        // Calculate rates for session types
        Object.keys(summary.by_session_type).forEach(sessionType => {
          const sessionData = summary.by_session_type[sessionType];
          sessionData.rate = sessionData.total > 0 ? (sessionData.present / sessionData.total) * 100 : 0;
        });
      }

      return summary;
    },
    enabled: !!profile?.id,
  });
};

// Hook to get students at risk (low attendance)
export const useStudentsAtRisk = (schoolId?: string, riskLevel: 'medium' | 'high' | 'critical' = 'high') => {
  const { profile } = useAuth();

  return useQuery<StudentAtRiskData[]>({
    queryKey: ['students-at-risk', schoolId, riskLevel],
    queryFn: async (): Promise<StudentAtRiskData[]> => {
      let query = supabase
        .from('attendance_analytics')
        .select(`
          *,
          student:students(
            id,
            first_name,
            last_name,
            student_number,
            grade_level,
            guardian_name,
            guardian_contact
          ),
          school:schools(name)
        `)
        .in('risk_level', riskLevel === 'medium' ? ['medium', 'high', 'critical'] : 
             riskLevel === 'high' ? ['high', 'critical'] : ['critical'])
        .eq('analysis_period', 'monthly')
        .order('attendance_rate', { ascending: true });

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching students at risk:', error);
        throw error;
      }

      return data;
    },
    enabled: !!profile?.id,
  });
};
