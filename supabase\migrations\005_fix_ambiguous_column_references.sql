-- Fix ambiguous column references in activity feed functions
-- This fixes the "column reference 'id' is ambiguous" error

-- Function to get recent activities (FIXED)
CREATE OR REPLACE FUNCTION get_recent_activities(
    p_limit INTEGER DEFAULT 20,
    p_offset INTEGER DEFAULT 0
)
RETURNS TABLE (
    id UUID,
    activity_type activity_type,
    user_id UUID,
    user_name VARCHAR,
    entity_type entity_type,
    entity_id UUID,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    entity_details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id,
        a.activity_type,
        a.user_id,
        p.name as user_name,
        a.entity_type,
        a.entity_id,
        a.description,
        a.metadata,
        a.created_at,
        CASE 
            WHEN a.entity_type = 'task' THEN
                (SELECT json_build_object(
                    'title', t.title,
                    'status', t.status,
                    'priority', t.priority
                ) FROM tasks t WHERE t.id = a.entity_id)
            WHEN a.entity_type = 'school' THEN
                (SELECT json_build_object(
                    'name', s.name,
                    'school_type', s.school_type
                ) FROM schools s WHERE s.id = a.entity_id)
            WHEN a.entity_type = 'distribution' THEN
                (SELECT json_build_object(
                    'school_name', s.name,
                    'quantity', bd.quantity
                ) FROM book_distributions bd
                JOIN schools s ON bd.school_id = s.id
                WHERE bd.id = a.entity_id)
            ELSE '{}'::jsonb
        END as entity_details
    FROM activities a
    JOIN profiles p ON a.user_id = p.id
    WHERE 
        -- Apply RLS: users can see activities they have access to
        (a.user_id = auth.uid() OR
         (a.entity_type = 'task' AND EXISTS (
             SELECT 1 FROM tasks t_check
             WHERE t_check.id = a.entity_id AND (
                 t_check.assigned_to = auth.uid() OR 
                 t_check.created_by = auth.uid()
             )
         )) OR
         (a.entity_type = 'school' AND EXISTS (
             SELECT 1 FROM profiles pr
             JOIN schools s_check ON s_check.division_id = pr.division_id
             WHERE pr.id = auth.uid() AND s_check.id = a.entity_id
         )) OR
         EXISTS (
             SELECT 1 FROM profiles p_check
             WHERE p_check.id = auth.uid() AND p_check.role IN ('admin', 'program_officer')
         ))
    ORDER BY a.created_at DESC
    LIMIT p_limit OFFSET p_offset;
END;
$$;

-- Function to get user-specific activities (FIXED)
CREATE OR REPLACE FUNCTION get_user_activities(
    p_user_id UUID,
    p_limit INTEGER DEFAULT 20
)
RETURNS TABLE (
    id UUID,
    activity_type activity_type,
    user_id UUID,
    user_name VARCHAR,
    entity_type entity_type,
    entity_id UUID,
    description TEXT,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE,
    entity_details JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission to view activities for this user
    IF p_user_id != auth.uid() AND NOT EXISTS (
        SELECT 1 FROM profiles p_check
        WHERE p_check.id = auth.uid() AND p_check.role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to view user activities';
    END IF;

    RETURN QUERY
    SELECT 
        a.id,
        a.activity_type,
        a.user_id,
        p.name as user_name,
        a.entity_type,
        a.entity_id,
        a.description,
        a.metadata,
        a.created_at,
        CASE 
            WHEN a.entity_type = 'task' THEN
                (SELECT json_build_object(
                    'title', t.title,
                    'status', t.status,
                    'priority', t.priority
                ) FROM tasks t WHERE t.id = a.entity_id)
            WHEN a.entity_type = 'school' THEN
                (SELECT json_build_object(
                    'name', s.name,
                    'school_type', s.school_type
                ) FROM schools s WHERE s.id = a.entity_id)
            WHEN a.entity_type = 'distribution' THEN
                (SELECT json_build_object(
                    'school_name', s.name,
                    'quantity', bd.quantity
                ) FROM book_distributions bd
                JOIN schools s ON bd.school_id = s.id
                WHERE bd.id = a.entity_id)
            ELSE '{}'::jsonb
        END as entity_details
    FROM activities a
    JOIN profiles p ON a.user_id = p.id
    WHERE a.user_id = p_user_id
    ORDER BY a.created_at DESC
    LIMIT p_limit;
END;
$$;

-- Function to get activity statistics (FIXED)
CREATE OR REPLACE FUNCTION get_activity_stats(
    p_days INTEGER DEFAULT 30
)
RETURNS TABLE (
    activity_type activity_type,
    count BIGINT,
    latest_activity TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.activity_type,
        COUNT(*) as count,
        MAX(a.created_at) as latest_activity
    FROM activities a
    WHERE 
        a.created_at >= NOW() - (p_days || ' days')::INTERVAL
        AND (
            -- Apply same RLS logic as get_recent_activities
            a.user_id = auth.uid() OR
            (a.entity_type = 'task' AND EXISTS (
                SELECT 1 FROM tasks t_check
                WHERE t_check.id = a.entity_id AND (
                    t_check.assigned_to = auth.uid() OR 
                    t_check.created_by = auth.uid()
                )
            )) OR
            (a.entity_type = 'school' AND EXISTS (
                SELECT 1 FROM profiles pr
                JOIN schools s_check ON s_check.division_id = pr.division_id
                WHERE pr.id = auth.uid() AND s_check.id = a.entity_id
            )) OR
            EXISTS (
                SELECT 1 FROM profiles p_check
                WHERE p_check.id = auth.uid() AND p_check.role IN ('admin', 'program_officer')
            )
        )
    GROUP BY a.activity_type
    ORDER BY count DESC;
END;
$$;
