-- Test Field Check-out Functionality
-- This script tests the complete field check-out flow with field report creation

-- Test data setup (replace with actual IDs from your database)
-- You'll need to replace these UUIDs with actual values from your database

-- 1. Test the field_staff_checkout function with sample data
SELECT field_staff_checkout(
    'REPLACE_WITH_ACTUAL_ATTENDANCE_ID'::UUID,  -- p_attendance_id
    'leadership_training'::field_activity_type,  -- p_activity_type
    -1.2921,  -- p_latitude (Kampala coordinates)
    36.8219,  -- p_longitude
    10.0,     -- p_accuracy
    'Test School Location',  -- p_address
    'Test checkout notes',   -- p_notes
    3,        -- p_round_table_sessions
    24,       -- p_total_students
    8,        -- p_students_per_session
    ARRAY['Leadership skills training', 'Team building exercises'],  -- p_activities_conducted
    ARRAY['Communication', 'Problem solving', 'Decision making'],    -- p_topics_covered
    'Limited time for all planned activities',  -- p_challenges
    'High student engagement and participation', -- p_wins
    'Students showed great enthusiasm for leadership concepts', -- p_observations
    'Need to focus more on practical applications', -- p_lessons_learned
    true,     -- p_follow_up_required
    'Schedule follow-up session next week',  -- p_follow_up_actions
    ARRAY['photo1.jpg', 'photo2.jpg'],  -- p_photos
    false     -- p_offline_sync
) AS field_report_id;

-- 2. Verify the field report was created correctly
SELECT 
    fr.id,
    fr.attendance_id,
    fr.staff_id,
    fr.school_id,
    fr.report_date,
    fr.activity_type,
    fr.round_table_sessions,
    fr.total_students,
    fr.students_per_session,
    fr.activities_conducted,
    fr.topics_covered,
    fr.challenges,
    fr.wins,
    fr.observations,
    fr.lessons_learned,
    fr.follow_up_required,
    fr.follow_up_actions,
    fr.photos,
    fr.notes,
    fr.offline_sync,
    -- Legacy columns
    fr.title,
    fr.description,
    fr.report_type,
    fr.created_at
FROM field_reports fr
WHERE fr.attendance_id = 'REPLACE_WITH_ACTUAL_ATTENDANCE_ID'::UUID
ORDER BY fr.created_at DESC
LIMIT 1;

-- 3. Verify the attendance record was updated
SELECT 
    fsa.id,
    fsa.staff_id,
    fsa.school_id,
    fsa.check_in_time,
    fsa.check_out_time,
    fsa.total_duration_minutes,
    fsa.status,
    fsa.notes
FROM field_staff_attendance fsa
WHERE fsa.id = 'REPLACE_WITH_ACTUAL_ATTENDANCE_ID'::UUID;

-- 4. Test error handling - try to check out again (should fail)
-- This should raise an exception: "Already checked out for this attendance record"
-- SELECT field_staff_checkout(
--     'REPLACE_WITH_ACTUAL_ATTENDANCE_ID'::UUID,
--     'school_visit'::field_activity_type
-- );

-- 5. Check the database schema to ensure all columns exist
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns
WHERE table_name = 'field_reports'
AND table_schema = 'public'
ORDER BY ordinal_position;
