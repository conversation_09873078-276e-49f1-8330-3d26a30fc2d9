import React from 'react';
import FieldStaffAccessControl from './FieldStaffAccessControl';

/**
 * Higher-order component for role-based access control
 */
export const withFieldStaffAccess = <P extends object>(
  Component: React.ComponentType<P>,
  requiredRoles: string[] = ['admin', 'program_officer', 'field_staff']
) => {
  return (props: P) => (
    <FieldStaffAccessControl requiredRoles={requiredRoles}>
      <Component {...props} />
    </FieldStaffAccessControl>
  );
};
