import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { TrendingUp, Users, AlertTriangle, CheckCircle, Plus } from 'lucide-react';

interface GradeProgressionTrackerProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
}

const GradeProgressionTracker: React.FC<GradeProgressionTrackerProps> = ({
  schoolId,
  dateRange
}) => {
  // Mock data for demonstration
  const progressionData = [
    {
      grade: 'Grade 1 to 2',
      totalStudents: 45,
      promoted: 42,
      retained: 3,
      dropped: 0,
      promotionRate: 93.3
    },
    {
      grade: 'Grade 2 to 3',
      totalStudents: 38,
      promoted: 35,
      retained: 2,
      dropped: 1,
      promotionRate: 92.1
    },
    {
      grade: 'Grade 3 to 4',
      totalStudents: 41,
      promoted: 39,
      retained: 1,
      dropped: 1,
      promotionRate: 95.1
    },
    {
      grade: 'Grade 4 to 5',
      totalStudents: 36,
      promoted: 33,
      retained: 2,
      dropped: 1,
      promotionRate: 91.7
    },
    {
      grade: 'Grade 5 to 6',
      totalStudents: 32,
      promoted: 30,
      retained: 1,
      dropped: 1,
      promotionRate: 93.8
    }
  ];

  const summaryStats = {
    totalStudents: progressionData.reduce((sum, item) => sum + item.totalStudents, 0),
    totalPromoted: progressionData.reduce((sum, item) => sum + item.promoted, 0),
    totalRetained: progressionData.reduce((sum, item) => sum + item.retained, 0),
    totalDropped: progressionData.reduce((sum, item) => sum + item.dropped, 0),
    overallPromotionRate: progressionData.reduce((sum, item) => sum + item.promotionRate, 0) / progressionData.length
  };

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Users className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{summaryStats.totalStudents}</p>
                <p className="text-sm text-gray-600">Total Students</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-green-100 p-2 rounded-lg">
                <CheckCircle className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{summaryStats.totalPromoted}</p>
                <p className="text-sm text-gray-600">Promoted</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-yellow-100 p-2 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{summaryStats.totalRetained}</p>
                <p className="text-sm text-gray-600">Retained</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-purple-100 p-2 rounded-lg">
                <TrendingUp className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{summaryStats.overallPromotionRate.toFixed(1)}%</p>
                <p className="text-sm text-gray-600">Promotion Rate</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Grade Progression Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Grade Progression Analysis</span>
              </CardTitle>
              <CardDescription>
                Student progression rates across grade levels for academic year {dateRange.start.getFullYear()}
              </CardDescription>
            </div>
            <Button className="bg-ilead-green hover:bg-ilead-dark-green">
              <Plus className="h-4 w-4 mr-2" />
              Add Progression Data
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="overflow-x-auto">
            <table className="w-full text-sm">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-3">Grade Transition</th>
                  <th className="text-right p-3">Total Students</th>
                  <th className="text-right p-3">Promoted</th>
                  <th className="text-right p-3">Retained</th>
                  <th className="text-right p-3">Dropped Out</th>
                  <th className="text-right p-3">Promotion Rate</th>
                  <th className="text-center p-3">Status</th>
                </tr>
              </thead>
              <tbody>
                {progressionData.map((progression, index) => (
                  <tr key={index} className="border-b hover:bg-gray-50">
                    <td className="p-3 font-medium">{progression.grade}</td>
                    <td className="p-3 text-right">{progression.totalStudents}</td>
                    <td className="p-3 text-right text-green-600 font-medium">
                      {progression.promoted}
                    </td>
                    <td className="p-3 text-right text-yellow-600 font-medium">
                      {progression.retained}
                    </td>
                    <td className="p-3 text-right text-red-600 font-medium">
                      {progression.dropped}
                    </td>
                    <td className="p-3 text-right">
                      <span className={`font-medium ${
                        progression.promotionRate >= 95 ? 'text-green-600' :
                        progression.promotionRate >= 90 ? 'text-blue-600' :
                        progression.promotionRate >= 85 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {progression.promotionRate.toFixed(1)}%
                      </span>
                    </td>
                    <td className="p-3 text-center">
                      {progression.promotionRate >= 95 ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Excellent
                        </span>
                      ) : progression.promotionRate >= 90 ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Good
                        </span>
                      ) : progression.promotionRate >= 85 ? (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Fair
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Needs Attention
                        </span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      {/* Insights and Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Key Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="bg-green-100 p-1 rounded-full mt-1">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                </div>
                <div>
                  <p className="font-medium text-green-800">Strong Overall Performance</p>
                  <p className="text-sm text-gray-600">
                    Average promotion rate of {summaryStats.overallPromotionRate.toFixed(1)}% exceeds the target of 90%
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 p-1 rounded-full mt-1">
                  <TrendingUp className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <p className="font-medium text-blue-800">Grade 3-4 Transition Excellence</p>
                  <p className="text-sm text-gray-600">
                    Highest promotion rate at 95.1%, indicating effective early grade support
                  </p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <div className="bg-yellow-100 p-1 rounded-full mt-1">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                </div>
                <div>
                  <p className="font-medium text-yellow-800">Monitor Grade 4-5 Transition</p>
                  <p className="text-sm text-gray-600">
                    Slightly lower promotion rate (91.7%) may need additional support
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Recommendations</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-1">Early Intervention</h4>
                <p className="text-sm text-blue-700">
                  Implement targeted support for students at risk of retention in Grade 4
                </p>
              </div>
              
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800 mb-1">Best Practice Sharing</h4>
                <p className="text-sm text-green-700">
                  Share successful Grade 3-4 transition strategies with other grade levels
                </p>
              </div>
              
              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-800 mb-1">Dropout Prevention</h4>
                <p className="text-sm text-purple-700">
                  Develop community engagement programs to reduce dropout rates
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default GradeProgressionTracker;
