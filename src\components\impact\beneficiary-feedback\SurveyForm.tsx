import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  MessageSquare, 
  Plus, 
  Save, 
  Star, 
  Users,
  School,
  Heart,
  ThumbsUp,
  Smile
} from 'lucide-react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { FeedbackType, SatisfactionRating } from '@/types/impact';

const surveyResponseSchema = z.object({
  survey_title: z.string().min(1, 'Survey title is required'),
  respondent_type: z.enum(['student', 'parent', 'teacher', 'community_member', 'school_administrator']),
  respondent_name: z.string().optional(),
  respondent_contact: z.string().optional(),
  school_name: z.string().optional(),
  overall_satisfaction: z.enum(['very_dissatisfied', 'dissatisfied', 'neutral', 'satisfied', 'very_satisfied']),
  
  // Education Quality Questions
  teaching_quality: z.enum(['very_poor', 'poor', 'fair', 'good', 'excellent']),
  learning_materials: z.enum(['very_poor', 'poor', 'fair', 'good', 'excellent']),
  school_facilities: z.enum(['very_poor', 'poor', 'fair', 'good', 'excellent']),
  
  // Program Impact Questions
  program_effectiveness: z.enum(['not_effective', 'slightly_effective', 'moderately_effective', 'very_effective', 'extremely_effective']),
  student_progress: z.enum(['much_worse', 'worse', 'same', 'better', 'much_better']),
  community_involvement: z.enum(['very_low', 'low', 'moderate', 'high', 'very_high']),
  
  // Specific Feedback
  most_valuable_aspect: z.string().optional(),
  areas_for_improvement: z.string().optional(),
  additional_support_needed: z.string().optional(),
  would_recommend: z.boolean().optional(),
  additional_comments: z.string().optional()
});

type SurveyResponseFormData = z.infer<typeof surveyResponseSchema>;

interface SurveyFormProps {
  onClose: () => void;
  schoolId?: string | null;
}

const SurveyForm: React.FC<SurveyFormProps> = ({ onClose, schoolId }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentSection, setCurrentSection] = useState(1);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<SurveyResponseFormData>({
    resolver: zodResolver(surveyResponseSchema),
    defaultValues: {
      survey_title: 'Education Program Feedback Survey',
      respondent_type: 'parent'
    }
  });

  const onSubmit = async (data: SurveyResponseFormData) => {
    setIsSubmitting(true);
    try {
      console.log('Survey response data:', data);
      // In real implementation, this would save to database
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  const getSatisfactionIcon = (rating: string) => {
    switch (rating) {
      case 'very_satisfied':
      case 'excellent':
      case 'extremely_effective':
        return <Smile className="h-5 w-5 text-green-500" />;
      case 'satisfied':
      case 'good':
      case 'very_effective':
        return <ThumbsUp className="h-5 w-5 text-blue-500" />;
      case 'neutral':
      case 'fair':
      case 'moderately_effective':
        return <Heart className="h-5 w-5 text-yellow-500" />;
      default:
        return <MessageSquare className="h-5 w-5 text-gray-500" />;
    }
  };

  const renderSection = () => {
    switch (currentSection) {
      case 1:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold mb-4">Respondent Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="respondent_type">I am a *</Label>
                <Select 
                  value={watch('respondent_type')} 
                  onValueChange={(value) => setValue('respondent_type', value as FeedbackType)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="student">Student</SelectItem>
                    <SelectItem value="parent">Parent/Guardian</SelectItem>
                    <SelectItem value="teacher">Teacher</SelectItem>
                    <SelectItem value="community_member">Community Member</SelectItem>
                    <SelectItem value="school_administrator">School Administrator</SelectItem>
                  </SelectContent>
                </Select>
                {errors.respondent_type && (
                  <p className="text-sm text-red-600 mt-1">{errors.respondent_type.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="respondent_name">Name (Optional)</Label>
                <Input {...register('respondent_name')} />
              </div>

              <div>
                <Label htmlFor="respondent_contact">Contact (Optional)</Label>
                <Input {...register('respondent_contact')} placeholder="Phone or email" />
              </div>

              <div>
                <Label htmlFor="school_name">School Name</Label>
                <Input {...register('school_name')} />
              </div>
            </div>

            <div>
              <Label htmlFor="overall_satisfaction">Overall, how satisfied are you with the education program? *</Label>
              <RadioGroup 
                value={watch('overall_satisfaction')}
                onValueChange={(value) => setValue('overall_satisfaction', value as SatisfactionRating)}
                className="mt-2"
              >
                <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
                  {[
                    { value: 'very_dissatisfied', label: 'Very Dissatisfied', icon: '😞' },
                    { value: 'dissatisfied', label: 'Dissatisfied', icon: '😕' },
                    { value: 'neutral', label: 'Neutral', icon: '😐' },
                    { value: 'satisfied', label: 'Satisfied', icon: '😊' },
                    { value: 'very_satisfied', label: 'Very Satisfied', icon: '😍' }
                  ].map((option) => (
                    <div key={option.value} className="flex items-center space-x-2 p-3 border rounded-lg hover:bg-gray-50">
                      <RadioGroupItem value={option.value} />
                      <div className="text-center">
                        <div className="text-2xl mb-1">{option.icon}</div>
                        <Label className="text-sm">{option.label}</Label>
                      </div>
                    </div>
                  ))}
                </div>
              </RadioGroup>
              {errors.overall_satisfaction && (
                <p className="text-sm text-red-600 mt-1">{errors.overall_satisfaction.message}</p>
              )}
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold mb-4">Education Quality Assessment</h3>
            
            <div className="space-y-4">
              <div>
                <Label>How would you rate the quality of teaching?</Label>
                <RadioGroup
                  value={watch('teaching_quality')}
                  onValueChange={(value) => setValue('teaching_quality', value as 'very_poor' | 'poor' | 'fair' | 'good' | 'excellent')}
                  className="mt-2"
                >
                  <div className="flex space-x-4">
                    {[
                      { value: 'very_poor', label: 'Very Poor' },
                      { value: 'poor', label: 'Poor' },
                      { value: 'fair', label: 'Fair' },
                      { value: 'good', label: 'Good' },
                      { value: 'excellent', label: 'Excellent' }
                    ].map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} />
                        <Label>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label>How would you rate the learning materials and resources?</Label>
                <RadioGroup
                  value={watch('learning_materials')}
                  onValueChange={(value) => setValue('learning_materials', value as 'very_poor' | 'poor' | 'fair' | 'good' | 'excellent')}
                  className="mt-2"
                >
                  <div className="flex space-x-4">
                    {[
                      { value: 'very_poor', label: 'Very Poor' },
                      { value: 'poor', label: 'Poor' },
                      { value: 'fair', label: 'Fair' },
                      { value: 'good', label: 'Good' },
                      { value: 'excellent', label: 'Excellent' }
                    ].map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} />
                        <Label>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label>How would you rate the school facilities?</Label>
                <RadioGroup
                  value={watch('school_facilities')}
                  onValueChange={(value) => setValue('school_facilities', value as 'very_poor' | 'poor' | 'fair' | 'good' | 'excellent')}
                  className="mt-2"
                >
                  <div className="flex space-x-4">
                    {[
                      { value: 'very_poor', label: 'Very Poor' },
                      { value: 'poor', label: 'Poor' },
                      { value: 'fair', label: 'Fair' },
                      { value: 'good', label: 'Good' },
                      { value: 'excellent', label: 'Excellent' }
                    ].map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} />
                        <Label>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold mb-4">Program Impact</h3>
            
            <div className="space-y-4">
              <div>
                <Label>How effective do you think the education program has been?</Label>
                <RadioGroup
                  value={watch('program_effectiveness')}
                  onValueChange={(value) => setValue('program_effectiveness', value as 'not_effective' | 'slightly_effective' | 'moderately_effective' | 'very_effective' | 'extremely_effective')}
                  className="mt-2"
                >
                  <div className="space-y-2">
                    {[
                      { value: 'not_effective', label: 'Not Effective' },
                      { value: 'slightly_effective', label: 'Slightly Effective' },
                      { value: 'moderately_effective', label: 'Moderately Effective' },
                      { value: 'very_effective', label: 'Very Effective' },
                      { value: 'extremely_effective', label: 'Extremely Effective' }
                    ].map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} />
                        <Label>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label>Compared to before the program, how is student progress now?</Label>
                <RadioGroup
                  value={watch('student_progress')}
                  onValueChange={(value) => setValue('student_progress', value as 'much_worse' | 'worse' | 'same' | 'better' | 'much_better')}
                  className="mt-2"
                >
                  <div className="space-y-2">
                    {[
                      { value: 'much_worse', label: 'Much Worse' },
                      { value: 'worse', label: 'Worse' },
                      { value: 'same', label: 'About the Same' },
                      { value: 'better', label: 'Better' },
                      { value: 'much_better', label: 'Much Better' }
                    ].map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} />
                        <Label>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label>How would you rate community involvement in the program?</Label>
                <RadioGroup
                  value={watch('community_involvement')}
                  onValueChange={(value) => setValue('community_involvement', value as 'very_low' | 'low' | 'moderate' | 'high' | 'very_high')}
                  className="mt-2"
                >
                  <div className="flex space-x-4">
                    {[
                      { value: 'very_low', label: 'Very Low' },
                      { value: 'low', label: 'Low' },
                      { value: 'moderate', label: 'Moderate' },
                      { value: 'high', label: 'High' },
                      { value: 'very_high', label: 'Very High' }
                    ].map((option) => (
                      <div key={option.value} className="flex items-center space-x-2">
                        <RadioGroupItem value={option.value} />
                        <Label>{option.label}</Label>
                      </div>
                    ))}
                  </div>
                </RadioGroup>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold mb-4">Additional Feedback</h3>
            
            <div className="space-y-4">
              <div>
                <Label htmlFor="most_valuable_aspect">What has been the most valuable aspect of the education program?</Label>
                <Textarea {...register('most_valuable_aspect')} className="mt-2" />
              </div>

              <div>
                <Label htmlFor="areas_for_improvement">What areas need improvement?</Label>
                <Textarea {...register('areas_for_improvement')} className="mt-2" />
              </div>

              <div>
                <Label htmlFor="additional_support_needed">What additional support is needed?</Label>
                <Textarea {...register('additional_support_needed')} className="mt-2" />
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox {...register('would_recommend')} />
                <Label>Would you recommend this program to others?</Label>
              </div>

              <div>
                <Label htmlFor="additional_comments">Any additional comments or suggestions?</Label>
                <Textarea {...register('additional_comments')} className="mt-2" />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Education Program Feedback Survey</span>
          </DialogTitle>
          <DialogDescription>
            Your feedback helps us improve our education programs and better serve the community
          </DialogDescription>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="flex items-center space-x-2 mb-6">
          {[1, 2, 3, 4].map((step) => (
            <div key={step} className="flex items-center">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                currentSection >= step ? 'bg-ilead-green text-white' : 'bg-gray-200 text-gray-600'
              }`}>
                {step}
              </div>
              {step < 4 && (
                <div className={`h-px w-8 ${currentSection > step ? 'bg-ilead-green' : 'bg-gray-200'}`} />
              )}
            </div>
          ))}
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {renderSection()}

          {/* Form Actions */}
          <div className="flex justify-between">
            <div>
              {currentSection > 1 && (
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setCurrentSection(currentSection - 1)}
                >
                  Previous
                </Button>
              )}
            </div>
            
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              
              {currentSection < 4 ? (
                <Button 
                  type="button" 
                  onClick={() => setCurrentSection(currentSection + 1)}
                  className="bg-ilead-green hover:bg-ilead-dark-green"
                >
                  Next
                </Button>
              ) : (
                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="bg-ilead-green hover:bg-ilead-dark-green"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Submitting...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Submit Survey
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default SurveyForm;
