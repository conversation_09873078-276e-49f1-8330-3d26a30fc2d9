import React from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Package, TrendingDown, Eye, Edit } from 'lucide-react';
import { BookWithInventory } from '@/hooks/useBookOperations';
import { formatBookLanguage } from '@/types/book';

interface LowStockAlertProps {
  lowStockBooks: BookWithInventory[];
  onViewBook: (book: BookWithInventory) => void;
  onEditBook: (book: BookWithInventory) => void;
  onUpdateInventory: (book: BookWithInventory) => void;
}

const LowStockAlert: React.FC<LowStockAlertProps> = ({
  lowStockBooks,
  onViewBook,
  onEditBook,
  onUpdateInventory,
}) => {
  if (lowStockBooks.length === 0) {
    return null;
  }

  const criticalStockBooks = lowStockBooks.filter(book => book.available_quantity === 0);
  const lowStockOnlyBooks = lowStockBooks.filter(book => book.available_quantity > 0);

  return (
    <div className="space-y-4">
      {/* Critical Stock Alert */}
      {criticalStockBooks.length > 0 && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Critical Alert:</strong> {criticalStockBooks.length} book(s) are completely out of stock and need immediate attention.
          </AlertDescription>
        </Alert>
      )}

      {/* Low Stock Alert */}
      {lowStockOnlyBooks.length > 0 && (
        <Alert className="border-yellow-200 bg-yellow-50">
          <AlertTriangle className="h-4 w-4 text-yellow-600" />
          <AlertDescription className="text-yellow-800">
            <strong>Low Stock Warning:</strong> {lowStockOnlyBooks.length} book(s) are running low on stock.
          </AlertDescription>
        </Alert>
      )}

      {/* Low Stock Books List */}
      <Card>
        <CardHeader className="pb-3">
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingDown className="h-5 w-5 text-orange-600" />
            Low Stock Books ({lowStockBooks.length})
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {lowStockBooks.map((book) => (
              <div
                key={book.id}
                className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{book.title}</h4>
                      <p className="text-sm text-gray-600">by {book.author}</p>
                      <div className="flex items-center gap-2 mt-1">
                        <span className="text-xs text-gray-500">
                          {formatBookLanguage(book.language)}
                        </span>
                      </div>
                    </div>
                    
                    <div className="text-center">
                      <div className="flex items-center gap-2">
                        {book.available_quantity === 0 ? (
                          <Badge variant="destructive">Out of Stock</Badge>
                        ) : (
                          <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                            Low Stock
                          </Badge>
                        )}
                      </div>
                      <div className="mt-1 text-sm text-gray-600">
                        <span className="font-medium">{book.available_quantity}</span> / {book.total_quantity} available
                      </div>
                      <div className="text-xs text-gray-500">
                        Threshold: {book.minimum_threshold}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewBook(book)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onUpdateInventory(book)}
                    className="text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                  >
                    <Package className="h-4 w-4 mr-1" />
                    Restock
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onEditBook(book)}
                  >
                    <Edit className="h-4 w-4 mr-1" />
                    Edit
                  </Button>
                </div>
              </div>
            ))}
          </div>

          {/* Summary Statistics */}
          <div className="mt-6 pt-4 border-t">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="text-center p-3 bg-red-50 rounded">
                <p className="font-medium text-red-600">{criticalStockBooks.length}</p>
                <p className="text-xs text-gray-600">Out of Stock</p>
              </div>
              <div className="text-center p-3 bg-yellow-50 rounded">
                <p className="font-medium text-yellow-600">{lowStockOnlyBooks.length}</p>
                <p className="text-xs text-gray-600">Low Stock</p>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded">
                <p className="font-medium text-blue-600">
                  {lowStockBooks.reduce((sum, book) => sum + book.available_quantity, 0)}
                </p>
                <p className="text-xs text-gray-600">Total Available</p>
              </div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="mt-4 pt-4 border-t">
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Filter to show only low stock books
                  // This would be handled by the parent component
                }}
                className="text-orange-600 hover:text-orange-700 hover:bg-orange-50"
              >
                <TrendingDown className="h-4 w-4 mr-1" />
                View All Low Stock
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Export low stock report
                  // This would be handled by the parent component
                }}
              >
                <Package className="h-4 w-4 mr-1" />
                Export Report
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default LowStockAlert;
