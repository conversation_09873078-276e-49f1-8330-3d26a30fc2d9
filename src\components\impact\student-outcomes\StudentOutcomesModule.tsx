import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import {
  GraduationCap,
  Plus,
  BarChart3,
  TrendingUp,
  Users,
  BookOpen,
  Calculator,
  FileText
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import AssessmentForm from './AssessmentForm';
import LearningProgressChart from './LearningProgressChart';
import GradeProgressionTracker from './GradeProgressionTracker';
import { StudentLearningOutcome } from '@/types/impact';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { MetricCard } from '../shared';

interface StudentOutcomesModuleProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const StudentOutcomesModule: React.FC<StudentOutcomesModuleProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [showAssessmentForm, setShowAssessmentForm] = useState(false);

  // Fetch student learning outcomes
  const { data: learningOutcomes, isLoading } = useQuery<StudentLearningOutcome[]>({
    queryKey: ['student-learning-outcomes', schoolId, dateRange],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_student_learning_outcomes', {
        p_school_id: schoolId,
        p_academic_year: dateRange.start.getFullYear().toString()
      });
      
      if (error) throw error;
      return data || [];
    },
    enabled: canViewAllData
  });

  // Calculate summary statistics
  const summaryStats = React.useMemo(() => {
    if (!learningOutcomes?.length) {
      return {
        totalStudents: 0,
        avgImprovement: 0,
        subjectsTracked: 0,
        schoolsInvolved: 0,
        topPerformingSubject: 'N/A',
        improvementTrend: 'stable'
      };
    }

    const totalStudents = learningOutcomes.reduce((sum, outcome) => sum + outcome.total_students, 0);
    const avgImprovement = learningOutcomes.reduce((sum, outcome) => sum + outcome.avg_improvement, 0) / learningOutcomes.length;
    const subjectsTracked = new Set(learningOutcomes.map(outcome => outcome.subject)).size;
    const schoolsInvolved = new Set(learningOutcomes.map(outcome => outcome.school_id)).size;
    
    const topPerformingSubject = learningOutcomes.reduce((best, current) => 
      current.avg_improvement > best.avg_improvement ? current : best
    ).subject;

    const improvementTrend = avgImprovement > 15 ? 'improving' : avgImprovement > 10 ? 'stable' : 'declining';

    return {
      totalStudents,
      avgImprovement: Math.round(avgImprovement * 10) / 10,
      subjectsTracked,
      schoolsInvolved,
      topPerformingSubject,
      improvementTrend
    };
  }, [learningOutcomes]);



  // Access control is now handled by AdminOnlyWrapper

  return (
    <PageLayout>
      <PageHeader
        title="Student Learning Outcomes"
        description="Track literacy, numeracy, and academic progress"
        icon={GraduationCap}
        actions={[
          {
            label: 'New Assessment',
            onClick: () => setShowAssessmentForm(true),
            icon: Plus,
          }
        ]}
      />

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Students Assessed"
          value={summaryStats.totalStudents.toLocaleString()}
          icon={Users}
          color="blue"
        />
        <MetricCard
          title="Average Learning Improvement"
          value={`${summaryStats.avgImprovement}%`}
          icon={TrendingUp}
          color="green"
        />
        <MetricCard
          title="Subjects Tracked"
          value={summaryStats.subjectsTracked}
          icon={BookOpen}
          color="purple"
        />
        <MetricCard
          title="Schools Involved"
          value={summaryStats.schoolsInvolved}
          icon={GraduationCap}
          color="orange"
        />
      </div>

      {/* Performance Insights */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <BarChart3 className="h-5 w-5" />
            <span>Performance Insights</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <h4 className="font-semibold text-green-800">Top Performing Subject</h4>
              <p className="text-2xl font-bold text-green-600 capitalize">
                {summaryStats.topPerformingSubject}
              </p>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800">Improvement Trend</h4>
              <p className="text-2xl font-bold text-blue-600 capitalize">
                {summaryStats.improvementTrend}
              </p>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <h4 className="font-semibold text-purple-800">Assessment Coverage</h4>
              <p className="text-2xl font-bold text-purple-600">
                {summaryStats.subjectsTracked} Subjects
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Detailed Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="progress">Learning Progress</TabsTrigger>
          <TabsTrigger value="progression">Grade Progression</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Learning Outcomes by Subject</CardTitle>
              <CardDescription>
                Detailed breakdown of student performance across different subjects
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-ilead-green mx-auto"></div>
                  <p className="mt-2 text-gray-600">Loading outcomes data...</p>
                </div>
              ) : learningOutcomes?.length ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2">School</th>
                        <th className="text-left p-2">Subject</th>
                        <th className="text-right p-2">Students</th>
                        <th className="text-right p-2">Pre-Score</th>
                        <th className="text-right p-2">Post-Score</th>
                        <th className="text-right p-2">Improvement</th>
                        <th className="text-right p-2">Success Rate</th>
                      </tr>
                    </thead>
                    <tbody>
                      {learningOutcomes.map((outcome, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="p-2 font-medium">{outcome.school_name}</td>
                          <td className="p-2 capitalize">{outcome.subject}</td>
                          <td className="p-2 text-right">{outcome.total_students}</td>
                          <td className="p-2 text-right">{outcome.avg_pre_score.toFixed(1)}%</td>
                          <td className="p-2 text-right">{outcome.avg_post_score.toFixed(1)}%</td>
                          <td className="p-2 text-right">
                            <span className={`font-medium ${
                              outcome.avg_improvement > 15 ? 'text-green-600' : 
                              outcome.avg_improvement > 10 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {outcome.avg_improvement.toFixed(1)}%
                            </span>
                          </td>
                          <td className="p-2 text-right">{outcome.improvement_rate.toFixed(1)}%</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No learning outcomes data available</p>
                  <Button 
                    onClick={() => setShowAssessmentForm(true)}
                    className="mt-4"
                    variant="outline"
                  >
                    Add First Assessment
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="progress">
          <LearningProgressChart 
            data={learningOutcomes || []}
            schoolId={schoolId}
            dateRange={dateRange}
          />
        </TabsContent>

        <TabsContent value="progression">
          <GradeProgressionTracker 
            schoolId={schoolId}
            dateRange={dateRange}
          />
        </TabsContent>
      </Tabs>

      {/* Assessment Form Modal */}
      {showAssessmentForm && (
        <AssessmentForm
          onClose={() => setShowAssessmentForm(false)}
          schoolId={schoolId}
        />
      )}
    </PageLayout>
  );
};

export default StudentOutcomesModule;
