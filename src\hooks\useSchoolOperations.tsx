
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

// Type definitions for the hook
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type AdminDivision = Database['public']['Functions']['get_admin_divisions']['Returns'][0];
type AddSchoolArgs = Database['public']['Functions']['add_school']['Args'];

interface CurrentUser {
  id: string;
  [key: string]: unknown;
}

interface ContactInfo {
  id: string;
  name: string;
  phone: string;
  email: string;
}

interface SchoolFormData {
  // Required fields
  name: string;
  school_type: Database['public']['Enums']['school_type'];
  division_id: string;
  student_count: string | number;
  champion_teacher_count: string | number;
  champion_teachers?: ContactInfo[]; // At least 1 champion teacher required

  // Optional fields
  code?: string;
  school_category?: 'day' | 'boarding' | 'both';
  contact_phone?: string;
  email?: string;
  head_teacher_name?: string;
  head_teacher_phone?: string;
  head_teacher_email?: string;
  deputy_head_teacher_name?: string;
  deputy_head_teacher_phone?: string;
  deputy_head_teacher_email?: string;
  date_joined_ilead?: string;
  ownership_type?: string;
  location_coordinates?: string;
  is_partner_managed?: boolean;
  partner_name?: string;
  field_staff_id?: string;
  assistant_champion_teachers?: ContactInfo[];
}

export const useSchoolOperations = (currentUser: CurrentUser | null) => {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch schools using RPC function
  const schoolsQuery = useQuery({
    queryKey: ['schools'],
    queryFn: async (): Promise<SchoolWithDivision[]> => {
      const { data, error } = await supabase
        .rpc('get_schools_with_divisions');

      if (error) {
        console.error('Error fetching schools:', error);
        throw error;
      }
      return data || [];
    },
  });

  // Fetch administrative divisions using RPC function
  const divisionsQuery = useQuery({
    queryKey: ['divisions'],
    queryFn: async (): Promise<AdminDivision[]> => {
      const { data, error } = await supabase
        .rpc('get_admin_divisions');

      if (error) {
        console.error('Error fetching divisions:', error);
        throw error;
      }
      return data || [];
    },
  });

  // Add school mutation
  const addSchoolMutation = useMutation({
    mutationFn: async (schoolData: SchoolFormData): Promise<string> => {
      // Parse coordinates if provided
      let coordinates = null;
      if (schoolData.location_coordinates) {
        const coords = schoolData.location_coordinates.split(',').map(c => parseFloat(c.trim()));
        if (coords.length === 2 && !isNaN(coords[0]) && !isNaN(coords[1])) {
          coordinates = `(${coords[0]}, ${coords[1]})`;
        }
      }

      // Ensure champion_teachers is a valid array and clean up the data
      const championTeachers = Array.isArray(schoolData.champion_teachers)
        ? schoolData.champion_teachers.map(ct => ({
            name: ct.name,
            phone: ct.phone || '',
            email: ct.email || ''
          }))
        : [];

      const assistantChampionTeachers = Array.isArray(schoolData.assistant_champion_teachers)
        ? schoolData.assistant_champion_teachers.map(act => ({
            name: act.name,
            phone: act.phone || '',
            email: act.email || ''
          }))
        : [];

      // Debug logging
      console.log('Champion teachers being sent:', championTeachers);
      console.log('Assistant champion teachers being sent:', assistantChampionTeachers);

      const { data, error } = await supabase
        .rpc('add_school_enhanced', {
          p_name: schoolData.name,
          p_school_type: schoolData.school_type,
          p_division_id: schoolData.division_id,
          p_student_count: parseInt(schoolData.student_count.toString()),
          p_champion_teacher_count: parseInt(schoolData.champion_teacher_count.toString()),
          p_code: schoolData.code || null,
          p_school_category: schoolData.school_category || 'day',
          p_contact_phone: schoolData.contact_phone || null,
          p_email: schoolData.email || null,
          p_head_teacher_name: schoolData.head_teacher_name || null,
          p_head_teacher_phone: null, // Field doesn't exist in database
          p_head_teacher_email: null, // Field doesn't exist in database
          p_deputy_head_teacher_name: schoolData.deputy_head_teacher_name || null,
          p_deputy_head_teacher_phone: null, // Field doesn't exist in database
          p_deputy_head_teacher_email: null, // Field doesn't exist in database
          p_date_joined_ilead: schoolData.date_joined_ilead || null,
          p_ownership_type: schoolData.ownership_type || null,
          p_location_coordinates: coordinates,
          p_is_partner_managed: schoolData.is_partner_managed || false,
          p_partner_name: schoolData.partner_name || null,
          p_field_staff_id: null, // Field doesn't exist in database
          p_champion_teachers: championTeachers,
          p_assistant_champion_teachers: assistantChampionTeachers,
        });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      queryClient.invalidateQueries({ queryKey: ['schools-filtered'] });
      queryClient.invalidateQueries({ queryKey: ['school-statistics'] });
      toast({
        title: "Success",
        description: "School added successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to add school",
        variant: "destructive",
      });
    },
  });

  return {
    schools: schoolsQuery.data || [],
    divisions: divisionsQuery.data || [],
    isLoadingSchools: schoolsQuery.isLoading,
    isLoadingDivisions: divisionsQuery.isLoading,
    addSchool: addSchoolMutation.mutate,
    isAddingSchool: addSchoolMutation.isPending,
  };
};
