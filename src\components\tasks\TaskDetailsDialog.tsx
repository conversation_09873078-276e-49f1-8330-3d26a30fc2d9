import React from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import TaskDetailsOverview from './TaskDetailsOverview';
import TaskCommentsSection from '../comments/TaskCommentsSection';
import TaskAttachmentsSection from './TaskAttachmentsSection';
import { Database } from '@/integrations/supabase/types';

interface TaskDetailsDialogProps {
  taskId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface TaskDetails {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comments: Array<{
    id: string;
    comment: string;
    user_name: string;
    created_at: string;
  }>;
  attachments: Array<{
    id: string;
    file_name: string;
    file_url: string;
    file_size: number;
    uploaded_by_name: string;
    created_at: string;
  }>;
}

const TaskDetailsDialog: React.FC<TaskDetailsDialogProps> = ({
  taskId,
  open,
  onOpenChange
}) => {
  const { data: taskDetails, isLoading, error, refetch } = useQuery({
    queryKey: ['task-details', taskId],
    queryFn: async () => {
      if (!taskId) return null;
      
      console.log('🔍 Fetching task details for taskId:', taskId);
      
      const { data, error } = await supabase
        .rpc('get_task_details', { p_task_id: taskId });
      
      if (error) {
        console.error('❌ Error fetching task details:', error);
        throw error;
      }
      
      console.log('✅ Task details fetched successfully:', data);
      return data?.[0] as TaskDetails || null;
    },
    enabled: !!taskId && open,
  });

  if (!open || !taskId) return null;

  if (error) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[700px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Error Loading Task</DialogTitle>
          </DialogHeader>
          <div className="text-center py-8">
            <p className="text-red-600 mb-4">Failed to load task details</p>
            <Button onClick={() => refetch()} variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  if (isLoading || !taskDetails) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto mx-4 sm:mx-0">
          <DialogHeader>
            <DialogTitle>Loading Task Details...</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-20 bg-gray-100 rounded animate-pulse" />
            ))}
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto mx-4 sm:mx-0">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {taskDetails.title}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => refetch()}
              className="ml-auto"
            >
              <RefreshCw className="h-4 w-4" />
            </Button>
          </DialogTitle>
          <DialogDescription>
            Task details and collaboration
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Task Overview */}
          <TaskDetailsOverview taskDetails={taskDetails} />

          {/* Comments Section */}
          <TaskCommentsSection 
            taskId={taskId} 
            comments={taskDetails.comments} 
          />

          {/* Attachments Section */}
          <TaskAttachmentsSection attachments={taskDetails.attachments} />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default TaskDetailsDialog;
