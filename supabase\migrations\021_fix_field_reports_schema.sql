-- Fix Field Reports Schema Migration
-- Migration 021: Add missing columns to field_reports table for field staff daily reports
-- Enhanced with Activity Report format adoption

-- Add missing columns to field_reports table to support field staff daily reports
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS attendance_id UUID REFERENCES field_staff_attendance(id);
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS staff_id UUID REFERENCES profiles(id);
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS report_date DATE;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS activity_type field_activity_type;

-- Round table training data
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS round_table_sessions INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS total_students INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS students_per_session INTEGER DEFAULT 8;

-- Daily activities
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS activities_conducted TEXT[] DEFAULT '{}';
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS topics_covered TEXT[] DEFAULT '{}';

-- Challenges and wins
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS challenges TEXT;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS wins TEXT;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS observations TEXT;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS lessons_learned TEXT;

-- Follow-up actions
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS follow_up_required BOOLEAN DEFAULT false;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS follow_up_actions TEXT;

-- Additional metadata for field staff reports
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS notes TEXT;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS offline_sync BOOLEAN DEFAULT false;

-- Update the RPC function to use correct column names
CREATE OR REPLACE FUNCTION field_staff_checkout(
    p_attendance_id UUID,
    p_latitude DECIMAL(10,8) DEFAULT NULL,
    p_longitude DECIMAL(11,8) DEFAULT NULL,
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    -- Field report data
    p_activity_type field_activity_type,
    p_round_table_sessions INTEGER DEFAULT 0,
    p_total_students INTEGER DEFAULT 0,
    p_students_per_session INTEGER DEFAULT 8,
    p_activities_conducted TEXT[] DEFAULT '{}',
    p_topics_covered TEXT[] DEFAULT '{}',
    p_challenges TEXT DEFAULT NULL,
    p_wins TEXT DEFAULT NULL,
    p_observations TEXT DEFAULT NULL,
    p_lessons_learned TEXT DEFAULT NULL,
    p_follow_up_required BOOLEAN DEFAULT false,
    p_follow_up_actions TEXT DEFAULT NULL,
    p_photos TEXT[] DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT false
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_record RECORD;
    field_report_id UUID;
    report_title TEXT;
BEGIN
    -- Get attendance record and verify it belongs to current user
    SELECT * INTO attendance_record
    FROM field_staff_attendance
    WHERE id = p_attendance_id AND staff_id = auth.uid();

    IF NOT FOUND THEN
        RAISE EXCEPTION 'Attendance record not found or access denied';
    END IF;

    -- Check if already checked out
    IF attendance_record.check_out_time IS NOT NULL THEN
        RAISE EXCEPTION 'Already checked out for this attendance record';
    END IF;

    -- Update attendance record with check-out information
    UPDATE field_staff_attendance
    SET 
        check_out_time = NOW(),
        check_out_location = CASE 
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL 
            THEN POINT(p_longitude, p_latitude) 
            ELSE NULL 
        END,
        check_out_accuracy = p_accuracy,
        check_out_address = p_address,
        total_duration_minutes = EXTRACT(EPOCH FROM (NOW() - check_in_time)) / 60,
        status = 'completed',
        notes = COALESCE(notes || ' | ' || p_notes, p_notes, notes),
        updated_at = NOW()
    WHERE id = p_attendance_id;

    -- Generate appropriate report title based on activity type
    report_title := CASE p_activity_type
        WHEN 'leadership_training' THEN 'Leadership Training Session Report'
        WHEN 'school_visit' THEN 'School Visit Report'
        WHEN 'community_engagement' THEN 'Community Engagement Report'
        WHEN 'assessment' THEN 'Assessment Report'
        WHEN 'meeting' THEN 'Meeting Report'
        ELSE 'Field Activity Report'
    END;

    -- Create field report
    INSERT INTO field_reports (
        attendance_id, staff_id, school_id, report_date, activity_type,
        round_table_sessions, total_students, students_per_session,
        activities_conducted, topics_covered, challenges,
        wins, observations, lessons_learned,
        follow_up_required, follow_up_actions, photos,
        notes, offline_sync,
        -- Legacy columns for compatibility
        title, description, report_type, reported_by,
        gps_coordinates, created_at, updated_at
    )
    VALUES (
        p_attendance_id, auth.uid(), attendance_record.school_id, CURRENT_DATE, p_activity_type,
        p_round_table_sessions, p_total_students, p_students_per_session,
        p_activities_conducted, p_topics_covered, p_challenges,
        p_wins, p_observations, p_lessons_learned,
        p_follow_up_required, p_follow_up_actions, p_photos,
        p_notes, p_offline_sync,
        -- Legacy columns
        report_title, 
        CONCAT('Field report for ', p_activity_type, ' activity on ', CURRENT_DATE),
        'departure',
        auth.uid(),
        CASE 
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL 
            THEN POINT(p_longitude, p_latitude) 
            ELSE NULL 
        END,
        NOW(), NOW()
    )
    RETURNING id INTO field_report_id;

    -- Update or create daily timesheet
    PERFORM update_daily_timesheet(auth.uid(), CURRENT_DATE);

    -- Return the field report ID
    RETURN field_report_id;
END;
$$;

-- Add comment explaining the function
COMMENT ON FUNCTION field_staff_checkout IS 'Field staff check-out function with optional location capture and field report creation. Compatible with both legacy and new field_reports schema.';
