// Impact Measurement Types
// These types correspond to the database schema for impact tracking

export type AssessmentType = 'baseline' | 'midterm' | 'endline' | 'quarterly' | 'annual';

export type SubjectType = 
  | 'literacy' 
  | 'numeracy' 
  | 'english' 
  | 'mathematics' 
  | 'science' 
  | 'social_studies'
  | 'life_skills';

export type ImprovementType = 
  | 'infrastructure' 
  | 'equipment' 
  | 'resources' 
  | 'facilities' 
  | 'technology'
  | 'furniture'
  | 'utilities';

export type LeadershipProgramType =
  | 'leadership_skills'
  | 'communication'
  | 'teamwork'
  | 'problem_solving'
  | 'critical_thinking'
  | 'public_speaking'
  | 'project_management'
  | 'entrepreneurship'
  | 'civic_engagement'
  | 'peer_mentoring';

export type FeedbackType = 
  | 'student' 
  | 'parent' 
  | 'teacher' 
  | 'community_member'
  | 'school_administrator';

export type SatisfactionRating =
  | 'very_dissatisfied'
  | 'dissatisfied'
  | 'neutral'
  | 'satisfied'
  | 'very_satisfied';

// New types for NGO reporting requirements
export type FieldReportType =
  | 'arrival'
  | 'departure'
  | 'activity'
  | 'incident'
  | 'inspection'
  | 'training'
  | 'distribution';

export type ActivityReportType =
  | 'leadership_training'
  | 'school_visit'
  | 'community_engagement'
  | 'capacity_building'
  | 'monitoring_evaluation';

export type ReportStatus =
  | 'draft'
  | 'submitted'
  | 'reviewed'
  | 'approved'
  | 'published';

export type GenderType = 'male' | 'female' | 'other' | 'prefer_not_to_say';

export type LeadershipSkill =
  | 'communication'
  | 'teamwork'
  | 'problem_solving'
  | 'critical_thinking'
  | 'public_speaking'
  | 'decision_making'
  | 'conflict_resolution'
  | 'project_management'
  | 'time_management'
  | 'emotional_intelligence';

// Student Learning Outcomes
export interface Student {
  id: string;
  student_number?: string;
  first_name: string;
  last_name: string;
  date_of_birth?: string;
  gender?: string;
  grade_level: number;
  school_id: string;
  enrollment_date: string;
  status?: string;
  guardian_name?: string;
  guardian_contact?: string;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface StudentAssessment {
  id: string;
  student_id: string;
  school_id: string;
  assessment_type: AssessmentType;
  subject: SubjectType;
  grade_level: number;
  pre_assessment_score?: number;
  post_assessment_score?: number;
  improvement_percentage?: number;
  assessment_date: string;
  academic_year: string;
  assessor_name?: string;
  notes?: string;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface GradeProgression {
  id: string;
  student_id: string;
  school_id: string;
  from_grade: number;
  to_grade: number;
  academic_year: string;
  progression_date: string;
  retention_status?: boolean;
  dropout_status?: boolean;
  dropout_reason?: string;
  created_by: string;
  created_at?: string;
}

// School Performance
export interface SchoolInfrastructureImprovement {
  id: string;
  school_id: string;
  improvement_type: ImprovementType;
  title: string;
  description: string;
  before_condition?: string;
  after_condition?: string;
  before_photos?: string[];
  after_photos?: string[];
  improvement_date: string;
  completion_date?: string;
  cost_estimate?: number;
  actual_cost?: number;
  funding_source?: string;
  contractor_details?: string;
  impact_description?: string;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface SchoolAttendanceRecord {
  id: string;
  school_id: string;
  record_date: string;
  total_enrolled: number;
  total_present: number;
  attendance_rate: number;
  grade_level?: number;
  gender?: string;
  weather_condition?: string;
  special_events?: string;
  recorded_by: string;
  created_at?: string;
}

export interface TeacherStudentRatio {
  id: string;
  school_id: string;
  record_date: string;
  total_teachers: number;
  total_students: number;
  ratio_value: number;
  grade_level?: number;
  subject?: SubjectType;
  qualified_teachers?: number;
  notes?: string;
  recorded_by: string;
  created_at?: string;
}

// Student Leadership Training
export interface StudentLeadershipProgram {
  id: string;
  program_name: string;
  program_type: LeadershipProgramType;
  description?: string;
  duration_hours?: number;
  start_date: string;
  end_date: string;
  facilitator_name?: string;
  facilitator_organization?: string;
  target_participants?: number;
  actual_participants?: number;
  training_materials?: string[];
  venue?: string;
  cost?: number;
  funding_source?: string;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface StudentLeadershipParticipation {
  id: string;
  leadership_program_id: string;
  student_name: string;
  student_id?: string;
  school_id: string;
  attendance_percentage?: number;
  pre_program_score?: number;
  post_program_score?: number;
  improvement_score?: number;
  certification_received?: boolean;
  certification_date?: string;
  feedback_rating?: SatisfactionRating;
  feedback_comments?: string;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface ClassroomObservation {
  id: string;
  teacher_name: string;
  school_id: string;
  observation_date: string;
  observer_name: string;
  subject: SubjectType;
  grade_level: number;
  lesson_preparation_score: number;
  teaching_methods_score: number;
  student_engagement_score: number;
  classroom_management_score: number;
  assessment_methods_score: number;
  overall_score?: number;
  strengths?: string;
  areas_for_improvement?: string;
  recommendations?: string;
  follow_up_required?: boolean;
  follow_up_date?: string;
  created_by: string;
  created_at?: string;
}

// Beneficiary Feedback
export interface BeneficiaryFeedbackSurvey {
  id: string;
  survey_title: string;
  survey_description?: string;
  target_audience: FeedbackType;
  school_id?: string;
  survey_questions: Record<string, unknown>;
  start_date: string;
  end_date: string;
  is_active?: boolean;
  response_count?: number;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface SurveyResponse {
  id: string;
  survey_id: string;
  respondent_type: FeedbackType;
  school_id?: string;
  respondent_name?: string;
  respondent_contact?: string;
  responses: Record<string, unknown>;
  overall_satisfaction?: SatisfactionRating;
  additional_comments?: string;
  response_date: string;
  location_coordinates?: [number, number];
  created_at?: string;
}

// Longitudinal Tracking
export interface StudentCohort {
  id: string;
  cohort_name: string;
  cohort_year: number;
  school_id: string;
  starting_grade: number;
  initial_student_count: number;
  current_student_count: number;
  description?: string;
  tracking_start_date: string;
  tracking_end_date?: string;
  is_active?: boolean;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface LongitudinalStudentTracking {
  id: string;
  student_id: string;
  cohort_id: string;
  tracking_year: number;
  grade_level: number;
  attendance_rate?: number;
  academic_performance_score?: number;
  literacy_level?: string;
  numeracy_level?: string;
  behavioral_assessment?: string;
  health_status?: string;
  family_situation?: string;
  risk_factors?: string[];
  interventions_received?: string[];
  tracking_date: string;
  created_by: string;
  created_at?: string;
}

// Impact Indicators
export interface ImpactIndicator {
  id: string;
  indicator_name: string;
  indicator_category: string;
  description?: string;
  measurement_unit?: string;
  target_value?: number;
  baseline_value?: number;
  current_value?: number;
  school_id?: string;
  measurement_frequency?: string;
  last_measured_date?: string;
  next_measurement_date?: string;
  data_source?: string;
  responsible_person?: string;
  is_active?: boolean;
  created_by: string;
  created_at?: string;
  updated_at?: string;
}

export interface ImpactMeasurement {
  id: string;
  indicator_id: string;
  measurement_value: number;
  measurement_date: string;
  measurement_period?: string;
  data_quality_score?: number;
  verification_status?: boolean;
  verified_by?: string;
  verification_date?: string;
  notes?: string;
  supporting_documents?: string[];
  created_by: string;
  created_at?: string;
}

// Analytics Types
export interface StudentLearningOutcome {
  school_id: string;
  school_name: string;
  subject: SubjectType;
  total_students: number;
  avg_pre_score: number;
  avg_post_score: number;
  avg_improvement: number;
  students_improved: number;
  improvement_rate: number;
}

export interface SchoolPerformanceMetric {
  school_id: string;
  school_name: string;
  avg_attendance_rate: number;
  total_improvements: number;
  avg_teacher_student_ratio: number;
  infrastructure_score: number;
  performance_trend: string;
}

export interface StudentLeadershipEffectiveness {
  leadership_program_id: string;
  program_name: string;
  program_type: LeadershipProgramType;
  total_participants: number;
  completion_rate: number;
  avg_improvement_score: number;
  certification_rate: number;
  avg_satisfaction_rating: number;
  effectiveness_score: number;
}

export interface BeneficiaryFeedbackSummary {
  feedback_type: FeedbackType;
  total_responses: number;
  avg_satisfaction_score: number;
  satisfaction_distribution: Record<string, number>;
  common_themes: string[];
}

// New interfaces for NGO reporting requirements

// Field Reports
export interface FieldReport {
  id: string;
  report_type: FieldReportType;
  title: string;
  description: string;
  findings?: string;
  recommendations?: string;
  school_id?: string;
  distribution_id?: string;
  reported_by?: string;
  gps_coordinates?: [number, number]; // [lat, lng]
  photos?: string[];
  metadata?: Record<string, unknown>;
  status?: ReportStatus;
  created_at?: string;
  updated_at?: string;
}

// Activity Reports for structured NGO activity reporting
export interface ActivityReport {
  id: string;
  activity_name: string;
  activity_type: ActivityReportType;
  venue?: string;
  activity_date: string;
  start_time?: string;
  end_time?: string;
  duration_hours?: number;

  // Facilitator Information
  primary_facilitator?: string;
  co_facilitators?: string[];
  facilitator_organization?: string;

  // School and Participant Information
  school_id?: string;
  school_name?: string;
  school_district?: string;
  school_region?: string;

  // Participant Demographics
  total_participants?: number;
  male_participants?: number;
  female_participants?: number;
  grade_levels?: number[];

  // Activity Details
  topics_covered?: string[];
  learning_objectives?: string[];
  activities_conducted?: string[];
  materials_used?: string[];

  // Observations and Feedback
  participant_engagement_level?: number; // 1-5 scale
  key_observations?: string;
  participant_feedback?: string;
  challenges_encountered?: string;
  solutions_implemented?: string;

  // Outcomes and Impact
  lessons_learned?: string;
  recommendations?: string;
  follow_up_actions?: string;
  next_steps?: string;

  // Media and Documentation
  photos?: string[];
  videos?: string[];
  documents?: string[];

  // Administrative
  created_by: string;
  reviewed_by?: string;
  approved_by?: string;
  status?: ReportStatus;
  submission_date?: string;
  review_date?: string;
  approval_date?: string;
  created_at?: string;
  updated_at?: string;
}

// Monthly Reports for automated monthly compilations
export interface MonthlyReport {
  id: string;
  report_month: number;
  report_year: number;
  report_title: string;

  // Summary Statistics
  total_activities?: number;
  total_participants?: number;
  total_schools_reached?: number;
  total_facilitators?: number;

  // Activity Breakdown
  leadership_training_sessions?: number;
  school_visits?: number;
  community_engagements?: number;
  capacity_building_sessions?: number;

  // Participant Demographics Summary
  total_male_participants?: number;
  total_female_participants?: number;
  participant_age_distribution?: Record<string, number>;
  grade_level_distribution?: Record<string, number>;

  // Geographic Coverage
  regions_covered?: string[];
  districts_covered?: string[];
  schools_list?: Record<string, unknown>[];

  // Performance Metrics
  planned_activities?: number;
  achieved_activities?: number;
  achievement_rate?: number;
  average_participant_satisfaction?: number;

  // Challenges and Solutions
  key_challenges?: string;
  solutions_implemented?: string;
  lessons_learned?: string;

  // Forward Planning
  next_month_targets?: string;
  resource_requirements?: string;
  recommendations?: string;

  // Linked Activity Reports
  activity_report_ids?: string[];

  // Administrative
  generated_by: string;
  reviewed_by?: string;
  approved_by?: string;
  status?: ReportStatus;
  generation_date?: string;
  submission_date?: string;
  review_date?: string;
  approval_date?: string;
  created_at?: string;
  updated_at?: string;
}

// Baseline Questionnaires for participant assessments
export interface BaselineQuestionnaire {
  id: string;
  questionnaire_title: string;
  description?: string;
  version?: string;

  // Target Information
  target_program_type?: LeadershipProgramType;
  target_grade_levels?: number[];
  target_age_range?: string;

  // Questionnaire Structure
  demographic_questions?: Record<string, unknown>;
  leadership_skills_matrix?: Record<string, unknown>;
  open_ended_questions?: Record<string, unknown>;
  custom_questions?: Record<string, unknown>;

  // Scoring Configuration
  scoring_criteria?: Record<string, unknown>;
  skill_weights?: Record<string, unknown>;

  // Administrative
  created_by: string;
  is_active?: boolean;
  version_notes?: string;
  created_at?: string;
  updated_at?: string;
}

// Participant Responses for questionnaire data storage
export interface ParticipantResponse {
  id: string;
  questionnaire_id: string;

  // Participant Information
  participant_name: string;
  participant_age?: number;
  participant_gender?: GenderType;
  grade_level?: number;
  school_id?: string;
  school_name?: string;

  // Contact Information
  guardian_name?: string;
  guardian_contact?: string;
  participant_contact?: string;

  // Leadership Program Context
  leadership_program_id?: string;
  program_name?: string;
  assessment_type?: AssessmentType;

  // Response Data
  demographic_responses?: Record<string, unknown>;
  leadership_skill_scores?: Record<string, number>; // 1-5 scale scores
  open_ended_responses?: Record<string, string>;
  custom_responses?: Record<string, unknown>;

  // Calculated Scores
  overall_leadership_score?: number;
  skill_category_scores?: Record<string, number>;
  improvement_areas?: string[];
  strengths?: string[];

  // Administrative
  response_date?: string;
  completed_by?: string;
  completion_time_minutes?: number;
  response_quality_score?: number; // 1-5 scale
  notes?: string;

  // Follow-up Tracking
  follow_up_required?: boolean;
  follow_up_date?: string;
  follow_up_notes?: string;

  created_at?: string;
  updated_at?: string;
}

// Analytics and Summary Types for NGO Reporting
export interface ActivityReportSummary {
  total_activities: number;
  total_participants: number;
  total_male_participants: number;
  total_female_participants: number;
  total_schools_reached: number;
  average_engagement_level: number;
  most_common_topics: string[];
  key_challenges: string[];
  top_recommendations: string[];
}

export interface BaselineQuestionnaireAnalytics {
  total_responses: number;
  average_overall_score: number;
  gender_distribution: Record<string, number>;
  grade_level_distribution: Record<string, number>;
  skill_averages: Record<string, number>;
  improvement_areas_frequency: Record<string, number>;
  strengths_frequency: Record<string, number>;
  completion_rate: number;
}

export interface NGOImpactDashboard {
  period: {
    start_date: string;
    end_date: string;
    duration_months: number;
  };
  overall_impact: {
    total_students_reached: number;
    total_schools_engaged: number;
    total_activities_conducted: number;
    total_training_hours: number;
  };
  student_leadership_impact: {
    programs_completed: number;
    average_improvement_score: number;
    certification_rate: number;
  };
  geographic_reach: {
    regions_covered: string[];
    districts_covered: string[];
  };
  quality_metrics: {
    average_engagement_level: number;
    report_completion_rate: number;
  };
}
