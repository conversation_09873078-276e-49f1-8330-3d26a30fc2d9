import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Wifi, 
  WifiOff, 
  Loader2, 
  RefreshCw, 
  Clock,
  AlertCircle,
  CheckCircle,
  Database
} from 'lucide-react';
import { useOfflineSync } from '@/hooks/field-staff/useOfflineSync';
import { format } from 'date-fns';

interface OfflineStatusIndicatorProps {
  showDetails?: boolean;
  compact?: boolean;
}

const OfflineStatusIndicator: React.FC<OfflineStatusIndicatorProps> = ({
  showDetails = false,
  compact = false
}) => {
  const { syncStatus, syncOfflineData, clearOfflineData } = useOfflineSync();

  const getStatusIcon = () => {
    if (syncStatus.isSyncing) {
      return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }
    
    if (syncStatus.isOnline) {
      return <Wifi className="h-4 w-4 text-green-500" />;
    }
    
    return <WifiOff className="h-4 w-4 text-red-500" />;
  };

  const getStatusText = () => {
    if (syncStatus.isSyncing) {
      return 'Syncing...';
    }
    
    if (syncStatus.isOnline) {
      return syncStatus.pendingItems > 0 ? 'Online (Pending Sync)' : 'Online';
    }
    
    return 'Offline';
  };

  const getStatusColor = () => {
    if (syncStatus.isSyncing) {
      return 'bg-blue-100 text-blue-800';
    }
    
    if (syncStatus.isOnline) {
      return syncStatus.pendingItems > 0 ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800';
    }
    
    return 'bg-red-100 text-red-800';
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        {getStatusIcon()}
        <Badge className={getStatusColor()}>
          {getStatusText()}
        </Badge>
        {syncStatus.pendingItems > 0 && (
          <Badge variant="outline" className="text-xs">
            {syncStatus.pendingItems} pending
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className="border-l-4 border-l-blue-500">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-2">
            {getStatusIcon()}
            <span className="font-medium">Connection Status</span>
            <Badge className={getStatusColor()}>
              {getStatusText()}
            </Badge>
          </div>
          
          {syncStatus.isOnline && syncStatus.pendingItems > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={syncOfflineData}
              disabled={syncStatus.isSyncing}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Sync Now
            </Button>
          )}
        </div>

        {showDetails && (
          <div className="space-y-3">
            {/* Pending Items */}
            {syncStatus.pendingItems > 0 && (
              <div className="flex items-center gap-2 text-sm">
                <Database className="h-4 w-4 text-orange-500" />
                <span className="text-gray-600">
                  {syncStatus.pendingItems} item{syncStatus.pendingItems !== 1 ? 's' : ''} waiting to sync
                </span>
              </div>
            )}

            {/* Last Sync Time */}
            {syncStatus.lastSyncTime && (
              <div className="flex items-center gap-2 text-sm">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-gray-600">
                  Last synced: {format(syncStatus.lastSyncTime, 'MMM d, yyyy h:mm a')}
                </span>
              </div>
            )}

            {/* Status Messages */}
            <div className="space-y-2">
              {!syncStatus.isOnline && (
                <div className="flex items-start gap-2 p-2 bg-red-50 rounded-lg">
                  <AlertCircle className="h-4 w-4 text-red-500 mt-0.5" />
                  <div className="text-sm">
                    <div className="font-medium text-red-800">Offline Mode</div>
                    <div className="text-red-700">
                      Your data will be saved locally and synced when connection is restored.
                    </div>
                  </div>
                </div>
              )}

              {syncStatus.isOnline && syncStatus.pendingItems === 0 && (
                <div className="flex items-start gap-2 p-2 bg-green-50 rounded-lg">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5" />
                  <div className="text-sm">
                    <div className="font-medium text-green-800">All Synced</div>
                    <div className="text-green-700">
                      All your data is up to date.
                    </div>
                  </div>
                </div>
              )}

              {syncStatus.isOnline && syncStatus.pendingItems > 0 && (
                <div className="flex items-start gap-2 p-2 bg-yellow-50 rounded-lg">
                  <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5" />
                  <div className="text-sm">
                    <div className="font-medium text-yellow-800">Sync Pending</div>
                    <div className="text-yellow-700">
                      Some data is waiting to be synced to the server.
                    </div>
                  </div>
                </div>
              )}

              {syncStatus.isSyncing && (
                <div className="flex items-start gap-2 p-2 bg-blue-50 rounded-lg">
                  <Loader2 className="h-4 w-4 text-blue-500 mt-0.5 animate-spin" />
                  <div className="text-sm">
                    <div className="font-medium text-blue-800">Syncing Data</div>
                    <div className="text-blue-700">
                      Uploading your offline data to the server...
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Debug Actions (only in development) */}
            {process.env.NODE_ENV === 'development' && (
              <div className="pt-2 border-t">
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={clearOfflineData}
                    className="text-xs"
                  >
                    Clear Offline Data
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={syncOfflineData}
                    disabled={syncStatus.isSyncing}
                    className="text-xs"
                  >
                    Force Sync
                  </Button>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default OfflineStatusIndicator;
