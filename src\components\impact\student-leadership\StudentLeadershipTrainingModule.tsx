import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, Award, TrendingUp, BookOpen, Plus, GraduationCap } from 'lucide-react';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { MetricCard, ComingSoonCard } from '../shared';
import TrainingAssessmentForm from './TrainingAssessmentForm';

interface StudentLeadershipTrainingModuleProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const StudentLeadershipTrainingModule: React.FC<StudentLeadershipTrainingModuleProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [showTrainingForm, setShowTrainingForm] = useState(false);
  // Access control is now handled by AdminOnlyWrapper

  return (
    <PageLayout>
      <PageHeader
        title="Student Leadership Training"
        description="Track student leadership development and program effectiveness"
        icon={Users}
        actions={[
          {
            label: 'Add Program Data',
            onClick: () => setShowTrainingForm(true),
            icon: Plus,
          }
        ]}
      />

      {/* Training Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Students Trained"
          value="156"
          icon={Users}
          color="purple"
        />
        <MetricCard
          title="Certification Rate"
          value="89.2%"
          icon={Award}
          color="green"
        />
        <MetricCard
          title="Effectiveness Score"
          value="4.3/5"
          icon={TrendingUp}
          color="blue"
        />
        <MetricCard
          title="Leadership Programs"
          value="12"
          icon={BookOpen}
          color="orange"
        />
      </div>

      {/* Coming Soon Message */}
      <ComingSoonCard
        title="Student Leadership Analytics"
        description="This module will include comprehensive student leadership program tracking, effectiveness measurement, leadership skill assessments, and development analytics."
        icon={GraduationCap}
        placeholderIcon={Users}
        features={[
          'Pre/post leadership assessments',
          'Leadership skill development tracking',
          'Program completion monitoring',
          'Certification management',
          'Impact correlation analysis'
        ]}
      />

      {/* Training Assessment Form */}
      {showTrainingForm && (
        <TrainingAssessmentForm
          onClose={() => setShowTrainingForm(false)}
          schoolId={schoolId}
        />
      )}
    </PageLayout>
  );
};

export default StudentLeadershipTrainingModule;
