/**
 * Direct password reset using SQL approach
 * This script directly updates the password in the database
 */

import { createClient } from '@supabase/supabase-js';

// Use the same configuration as the client
const supabaseUrl = "https://bygrspebofyofymivmib.supabase.co";
const supabaseServiceKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTAzMjE4OCwiZXhwIjoyMDY0NjA4MTg4fQ.d9HbH3fF-tBa8v7HBAR5XnFOghjorx_bAVQh0ZaeDf4";

// Create Supabase client with service role key
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function resetPasswordDirectly(userId, email, newPassword) {
  try {
    console.log(`Resetting password for user: ${email} (${userId})`);
    
    // Method 1: Try using SQL to update the password
    console.log('Attempting SQL password update...');
    const { data: sqlResult, error: sqlError } = await supabase
      .rpc('reset_user_password', {
        user_id: userId,
        new_password: newPassword
      });
    
    if (!sqlError) {
      console.log('✅ Password reset via SQL function successful!');
      return true;
    }
    
    console.log('SQL function failed, trying direct update...');
    
    // Method 2: Direct SQL update
    const { data: directResult, error: directError } = await supabase
      .from('auth.users')
      .update({
        encrypted_password: `crypt('${newPassword}', gen_salt('bf', 10))`,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);
    
    if (!directError) {
      console.log('✅ Password reset via direct update successful!');
      return true;
    }
    
    console.log('Direct update failed, trying raw SQL...');
    
    // Method 3: Raw SQL query
    const { data: rawResult, error: rawError } = await supabase
      .rpc('exec_sql', {
        sql: `UPDATE auth.users SET encrypted_password = crypt('${newPassword}', gen_salt('bf', 10)), updated_at = now() WHERE id = '${userId}'`
      });
    
    if (!rawError) {
      console.log('✅ Password reset via raw SQL successful!');
      return true;
    }
    
    console.error('All methods failed');
    console.error('SQL Error:', sqlError);
    console.error('Direct Error:', directError);
    console.error('Raw Error:', rawError);
    
    return false;
    
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

async function createPasswordResetFunction() {
  try {
    console.log('Creating password reset function...');
    
    const createFunctionSQL = `
      CREATE OR REPLACE FUNCTION reset_user_password(user_id UUID, new_password TEXT)
      RETURNS BOOLEAN
      LANGUAGE plpgsql
      SECURITY DEFINER
      AS $$
      BEGIN
        UPDATE auth.users 
        SET encrypted_password = crypt(new_password, gen_salt('bf', 10)),
            updated_at = now()
        WHERE id = user_id;
        
        RETURN FOUND;
      END;
      $$;
    `;
    
    const { error } = await supabase.rpc('exec_sql', { sql: createFunctionSQL });
    
    if (error) {
      console.error('Error creating function:', error);
      return false;
    }
    
    console.log('✅ Password reset function created successfully!');
    return true;
    
  } catch (error) {
    console.error('Error creating function:', error);
    return false;
  }
}

// User details
const targetUser = {
  id: "88731d07-ee6e-49ee-bf3a-3e1002441abf",
  email: "<EMAIL>"
};

const newPassword = "Xzt4q87m";

async function main() {
  console.log('=== Direct Password Reset Script ===');
  console.log(`Target User: ${targetUser.email}`);
  console.log(`New Password: ${newPassword}`);
  console.log('');

  // First try to create the function
  await createPasswordResetFunction();
  
  // Then try to reset the password
  const success = await resetPasswordDirectly(targetUser.id, targetUser.email, newPassword);
  
  if (success) {
    console.log('✅ Password reset completed successfully!');
    console.log('');
    console.log('User can now log in with:');
    console.log(`Email: ${targetUser.email}`);
    console.log(`Password: ${newPassword}`);
  } else {
    console.log('❌ Password reset failed!');
    process.exit(1);
  }
}

// Run the script
main().catch(console.error);
