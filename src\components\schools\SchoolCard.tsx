
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { School, Users, MapPin, Phone } from 'lucide-react';
import { School as SchoolType } from '@/types/school';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface SchoolCardProps {
  school: SchoolType;
  currentUser: Profile | null;
}

const SchoolCard = ({ school, currentUser }: SchoolCardProps) => {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader>
        <CardTitle className="flex items-center">
          <School className="h-5 w-5 text-purple-600 mr-2" />
          {school.name}
        </CardTitle>
        <CardDescription>{school.code}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center text-sm text-gray-600">
            <MapPin className="h-4 w-4 mr-2" />
            {school.district}, {school.sub_county}
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <Users className="h-4 w-4 mr-2" />
            {school.student_count} students, {school.teacher_count} teachers
          </div>
          {school.contact_phone && (
            <div className="flex items-center text-sm text-gray-600">
              <Phone className="h-4 w-4 mr-2" />
              {school.contact_phone}
            </div>
          )}
          <div className="flex justify-between items-center pt-2">
            <span className={`px-2 py-1 rounded-full text-xs ${
              school.school_type === 'primary' ? 'bg-blue-100 text-blue-800' :
              school.school_type === 'secondary' ? 'bg-purple-100 text-purple-800' :
              'bg-gray-100 text-gray-800'
            }`}>
              {school.school_type}
            </span>
            <span className={`px-2 py-1 rounded-full text-xs ${
              school.registration_status === 'registered' ? 'bg-green-100 text-green-800' :
              school.registration_status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
              'bg-red-100 text-red-800'
            }`}>
              {school.registration_status === 'registered' ? 'active' :
               school.registration_status === 'pending' ? 'pending' :
               school.registration_status === 'unregistered' ? 'inactive' :
               'inactive'}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SchoolCard;
