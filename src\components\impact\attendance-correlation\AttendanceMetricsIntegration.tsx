import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  BookOpen, 
  Target,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Sync,
  Database,
  Activity
} from 'lucide-react';
import { useUpdateLongitudinalTracking } from '@/hooks/impact/useAttendanceImpactCorrelation';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface AttendanceMetricsIntegrationProps {
  schoolId?: string;
}

const AttendanceMetricsIntegration: React.FC<AttendanceMetricsIntegrationProps> = ({
  schoolId,
}) => {
  const [syncInProgress, setSyncInProgress] = useState(false);
  const updateLongitudinalTracking = useUpdateLongitudinalTracking();

  // Fetch current impact indicators that can be enhanced with attendance data
  const { data: impactIndicators, isLoading: indicatorsLoading } = useQuery({
    queryKey: ['impact-indicators', schoolId],
    queryFn: async () => {
      let query = supabase
        .from('impact_indicators')
        .select('*')
        .eq('is_active', true);

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      const { data, error } = await query;
      if (error) throw error;
      return data;
    },
  });

  // Fetch attendance-enhanced metrics
  const { data: attendanceMetrics, isLoading: metricsLoading } = useQuery({
    queryKey: ['attendance-enhanced-metrics', schoolId],
    queryFn: async () => {
      // Get attendance data aggregated by school and time period
      let query = supabase
        .from('student_attendance')
        .select(`
          school_id,
          attendance_status,
          recorded_at,
          participation_score,
          student:students(grade_level, gender),
          session:attendance_sessions(session_type, session_date)
        `);

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      // Get data from last 6 months
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      query = query.gte('recorded_at', sixMonthsAgo.toISOString());

      const { data, error } = await query;
      if (error) throw error;

      // Process data into metrics
      const metrics = {
        overall_attendance_rate: 0,
        attendance_by_grade: {} as Record<number, { total: number; attended: number; rate: number }>,
        attendance_by_gender: {} as Record<string, { total: number; attended: number; rate: number }>,
        attendance_by_session_type: {} as Record<string, { total: number; attended: number; rate: number }>,
        participation_correlation: 0,
        monthly_trends: [] as Array<{ month: string; attendance_rate: number; participation_avg: number }>,
        risk_indicators: {
          low_attendance_students: 0,
          declining_trends: 0,
          intervention_needed: 0,
        },
      };

      if (data && data.length > 0) {
        // Calculate overall attendance rate
        const totalRecords = data.length;
        const attendedRecords = data.filter(r => ['present', 'late'].includes(r.attendance_status)).length;
        metrics.overall_attendance_rate = (attendedRecords / totalRecords) * 100;

        // Group by grade level
        data.forEach(record => {
          const grade = record.student?.grade_level;
          if (grade) {
            if (!metrics.attendance_by_grade[grade]) {
              metrics.attendance_by_grade[grade] = { total: 0, attended: 0, rate: 0 };
            }
            metrics.attendance_by_grade[grade].total++;
            if (['present', 'late'].includes(record.attendance_status)) {
              metrics.attendance_by_grade[grade].attended++;
            }
          }
        });

        // Calculate rates for grades
        Object.keys(metrics.attendance_by_grade).forEach(grade => {
          const gradeData = metrics.attendance_by_grade[parseInt(grade)];
          gradeData.rate = gradeData.total > 0 ? (gradeData.attended / gradeData.total) * 100 : 0;
        });

        // Group by gender
        data.forEach(record => {
          const gender = record.student?.gender;
          if (gender) {
            if (!metrics.attendance_by_gender[gender]) {
              metrics.attendance_by_gender[gender] = { total: 0, attended: 0, rate: 0 };
            }
            metrics.attendance_by_gender[gender].total++;
            if (['present', 'late'].includes(record.attendance_status)) {
              metrics.attendance_by_gender[gender].attended++;
            }
          }
        });

        // Calculate rates for genders
        Object.keys(metrics.attendance_by_gender).forEach(gender => {
          const genderData = metrics.attendance_by_gender[gender];
          genderData.rate = genderData.total > 0 ? (genderData.attended / genderData.total) * 100 : 0;
        });

        // Group by session type
        data.forEach(record => {
          const sessionType = record.session?.session_type || 'unknown';
          if (!metrics.attendance_by_session_type[sessionType]) {
            metrics.attendance_by_session_type[sessionType] = { total: 0, attended: 0, rate: 0 };
          }
          metrics.attendance_by_session_type[sessionType].total++;
          if (['present', 'late'].includes(record.attendance_status)) {
            metrics.attendance_by_session_type[sessionType].attended++;
          }
        });

        // Calculate rates for session types
        Object.keys(metrics.attendance_by_session_type).forEach(sessionType => {
          const sessionData = metrics.attendance_by_session_type[sessionType];
          sessionData.rate = sessionData.total > 0 ? (sessionData.attended / sessionData.total) * 100 : 0;
        });

        // Calculate monthly trends
        const monthlyData = new Map<string, { total: number; attended: number; participationScores: number[] }>();
        data.forEach(record => {
          const month = new Date(record.recorded_at).toISOString().slice(0, 7); // YYYY-MM
          if (!monthlyData.has(month)) {
            monthlyData.set(month, { total: 0, attended: 0, participationScores: [] });
          }
          const monthData = monthlyData.get(month)!;
          monthData.total++;
          if (['present', 'late'].includes(record.attendance_status)) {
            monthData.attended++;
          }
          if (record.participation_score) {
            monthData.participationScores.push(record.participation_score);
          }
        });

        metrics.monthly_trends = Array.from(monthlyData.entries())
          .map(([month, data]) => ({
            month,
            attendance_rate: data.total > 0 ? (data.attended / data.total) * 100 : 0,
            participation_avg: data.participationScores.length > 0 
              ? data.participationScores.reduce((sum, score) => sum + score, 0) / data.participationScores.length 
              : 0,
          }))
          .sort((a, b) => a.month.localeCompare(b.month));
      }

      return metrics;
    },
  });

  const handleSyncAttendanceData = async () => {
    setSyncInProgress(true);
    try {
      // Get all students that need longitudinal tracking updates
      let query = supabase
        .from('students')
        .select('id')
        .eq('status', 'active');

      if (schoolId) {
        query = query.eq('school_id', schoolId);
      }

      const { data: students, error } = await query;
      if (error) throw error;

      // Update longitudinal tracking for each student
      for (const student of students || []) {
        await updateLongitudinalTracking.mutateAsync(student.id);
      }

      // Update impact indicators with attendance data
      await updateImpactIndicators();

    } catch (error) {
      console.error('Error syncing attendance data:', error);
    } finally {
      setSyncInProgress(false);
    }
  };

  const updateImpactIndicators = async () => {
    // Update or create attendance-related impact indicators
    const attendanceIndicators = [
      {
        indicator_name: 'Overall Attendance Rate',
        indicator_category: 'Attendance',
        description: 'Percentage of students attending sessions regularly',
        measurement_unit: 'percentage',
        target_value: 90,
        current_value: attendanceMetrics?.overall_attendance_rate || 0,
        school_id: schoolId,
        measurement_frequency: 'monthly',
        data_source: 'Attendance Tracking System',
        responsible_person: 'Field Staff',
      },
      {
        indicator_name: 'Leadership Program Attendance',
        indicator_category: 'Leadership Development',
        description: 'Attendance rate specifically for leadership training sessions',
        measurement_unit: 'percentage',
        target_value: 85,
        current_value: attendanceMetrics?.attendance_by_session_type?.leadership_program?.rate || 0,
        school_id: schoolId,
        measurement_frequency: 'monthly',
        data_source: 'Attendance Tracking System',
        responsible_person: 'Program Officer',
      },
    ];

    for (const indicator of attendanceIndicators) {
      // Check if indicator already exists
      const { data: existing } = await supabase
        .from('impact_indicators')
        .select('id')
        .eq('indicator_name', indicator.indicator_name)
        .eq('school_id', schoolId)
        .single();

      if (existing) {
        // Update existing indicator
        await supabase
          .from('impact_indicators')
          .update({
            current_value: indicator.current_value,
            last_measured_date: new Date().toISOString().split('T')[0],
            updated_at: new Date().toISOString(),
          })
          .eq('id', existing.id);
      } else {
        // Create new indicator
        await supabase
          .from('impact_indicators')
          .insert(indicator);
      }
    }
  };

  // Prepare chart data
  const gradeChartData = attendanceMetrics ? Object.entries(attendanceMetrics.attendance_by_grade).map(([grade, data]) => ({
    grade: `Grade ${grade}`,
    attendance_rate: data.rate,
    total_students: data.total,
  })) : [];

  const sessionTypeChartData = attendanceMetrics ? Object.entries(attendanceMetrics.attendance_by_session_type).map(([type, data]) => ({
    session_type: type.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    attendance_rate: data.rate,
    total_sessions: data.total,
  })) : [];

  return (
    <div className="space-y-6">
      {/* Integration Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5" />
            Attendance Data Integration
          </CardTitle>
          <CardDescription>
            Sync attendance data with impact measurement system for comprehensive analysis
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Activity className="h-4 w-4 text-green-600" />
                <span className="text-sm">Real-time attendance tracking active</span>
              </div>
              <div className="flex items-center gap-2">
                <Sync className="h-4 w-4 text-blue-600" />
                <span className="text-sm">Impact indicators synchronized</span>
              </div>
            </div>
            <Button 
              onClick={handleSyncAttendanceData}
              disabled={syncInProgress}
            >
              {syncInProgress ? (
                <>
                  <Sync className="h-4 w-4 mr-2 animate-spin" />
                  Syncing...
                </>
              ) : (
                <>
                  <Sync className="h-4 w-4 mr-2" />
                  Sync Data
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Enhanced Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {attendanceMetrics?.overall_attendance_rate.toFixed(1) || '0'}%
                </p>
                <p className="text-sm text-gray-600">Overall Attendance</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {Object.keys(attendanceMetrics?.attendance_by_grade || {}).length}
                </p>
                <p className="text-sm text-gray-600">Grades Tracked</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <BookOpen className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {attendanceMetrics?.attendance_by_session_type?.leadership_program?.rate.toFixed(1) || '0'}%
                </p>
                <p className="text-sm text-gray-600">Leadership Programs</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg">
                <Target className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {attendanceMetrics?.monthly_trends.length || 0}
                </p>
                <p className="text-sm text-gray-600">Months Tracked</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Attendance by Grade */}
        <Card>
          <CardHeader>
            <CardTitle>Attendance by Grade Level</CardTitle>
            <CardDescription>
              Attendance rates across different grade levels
            </CardDescription>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              </div>
            ) : gradeChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={gradeChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="grade" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, 'Attendance Rate']} />
                  <Bar dataKey="attendance_rate" fill="#8884d8" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No grade-level data available
              </div>
            )}
          </CardContent>
        </Card>

        {/* Attendance by Session Type */}
        <Card>
          <CardHeader>
            <CardTitle>Attendance by Session Type</CardTitle>
            <CardDescription>
              Attendance rates for different types of sessions
            </CardDescription>
          </CardHeader>
          <CardContent>
            {metricsLoading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
              </div>
            ) : sessionTypeChartData.length > 0 ? (
              <ResponsiveContainer width="100%" height={300}>
                <BarChart data={sessionTypeChartData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="session_type" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip formatter={(value) => [`${Number(value).toFixed(1)}%`, 'Attendance Rate']} />
                  <Bar dataKey="attendance_rate" fill="#82ca9d" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No session type data available
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Monthly Trends */}
      {attendanceMetrics?.monthly_trends && attendanceMetrics.monthly_trends.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Attendance & Participation Trends</CardTitle>
            <CardDescription>
              Monthly trends showing attendance rates and participation scores
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={attendanceMetrics.monthly_trends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Line 
                  type="monotone" 
                  dataKey="attendance_rate" 
                  stroke="#8884d8" 
                  name="Attendance Rate (%)"
                />
                <Line 
                  type="monotone" 
                  dataKey="participation_avg" 
                  stroke="#82ca9d" 
                  name="Participation Score (1-5)"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}

      {/* Impact Indicators Integration */}
      <Card>
        <CardHeader>
          <CardTitle>Impact Indicators Integration</CardTitle>
          <CardDescription>
            Attendance data integrated with existing impact measurement framework
          </CardDescription>
        </CardHeader>
        <CardContent>
          {indicatorsLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : (
            <div className="space-y-4">
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Attendance data is automatically integrated with the impact measurement system. 
                  Key metrics are updated in real-time and contribute to overall program effectiveness analysis.
                </AlertDescription>
              </Alert>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Enhanced Indicators</h3>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>• Overall attendance rate tracking</li>
                    <li>• Leadership program effectiveness</li>
                    <li>• Student retention correlation</li>
                    <li>• Academic performance correlation</li>
                  </ul>
                </div>
                <div className="border rounded-lg p-4">
                  <h3 className="font-medium mb-2">Automated Updates</h3>
                  <ul className="space-y-1 text-sm text-gray-600">
                    <li>• Daily attendance rate calculations</li>
                    <li>• Monthly trend analysis</li>
                    <li>• Risk factor identification</li>
                    <li>• Intervention recommendations</li>
                  </ul>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default AttendanceMetricsIntegration;
