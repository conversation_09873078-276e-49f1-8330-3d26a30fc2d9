# Potentially Unused Files

The following files appear to be unused and may be candidates for deletion.
Please review each file carefully before deletion as some may be used in ways not detected by static analysis.

## Files:

1. `src\components\BookDistribution.tsx`
2. `src\components\Navbar.tsx`
3. `src\components\Reports.tsx`
4. `src\components\attendance\SessionDetails.tsx`
5. `src\components\attendance\SessionsOverview.tsx`
6. `src\components\attendance\StudentRosterManager.tsx`
7. `src\components\attendance\testing\AttendanceSystemValidator.tsx`
8. `src\components\distributions\DistributionStatistics.tsx`
9. `src\components\field-staff\FieldStaffAttendance.tsx`
10. `src\components\field-staff\FieldStaffCheckInModal.tsx`
11. `src\components\field-staff\FieldStaffCheckOutModal.tsx`
12. `src\components\field-staff\StorageMonitor.tsx`
13. `src\components\field-staff\fieldStaffUtils.ts`
14. `src\components\field-staff\withFieldStaffAccess.tsx`
15. `src\components\help\Documentation.tsx`
16. `src\components\help\HelpSupport.tsx`
17. `src\components\impact\ImpactAnalyticsDashboard.tsx`
18. `src\components\impact\attendance-correlation\AttendanceImpactDashboard.tsx`
19. `src\components\impact\attendance-correlation\AttendanceMetricsIntegration.tsx`
20. `src\components\impact\shared\ComingSoonCard.tsx`
21. `src\components\impact\shared\MetricCard.tsx`
22. `src\components\layout\ContentCard.tsx`
23. `src\components\layout\PageHeader.tsx`
24. `src\components\layout\PageLayout.tsx`
25. `src\components\layout\StandardModal.tsx`
26. `src\components\schools\AddSchool.tsx`
27. `src\components\schools\SchoolCard.tsx`
28. `src\components\tasks\CompletedTasks.tsx`
29. `src\components\tasks\ManagedTasks.tsx`
30. `src\components\tasks\MyTasks.tsx`
31. `src\components\tasks\OverdueTasks.tsx`
32. `src\components\ui\accordion.tsx`
33. `src\components\ui\aspect-ratio.tsx`
34. `src\components\ui\breadcrumb.tsx`
35. `src\components\ui\carousel.tsx`
36. `src\components\ui\chart.tsx`
37. `src\components\ui\command.tsx`
38. `src\components\ui\context-menu.tsx`
39. `src\components\ui\drawer.tsx`
40. `src\components\ui\hover-card.tsx`
41. `src\components\ui\input-otp.tsx`
42. `src\components\ui\menubar.tsx`
43. `src\components\ui\navigation-menu.tsx`
44. `src\components\ui\pagination.tsx`
45. `src\components\ui\resizable.tsx`
46. `src\components\ui\scroll-area.tsx`
47. `src\components\ui\sidebar.tsx`
48. `src\components\ui\slider.tsx`
49. `src\components\ui\switch.tsx`
50. `src\components\ui\toggle-group.tsx`
51. `src\components\ui\use-toast.ts`
52. `src\hooks\activities\mutations.ts`
53. `src\hooks\activities\useFilteredActivities.ts`
54. `src\hooks\activities\utils.ts`
55. `src\hooks\field-staff\useOfflineSync.backup.ts`
56. `src\hooks\tasks\mutations.ts`
57. `src\hooks\tasks\queries.ts`
58. `src\hooks\useStorageMonitor.ts`
59. `src\scripts\seedUsers.ts`
60. `src\scripts\testMemoryLeakFixes.ts`
61. `src\services\csvService.ts`
62. `src\utils\attendance\integrationTests.ts`
63. `src\utils\attendance\notificationTriggers.ts`
64. `src\utils\fieldStaffTestUtils.ts`
65. `src\utils\performance.ts`
66. `src\utils\performance\usePerformanceMetrics.ts`
67. `src\utils\performance\utils.ts`
68. `src\utils\progressiveUpload.ts`