
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type AdminDivision = Database['public']['Functions']['get_admin_divisions']['Returns'][0];
type SchoolType = Database['public']['Enums']['school_type'];
type OwnershipType = 'government' | 'private' | 'community';

interface SchoolFormData {
  // Required fields
  name: string;
  school_type: SchoolType;
  division_id: string;

  // Optional fields
  code?: string;
  student_count?: string;
  teacher_count?: string;
  contact_phone?: string;
  email?: string;
  classes_count?: string;
  streams_per_class?: string;
  head_teacher_name?: string;
  deputy_head_teacher_name?: string;
  year_established?: string;
  ownership_type?: OwnershipType;
  location_description?: string;
  nearest_health_center?: string;
  distance_to_main_road?: string;
  infrastructure_notes?: string;
}

interface AddSchoolFormProps {
  divisions: AdminDivision[];
  onAddSchool: (schoolData: SchoolFormData) => void;
  isAdding: boolean;
}

const AddSchoolForm = ({ divisions, onAddSchool, isAdding }: AddSchoolFormProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [formData, setFormData] = useState<SchoolFormData>({
    // Required fields
    name: '',
    school_type: 'primary',
    division_id: '',

    // Optional fields with defaults
    code: '',
    student_count: '',
    teacher_count: '',
    contact_phone: '',
    email: '',
    classes_count: '',
    streams_per_class: '',
    head_teacher_name: '',
    deputy_head_teacher_name: '',
    year_established: '',
    ownership_type: 'government',
    location_description: '',
    nearest_health_center: '',
    distance_to_main_road: '',
    infrastructure_notes: '',
  });

  const { toast } = useToast();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.division_id) {
      toast({
        title: "Error",
        description: "Please select a district",
        variant: "destructive",
      });
      return;
    }
    
    onAddSchool(formData);
    
    // Reset form and close modal on success
    setFormData({
      // Required fields
      name: '',
      school_type: 'primary',
      division_id: '',

      // Optional fields with defaults
      code: '',
      student_count: '',
      teacher_count: '',
      contact_phone: '',
      email: '',
      classes_count: '',
      streams_per_class: '',
      head_teacher_name: '',
      deputy_head_teacher_name: '',
      year_established: '',
      ownership_type: 'government',
      location_description: '',
      nearest_health_center: '',
      distance_to_main_road: '',
      infrastructure_notes: '',
    });
    setIsOpen(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className="bg-purple-600 hover:bg-purple-700">
          <Plus className="h-4 w-4 mr-2" />
          Add School
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New School</DialogTitle>
          <DialogDescription>
            Register a new school in the iLead program with complete information
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Basic Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">School Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Enter full school name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">School Code</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => setFormData({ ...formData, code: e.target.value })}
                  placeholder="e.g., UG-PS-001"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="school_type">School Type *</Label>
                <Select value={formData.school_type} onValueChange={(value: SchoolType) => setFormData({ ...formData, school_type: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="primary">Primary School (P1-P7)</SelectItem>
                    <SelectItem value="secondary">Secondary School (S1-S6)</SelectItem>
                    <SelectItem value="tertiary">Tertiary Institution</SelectItem>
                    <SelectItem value="vocational">Vocational Training</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="ownership_type">Ownership Type</Label>
                <Select value={formData.ownership_type} onValueChange={(value: OwnershipType) => setFormData({ ...formData, ownership_type: value })}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="government">Government School</SelectItem>
                    <SelectItem value="private">Private School</SelectItem>
                    <SelectItem value="community">Community School</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="year_established">Year Established</Label>
                <Input
                  id="year_established"
                  type="number"
                  value={formData.year_established}
                  onChange={(e) => setFormData({ ...formData, year_established: e.target.value })}
                  placeholder="e.g., 1985"
                  min="1900"
                  max={new Date().getFullYear()}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="division">District *</Label>
                <Select value={formData.division_id} onValueChange={(value) => setFormData({ ...formData, division_id: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select district" />
                  </SelectTrigger>
                  <SelectContent>
                    {divisions.map((division: AdminDivision) => (
                      <SelectItem key={division.id} value={division.id}>
                        {division.district}, {division.sub_county}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Academic Structure */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Academic Structure</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <Label htmlFor="student_count">Total Students</Label>
                <Input
                  id="student_count"
                  type="number"
                  value={formData.student_count}
                  onChange={(e) => setFormData({ ...formData, student_count: e.target.value })}
                  placeholder="Number of students"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="teacher_count">Total Teachers</Label>
                <Input
                  id="teacher_count"
                  type="number"
                  value={formData.teacher_count}
                  onChange={(e) => setFormData({ ...formData, teacher_count: e.target.value })}
                  placeholder="Number of teachers"
                  min="0"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="classes_count">Number of Classes</Label>
                <Input
                  id="classes_count"
                  type="number"
                  value={formData.classes_count}
                  onChange={(e) => setFormData({ ...formData, classes_count: e.target.value })}
                  placeholder="e.g., 7 (P1-P7)"
                  min="1"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="streams_per_class">Streams per Class</Label>
                <Input
                  id="streams_per_class"
                  type="number"
                  value={formData.streams_per_class}
                  onChange={(e) => setFormData({ ...formData, streams_per_class: e.target.value })}
                  placeholder="e.g., 2 (A, B)"
                  min="1"
                />
              </div>
            </div>
          </div>

          {/* Leadership */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">School Leadership</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="head_teacher_name">Head Teacher Name</Label>
                <Input
                  id="head_teacher_name"
                  value={formData.head_teacher_name}
                  onChange={(e) => setFormData({ ...formData, head_teacher_name: e.target.value })}
                  placeholder="Full name of head teacher"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="deputy_head_teacher_name">Deputy Head Teacher Name</Label>
                <Input
                  id="deputy_head_teacher_name"
                  value={formData.deputy_head_teacher_name}
                  onChange={(e) => setFormData({ ...formData, deputy_head_teacher_name: e.target.value })}
                  placeholder="Full name of deputy head teacher"
                />
              </div>
            </div>
          </div>

          {/* Contact Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Contact Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contact_phone">Contact Phone</Label>
                <Input
                  id="contact_phone"
                  value={formData.contact_phone}
                  onChange={(e) => setFormData({ ...formData, contact_phone: e.target.value })}
                  placeholder="+256-700123456"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email">Email Address</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  placeholder="<EMAIL>"
                />
              </div>
            </div>
          </div>

          {/* Location Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Location & Infrastructure</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="location_description">Location Description</Label>
                <Textarea
                  id="location_description"
                  value={formData.location_description}
                  onChange={(e) => setFormData({ ...formData, location_description: e.target.value })}
                  placeholder="Describe the school's location and surroundings"
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="infrastructure_notes">Infrastructure Notes</Label>
                <Textarea
                  id="infrastructure_notes"
                  value={formData.infrastructure_notes}
                  onChange={(e) => setFormData({ ...formData, infrastructure_notes: e.target.value })}
                  placeholder="Building conditions, facilities, water, electricity, etc."
                  rows={3}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="nearest_health_center">Nearest Health Center</Label>
                <Input
                  id="nearest_health_center"
                  value={formData.nearest_health_center}
                  onChange={(e) => setFormData({ ...formData, nearest_health_center: e.target.value })}
                  placeholder="Name and distance to nearest health facility"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="distance_to_main_road">Distance to Main Road</Label>
                <Input
                  id="distance_to_main_road"
                  value={formData.distance_to_main_road}
                  onChange={(e) => setFormData({ ...formData, distance_to_main_road: e.target.value })}
                  placeholder="e.g., 2 km from tarmac road"
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end space-x-4 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-purple-600 hover:bg-purple-700"
              disabled={isAdding}
            >
              {isAdding ? 'Adding School...' : 'Add School'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AddSchoolForm;
