import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from 'recharts';
import { BarChart3, TrendingUp, Pie<PERSON><PERSON> as PieChartIcon, Activity } from 'lucide-react';

interface ImpactChartsProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const ImpactCharts: React.FC<ImpactChartsProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [selectedChart, setSelectedChart] = useState('learning-outcomes');

  // Mock data for demonstration
  const learningOutcomesData = [
    { subject: 'Literacy', preScore: 65, postScore: 78, improvement: 20 },
    { subject: 'Numeracy', preScore: 58, postScore: 72, improvement: 24 },
    { subject: 'English', preScore: 62, postScore: 75, improvement: 21 },
    { subject: 'Mathematics', preScore: 55, postScore: 68, improvement: 24 },
    { subject: 'Science', preScore: 60, postScore: 71, improvement: 18 }
  ];

  const attendanceTrendData = [
    { month: 'Jan', attendance: 82, target: 85 },
    { month: 'Feb', attendance: 84, target: 85 },
    { month: 'Mar', attendance: 87, target: 85 },
    { month: 'Apr', attendance: 89, target: 85 },
    { month: 'May', attendance: 86, target: 85 },
    { month: 'Jun', attendance: 88, target: 85 }
  ];

  const trainingEffectivenessData = [
    { name: 'Pedagogy', participants: 45, effectiveness: 4.2, certification: 89 },
    { name: 'Technology', participants: 32, effectiveness: 3.8, certification: 78 },
    { name: 'Assessment', participants: 28, effectiveness: 4.5, certification: 93 },
    { name: 'Classroom Mgmt', participants: 38, effectiveness: 4.1, certification: 85 }
  ];

  const satisfactionData = [
    { name: 'Very Satisfied', value: 45, color: '#10B981' },
    { name: 'Satisfied', value: 32, color: '#3B82F6' },
    { name: 'Neutral', value: 15, color: '#F59E0B' },
    { name: 'Dissatisfied', value: 6, color: '#EF4444' },
    { name: 'Very Dissatisfied', value: 2, color: '#DC2626' }
  ];

  const renderChart = () => {
    switch (selectedChart) {
      case 'learning-outcomes':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={learningOutcomesData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="subject" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="preScore" fill="#94A3B8" name="Pre-Assessment" />
              <Bar dataKey="postScore" fill="#10B981" name="Post-Assessment" />
              <Bar dataKey="improvement" fill="#3B82F6" name="Improvement %" />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'attendance-trends':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <AreaChart data={attendanceTrendData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Area 
                type="monotone" 
                dataKey="attendance" 
                stroke="#10B981" 
                fill="#10B981" 
                fillOpacity={0.3}
                name="Attendance Rate"
              />
              <Line 
                type="monotone" 
                dataKey="target" 
                stroke="#EF4444" 
                strokeDasharray="5 5"
                name="Target"
              />
            </AreaChart>
          </ResponsiveContainer>
        );

      case 'training-effectiveness':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={trainingEffectivenessData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis yAxisId="left" />
              <YAxis yAxisId="right" orientation="right" />
              <Tooltip />
              <Legend />
              <Bar yAxisId="left" dataKey="participants" fill="#3B82F6" name="Participants" />
              <Bar yAxisId="right" dataKey="effectiveness" fill="#10B981" name="Effectiveness Score" />
              <Bar yAxisId="right" dataKey="certification" fill="#F59E0B" name="Certification %" />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'satisfaction-distribution':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={satisfactionData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
              >
                {satisfactionData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  const chartOptions = [
    { value: 'learning-outcomes', label: 'Learning Outcomes', icon: BarChart3 },
    { value: 'attendance-trends', label: 'Attendance Trends', icon: TrendingUp },
    { value: 'training-effectiveness', label: 'Training Effectiveness', icon: Activity },
    { value: 'satisfaction-distribution', label: 'Satisfaction Distribution', icon: PieChartIcon }
  ];

  const getChartDescription = () => {
    switch (selectedChart) {
      case 'learning-outcomes':
        return 'Pre and post-assessment scores showing learning improvement across subjects';
      case 'attendance-trends':
        return 'Monthly attendance rates compared to target benchmarks';
      case 'training-effectiveness':
        return 'Teacher training program effectiveness and certification rates';
      case 'satisfaction-distribution':
        return 'Beneficiary satisfaction levels across all stakeholder groups';
      default:
        return '';
    }
  };

  if (!canViewAllData) {
    return (
      <Card className="border-yellow-200 bg-yellow-50">
        <CardContent className="p-6 text-center">
          <div className="bg-yellow-100 p-3 rounded-lg inline-block mb-4">
            <BarChart3 className="h-8 w-8 text-yellow-600" />
          </div>
          <h3 className="text-lg font-semibold text-yellow-800 mb-2">
            Limited Access to Impact Charts
          </h3>
          <p className="text-yellow-700">
            Contact your program officer to view comprehensive impact visualizations.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <BarChart3 className="h-5 w-5" />
              <span>Impact Visualization</span>
            </CardTitle>
            <CardDescription>
              {getChartDescription()}
            </CardDescription>
          </div>
          
          <Select value={selectedChart} onValueChange={setSelectedChart}>
            <SelectTrigger className="w-64">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {chartOptions.map((option) => {
                const Icon = option.icon;
                return (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center space-x-2">
                      <Icon className="h-4 w-4" />
                      <span>{option.label}</span>
                    </div>
                  </SelectItem>
                );
              })}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="mb-6">
          {renderChart()}
        </div>
        
        {/* Chart Insights */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 pt-6 border-t">
          {selectedChart === 'learning-outcomes' && (
            <>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800">Best Performing</h4>
                <p className="text-lg font-bold text-green-600">Mathematics</p>
                <p className="text-sm text-green-700">24% improvement</p>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800">Average Improvement</h4>
                <p className="text-lg font-bold text-blue-600">21.4%</p>
                <p className="text-sm text-blue-700">Across all subjects</p>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <h4 className="font-semibold text-purple-800">Students Assessed</h4>
                <p className="text-lg font-bold text-purple-600">1,247</p>
                <p className="text-sm text-purple-700">Total assessments</p>
              </div>
            </>
          )}
          
          {selectedChart === 'attendance-trends' && (
            <>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800">Peak Month</h4>
                <p className="text-lg font-bold text-green-600">April</p>
                <p className="text-sm text-green-700">89% attendance</p>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800">Average Rate</h4>
                <p className="text-lg font-bold text-blue-600">86%</p>
                <p className="text-sm text-blue-700">Above target</p>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <h4 className="font-semibold text-orange-800">Trend</h4>
                <p className="text-lg font-bold text-orange-600">Improving</p>
                <p className="text-sm text-orange-700">+7% since January</p>
              </div>
            </>
          )}
          
          {selectedChart === 'training-effectiveness' && (
            <>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800">Top Program</h4>
                <p className="text-lg font-bold text-green-600">Assessment</p>
                <p className="text-sm text-green-700">4.5/5 effectiveness</p>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800">Total Trained</h4>
                <p className="text-lg font-bold text-blue-600">143</p>
                <p className="text-sm text-blue-700">Teachers certified</p>
              </div>
              <div className="text-center p-3 bg-purple-50 rounded-lg">
                <h4 className="font-semibold text-purple-800">Avg Certification</h4>
                <p className="text-lg font-bold text-purple-600">86%</p>
                <p className="text-sm text-purple-700">Success rate</p>
              </div>
            </>
          )}
          
          {selectedChart === 'satisfaction-distribution' && (
            <>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800">Satisfied</h4>
                <p className="text-lg font-bold text-green-600">77%</p>
                <p className="text-sm text-green-700">Combined positive</p>
              </div>
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800">Total Responses</h4>
                <p className="text-lg font-bold text-blue-600">342</p>
                <p className="text-sm text-blue-700">Feedback collected</p>
              </div>
              <div className="text-center p-3 bg-orange-50 rounded-lg">
                <h4 className="font-semibold text-orange-800">Response Rate</h4>
                <p className="text-lg font-bold text-orange-600">78%</p>
                <p className="text-sm text-orange-700">Participation rate</p>
              </div>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default ImpactCharts;
