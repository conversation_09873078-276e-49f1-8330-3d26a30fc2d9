-- Create RPC Functions for Report Data Aggregation
-- This migration creates functions to support NGO funder reporting requirements

-- Function to get activity report summary for a given period
CREATE OR REPLACE FUNCTION get_activity_report_summary(
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL,
    p_school_id UUID DEFAULT NULL,
    p_activity_type activity_report_type DEFAULT NULL
)
RETURNS TABLE (
    total_activities INTEGER,
    total_participants INTEGER,
    total_male_participants INTEGER,
    total_female_participants INTEGER,
    total_schools_reached INTEGER,
    average_engagement_level DECIMAL(3,2),
    most_common_topics TEXT[],
    key_challenges TEXT[],
    top_recommendations TEXT[]
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(ar.id)::INTEGER as total_activities,
        COALESCE(SUM(ar.total_participants), 0)::INTEGER as total_participants,
        COALESCE(SUM(ar.male_participants), 0)::INTEGER as total_male_participants,
        COALESCE(SUM(ar.female_participants), 0)::INTEGER as total_female_participants,
        COUNT(DISTINCT ar.school_id)::INTEGER as total_schools_reached,
        ROUND(AVG(ar.participant_engagement_level), 2) as average_engagement_level,
        
        -- Most common topics (aggregate from JSONB arrays)
        ARRAY(
            SELECT DISTINCT unnest(ar.topics_covered) 
            FROM activity_reports ar2 
            WHERE (p_start_date IS NULL OR ar2.activity_date >= p_start_date)
            AND (p_end_date IS NULL OR ar2.activity_date <= p_end_date)
            AND (p_school_id IS NULL OR ar2.school_id = p_school_id)
            AND (p_activity_type IS NULL OR ar2.activity_type = p_activity_type)
            AND ar2.topics_covered IS NOT NULL
            LIMIT 10
        ) as most_common_topics,
        
        -- Key challenges
        ARRAY(
            SELECT DISTINCT ar2.challenges_encountered 
            FROM activity_reports ar2 
            WHERE (p_start_date IS NULL OR ar2.activity_date >= p_start_date)
            AND (p_end_date IS NULL OR ar2.activity_date <= p_end_date)
            AND (p_school_id IS NULL OR ar2.school_id = p_school_id)
            AND (p_activity_type IS NULL OR ar2.activity_type = p_activity_type)
            AND ar2.challenges_encountered IS NOT NULL
            AND LENGTH(ar2.challenges_encountered) > 0
            LIMIT 5
        ) as key_challenges,
        
        -- Top recommendations
        ARRAY(
            SELECT DISTINCT ar2.recommendations 
            FROM activity_reports ar2 
            WHERE (p_start_date IS NULL OR ar2.activity_date >= p_start_date)
            AND (p_end_date IS NULL OR ar2.activity_date <= p_end_date)
            AND (p_school_id IS NULL OR ar2.school_id = p_school_id)
            AND (p_activity_type IS NULL OR ar2.activity_type = p_activity_type)
            AND ar2.recommendations IS NOT NULL
            AND LENGTH(ar2.recommendations) > 0
            LIMIT 5
        ) as top_recommendations
        
    FROM activity_reports ar
    WHERE 
        (p_start_date IS NULL OR ar.activity_date >= p_start_date)
        AND (p_end_date IS NULL OR ar.activity_date <= p_end_date)
        AND (p_school_id IS NULL OR ar.school_id = p_school_id)
        AND (p_activity_type IS NULL OR ar.activity_type = p_activity_type)
        AND ar.status IN ('approved', 'published');
END;
$$;

-- Function to generate monthly report data
CREATE OR REPLACE FUNCTION generate_monthly_report_data(
    p_month INTEGER,
    p_year INTEGER
)
RETURNS TABLE (
    report_data JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    start_date DATE;
    end_date DATE;
    report_json JSONB;
BEGIN
    -- Calculate date range for the month
    start_date := DATE(p_year || '-' || LPAD(p_month::TEXT, 2, '0') || '-01');
    end_date := (start_date + INTERVAL '1 month' - INTERVAL '1 day')::DATE;
    
    -- Build comprehensive report data
    SELECT jsonb_build_object(
        'period', jsonb_build_object(
            'month', p_month,
            'year', p_year,
            'start_date', start_date,
            'end_date', end_date
        ),
        'summary', jsonb_build_object(
            'total_activities', COUNT(ar.id),
            'total_participants', COALESCE(SUM(ar.total_participants), 0),
            'total_male_participants', COALESCE(SUM(ar.male_participants), 0),
            'total_female_participants', COALESCE(SUM(ar.female_participants), 0),
            'total_schools_reached', COUNT(DISTINCT ar.school_id),
            'average_engagement_level', ROUND(AVG(ar.participant_engagement_level), 2)
        ),
        'activity_breakdown', jsonb_build_object(
            'leadership_training', COUNT(CASE WHEN ar.activity_type = 'leadership_training' THEN 1 END),
            'school_visits', COUNT(CASE WHEN ar.activity_type = 'school_visit' THEN 1 END),
            'community_engagement', COUNT(CASE WHEN ar.activity_type = 'community_engagement' THEN 1 END),
            'capacity_building', COUNT(CASE WHEN ar.activity_type = 'capacity_building' THEN 1 END),
            'monitoring_evaluation', COUNT(CASE WHEN ar.activity_type = 'monitoring_evaluation' THEN 1 END)
        ),
        'geographic_coverage', jsonb_build_object(
            'regions', ARRAY_AGG(DISTINCT ar.school_region) FILTER (WHERE ar.school_region IS NOT NULL),
            'districts', ARRAY_AGG(DISTINCT ar.school_district) FILTER (WHERE ar.school_district IS NOT NULL),
            'schools', jsonb_agg(DISTINCT jsonb_build_object(
                'id', ar.school_id,
                'name', ar.school_name,
                'district', ar.school_district,
                'region', ar.school_region
            )) FILTER (WHERE ar.school_id IS NOT NULL)
        ),
        'performance_metrics', jsonb_build_object(
            'completion_rate', ROUND(
                (COUNT(CASE WHEN ar.status IN ('approved', 'published') THEN 1 END)::DECIMAL / 
                 NULLIF(COUNT(ar.id)::DECIMAL, 0)) * 100, 2
            ),
            'average_duration', ROUND(AVG(ar.duration_hours), 2),
            'participant_satisfaction', ROUND(AVG(ar.participant_engagement_level), 2)
        )
    ) INTO report_json
    FROM activity_reports ar
    WHERE ar.activity_date >= start_date 
    AND ar.activity_date <= end_date;
    
    RETURN QUERY SELECT report_json;
END;
$$;

-- Function to get baseline questionnaire analytics
CREATE OR REPLACE FUNCTION get_baseline_questionnaire_analytics(
    p_questionnaire_id UUID DEFAULT NULL,
    p_program_id UUID DEFAULT NULL,
    p_school_id UUID DEFAULT NULL,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    total_responses INTEGER,
    average_overall_score DECIMAL(4,2),
    gender_distribution JSONB,
    grade_level_distribution JSONB,
    skill_averages JSONB,
    improvement_areas_frequency JSONB,
    strengths_frequency JSONB,
    completion_rate DECIMAL(5,2)
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COUNT(pr.id)::INTEGER as total_responses,
        ROUND(AVG(pr.overall_leadership_score), 2) as average_overall_score,
        
        -- Gender distribution
        jsonb_build_object(
            'male', COUNT(CASE WHEN pr.participant_gender = 'male' THEN 1 END),
            'female', COUNT(CASE WHEN pr.participant_gender = 'female' THEN 1 END),
            'other', COUNT(CASE WHEN pr.participant_gender = 'other' THEN 1 END),
            'not_specified', COUNT(CASE WHEN pr.participant_gender IS NULL THEN 1 END)
        ) as gender_distribution,
        
        -- Grade level distribution
        jsonb_object_agg(
            COALESCE(pr.grade_level::TEXT, 'unspecified'),
            COUNT(CASE WHEN pr.grade_level IS NOT NULL THEN 1 END)
        ) FILTER (WHERE pr.grade_level IS NOT NULL) as grade_level_distribution,
        
        -- Average skill scores (from JSONB)
        jsonb_build_object(
            'communication', ROUND(AVG((pr.leadership_skill_scores->>'communication')::DECIMAL), 2),
            'teamwork', ROUND(AVG((pr.leadership_skill_scores->>'teamwork')::DECIMAL), 2),
            'problem_solving', ROUND(AVG((pr.leadership_skill_scores->>'problem_solving')::DECIMAL), 2),
            'critical_thinking', ROUND(AVG((pr.leadership_skill_scores->>'critical_thinking')::DECIMAL), 2),
            'public_speaking', ROUND(AVG((pr.leadership_skill_scores->>'public_speaking')::DECIMAL), 2)
        ) as skill_averages,
        
        -- Most common improvement areas
        jsonb_object_agg(
            improvement_area,
            improvement_count
        ) as improvement_areas_frequency,
        
        -- Most common strengths
        jsonb_object_agg(
            strength,
            strength_count
        ) as strengths_frequency,
        
        -- Completion rate (responses with all required fields)
        ROUND(
            (COUNT(CASE WHEN pr.overall_leadership_score IS NOT NULL 
                   AND pr.leadership_skill_scores IS NOT NULL 
                   THEN 1 END)::DECIMAL / 
             NULLIF(COUNT(pr.id)::DECIMAL, 0)) * 100, 2
        ) as completion_rate
        
    FROM participant_responses pr
    LEFT JOIN LATERAL (
        SELECT unnest(pr.improvement_areas) as improvement_area
    ) ia ON true
    LEFT JOIN LATERAL (
        SELECT improvement_area, COUNT(*) as improvement_count
        FROM (SELECT unnest(pr.improvement_areas) as improvement_area) sub
        GROUP BY improvement_area
    ) iac ON iac.improvement_area = ia.improvement_area
    LEFT JOIN LATERAL (
        SELECT unnest(pr.strengths) as strength
    ) st ON true
    LEFT JOIN LATERAL (
        SELECT strength, COUNT(*) as strength_count
        FROM (SELECT unnest(pr.strengths) as strength) sub
        GROUP BY strength
    ) stc ON stc.strength = st.strength
    WHERE 
        (p_questionnaire_id IS NULL OR pr.questionnaire_id = p_questionnaire_id)
        AND (p_program_id IS NULL OR pr.leadership_program_id = p_program_id)
        AND (p_school_id IS NULL OR pr.school_id = p_school_id)
        AND (p_start_date IS NULL OR pr.response_date >= p_start_date)
        AND (p_end_date IS NULL OR pr.response_date <= p_end_date);
END;
$$;

-- Function to get comprehensive NGO impact dashboard data
CREATE OR REPLACE FUNCTION get_ngo_impact_dashboard(
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    dashboard_data JSONB
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    dashboard_json JSONB;
BEGIN
    -- Set default date range if not provided (last 12 months)
    IF p_start_date IS NULL THEN
        p_start_date := (CURRENT_DATE - INTERVAL '12 months')::DATE;
    END IF;

    IF p_end_date IS NULL THEN
        p_end_date := CURRENT_DATE;
    END IF;

    -- Build comprehensive dashboard data
    SELECT jsonb_build_object(
        'period', jsonb_build_object(
            'start_date', p_start_date,
            'end_date', p_end_date,
            'duration_months', EXTRACT(MONTH FROM AGE(p_end_date, p_start_date))
        ),
        'overall_impact', jsonb_build_object(
            'total_students_reached', (
                SELECT COALESCE(SUM(ar.total_participants), 0)
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
                AND ar.status IN ('approved', 'published')
            ),
            'total_schools_engaged', (
                SELECT COUNT(DISTINCT ar.school_id)
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
                AND ar.status IN ('approved', 'published')
            ),
            'total_activities_conducted', (
                SELECT COUNT(ar.id)
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
                AND ar.status IN ('approved', 'published')
            ),
            'total_training_hours', (
                SELECT COALESCE(SUM(ar.duration_hours), 0)
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
                AND ar.status IN ('approved', 'published')
            )
        ),
        'student_leadership_impact', jsonb_build_object(
            'programs_completed', (
                SELECT COUNT(slp.id)
                FROM student_leadership_programs slp
                WHERE slp.end_date BETWEEN p_start_date AND p_end_date
            ),
            'average_improvement_score', (
                SELECT ROUND(AVG(slpa.improvement_score), 2)
                FROM student_leadership_participation slpa
                JOIN student_leadership_programs slp ON slpa.leadership_program_id = slp.id
                WHERE slp.end_date BETWEEN p_start_date AND p_end_date
            ),
            'certification_rate', (
                SELECT ROUND(
                    (COUNT(CASE WHEN slpa.certification_received = true THEN 1 END)::DECIMAL /
                     NULLIF(COUNT(slpa.id)::DECIMAL, 0)) * 100, 2
                )
                FROM student_leadership_participation slpa
                JOIN student_leadership_programs slp ON slpa.leadership_program_id = slp.id
                WHERE slp.end_date BETWEEN p_start_date AND p_end_date
            )
        ),
        'geographic_reach', jsonb_build_object(
            'regions_covered', (
                SELECT ARRAY_AGG(DISTINCT ar.school_region)
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
                AND ar.school_region IS NOT NULL
            ),
            'districts_covered', (
                SELECT ARRAY_AGG(DISTINCT ar.school_district)
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
                AND ar.school_district IS NOT NULL
            )
        ),
        'quality_metrics', jsonb_build_object(
            'average_engagement_level', (
                SELECT ROUND(AVG(ar.participant_engagement_level), 2)
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
                AND ar.participant_engagement_level IS NOT NULL
            ),
            'report_completion_rate', (
                SELECT ROUND(
                    (COUNT(CASE WHEN ar.status IN ('approved', 'published') THEN 1 END)::DECIMAL /
                     NULLIF(COUNT(ar.id)::DECIMAL, 0)) * 100, 2
                )
                FROM activity_reports ar
                WHERE ar.activity_date BETWEEN p_start_date AND p_end_date
            )
        )
    ) INTO dashboard_json;

    RETURN QUERY SELECT dashboard_json;
END;
$$;

-- Function to create automated monthly report
CREATE OR REPLACE FUNCTION create_automated_monthly_report(
    p_month INTEGER,
    p_year INTEGER,
    p_generated_by UUID
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    report_id UUID;
    report_data JSONB;
    activity_ids UUID[];
BEGIN
    -- Get the generated report data
    SELECT rd.report_data INTO report_data
    FROM generate_monthly_report_data(p_month, p_year) rd;

    -- Get all activity report IDs for the month
    SELECT ARRAY_AGG(ar.id) INTO activity_ids
    FROM activity_reports ar
    WHERE EXTRACT(MONTH FROM ar.activity_date) = p_month
    AND EXTRACT(YEAR FROM ar.activity_date) = p_year
    AND ar.status IN ('approved', 'published');

    -- Insert the monthly report
    INSERT INTO monthly_reports (
        report_month,
        report_year,
        report_title,
        total_activities,
        total_participants,
        total_schools_reached,
        leadership_training_sessions,
        school_visits,
        community_engagements,
        capacity_building_sessions,
        total_male_participants,
        total_female_participants,
        regions_covered,
        districts_covered,
        schools_list,
        achievement_rate,
        average_participant_satisfaction,
        activity_report_ids,
        generated_by,
        status
    ) VALUES (
        p_month,
        p_year,
        'Monthly Report - ' || TO_CHAR(DATE(p_year || '-' || p_month || '-01'), 'Month YYYY'),
        (report_data->'summary'->>'total_activities')::INTEGER,
        (report_data->'summary'->>'total_participants')::INTEGER,
        (report_data->'summary'->>'total_schools_reached')::INTEGER,
        (report_data->'activity_breakdown'->>'leadership_training')::INTEGER,
        (report_data->'activity_breakdown'->>'school_visits')::INTEGER,
        (report_data->'activity_breakdown'->>'community_engagement')::INTEGER,
        (report_data->'activity_breakdown'->>'capacity_building')::INTEGER,
        (report_data->'summary'->>'total_male_participants')::INTEGER,
        (report_data->'summary'->>'total_female_participants')::INTEGER,
        ARRAY(SELECT jsonb_array_elements_text(report_data->'geographic_coverage'->'regions')),
        ARRAY(SELECT jsonb_array_elements_text(report_data->'geographic_coverage'->'districts')),
        report_data->'geographic_coverage'->'schools',
        (report_data->'performance_metrics'->>'completion_rate')::DECIMAL,
        (report_data->'performance_metrics'->>'participant_satisfaction')::DECIMAL,
        activity_ids,
        p_generated_by,
        'draft'
    ) RETURNING id INTO report_id;

    RETURN report_id;
END;
$$;
