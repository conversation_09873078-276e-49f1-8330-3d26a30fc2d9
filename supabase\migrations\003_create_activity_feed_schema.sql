-- Activity Feed Database Schema
-- Task T1.5: Activity Feed Database Schema

-- Create activity type enum
CREATE TYPE activity_type AS ENUM (
    'task_created', 
    'task_updated', 
    'task_completed', 
    'distribution_logged', 
    'school_added', 
    'comment_added'
);

-- Create entity type enum
CREATE TYPE entity_type AS ENUM ('task', 'distribution', 'school', 'comment');

-- Create activities table
CREATE TABLE activities (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    activity_type activity_type NOT NULL,
    user_id UUID REFERENCES profiles(id) NOT NULL,
    entity_type entity_type NOT NULL,
    entity_id UUID NOT NULL,
    description TEXT NOT NULL,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_activities_user_id ON activities(user_id);
CREATE INDEX idx_activities_activity_type ON activities(activity_type);
CREATE INDEX idx_activities_entity_type ON activities(entity_type);
CREATE INDEX idx_activities_entity_id ON activities(entity_id);
CREATE INDEX idx_activities_created_at ON activities(created_at DESC);

-- Row Level Security (RLS) policies
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;

-- RLS policy for activities - users can see activities related to their work
CREATE POLICY "Users can view relevant activities" ON activities
    FOR SELECT USING (
        -- Users can see their own activities
        user_id = auth.uid() OR
        -- Users can see activities for tasks they're involved in
        (entity_type = 'task' AND EXISTS (
            SELECT 1 FROM tasks 
            WHERE id = activities.entity_id AND (
                assigned_to = auth.uid() OR 
                created_by = auth.uid()
            )
        )) OR
        -- Users can see activities for schools in their division (if they have one)
        (entity_type = 'school' AND EXISTS (
            SELECT 1 FROM profiles p
            JOIN schools s ON s.division_id = p.division_id
            WHERE p.id = auth.uid() AND s.id = activities.entity_id
        )) OR
        -- Admins and program officers can see all activities
        EXISTS (
            SELECT 1 FROM profiles 
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Function to automatically log activities via triggers
CREATE OR REPLACE FUNCTION log_activity_trigger()
RETURNS TRIGGER AS $$
BEGIN
    -- Log task creation
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'task_created',
            NEW.created_by,
            'task',
            NEW.id,
            'Created task: ' || NEW.title,
            json_build_object(
                'task_id', NEW.id,
                'task_title', NEW.title,
                'priority', NEW.priority,
                'assigned_to', NEW.assigned_to
            )
        );
        RETURN NEW;
    END IF;

    -- Log task updates
    IF TG_TABLE_NAME = 'tasks' AND TG_OP = 'UPDATE' THEN
        -- Log status changes
        IF OLD.status != NEW.status THEN
            INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
            VALUES (
                CASE 
                    WHEN NEW.status = 'completed' THEN 'task_completed'
                    ELSE 'task_updated'
                END,
                auth.uid(),
                'task',
                NEW.id,
                'Updated task status: ' || NEW.title || ' (' || OLD.status || ' → ' || NEW.status || ')',
                json_build_object(
                    'task_id', NEW.id,
                    'task_title', NEW.title,
                    'old_status', OLD.status,
                    'new_status', NEW.status
                )
            );
        END IF;

        -- Log assignment changes
        IF COALESCE(OLD.assigned_to::text, '') != COALESCE(NEW.assigned_to::text, '') THEN
            INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
            VALUES (
                'task_updated',
                auth.uid(),
                'task',
                NEW.id,
                'Reassigned task: ' || NEW.title,
                json_build_object(
                    'task_id', NEW.id,
                    'task_title', NEW.title,
                    'old_assigned_to', OLD.assigned_to,
                    'new_assigned_to', NEW.assigned_to
                )
            );
        END IF;

        RETURN NEW;
    END IF;

    -- Log task comments
    IF TG_TABLE_NAME = 'task_comments' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'comment_added',
            NEW.user_id,
            'comment',
            NEW.id,
            'Added comment to task',
            json_build_object(
                'task_id', NEW.task_id,
                'comment_id', NEW.id,
                'comment_preview', LEFT(NEW.comment, 100)
            )
        );
        RETURN NEW;
    END IF;

    -- Log book distributions
    IF TG_TABLE_NAME = 'book_distributions' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'distribution_logged',
            NEW.supervisor_id,
            'distribution',
            NEW.id,
            'Logged book distribution',
            json_build_object(
                'distribution_id', NEW.id,
                'school_id', NEW.school_id,
                'quantity', NEW.quantity
            )
        );
        RETURN NEW;
    END IF;

    -- Log school additions
    IF TG_TABLE_NAME = 'schools' AND TG_OP = 'INSERT' THEN
        INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description, metadata)
        VALUES (
            'school_added',
            COALESCE(NEW.created_by, auth.uid()),
            'school',
            NEW.id,
            'Added new school: ' || NEW.name,
            json_build_object(
                'school_id', NEW.id,
                'school_name', NEW.name,
                'school_type', NEW.school_type
            )
        );
        RETURN NEW;
    END IF;

    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create triggers for automatic activity logging
CREATE TRIGGER trigger_log_task_activities
    AFTER INSERT OR UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();

CREATE TRIGGER trigger_log_task_comment_activities
    AFTER INSERT ON task_comments
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();

CREATE TRIGGER trigger_log_distribution_activities
    AFTER INSERT ON book_distributions
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();

CREATE TRIGGER trigger_log_school_activities
    AFTER INSERT ON schools
    FOR EACH ROW EXECUTE FUNCTION log_activity_trigger();

-- Function to clean up old activities (>90 days)
CREATE OR REPLACE FUNCTION cleanup_old_activities()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM activities 
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
