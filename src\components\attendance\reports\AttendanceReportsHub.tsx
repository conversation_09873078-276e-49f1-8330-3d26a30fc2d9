import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  FileText, 
  Users, 
  School, 
  TrendingUp,
  Calendar,
  Download,
  Search,
  Filter,
  Plus
} from 'lucide-react';
import { useSchools } from '@/hooks/useSchools';
import { useAuth } from '@/hooks/useAuth';
import IndividualStudentReport from './IndividualStudentReport';
import ClassSummaryReport from './ClassSummaryReport';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

interface AttendanceReportsHubProps {
  defaultSchoolId?: string;
}

const AttendanceReportsHub: React.FC<AttendanceReportsHubProps> = ({
  defaultSchoolId,
}) => {
  const { profile } = useAuth();
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || '');
  const [selectedStudentId, setSelectedStudentId] = useState('');
  const [selectedSessionId, setSelectedSessionId] = useState('');
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0],
  });
  const [activeTab, setActiveTab] = useState('individual');

  const { data: schools } = useSchools();

  const reportTypes = [
    {
      id: 'individual',
      title: 'Individual Student Report',
      description: 'Detailed attendance analysis for a specific student',
      icon: Users,
      color: 'bg-blue-100 text-blue-600',
    },
    {
      id: 'class',
      title: 'Class Summary Report',
      description: 'Session-based attendance summary with participation analysis',
      icon: School,
      color: 'bg-green-100 text-green-600',
    },
    {
      id: 'school',
      title: 'School-Level Report',
      description: 'Comprehensive school attendance analytics and trends',
      icon: TrendingUp,
      color: 'bg-purple-100 text-purple-600',
    },
    {
      id: 'funder',
      title: 'Funder Impact Report',
      description: 'NGO-ready report with impact metrics and outcomes correlation',
      icon: FileText,
      color: 'bg-orange-100 text-orange-600',
    },
  ];

  const handleGenerateReport = () => {
    // Report generation logic is handled by the individual report components
    console.log('Generating report for tab:', activeTab);
  };

  const canAccessReports = () => {
    return profile?.role === 'admin' || profile?.role === 'program_officer';
  };

  if (!canAccessReports()) {
    return (
      <PageLayout>
        <ContentCard>
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
            <p className="text-gray-600">
              You don't have permission to access attendance reports. Please contact your administrator.
            </p>
          </div>
        </ContentCard>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Attendance Reports"
        description="Generate comprehensive attendance reports for students, classes, schools, and funders"
      />

      {/* Report Type Selection */}
      <ContentCard>
        <CardHeader>
          <CardTitle>Report Types</CardTitle>
          <CardDescription>
            Choose the type of attendance report you want to generate
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {reportTypes.map((type) => {
              const Icon = type.icon;
              return (
                <div
                  key={type.id}
                  className={`border rounded-lg p-4 cursor-pointer transition-all ${
                    activeTab === type.id 
                      ? 'border-purple-200 bg-purple-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setActiveTab(type.id)}
                >
                  <div className={`p-2 rounded-lg w-fit mb-3 ${type.color}`}>
                    <Icon className="h-5 w-5" />
                  </div>
                  <div className="font-medium mb-1">{type.title}</div>
                  <div className="text-sm text-gray-600">{type.description}</div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </ContentCard>

      {/* Report Configuration */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsContent value="individual" className="space-y-6">
          <ContentCard>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Individual Student Report Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">School</label>
                  <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select school" />
                    </SelectTrigger>
                    <SelectContent>
                      {schools?.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Student ID</label>
                  <Input
                    placeholder="Enter student ID"
                    value={selectedStudentId}
                    onChange={(e) => setSelectedStudentId(e.target.value)}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Start Date</label>
                  <Input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">End Date</label>
                  <Input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  />
                </div>
              </div>
            </CardContent>
          </ContentCard>

          {/* Individual Student Report */}
          {selectedStudentId && (
            <IndividualStudentReport
              studentId={selectedStudentId}
              startDate={new Date(dateRange.start)}
              endDate={new Date(dateRange.end)}
              schoolId={selectedSchoolId || undefined}
            />
          )}
        </TabsContent>

        <TabsContent value="class" className="space-y-6">
          <ContentCard>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <School className="h-5 w-5" />
                Class Summary Report Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">School</label>
                  <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select school" />
                    </SelectTrigger>
                    <SelectContent>
                      {schools?.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Session ID</label>
                  <Input
                    placeholder="Enter session ID"
                    value={selectedSessionId}
                    onChange={(e) => setSelectedSessionId(e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </ContentCard>

          {/* Class Summary Report */}
          {selectedSessionId && (
            <ClassSummaryReport sessionId={selectedSessionId} />
          )}
        </TabsContent>

        <TabsContent value="school" className="space-y-6">
          <ContentCard>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                School-Level Report Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">School</label>
                  <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select school" />
                    </SelectTrigger>
                    <SelectContent>
                      {schools?.map((school) => (
                        <SelectItem key={school.id} value={school.id}>
                          {school.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Start Date</label>
                  <Input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">End Date</label>
                  <Input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  />
                </div>
              </div>

              <div className="mt-6 text-center">
                <div className="text-gray-500 mb-4">
                  <TrendingUp className="h-12 w-12 mx-auto mb-2" />
                  School-level reporting coming soon
                </div>
                <p className="text-sm text-gray-600">
                  Comprehensive school analytics with grade-level breakdowns, trends, and comparative analysis.
                </p>
              </div>
            </CardContent>
          </ContentCard>
        </TabsContent>

        <TabsContent value="funder" className="space-y-6">
          <ContentCard>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Funder Impact Report Configuration
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Reporting Period</label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select period" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="annual">Annual</SelectItem>
                      <SelectItem value="custom">Custom Range</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Start Date</label>
                  <Input
                    type="date"
                    value={dateRange.start}
                    onChange={(e) => setDateRange(prev => ({ ...prev, start: e.target.value }))}
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">End Date</label>
                  <Input
                    type="date"
                    value={dateRange.end}
                    onChange={(e) => setDateRange(prev => ({ ...prev, end: e.target.value }))}
                  />
                </div>
              </div>

              <div className="mt-6 text-center">
                <div className="text-gray-500 mb-4">
                  <FileText className="h-12 w-12 mx-auto mb-2" />
                  Funder impact reporting coming soon
                </div>
                <p className="text-sm text-gray-600">
                  Comprehensive impact reports with attendance correlation to learning outcomes, 
                  demographic analysis, and program effectiveness metrics for NGO funders.
                </p>
              </div>
            </CardContent>
          </ContentCard>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <ContentCard>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export All Reports
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule Report
            </Button>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create Template
            </Button>
          </div>
        </CardContent>
      </ContentCard>
    </PageLayout>
  );
};

export default AttendanceReportsHub;
