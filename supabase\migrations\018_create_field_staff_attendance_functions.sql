-- Field Staff Attendance Functions
-- Migration 018: Database functions for field staff check-in/check-out and reporting

-- Function for field staff check-in
CREATE OR REPLACE FUNCTION field_staff_checkin(
    p_school_id UUID,
    p_latitude DECIMAL(10,8),
    p_longitude DECIMAL(11,8),
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_verification_method VARCHAR(50) DEFAULT 'gps',
    p_device_info JSONB DEFAULT '{}',
    p_network_info JSONB DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT false
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_id UUID;
    existing_attendance_id UUID;
    distance_meters DECIMAL(8,2);
    school_location POINT;
BEGIN
    -- Check if user is field staff
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
    ) THEN
        RAISE EXCEPTION 'Only field staff can check in';
    END IF;

    -- Check if staff already checked in today at this school
    SELECT id INTO existing_attendance_id
    FROM field_staff_attendance
    WHERE staff_id = auth.uid()
    AND school_id = p_school_id
    AND attendance_date = CURRENT_DATE
    AND status = 'active';

    IF existing_attendance_id IS NOT NULL THEN
        RAISE EXCEPTION 'Already checked in at this school today';
    END IF;

    -- Get school location for distance calculation (if available)
    SELECT location_coordinates INTO school_location
    FROM schools WHERE id = p_school_id;

    -- Calculate distance from school
    IF school_location IS NOT NULL THEN
        distance_meters := ST_Distance(
            ST_GeogFromText('POINT(' || p_longitude || ' ' || p_latitude || ')'),
            ST_GeogFromText('POINT(' || ST_X(school_location) || ' ' || ST_Y(school_location) || ')')
        );
    END IF;

    -- Insert attendance record
    INSERT INTO field_staff_attendance (
        staff_id, school_id, attendance_date, check_in_time,
        check_in_location, check_in_accuracy, check_in_address,
        distance_from_school, location_verified, verification_method,
        device_info, network_info, offline_sync, status
    )
    VALUES (
        auth.uid(), p_school_id, CURRENT_DATE, NOW(),
        POINT(p_longitude, p_latitude), p_accuracy, p_address,
        distance_meters, (distance_meters IS NULL OR distance_meters <= 100),
        p_verification_method, p_device_info, p_network_info, p_offline_sync, 'active'
    )
    RETURNING id INTO attendance_id;

    -- Log activity
    INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description)
    VALUES (
        'task_created'::activity_type,
        auth.uid(),
        'task'::entity_type,
        attendance_id,
        'Checked in at school: ' || (SELECT name FROM schools WHERE id = p_school_id)
    );

    RETURN attendance_id;
END;
$$;

-- Function for field staff check-out with field report (location optional)
CREATE OR REPLACE FUNCTION field_staff_checkout(
    p_attendance_id UUID,
    p_latitude DECIMAL(10,8) DEFAULT NULL,
    p_longitude DECIMAL(11,8) DEFAULT NULL,
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    -- Field report data
    p_activity_type field_activity_type,
    p_round_table_sessions INTEGER DEFAULT 0,
    p_total_students INTEGER DEFAULT 0,
    p_students_per_session INTEGER DEFAULT 8,
    p_activities_conducted TEXT[] DEFAULT '{}',
    p_topics_covered TEXT[] DEFAULT '{}',
    p_challenges TEXT DEFAULT NULL,
    p_wins TEXT DEFAULT NULL,
    p_observations TEXT DEFAULT NULL,
    p_lessons_learned TEXT DEFAULT NULL,
    p_follow_up_required BOOLEAN DEFAULT false,
    p_follow_up_actions TEXT DEFAULT NULL,
    p_photos TEXT[] DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT false
)
RETURNS JSONB
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_record RECORD;
    field_report_id UUID;
    duration_minutes INTEGER;
    result JSONB;
BEGIN
    -- Get attendance record and verify ownership
    SELECT * INTO attendance_record
    FROM field_staff_attendance
    WHERE id = p_attendance_id AND staff_id = auth.uid() AND status = 'active';

    IF attendance_record IS NULL THEN
        RAISE EXCEPTION 'Invalid attendance record or already checked out';
    END IF;

    -- Calculate duration
    duration_minutes := EXTRACT(EPOCH FROM (NOW() - attendance_record.check_in_time)) / 60;

    -- Update attendance record with check-out info (location optional)
    UPDATE field_staff_attendance
    SET
        check_out_time = NOW(),
        check_out_location = CASE
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL
            THEN POINT(p_longitude, p_latitude)
            ELSE NULL
        END,
        check_out_accuracy = p_accuracy,
        check_out_address = p_address,
        total_duration_minutes = duration_minutes,
        status = 'completed',
        notes = p_notes,
        sync_timestamp = CASE WHEN p_offline_sync THEN NOW() ELSE NULL END,
        updated_at = NOW()
    WHERE id = p_attendance_id;

    -- Create field report
    INSERT INTO field_reports (
        attendance_id, staff_id, school_id, report_date, activity_type,
        round_table_sessions_count, total_students_attended, students_per_session,
        activities_conducted, topics_covered, challenges_encountered,
        wins_achieved, general_observations, lessons_learned,
        follow_up_required, follow_up_actions, photos,
        report_location, location_verified, offline_created,
        sync_timestamp, report_status
    )
    VALUES (
        p_attendance_id, auth.uid(), attendance_record.school_id, CURRENT_DATE, p_activity_type,
        p_round_table_sessions, p_total_students, p_students_per_session,
        p_activities_conducted, p_topics_covered, p_challenges,
        p_wins, p_observations, p_lessons_learned,
        p_follow_up_required, p_follow_up_actions, p_photos,
        CASE
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL
            THEN POINT(p_longitude, p_latitude)
            ELSE NULL
        END,
        CASE
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL
            THEN true
            ELSE false
        END,
        p_offline_sync,
        CASE WHEN p_offline_sync THEN NOW() ELSE NULL END, 'submitted'
    )
    RETURNING id INTO field_report_id;

    -- Update or create daily timesheet
    PERFORM update_daily_timesheet(auth.uid(), CURRENT_DATE);

    -- Log activity
    INSERT INTO activities (activity_type, user_id, entity_type, entity_id, description)
    VALUES (
        'task_completed'::activity_type,
        auth.uid(),
        'task'::entity_type,
        field_report_id,
        'Checked out and submitted field report for: ' || (SELECT name FROM schools WHERE id = attendance_record.school_id)
    );

    -- Return result
    result := jsonb_build_object(
        'attendance_id', p_attendance_id,
        'field_report_id', field_report_id,
        'duration_minutes', duration_minutes,
        'status', 'success'
    );

    RETURN result;
END;
$$;

-- Function to update daily timesheet (auto-generated)
CREATE OR REPLACE FUNCTION update_daily_timesheet(
    p_staff_id UUID,
    p_date DATE
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    timesheet_id UUID;
    total_hours DECIMAL(4,2);
    schools_count INTEGER;
    students_count INTEGER;
    sessions_count INTEGER;
    schools_data JSONB;
    key_achievements TEXT;
    main_challenges TEXT;
BEGIN
    -- Calculate daily metrics
    SELECT 
        COALESCE(SUM(total_duration_minutes), 0) / 60.0,
        COUNT(DISTINCT school_id),
        COALESCE(SUM(fr.total_students_attended), 0),
        COALESCE(SUM(fr.round_table_sessions_count), 0)
    INTO total_hours, schools_count, students_count, sessions_count
    FROM field_staff_attendance fsa
    LEFT JOIN field_reports fr ON fsa.id = fr.attendance_id
    WHERE fsa.staff_id = p_staff_id 
    AND fsa.attendance_date = p_date
    AND fsa.status = 'completed';

    -- Get schools visited data
    SELECT jsonb_agg(
        jsonb_build_object(
            'school_id', s.id,
            'school_name', s.name,
            'check_in_time', fsa.check_in_time,
            'check_out_time', fsa.check_out_time,
            'duration_minutes', fsa.total_duration_minutes,
            'students_reached', COALESCE(fr.total_students_attended, 0),
            'sessions_conducted', COALESCE(fr.round_table_sessions_count, 0)
        )
    )
    INTO schools_data
    FROM field_staff_attendance fsa
    JOIN schools s ON fsa.school_id = s.id
    LEFT JOIN field_reports fr ON fsa.id = fr.attendance_id
    WHERE fsa.staff_id = p_staff_id 
    AND fsa.attendance_date = p_date
    AND fsa.status = 'completed';

    -- Aggregate achievements and challenges
    SELECT 
        string_agg(DISTINCT fr.wins_achieved, '; '),
        string_agg(DISTINCT fr.challenges_encountered, '; ')
    INTO key_achievements, main_challenges
    FROM field_reports fr
    WHERE fr.staff_id = p_staff_id 
    AND fr.report_date = p_date
    AND fr.wins_achieved IS NOT NULL;

    -- Insert or update timesheet
    INSERT INTO daily_timesheets (
        staff_id, timesheet_date, total_work_hours, total_schools_visited,
        total_students_reached, total_sessions_conducted, schools_visited,
        key_achievements, main_challenges, auto_generated, timesheet_status
    )
    VALUES (
        p_staff_id, p_date, total_hours, schools_count,
        students_count, sessions_count, COALESCE(schools_data, '[]'::jsonb),
        key_achievements, main_challenges, true, 'submitted'
    )
    ON CONFLICT (staff_id, timesheet_date)
    DO UPDATE SET
        total_work_hours = EXCLUDED.total_work_hours,
        total_schools_visited = EXCLUDED.total_schools_visited,
        total_students_reached = EXCLUDED.total_students_reached,
        total_sessions_conducted = EXCLUDED.total_sessions_conducted,
        schools_visited = EXCLUDED.schools_visited,
        key_achievements = EXCLUDED.key_achievements,
        main_challenges = EXCLUDED.main_challenges,
        updated_at = NOW()
    RETURNING id INTO timesheet_id;

    RETURN timesheet_id;
END;
$$;

-- Function to get field staff daily timesheets for admin dashboard
CREATE OR REPLACE FUNCTION get_field_staff_timesheets(
    p_date DATE DEFAULT CURRENT_DATE,
    p_staff_id UUID DEFAULT NULL
)
RETURNS TABLE (
    timesheet_id UUID,
    staff_id UUID,
    staff_name TEXT,
    staff_role TEXT,
    timesheet_date DATE,
    total_work_hours DECIMAL(4,2),
    total_schools_visited INTEGER,
    total_students_reached INTEGER,
    total_sessions_conducted INTEGER,
    schools_visited JSONB,
    key_achievements TEXT,
    main_challenges TEXT,
    timesheet_status TEXT,
    productivity_score DECIMAL(3,2),
    submitted_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check if user has permission to view timesheets
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to view timesheets';
    END IF;

    RETURN QUERY
    SELECT
        dt.id as timesheet_id,
        dt.staff_id,
        p.name as staff_name,
        p.role::TEXT as staff_role,
        dt.timesheet_date,
        dt.total_work_hours,
        dt.total_schools_visited,
        dt.total_students_reached,
        dt.total_sessions_conducted,
        dt.schools_visited,
        dt.key_achievements,
        dt.main_challenges,
        dt.timesheet_status,
        dt.productivity_score,
        dt.submitted_at
    FROM daily_timesheets dt
    JOIN profiles p ON dt.staff_id = p.id
    WHERE dt.timesheet_date = p_date
    AND (p_staff_id IS NULL OR dt.staff_id = p_staff_id)
    AND p.role = 'field_staff'
    ORDER BY p.name;
END;
$$;

-- Function to get field staff attendance status
CREATE OR REPLACE FUNCTION get_field_staff_attendance_status(
    p_date DATE DEFAULT CURRENT_DATE,
    p_staff_id UUID DEFAULT NULL
)
RETURNS TABLE (
    staff_id UUID,
    staff_name TEXT,
    attendance_date DATE,
    total_check_ins INTEGER,
    current_status TEXT,
    current_school_name TEXT,
    check_in_time TIMESTAMP WITH TIME ZONE,
    hours_worked DECIMAL(4,2),
    schools_visited_today INTEGER
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- Check permissions
    IF NOT EXISTS (
        SELECT 1 FROM profiles
        WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
    ) THEN
        RAISE EXCEPTION 'Insufficient permissions to view attendance status';
    END IF;

    RETURN QUERY
    SELECT
        p.id as staff_id,
        p.name as staff_name,
        p_date as attendance_date,
        COALESCE(daily_stats.total_check_ins, 0) as total_check_ins,
        COALESCE(current_attendance.status, 'not_checked_in') as current_status,
        current_school.name as current_school_name,
        current_attendance.check_in_time,
        COALESCE(daily_stats.hours_worked, 0) as hours_worked,
        COALESCE(daily_stats.schools_visited, 0) as schools_visited_today
    FROM profiles p
    LEFT JOIN (
        SELECT
            fsa.staff_id,
            COUNT(*) as total_check_ins,
            SUM(COALESCE(fsa.total_duration_minutes, 0)) / 60.0 as hours_worked,
            COUNT(DISTINCT fsa.school_id) as schools_visited
        FROM field_staff_attendance fsa
        WHERE fsa.attendance_date = p_date
        GROUP BY fsa.staff_id
    ) daily_stats ON p.id = daily_stats.staff_id
    LEFT JOIN field_staff_attendance current_attendance ON (
        p.id = current_attendance.staff_id
        AND current_attendance.attendance_date = p_date
        AND current_attendance.status = 'active'
    )
    LEFT JOIN schools current_school ON current_attendance.school_id = current_school.id
    WHERE p.role = 'field_staff'
    AND p.is_active = true
    AND (p_staff_id IS NULL OR p.id = p_staff_id)
    ORDER BY p.name;
END;
$$;
