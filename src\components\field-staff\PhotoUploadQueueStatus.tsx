import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Upload,
  CheckCircle,
  AlertCircle,
  Loader2,
  Pause,
  Play,
  RotateCcw,
  Trash2,
  Image,
  Clock,
  Wifi,
  WifiOff
} from 'lucide-react';
import { usePhotoUploadQueue, usePhotoUploadStats } from '@/hooks/field-staff/usePhotoUploadQueue';
import { formatFileSize, formatDuration } from '@/utils/formatters';

interface PhotoUploadQueueStatusProps {
  compact?: boolean;
  showDetails?: boolean;
}

const PhotoUploadQueueStatus: React.FC<PhotoUploadQueueStatusProps> = ({
  compact = false,
  showDetails = false
}) => {
  const [showFullQueue, setShowFullQueue] = useState(false);
  const {
    queueStats,
    queueItems,
    isProcessing,
    retryFailedUploads,
    clearCompleted,
    pauseQueue,
    resumeQueue,
    removeFromQueue
  } = usePhotoUploadQueue();

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'uploading':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'paused':
        return <Pause className="h-4 w-4 text-orange-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      pending: 'secondary',
      uploading: 'default',
      completed: 'success',
      failed: 'destructive',
      paused: 'warning'
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };

  const formatEstimatedTime = (ms?: number) => {
    if (!ms) return 'Unknown';
    const seconds = Math.ceil(ms / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.ceil(seconds / 60);
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.ceil(minutes / 60);
    return `${hours}h`;
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2 p-2 bg-gray-50 rounded-lg">
        <Upload className="h-4 w-4 text-gray-600" />
        <span className="text-sm text-gray-700">
          {queueStats.uploadingItems > 0 ? (
            <>Uploading {queueStats.uploadingItems} photos...</>
          ) : queueStats.pendingItems > 0 ? (
            <>
              {queueStats.pendingItems} photos queued
              {!navigator.onLine && <WifiOff className="h-3 w-3 ml-1 text-orange-500" />}
            </>
          ) : queueStats.totalItems > 0 ? (
            <>All photos uploaded</>
          ) : (
            <>No photos in queue</>
          )}
        </span>
        {queueStats.totalItems > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowFullQueue(!showFullQueue)}
          >
            {showFullQueue ? 'Hide' : 'Show'}
          </Button>
        )}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Photo Upload Queue
          {!navigator.onLine && (
            <Badge variant="warning" className="ml-2">
              <WifiOff className="h-3 w-3 mr-1" />
              Offline
            </Badge>
          )}
        </CardTitle>
        <CardDescription>
          Monitor and manage photo uploads
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Queue Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{queueStats.totalItems}</div>
            <div className="text-sm text-blue-700">Total</div>
          </div>
          <div className="text-center p-3 bg-orange-50 rounded-lg">
            <div className="text-2xl font-bold text-orange-600">{queueStats.pendingItems}</div>
            <div className="text-sm text-orange-700">Pending</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{queueStats.completedItems}</div>
            <div className="text-sm text-green-700">Completed</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{queueStats.failedItems}</div>
            <div className="text-sm text-red-700">Failed</div>
          </div>
        </div>

        {/* Overall Progress */}
        {queueStats.totalItems > 0 && (
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Overall Progress</span>
              <span>
                {formatFileSize(queueStats.uploadedSize)} / {formatFileSize(queueStats.totalSize)}
              </span>
            </div>
            <Progress 
              value={(queueStats.uploadedSize / queueStats.totalSize) * 100} 
              className="h-2"
            />
            {queueStats.estimatedTimeRemaining && (
              <div className="text-xs text-gray-500 text-center">
                Estimated time remaining: {formatEstimatedTime(queueStats.estimatedTimeRemaining)}
              </div>
            )}
          </div>
        )}

        {/* Queue Controls */}
        <div className="flex flex-wrap gap-2">
          {isProcessing ? (
            <Button variant="outline" size="sm" onClick={pauseQueue}>
              <Pause className="h-4 w-4 mr-2" />
              Pause Queue
            </Button>
          ) : (
            <Button variant="outline" size="sm" onClick={resumeQueue}>
              <Play className="h-4 w-4 mr-2" />
              Resume Queue
            </Button>
          )}
          
          {queueStats.failedItems > 0 && (
            <Button variant="outline" size="sm" onClick={retryFailedUploads}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Retry Failed ({queueStats.failedItems})
            </Button>
          )}
          
          {queueStats.completedItems > 0 && (
            <Button variant="outline" size="sm" onClick={clearCompleted}>
              <Trash2 className="h-4 w-4 mr-2" />
              Clear Completed ({queueStats.completedItems})
            </Button>
          )}
        </div>

        {/* Connection Status */}
        {!navigator.onLine && (
          <Alert>
            <WifiOff className="h-4 w-4" />
            <AlertDescription>
              You're currently offline. Photos will be uploaded automatically when connection is restored.
            </AlertDescription>
          </Alert>
        )}

        {/* Queue Items (if showing details) */}
        {(showDetails || showFullQueue) && queueItems.length > 0 && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h4 className="font-medium">Queue Items</h4>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFullQueue(!showFullQueue)}
              >
                {showFullQueue ? 'Hide Details' : 'Show Details'}
              </Button>
            </div>
            
            {showFullQueue && (
              <div className="space-y-2 max-h-64 overflow-y-auto">
                {queueItems.map((item) => (
                  <div
                    key={item.id}
                    className="flex items-center gap-3 p-3 border rounded-lg"
                  >
                    <Image className="h-4 w-4 text-gray-500" />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium truncate">
                          {item.fileName}
                        </span>
                        {getStatusBadge(item.status)}
                      </div>
                      
                      <div className="text-xs text-gray-500">
                        {formatFileSize(item.blob.size)}
                        {item.metadata && (
                          <span className="ml-2">
                            (Compressed: {Math.round(item.metadata.compressionRatio * 100)}%)
                          </span>
                        )}
                      </div>
                      
                      {item.status === 'uploading' && (
                        <Progress value={item.uploadProgress} className="h-1 mt-1" />
                      )}
                      
                      {item.error && (
                        <div className="text-xs text-red-600 mt-1">{item.error}</div>
                      )}
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {getStatusIcon(item.status)}
                      
                      {(item.status === 'failed' || item.status === 'completed') && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => removeFromQueue(item.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Empty State */}
        {queueStats.totalItems === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Upload className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No photos in upload queue</p>
            <p className="text-sm">Photos will appear here when added for upload</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PhotoUploadQueueStatus;
