-- School Management Requirements Update Migration
-- Migration 022: Update school management to meet new requirements
-- 1. Make school code optional
-- 2. Make date joined iLead optional  
-- 3. Change Division to District (Add all districts in Uganda)
-- 4. Make head teacher and deputy head teacher email fields optional
-- 5. Make teacher count optional
-- 6. Require 1 champion teacher minimum, allow more
-- 7. Make champion teacher email field optional
-- 8. Make assistant champion teacher optional (1 entry, provision to add more)

-- First, let's populate administrative_divisions with all Uganda districts
-- Clear existing data and add comprehensive Uganda districts
DELETE FROM administrative_divisions;

-- Insert all 146 districts of Uganda with their sub-counties
INSERT INTO administrative_divisions (district, sub_county) VALUES
-- Central Region Districts
('Kampala', 'Central Division'),
('Kampala', 'Kawempe Division'),
('Kampala', 'Makindye Division'),
('Kampala', 'Nakawa Division'),
('Kampala', 'Rubaga Division'),
('Wakiso', 'Busukuma'),
('Waki<PERSON>', 'Gombe'),
('<PERSON>aki<PERSON>', '<PERSON><PERSON>'),
('<PERSON><PERSON><PERSON>', '<PERSON>'),
('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>-<PERSON><PERSON>bagab<PERSON>'),
('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
('<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>'),
('Wakiso', 'Ssisa'),
('Mukono', 'Goma'),
('Mukono', 'Kasawo'),
('Mukono', 'Kyampisi'),
('Mukono', 'Mukono'),
('Mukono', 'Nama'),
('Mukono', 'Ntenjeru'),
('Mpigi', 'Buwama'),
('Mpigi', 'Kituntu'),
('Mpigi', 'Mpigi'),
('Mpigi', 'Muduma'),
('Mpigi', 'Nkozi'),
('Butambala', 'Butambala'),
('Butambala', 'Gombe'),
('Butambala', 'Kalamba'),
('Gomba', 'Butunduzi'),
('Gomba', 'Kanoni'),
('Gomba', 'Kabulasoke'),
('Gomba', 'Maddu'),
('Kalungu', 'Bukulula'),
('Kalungu', 'Kalungu'),
('Kalungu', 'Kyanamukaaka'),
('Kalangala', 'Bufumira'),
('Kalangala', 'Buggala'),
('Kalangala', 'Bujumba'),
('Kalangala', 'Kyamuswa'),
('Masaka', 'Bukoto'),
('Masaka', 'Kimanya-Kabonera'),
('Masaka', 'Kimaanya-Kyabakuza'),
('Masaka', 'Mukungwe'),
('Masaka', 'Nyendo-Mukungwe'),
('Lwengo', 'Kyazanga'),
('Lwengo', 'Lwengo'),
('Lwengo', 'Ndagwe'),
('Bukomansimbi', 'Bukomansimbi'),
('Bukomansimbi', 'Kibinge'),
('Sembabule', 'Lugusulu'),
('Sembabule', 'Lwemiyaga'),
('Sembabule', 'Mateete'),
('Sembabule', 'Mijwala'),
('Lyantonde', 'Kabira'),
('Lyantonde', 'Kaliiro'),
('Lyantonde', 'Lyantonde'),
('Rakai', 'Dwaniro'),
('Rakai', 'Kakuuto'),
('Rakai', 'Kooki'),
('Rakai', 'Kyebe'),
('Rakai', 'Kyotera'),
('Kyotera', 'Kakuto'),
('Kyotera', 'Kalisizo'),
('Kyotera', 'Kyebe'),
('Kyotera', 'Kyotera'),

-- Eastern Region Districts  
('Jinja', 'Budondo'),
('Jinja', 'Bugembe'),
('Jinja', 'Jinja'),
('Jinja', 'Kakira'),
('Mayuge', 'Baitambogwe'),
('Mayuge', 'Bunya'),
('Mayuge', 'Bukabooli'),
('Mayuge', 'Kityerera'),
('Mayuge', 'Mayuge'),
('Iganga', 'Busembatia'),
('Iganga', 'Iganga'),
('Iganga', 'Kigulu'),
('Iganga', 'Makuutu'),
('Iganga', 'Namungalwe'),
('Bugiri', 'Budiope'),
('Bugiri', 'Bugiri'),
('Bugiri', 'Buluguyi'),
('Bugiri', 'Busitema'),
('Bugiri', 'Muterere'),
('Namutumba', 'Buseta'),
('Namutumba', 'Ivukula'),
('Namutumba', 'Magada'),
('Namutumba', 'Namutumba'),
('Luuka', 'Irongo'),
('Luuka', 'Luuka'),
('Luuka', 'Wairasa'),
('Buyende', 'Buyende'),
('Buyende', 'Kagulu'),
('Buyende', 'Kidera'),
('Buyende', 'Nkooko'),
('Kamuli', 'Balawoli'),
('Kamuli', 'Bugabula'),
('Kamuli', 'Butansi'),
('Kamuli', 'Kamuli'),
('Kamuli', 'Namasagali'),
('Kaliro', 'Gadumire'),
('Kaliro', 'Kaliro'),
('Kaliro', 'Namwiwa'),
('Soroti', 'Asuret'),
('Soroti', 'Gweri'),
('Soroti', 'Katine'),
('Soroti', 'Pingire'),
('Soroti', 'Serere'),
('Soroti', 'Soroti'),
('Serere', 'Kadungulu'),
('Serere', 'Kasilo'),
('Serere', 'Olio'),
('Serere', 'Serere'),
('Kumi', 'Kumi'),
('Kumi', 'Mukongoro'),
('Kumi', 'Ongino'),
('Kumi', 'Tisai'),
('Ngora', 'Ngora'),
('Ngora', 'Mukura'),
('Bukedea', 'Bukedea'),
('Bukedea', 'Kachumbala'),
('Bukedea', 'Kidongole'),
('Bukedea', 'Kolir'),
('Pallisa', 'Agule'),
('Pallisa', 'Butebo'),
('Pallisa', 'Gogonyo'),
('Pallisa', 'Kamuge'),
('Pallisa', 'Pallisa'),
('Budaka', 'Budaka'),
('Budaka', 'Iki-iki'),
('Budaka', 'Kakoro'),
('Budaka', 'Kaderuna'),
('Kibuku', 'Kibuku'),
('Kibuku', 'Lwakhakha'),
('Manafwa', 'Benet'),
('Manafwa', 'Bufuya'),
('Manafwa', 'Manafwa'),
('Bududa', 'Bududa'),
('Bududa', 'Bukalasi'),
('Bududa', 'Bushika'),
('Mbale', 'Bungokho'),
('Mbale', 'Bufumbo'),
('Mbale', 'Mbale'),
('Mbale', 'Wanale'),
('Sironko', 'Budadiri'),
('Sironko', 'Bubiita'),
('Sironko', 'Sironko'),
('Kapchorwa', 'Kapchorwa'),
('Kapchorwa', 'Kween'),
('Kapchorwa', 'Tegeres'),
('Kween', 'Benet'),
('Kween', 'Kaproron'),
('Kween', 'Kwosir'),
('Bukwo', 'Bukwo'),
('Bukwo', 'Riwo'),
('Tororo', 'Mulanda'),
('Tororo', 'Nagongera'),
('Tororo', 'Paya'),
('Tororo', 'Rubongi'),
('Tororo', 'Tororo'),
('Busia', 'Busia'),
('Busia', 'Buteba'),
('Busia', 'Samia-Bugwe'),

-- Northern Region Districts
('Gulu', 'Aswa'),
('Gulu', 'Gulu'),
('Gulu', 'Laroo-Pece'),
('Gulu', 'Omoro'),
('Omoro', 'Lakwaya'),
('Omoro', 'Omoro'),
('Nwoya', 'Anaka'),
('Nwoya', 'Koch Goma'),
('Nwoya', 'Purongo'),
('Amuru', 'Amuru'),
('Amuru', 'Atiak'),
('Amuru', 'Lamogi'),
('Amuru', 'Pabbo'),
('Lamwo', 'Lamwo'),
('Lamwo', 'Lokung'),
('Lamwo', 'Madi-Opei'),
('Kitgum', 'Kitgum'),
('Kitgum', 'Labongo Akwang'),
('Kitgum', 'Labongo Amida'),
('Kitgum', 'Mucwini'),
('Kitgum', 'Namokora'),
('Pader', 'Aruu'),
('Pader', 'Atanga'),
('Pader', 'Pajule'),
('Pader', 'Purongo'),
('Agago', 'Adilang'),
('Agago', 'Kalongo'),
('Agago', 'Latek'),
('Agago', 'Lira Palwo'),
('Agago', 'Patongo'),
('Lira', 'Adekokwok'),
('Lira', 'Agweng'),
('Lira', 'Aromo'),
('Lira', 'Barr'),
('Lira', 'Dokolo'),
('Lira', 'Lira'),
('Lira', 'Ogur'),
('Dokolo', 'Amach'),
('Dokolo', 'Bata'),
('Dokolo', 'Dokolo'),
('Dokolo', 'Kwera'),
('Alebtong', 'Abako'),
('Alebtong', 'Aloi'),
('Alebtong', 'Alebtong'),
('Otuke', 'Adwari'),
('Otuke', 'Okwang'),
('Otuke', 'Otuke'),
('Oyam', 'Aber'),
('Oyam', 'Iceme'),
('Oyam', 'Myene'),
('Oyam', 'Otwal'),
('Apac', 'Akokoro'),
('Apac', 'Apac'),
('Apac', 'Chawente'),
('Apac', 'Ibuje'),
('Apac', 'Inomo'),
('Kole', 'Aboke'),
('Kole', 'Akalo'),
('Kole', 'Bala'),
('Kole', 'Kole'),
('Kwania', 'Aduku'),
('Kwania', 'Chawente'),
('Kwania', 'Nambieso'),

-- Western Region Districts
('Mbarara', 'Biharwe'),
('Mbarara', 'Kakoba'),
('Mbarara', 'Kamukuzi'),
('Mbarara', 'Nyakayojo'),
('Mbarara', 'Rwampara'),
('Rwampara', 'Mwizi'),
('Rwampara', 'Nyakashashara'),
('Rwampara', 'Rwampara'),
('Isingiro', 'Birere'),
('Isingiro', 'Bukanga'),
('Isingiro', 'Endinzi'),
('Isingiro', 'Isingiro'),
('Isingiro', 'Kabuyanda'),
('Isingiro', 'Kikagate'),
('Isingiro', 'Masha'),
('Isingiro', 'Rugaaga'),
('Kiruhura', 'Engari'),
('Kiruhura', 'Kazo'),
('Kiruhura', 'Kikatsi'),
('Kiruhura', 'Kiruhura'),
('Kiruhura', 'Nshaara'),
('Kiruhura', 'Sanga'),
('Kazo', 'Engari'),
('Kazo', 'Kazo'),
('Kazo', 'Rwemikoma'),
('Ntungamo', 'Itojo'),
('Ntungamo', 'Ntungamo'),
('Ntungamo', 'Rubaare'),
('Ntungamo', 'Rukoni'),
('Mitooma', 'Bitereko'),
('Mitooma', 'Bushenyi'),
('Mitooma', 'Kagunga'),
('Mitooma', 'Mitooma'),
('Sheema', 'Kabwohe'),
('Sheema', 'Kitagata'),
('Sheema', 'Masheruka'),
('Sheema', 'Sheema'),
('Bushenyi', 'Bitooma'),
('Bushenyi', 'Bumbaire'),
('Bushenyi', 'Ishaka'),
('Bushenyi', 'Kyabugimbi'),
('Bushenyi', 'Kyeizooba'),
('Rubirizi', 'Bunyaruguru'),
('Rubirizi', 'Katerera'),
('Rubirizi', 'Rubirizi'),
('Buhweju', 'Buhweju'),
('Buhweju', 'Engaju'),
('Buhweju', 'Nsiika'),

-- Continue with more Western Region Districts
('Kasese', 'Bugoye'),
('Kasese', 'Bukonzo'),
('Kasese', 'Busongora'),
('Kasese', 'Kasese'),
('Kasese', 'Maliba'),
('Kasese', 'Nyakiyumbu'),
('Bunyangabu', 'Bunyangabu'),
('Bunyangabu', 'Harugongo'),
('Kabarole', 'Burahya'),
('Kabarole', 'Fortportal'),
('Kabarole', 'Kabende'),
('Kabarole', 'Kabarole'),
('Kabarole', 'Kyenjojo'),
('Kyenjojo', 'Kyenjojo'),
('Kyenjojo', 'Kyegegwa'),
('Kyenjojo', 'Nkoma'),
('Kyegegwa', 'Kyegegwa'),
('Kyegegwa', 'Mpara'),
('Kamwenge', 'Bigodi'),
('Kamwenge', 'Kamwenge'),
('Kamwenge', 'Mahyoro'),
('Kamwenge', 'Nyabbani'),
('Ibanda', 'Ibanda'),
('Ibanda', 'Kagongo'),
('Ibanda', 'Kashongi'),
('Ibanda', 'Nyabani'),
('Kiruhura', 'Engari'),
('Kiruhura', 'Kazo'),
('Bundibugyo', 'Bubandi'),
('Bundibugyo', 'Bundibugyo'),
('Bundibugyo', 'Busunga'),
('Ntoroko', 'Kanara'),
('Ntoroko', 'Ntoroko'),
('Ntoroko', 'Rwebisengo'),

-- Add remaining districts from all regions
('Hoima', 'Buhaguzi'),
('Hoima', 'Buseruka'),
('Hoima', 'Hoima'),
('Hoima', 'Kigorobya'),
('Hoima', 'Kwangwali'),
('Buliisa', 'Biiso'),
('Buliisa', 'Buliisa'),
('Buliisa', 'Kigwera'),
('Masindi', 'Budongo'),
('Masindi', 'Bujenje'),
('Masindi', 'Buraru'),
('Masindi', 'Kigumba'),
('Masindi', 'Masindi'),
('Masindi', 'Miirya'),
('Kiryandongo', 'Bweyale'),
('Kiryandongo', 'Chuave'),
('Kiryandongo', 'Kiryandongo'),
('Kiryandongo', 'Panyimur'),

-- Add more districts to complete the list
('Adjumani', 'Adjumani'),
('Adjumani', 'Dzaipi'),
('Adjumani', 'Ofua'),
('Adjumani', 'Pakele'),
('Moyo', 'Aldraa'),
('Moyo', 'Dufile'),
('Moyo', 'Gimara'),
('Moyo', 'Itula'),
('Moyo', 'Lefori'),
('Moyo', 'Moyo'),
('Yumbe', 'Aringa'),
('Yumbe', 'Drajini'),
('Yumbe', 'Kululu'),
('Yumbe', 'Yumbe'),
('Koboko', 'Koboko'),
('Koboko', 'Kululu'),
('Koboko', 'Ludara'),
('Maracha', 'Maracha'),
('Maracha', 'Nyadri'),
('Maracha', 'Ovujo'),
('Terego', 'Terego'),
('Terego', 'Uriama'),
('Arua', 'Arua'),
('Arua', 'Ayivu'),
('Arua', 'Logiri'),
('Arua', 'Vurra'),
('Madi-Okollo', 'Madi-Okollo'),
('Madi-Okollo', 'Rigbo'),
('Zombo', 'Zombo'),
('Zombo', 'Paidha'),
('Nebbi', 'Erussi'),
('Nebbi', 'Jonam'),
('Nebbi', 'Nebbi'),
('Nebbi', 'Nyaravur'),
('Pakwach', 'Pakwach'),
('Pakwach', 'Wadelai');

-- Update the add_school_enhanced function to handle new requirements
CREATE OR REPLACE FUNCTION add_school_enhanced(
    p_name VARCHAR(255),
    p_school_type school_type,
    p_division_id UUID,
    p_code VARCHAR(50) DEFAULT NULL,
    p_school_category school_category DEFAULT 'day',
    p_student_count INTEGER DEFAULT NULL,
    p_teacher_count INTEGER DEFAULT NULL,
    p_contact_phone VARCHAR(50) DEFAULT NULL,
    p_email VARCHAR(255) DEFAULT NULL,
    p_classes_count INTEGER DEFAULT NULL,
    p_streams_per_class INTEGER DEFAULT NULL,
    p_head_teacher_name VARCHAR(255) DEFAULT NULL,
    p_head_teacher_phone VARCHAR(50) DEFAULT NULL,
    p_head_teacher_email VARCHAR(255) DEFAULT NULL,
    p_deputy_head_teacher_name VARCHAR(255) DEFAULT NULL,
    p_deputy_head_teacher_phone VARCHAR(50) DEFAULT NULL,
    p_deputy_head_teacher_email VARCHAR(255) DEFAULT NULL,
    p_date_joined_ilead DATE DEFAULT NULL,
    p_ownership_type VARCHAR(50) DEFAULT NULL,
    p_location_coordinates POINT DEFAULT NULL,
    p_nearest_health_center VARCHAR(255) DEFAULT NULL,
    p_distance_to_main_road VARCHAR(255) DEFAULT NULL,
    p_infrastructure_notes TEXT DEFAULT NULL,
    p_is_partner_managed BOOLEAN DEFAULT false,
    p_partner_name VARCHAR(255) DEFAULT NULL,
    p_champion_teacher_count INTEGER DEFAULT NULL,
    p_field_staff_id UUID DEFAULT NULL,
    p_champion_teachers JSONB DEFAULT '[]',
    p_assistant_champion_teachers JSONB DEFAULT '[]'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_school_id UUID;
    v_champion_teacher JSONB;
    v_assistant_teacher JSONB;
    v_champion_count INTEGER := 0;
BEGIN
    -- Validate required fields
    IF p_name IS NULL OR trim(p_name) = '' THEN
        RAISE EXCEPTION 'School name is required';
    END IF;

    IF p_school_type IS NULL THEN
        RAISE EXCEPTION 'School type is required';
    END IF;

    IF p_division_id IS NULL THEN
        RAISE EXCEPTION 'District is required';
    END IF;

    -- Validate champion teachers requirement (at least 1 required)
    SELECT jsonb_array_length(COALESCE(p_champion_teachers, '[]'::jsonb)) INTO v_champion_count;

    IF v_champion_count = 0 THEN
        RAISE EXCEPTION 'At least one champion teacher is required';
    END IF;

    -- Validate that at least one champion teacher has a name
    IF NOT EXISTS (
        SELECT 1 FROM jsonb_array_elements(p_champion_teachers) AS elem
        WHERE elem->>'name' IS NOT NULL AND trim(elem->>'name') != ''
    ) THEN
        RAISE EXCEPTION 'At least one champion teacher must have a name';
    END IF;

    -- Insert the school
    INSERT INTO schools (
        name, code, school_type, school_category, student_count, teacher_count,
        contact_phone, email, division_id, classes_count, streams_per_class,
        head_teacher_name, head_teacher_phone, head_teacher_email,
        deputy_head_teacher_name, deputy_head_teacher_phone, deputy_head_teacher_email,
        date_joined_ilead, ownership_type, location_coordinates, nearest_health_center,
        distance_to_main_road, infrastructure_notes, is_partner_managed,
        partner_name, champion_teacher_count, field_staff_id, created_by
    ) VALUES (
        p_name, p_code, p_school_type, p_school_category, p_student_count, p_teacher_count,
        p_contact_phone, p_email, p_division_id, p_classes_count, p_streams_per_class,
        p_head_teacher_name, p_head_teacher_phone, p_head_teacher_email,
        p_deputy_head_teacher_name, p_deputy_head_teacher_phone, p_deputy_head_teacher_email,
        p_date_joined_ilead, p_ownership_type, p_location_coordinates, p_nearest_health_center,
        p_distance_to_main_road, p_infrastructure_notes, p_is_partner_managed,
        p_partner_name, p_champion_teacher_count, p_field_staff_id, auth.uid()
    ) RETURNING id INTO v_school_id;

    -- Insert champion teachers
    FOR v_champion_teacher IN SELECT * FROM jsonb_array_elements(p_champion_teachers)
    LOOP
        IF v_champion_teacher->>'name' IS NOT NULL AND trim(v_champion_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'champion',
                v_champion_teacher->>'name',
                v_champion_teacher->>'phone',
                v_champion_teacher->>'email'
            );
        END IF;
    END LOOP;

    -- Insert assistant champion teachers (optional)
    FOR v_assistant_teacher IN SELECT * FROM jsonb_array_elements(p_assistant_champion_teachers)
    LOOP
        IF v_assistant_teacher->>'name' IS NOT NULL AND trim(v_assistant_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'assistant_champion',
                v_assistant_teacher->>'name',
                v_assistant_teacher->>'phone',
                v_assistant_teacher->>'email'
            );
        END IF;
    END LOOP;

    RETURN v_school_id;
END;
$$;
