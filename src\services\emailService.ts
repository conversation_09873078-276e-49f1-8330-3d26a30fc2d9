import { supabase } from '@/integrations/supabase/client';

interface InvitationEmailData {
  to: string;
  name: string;
  inviterName: string;
  invitationToken: string;
  role: string;
  organizationName?: string;
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export class EmailService {
  private static readonly BASE_URL = window.location.origin;
  private static readonly ORGANIZATION_NAME = 'iLead Field Track';

  /**
   * Generate invitation email template
   */
  private static generateInvitationTemplate(data: InvitationEmailData): EmailTemplate {
    const acceptUrl = `${this.BASE_URL}/accept-invitation?token=${data.invitationToken}`;
    const roleDisplay = data.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

    const subject = `Invitation to join ${this.ORGANIZATION_NAME}`;

    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Invitation to ${this.ORGANIZATION_NAME}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
          .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
          .warning { background-color: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${this.ORGANIZATION_NAME}</h1>
            <p>You've been invited to join our team</p>
          </div>
          
          <div class="content">
            <h2>Hello ${data.name},</h2>
            
            <p>You have been invited by <strong>${data.inviterName}</strong> to join ${this.ORGANIZATION_NAME} as a <strong>${roleDisplay}</strong>.</p>
            
            <p>To accept this invitation and set up your account, please click the button below:</p>
            
            <div style="text-align: center;">
              <a href="${acceptUrl}" class="button">Accept Invitation</a>
            </div>
            
            <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
            <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace;">
              ${acceptUrl}
            </p>
            
            <div class="warning">
              <strong>Important:</strong> This invitation will expire in 7 days. If you don't accept it by then, you'll need to request a new invitation.
            </div>
            
            <h3>What happens next?</h3>
            <ol>
              <li>Click the invitation link above</li>
              <li>Create a secure password for your account</li>
              <li>Complete your profile setup</li>
              <li>Start using ${this.ORGANIZATION_NAME}</li>
            </ol>
            
            <p>If you have any questions or need assistance, please contact your administrator.</p>
            
            <p>Welcome to the team!</p>
          </div>
          
          <div class="footer">
            <p>This invitation was sent to ${data.to}. If you weren't expecting this invitation, you can safely ignore this email.</p>
            <p>&copy; ${new Date().getFullYear()} ${this.ORGANIZATION_NAME}. All rights reserved.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    const text = `
      ${this.ORGANIZATION_NAME} - Invitation to Join

      Hello ${data.name},

      You have been invited by ${data.inviterName} to join ${this.ORGANIZATION_NAME} as a ${roleDisplay}.

      To accept this invitation and set up your account, please visit:
      ${acceptUrl}

      This invitation will expire in 7 days.

      What happens next:
      1. Click the invitation link above
      2. Create a secure password for your account
      3. Complete your profile setup
      4. Start using ${this.ORGANIZATION_NAME}

      If you have any questions or need assistance, please contact your administrator.

      Welcome to the team!

      ---
      This invitation was sent to ${data.to}. If you weren't expecting this invitation, you can safely ignore this email.
      © ${new Date().getFullYear()} ${this.ORGANIZATION_NAME}. All rights reserved.
    `;

    return { subject, html, text };
  }

  /**
   * Send invitation email using Supabase Edge Function
   */
  static async sendInvitationEmail(data: InvitationEmailData): Promise<boolean> {
    try {
      // Call Supabase Edge Function for email sending
      const { data: result, error } = await supabase.functions.invoke('send-invitation-email', {
        body: { invitationData: data }
      });

      if (error) {
        console.error('Error calling email function:', error);

        // Update invitation status to 'failed'
        await supabase
          .from('user_invitations')
          .update({
            status: 'failed',
            failed_reason: error.message || 'Email function error'
          })
          .eq('invitation_token', data.invitationToken);

        return false;
      }

      if (!result?.success) {
        console.error('Email function returned error:', result);

        // Update invitation status to 'failed'
        await supabase
          .from('user_invitations')
          .update({
            status: 'failed',
            failed_reason: result?.error || 'Email sending failed'
          })
          .eq('invitation_token', data.invitationToken);

        return false;
      }

      console.log('Invitation email sent successfully:', result);
      return true;

    } catch (error) {
      console.error('Error sending invitation email:', error);

      // Update invitation status to 'failed'
      await supabase
        .from('user_invitations')
        .update({
          status: 'failed',
          failed_reason: error instanceof Error ? error.message : 'Unknown error'
        })
        .eq('invitation_token', data.invitationToken);

      return false;
    }
  }

  /**
   * Resend invitation email
   */
  static async resendInvitation(invitationId: string): Promise<boolean> {
    try {
      // Get invitation details
      const { data: invitation, error } = await supabase
        .from('user_invitations')
        .select(`
          *,
          inviter:profiles!user_invitations_invited_by_fkey(name)
        `)
        .eq('id', invitationId)
        .single();

      if (error || !invitation) {
        console.error('Error fetching invitation:', error);
        return false;
      }

      // Check if invitation is still valid
      if (new Date(invitation.expires_at) < new Date()) {
        // Extend expiration by 7 days
        const newExpiryDate = new Date();
        newExpiryDate.setDate(newExpiryDate.getDate() + 7);

        await supabase
          .from('user_invitations')
          .update({ expires_at: newExpiryDate.toISOString() })
          .eq('id', invitationId);
      }

      // Send the email
      const emailData: InvitationEmailData = {
        to: invitation.email,
        name: invitation.name,
        inviterName: invitation.inviter?.name || 'Administrator',
        invitationToken: invitation.invitation_token,
        role: invitation.role
      };

      const success = await this.sendInvitationEmail(emailData);

      if (success) {
        // Increment retry count
        await supabase
          .from('user_invitations')
          .update({ 
            retry_count: invitation.retry_count + 1,
            status: 'sent',
            sent_at: new Date().toISOString()
          })
          .eq('id', invitationId);
      }

      return success;
    } catch (error) {
      console.error('Error resending invitation:', error);
      return false;
    }
  }

  /**
   * Send welcome email after account creation
   */
  static async sendWelcomeEmail(userEmail: string, userName: string): Promise<boolean> {
    try {
      // For now, log the welcome email (can be extended to use Edge Function)
      console.log('Sending welcome email:', {
        to: userEmail,
        subject: `Welcome to ${this.ORGANIZATION_NAME}!`,
        userName
      });

      // TODO: Implement welcome email Edge Function if needed
      // This could call a separate 'send-welcome-email' function

      return true;
    } catch (error) {
      console.error('Error sending welcome email:', error);
      return false;
    }
  }
}
