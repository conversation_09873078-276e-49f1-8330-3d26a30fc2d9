import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { 
  Users, 
  TrendingUp, 
  TrendingDown, 
  Calendar, 
  AlertTriangle,
  CheckCircle,
  Plus,
  Filter
} from 'lucide-react';

interface AttendanceMetricsProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
}

const AttendanceMetrics: React.FC<AttendanceMetricsProps> = ({
  schoolId,
  dateRange
}) => {
  const [selectedView, setSelectedView] = useState('trends');
  const [selectedGrade, setSelectedGrade] = useState('all');
  const [selected<PERSON><PERSON>, setSelectedGender] = useState('all');

  // Mock attendance data
  const monthlyAttendance = [
    { month: 'Jan', attendance: 82, enrolled: 450, present: 369, target: 85 },
    { month: 'Feb', attendance: 84, enrolled: 455, present: 382, target: 85 },
    { month: 'Mar', attendance: 87, enrolled: 460, present: 400, target: 85 },
    { month: 'Apr', attendance: 89, enrolled: 465, present: 414, target: 85 },
    { month: 'May', attendance: 86, enrolled: 470, present: 404, target: 85 },
    { month: 'Jun', attendance: 88, enrolled: 475, present: 418, target: 85 },
    { month: 'Jul', attendance: 85, enrolled: 480, present: 408, target: 85 },
    { month: 'Aug', attendance: 90, enrolled: 485, present: 437, target: 85 },
    { month: 'Sep', attendance: 92, enrolled: 490, present: 451, target: 85 },
    { month: 'Oct', attendance: 88, enrolled: 495, present: 436, target: 85 }
  ];

  const gradeAttendance = [
    { grade: 'Grade 1', attendance: 91, enrolled: 65, present: 59 },
    { grade: 'Grade 2', attendance: 89, enrolled: 62, present: 55 },
    { grade: 'Grade 3', attendance: 87, enrolled: 58, present: 50 },
    { grade: 'Grade 4', attendance: 85, enrolled: 55, present: 47 },
    { grade: 'Grade 5', attendance: 83, enrolled: 52, present: 43 },
    { grade: 'Grade 6', attendance: 88, enrolled: 48, present: 42 },
    { grade: 'Grade 7', attendance: 86, enrolled: 45, present: 39 },
    { grade: 'Grade 8', attendance: 84, enrolled: 42, present: 35 }
  ];

  const genderAttendance = [
    { name: 'Male', value: 52, attendance: 87, color: '#3B82F6' },
    { name: 'Female', value: 48, attendance: 89, color: '#EC4899' }
  ];

  const attendanceFactors = [
    { factor: 'Weather', impact: 15, description: 'Rainy season affects rural students' },
    { factor: 'Health', impact: 12, description: 'Illness and health-related absences' },
    { factor: 'Economic', impact: 18, description: 'Students helping with family income' },
    { factor: 'Distance', impact: 8, description: 'Long walking distances to school' },
    { factor: 'Cultural', impact: 7, description: 'Cultural events and ceremonies' }
  ];

  const currentAttendance = monthlyAttendance[monthlyAttendance.length - 1];
  const previousAttendance = monthlyAttendance[monthlyAttendance.length - 2];
  const attendanceChange = currentAttendance.attendance - previousAttendance.attendance;
  const averageAttendance = monthlyAttendance.reduce((sum, month) => sum + month.attendance, 0) / monthlyAttendance.length;

  const renderChart = () => {
    switch (selectedView) {
      case 'trends':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={monthlyAttendance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" />
              <YAxis domain={[70, 100]} />
              <Tooltip 
                formatter={(value, name) => [
                  `${value}${name === 'attendance' ? '%' : ''}`,
                  name === 'attendance' ? 'Attendance Rate' : 
                  name === 'target' ? 'Target' : name
                ]}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="attendance" 
                stroke="#10B981" 
                strokeWidth={3}
                name="Attendance Rate"
              />
              <Line 
                type="monotone" 
                dataKey="target" 
                stroke="#EF4444" 
                strokeDasharray="5 5"
                name="Target (85%)"
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'grades':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={gradeAttendance}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="grade" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="attendance" fill="#3B82F6" name="Attendance Rate %" />
              <Bar dataKey="enrolled" fill="#94A3B8" name="Enrolled Students" />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'gender':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={genderAttendance}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value, attendance }) => `${name}: ${value}% (${attendance}% attendance)`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
              >
                {genderAttendance.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="bg-green-100 p-3 rounded-lg">
            <Users className="h-6 w-6 text-green-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Attendance Metrics</h2>
            <p className="text-gray-600">Monitor student attendance patterns and trends</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </Button>
          <Button className="bg-ilead-green hover:bg-ilead-dark-green">
            <Plus className="h-4 w-4 mr-2" />
            Record Attendance
          </Button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-green-100 p-2 rounded-lg">
                <Users className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{currentAttendance.attendance}%</p>
                <p className="text-sm text-gray-600">Current Attendance</p>
                <div className="flex items-center mt-1">
                  {attendanceChange > 0 ? (
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                  ) : (
                    <TrendingDown className="h-3 w-3 text-red-500 mr-1" />
                  )}
                  <span className={`text-xs ${attendanceChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                    {attendanceChange > 0 ? '+' : ''}{attendanceChange}% vs last month
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 p-2 rounded-lg">
                <Calendar className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{averageAttendance.toFixed(1)}%</p>
                <p className="text-sm text-gray-600">Average Attendance</p>
                <p className="text-xs text-gray-500 mt-1">Year to date</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-purple-100 p-2 rounded-lg">
                <CheckCircle className="h-5 w-5 text-purple-600" />
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">{currentAttendance.present}</p>
                <p className="text-sm text-gray-600">Students Present</p>
                <p className="text-xs text-gray-500 mt-1">Out of {currentAttendance.enrolled} enrolled</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <div className="bg-orange-100 p-2 rounded-lg">
                {averageAttendance >= 85 ? (
                  <CheckCircle className="h-5 w-5 text-green-600" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-orange-600" />
                )}
              </div>
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {averageAttendance >= 85 ? 'On Track' : 'Below Target'}
                </p>
                <p className="text-sm text-gray-600">Target: 85%</p>
                <p className="text-xs text-gray-500 mt-1">
                  {averageAttendance >= 85 ? 'Meeting goals' : 'Needs improvement'}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Attendance Visualization */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Attendance Analysis</CardTitle>
              <CardDescription>
                Detailed attendance patterns and trends
              </CardDescription>
            </div>
            
            <Select value={selectedView} onValueChange={setSelectedView}>
              <SelectTrigger className="w-48">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="trends">Monthly Trends</SelectItem>
                <SelectItem value="grades">By Grade Level</SelectItem>
                <SelectItem value="gender">By Gender</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardHeader>
        <CardContent>
          {renderChart()}
        </CardContent>
      </Card>

      {/* Attendance Factors */}
      <Card>
        <CardHeader>
          <CardTitle>Attendance Impact Factors</CardTitle>
          <CardDescription>
            Key factors affecting student attendance rates
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {attendanceFactors.map((factor, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{factor.factor}</h4>
                  <p className="text-sm text-gray-600">{factor.description}</p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-gray-900">{factor.impact}%</p>
                  <p className="text-sm text-gray-500">Impact</p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle>Attendance Improvement Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">Short-term Actions</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Implement daily attendance tracking system</li>
                <li>• Create parent notification system for absences</li>
                <li>• Establish attendance incentive programs</li>
                <li>• Provide weather-appropriate facilities</li>
              </ul>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg">
              <h4 className="font-medium text-green-800 mb-2">Long-term Strategies</h4>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• Develop community engagement programs</li>
                <li>• Address economic barriers to attendance</li>
                <li>• Improve school infrastructure and facilities</li>
                <li>• Implement health and nutrition programs</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AttendanceMetrics;
