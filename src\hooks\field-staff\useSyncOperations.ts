/**
 * useSyncOperations - Focused hook for sync operations
 * Handles online/offline status, sync execution, and connectivity
 */

import { useCallback, useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { toast } from 'sonner';
import { 
  SyncResult,
  BatchSyncOptions,
  OfflineSyncStatus
} from '@/types/offlineSync.types';
import { 
  syncAllOfflineData,
  retryFailedItems,
  checkOnlineStatus,
  testServerConnectivity
} from '@/utils/syncOperations';

export const useSyncOperations = () => {
  const queryClient = useQueryClient();
  
  const [syncStatus, setSyncStatus] = useState<Pick<OfflineSyncStatus, 
    'isOnline' | 'isSyncing' | 'syncProgress' | 'lastSyncTime'
  >>({
    isOnline: checkOnlineStatus(),
    isSyncing: false,
    syncProgress: 0,
    lastSyncTime: null,
  });

  // Update online status
  const updateOnlineStatus = useCallback(() => {
    const isOnline = checkOnlineStatus();
    setSyncStatus(prev => ({ ...prev, isOnline }));
    return isOnline;
  }, []);

  // Sync offline data
  const syncOfflineData = useCallback(async (options?: BatchSyncOptions): Promise<SyncResult> => {
    if (!checkOnlineStatus()) {
      toast.error('No internet connection');
      return { 
        success: false, 
        processed: 0, 
        failed: 0, 
        conflicts: 0, 
        errors: ['No internet connection'] 
      };
    }

    setSyncStatus(prev => ({ ...prev, isSyncing: true, syncProgress: 0 }));

    try {
      const result = await syncAllOfflineData(options);
      
      setSyncStatus(prev => ({
        ...prev,
        isSyncing: false,
        syncProgress: 100,
        lastSyncTime: new Date(),
      }));

      if (result.success) {
        toast.success(`Synced ${result.processed} items successfully`);
        queryClient.invalidateQueries({ queryKey: ['attendance-sessions'] });
        queryClient.invalidateQueries({ queryKey: ['field-reports'] });
      } else {
        toast.error(`Sync completed with ${result.failed} failures and ${result.conflicts} conflicts`);
      }

      return result;
    } catch (error) {
      setSyncStatus(prev => ({ ...prev, isSyncing: false, syncProgress: 0 }));
      console.error('Sync failed:', error);
      toast.error('Sync operation failed');
      throw error;
    }
  }, [queryClient]);

  // Retry failed items
  const retryFailedSync = useCallback(async (): Promise<SyncResult> => {
    if (!checkOnlineStatus()) {
      toast.error('No internet connection');
      return { 
        success: false, 
        processed: 0, 
        failed: 0, 
        conflicts: 0, 
        errors: ['No internet connection'] 
      };
    }

    setSyncStatus(prev => ({ ...prev, isSyncing: true, syncProgress: 0 }));

    try {
      const result = await retryFailedItems();
      
      setSyncStatus(prev => ({
        ...prev,
        isSyncing: false,
        syncProgress: 100,
        lastSyncTime: new Date(),
      }));

      if (result.processed > 0) {
        toast.success(`Retried ${result.processed} failed items`);
      }

      return result;
    } catch (error) {
      setSyncStatus(prev => ({ ...prev, isSyncing: false, syncProgress: 0 }));
      console.error('Retry failed:', error);
      toast.error('Retry operation failed');
      throw error;
    }
  }, []);

  // Test server connectivity
  const testConnectivity = useCallback(async (): Promise<boolean> => {
    try {
      return await testServerConnectivity();
    } catch (error) {
      console.error('Connectivity test failed:', error);
      return false;
    }
  }, []);

  // Force sync
  const forceSync = useCallback(async (): Promise<SyncResult> => {
    return syncOfflineData({ 
      retryFailedItems: true, 
      ignoreConflicts: false 
    });
  }, [syncOfflineData]);

  // Monitor online status
  useEffect(() => {
    const handleOnlineStatusChange = () => {
      const isOnline = updateOnlineStatus();
      
      if (isOnline) {
        toast.success('Connection restored');
        // Auto-sync when coming back online
        syncOfflineData({ retryFailedItems: true });
      } else {
        toast.warning('Connection lost - working offline');
      }
    };

    window.addEventListener('online', handleOnlineStatusChange);
    window.addEventListener('offline', handleOnlineStatusChange);

    return () => {
      window.removeEventListener('online', handleOnlineStatusChange);
      window.removeEventListener('offline', handleOnlineStatusChange);
    };
  }, [syncOfflineData, updateOnlineStatus]);

  return {
    syncStatus,
    syncOfflineData,
    retryFailedSync,
    testConnectivity,
    forceSync,
    updateOnlineStatus,
  };
};
