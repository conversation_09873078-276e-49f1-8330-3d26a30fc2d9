import React, { ReactNode } from 'react';
import { toast } from 'sonner';

// Import the component type to avoid circular dependencies
type FieldErrorBoundaryProps = {
  fallback?: ReactNode;
  children: React.ReactNode;
};

// Higher-order component for wrapping field components
export const withFieldErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  customFallback?: ReactNode
) => {
  // Lazy import to avoid circular dependencies
  const FieldErrorBoundary = React.lazy(() => import('@/components/field-staff/FieldErrorBoundary'));
  
  const WrappedComponent = (props: P) => (
    <React.Suspense fallback={<div>Loading...</div>}>
      <FieldErrorBoundary fallback={customFallback}>
        <Component {...props} />
      </FieldErrorBoundary>
    </React.Suspense>
  );

  WrappedComponent.displayName = `withFieldErrorBoundary(${Component.displayName || Component.name})`;
  return WrappedComponent;
};

// Hook for programmatic error reporting
export const useErrorReporting = () => {
  const reportError = (error: Error, context?: Record<string, unknown>) => {
    console.error('Manual error report:', {
      error: error.message,
      stack: error.stack,
      context,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href,
    });

    toast.error('An error occurred', {
      description: error.message,
      duration: 5000,
    });
  };

  return { reportError };
};
