
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import TaskDetailsDialog from './tasks/TaskDetailsDialog';
import TaskStats from './TaskStats';
import TaskFilters from './TaskFilters';
import TaskGrid from './tasks/TaskGrid';
import TaskTable from './tasks/TaskTable';
import TaskListHeader from './tasks/TaskListHeader';
import TaskListLoading from './tasks/TaskListLoading';
import { useTaskList } from '@/hooks/useTaskList';
import { Database } from '@/integrations/supabase/types';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

interface TaskListProps {
  tasks?: Task[];
  loading?: boolean;
  currentUserId?: string;
  onViewDetails?: (taskId: string) => void;
  onUpdateStatus?: (taskId: string, status: Database['public']['Enums']['task_status']) => void;
  onEdit?: (taskId: string) => void;
  showFilters?: boolean;
  title?: string;
  description?: string;
  className?: string;
}

const TaskList: React.FC<TaskListProps> = ({
  tasks: propTasks,
  loading: propLoading = false,
  currentUserId,
  onViewDetails,
  onUpdateStatus,
  onEdit,
  showFilters = true,
  title = "Tasks",
  description,
  className
}) => {
  const {
    tasks,
    loading,
    searchTerm,
    statusFilter,
    priorityFilter,
    taskDetailsOpen,
    viewMode,
    currentPage,
    selectedTaskId,
    handleTaskViewDetails,
    handleTaskUpdateStatus,
    getStatusStats,
    handleSearchChange,
    handleStatusFilterChange,
    handlePriorityFilterChange,
    handleViewModeChange,
    setCurrentPage,
    setTaskDetailsOpen,
    clearSelection
  } = useTaskList({
    propTasks,
    propLoading,
    onViewDetails,
    onUpdateStatus
  });

  const stats = getStatusStats();

  if (loading) {
    return <TaskListLoading className={className} />;
  }

  return (
    <div className={`space-y-6 ${className || ''}`}>
      {/* Task Statistics */}
      <TaskStats {...stats} />

      <Card>
        <TaskListHeader
          title={title}
          description={description}
          taskCount={tasks.length}
          viewMode={viewMode}
          onViewModeChange={handleViewModeChange}
        />
        
        <CardContent>
          {showFilters && (
            <TaskFilters
              searchTerm={searchTerm}
              onSearchChange={handleSearchChange}
              statusFilter={statusFilter}
              onStatusFilterChange={handleStatusFilterChange}
              priorityFilter={priorityFilter}
              onPriorityFilterChange={handlePriorityFilterChange}
            />
          )}

          {viewMode === 'cards' ? (
            <TaskGrid
              tasks={tasks}
              currentUserId={currentUserId}
              onViewDetails={handleTaskViewDetails}
              onUpdateStatus={handleTaskUpdateStatus}
              onEdit={onEdit}
              searchTerm={searchTerm}
              statusFilter={statusFilter}
              priorityFilter={priorityFilter}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
              itemsPerPage={9}
            />
          ) : (
            <TaskTable
              tasks={tasks}
              currentUserId={currentUserId}
              onViewDetails={handleTaskViewDetails}
              onUpdateStatus={handleTaskUpdateStatus}
              onEdit={onEdit}
              searchTerm={searchTerm}
              statusFilter={statusFilter}
              priorityFilter={priorityFilter}
              currentPage={currentPage}
              onPageChange={setCurrentPage}
              itemsPerPage={10}
            />
          )}
        </CardContent>
      </Card>

      {/* Task Details Dialog */}
      <TaskDetailsDialog
        taskId={selectedTaskId}
        open={taskDetailsOpen}
        onOpenChange={(open) => {
          setTaskDetailsOpen(open);
          if (!open) {
            clearSelection();
          }
        }}
      />
    </div>
  );
};

export default TaskList;
