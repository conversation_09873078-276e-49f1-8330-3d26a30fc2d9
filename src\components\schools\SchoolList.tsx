
import React from 'react';
import { List } from 'lucide-react';
import SchoolManagement from '../SchoolManagement';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

const SchoolList = () => {
  const { profile } = useAuth();

  return (
    <PageLayout>
      <PageHeader
        title="All Schools"
        description="View and manage all registered schools"
        icon={List}
      />

      <ContentCard noPadding>
        <SchoolManagement currentUser={profile} />
      </ContentCard>
    </PageLayout>
  );
};

export default SchoolList;
