
import React from 'react';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Search, Filter, X } from 'lucide-react';
import { SchoolFilters, SchoolSort } from '@/types/school';

interface SchoolFiltersComponentProps {
  filters: SchoolFilters;
  sort: SchoolSort;
  onFiltersChange: (filters: SchoolFilters) => void;
  onSortChange: (sort: SchoolSort) => void;
  onClearFilters: () => void;
  districts: string[];
}

const SchoolFiltersComponent = ({
  filters,
  sort,
  onFiltersChange,
  onSortChange,
  onClearFilters,
  districts
}: SchoolFiltersComponentProps) => {
  const hasActiveFilters = Object.values(filters).some(value => value !== undefined && value !== '');

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Filters & Search
        </h3>
        {hasActiveFilters && (
          <Button variant="outline" size="sm" onClick={onClearFilters}>
            <X className="h-4 w-4 mr-2" />
            Clear All
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search schools..."
            value={filters.search || ''}
            onChange={(e) => onFiltersChange({ ...filters, search: e.target.value })}
            className="pl-10"
          />
        </div>

        {/* School Type Filter */}
        <Select
          value={filters.school_type || 'all'}
          onValueChange={(value) =>
            onFiltersChange({
              ...filters,
              school_type: value === 'all' ? undefined : value as SchoolFilters['school_type']
            })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="School Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            <SelectItem value="primary">Primary</SelectItem>
            <SelectItem value="secondary">Secondary</SelectItem>
            <SelectItem value="tertiary">Tertiary</SelectItem>
            <SelectItem value="vocational">Vocational</SelectItem>
          </SelectContent>
        </Select>

        {/* Registration Status Filter */}
        <Select
          value={filters.registration_status || 'all'}
          onValueChange={(value) =>
            onFiltersChange({
              ...filters,
              registration_status: value === 'all' ? undefined : value as SchoolFilters['registration_status']
            })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="registered">Registered</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="unregistered">Unregistered</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>

        {/* District Filter */}
        <Select
          value={filters.district || 'all'}
          onValueChange={(value) => 
            onFiltersChange({ 
              ...filters, 
              district: value === 'all' ? undefined : value 
            })
          }
        >
          <SelectTrigger>
            <SelectValue placeholder="District" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Districts</SelectItem>
            {districts.map((district) => (
              <SelectItem key={district} value={district}>
                {district}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Sort Options */}
      <div className="flex items-center gap-2">
        <span className="text-sm font-medium">Sort by:</span>
        <Select
          value={sort.field}
          onValueChange={(value) =>
            onSortChange({ ...sort, field: value as SchoolSort['field'] })
          }
        >
          <SelectTrigger className="w-40">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">Name</SelectItem>
            <SelectItem value="student_count">Students</SelectItem>
            <SelectItem value="teacher_count">Teachers</SelectItem>
            <SelectItem value="created_at">Date Added</SelectItem>
            <SelectItem value="registration_status">Status</SelectItem>
          </SelectContent>
        </Select>
        
        <Select
          value={sort.direction}
          onValueChange={(value) =>
            onSortChange({ ...sort, direction: value as SchoolSort['direction'] })
          }
        >
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="asc">Ascending</SelectItem>
            <SelectItem value="desc">Descending</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  );
};

export default SchoolFiltersComponent;
