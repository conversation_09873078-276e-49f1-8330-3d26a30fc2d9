import React from 'react';
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import TaskTableRow from './TaskTableRow';
import TaskPagination from '../TaskPagination';
import TaskStatusBadge from '../TaskStatusBadge';
import TaskPriorityBadge from '../TaskPriorityBadge';
import { useTaskFiltering } from '@/hooks/useTaskFiltering';
import { Database } from '@/integrations/supabase/types';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

interface TaskTableProps {
  tasks: Task[];
  currentUserId?: string;
  onViewDetails: (taskId: string) => void;
  onUpdateStatus: (taskId: string, status: Database['public']['Enums']['task_status']) => void;
  onEdit?: (taskId: string) => void;
  searchTerm: string;
  statusFilter: string;
  priorityFilter: string;
  currentPage: number;
  onPageChange: (page: number) => void;
  itemsPerPage?: number;
}

const TaskTable: React.FC<TaskTableProps> = ({
  tasks,
  currentUserId,
  onViewDetails,
  onUpdateStatus,
  onEdit,
  searchTerm,
  statusFilter,
  priorityFilter,
  currentPage,
  onPageChange,
  itemsPerPage = 10
}) => {
  const { paginatedTasks, totalPages, totalItems } = useTaskFiltering({
    tasks,
    searchTerm,
    statusFilter,
    priorityFilter,
    currentPage,
    itemsPerPage
  });

  if (totalItems === 0) {
    return (
      <div className="text-center py-12">
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' 
            ? 'No matching tasks found' 
            : 'No tasks yet'
          }
        </h3>
        <p className="text-gray-600">
          {searchTerm || statusFilter !== 'all' || priorityFilter !== 'all'
            ? 'Try adjusting your search or filter criteria'
            : 'Tasks will appear here when they are created'
          }
        </p>
      </div>
    );
  }

  return (
    <div>
      {/* Desktop Table View */}
      <div className="hidden lg:block border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Task</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Assigned To</TableHead>
              <TableHead>School</TableHead>
              <TableHead>Due Date</TableHead>
              <TableHead>Comments</TableHead>
              <TableHead className="w-12"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedTasks.map((task) => (
              <TaskTableRow
                key={task.id}
                task={task}
                currentUserId={currentUserId}
                onViewDetails={onViewDetails}
                onUpdateStatus={onUpdateStatus}
                onEdit={onEdit}
              />
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile Card View */}
      <div className="lg:hidden space-y-3">
        {paginatedTasks.map((task) => (
          <div
            key={task.id}
            className={`border rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow ${
              task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed'
                ? 'border-red-200 bg-red-50'
                : 'bg-white'
            }`}
            onClick={() => onViewDetails(task.id)}
          >
            <div className="space-y-3">
              {/* Task Title and Description */}
              <div>
                <h3 className="font-medium text-gray-900 hover:text-ilead-green">
                  {task.title}
                </h3>
                {task.description && (
                  <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                    {task.description}
                  </p>
                )}
              </div>

              {/* Status and Priority Row */}
              <div className="flex items-center gap-2">
                <TaskStatusBadge status={task.status} />
                <TaskPriorityBadge priority={task.priority} />
              </div>

              {/* Details Grid */}
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div>
                  <span className="text-gray-500">Assigned to:</span>
                  <div className="font-medium">
                    {task.assigned_to_name || 'Unassigned'}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">School:</span>
                  <div className="font-medium">
                    {task.school_name || 'Not specified'}
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Due date:</span>
                  <div className={`font-medium ${
                    task.due_date && new Date(task.due_date) < new Date() && task.status !== 'completed'
                      ? 'text-red-600'
                      : ''
                  }`}>
                    {task.due_date
                      ? new Date(task.due_date).toLocaleDateString('en-US', {
                          month: 'short',
                          day: 'numeric',
                          year: 'numeric'
                        })
                      : 'Not set'
                    }
                  </div>
                </div>
                <div>
                  <span className="text-gray-500">Comments:</span>
                  <div className="font-medium">
                    {task.comment_count || 0}
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <TaskPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
        totalItems={totalItems}
        itemsPerPage={itemsPerPage}
      />
    </div>
  );
};

export default TaskTable;
