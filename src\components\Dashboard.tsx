import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BookOpen, School, Users, TrendingUp, BarChart3, FileText, Calendar, CheckCircle, Clock, Home, MapPin, LogIn, LogOut, Plus } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';
import ActivityFeed from './ActivityFeed';
import FieldReportForm from './FieldReportForm';
import ExportManager from './ExportManager';
import { useAuth } from '@/hooks/useAuth';
import { useRecentTasks, useCreateTask, TaskFormData } from '@/hooks/tasks';
import { useToast } from '@/hooks/use-toast';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { FieldStaffCheckInModal, FieldStaffCheckOutModal } from './field-staff';
import CreateTaskDialog from './CreateTaskDialog';
import { ComprehensiveAdminDashboard } from './dashboard/ComprehensiveAdminDashboard';

// Type definitions for dashboard data
type DashboardSchool = {
  id: string;
  name: string;
  code?: string;
  school_type: Database['public']['Enums']['school_type'];
  student_count?: number;
  teacher_count?: number;
  contact_phone?: string;
  email?: string;
  district?: string;
  sub_county?: string;
  registration_status?: Database['public']['Enums']['registration_status'];
};

type DashboardDistribution = {
  id: string;
  school_id: string;
  school_name: string;
  book_title: string;
  quantity: number;
  delivery_date: string;
  supervisor_name: string;
};

type DashboardTask = {
  id: string;
  title: string;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  school_name?: string;
  assigned_to_name?: string;
};

interface DashboardProps {
  onViewChange?: (view: string) => void;
}

const Dashboard = ({ onViewChange }: DashboardProps) => {
  const { profile } = useAuth();
  const { data: unifiedStatus } = useUnifiedCheckInStatus();
  const { toast } = useToast();

  // Modal states
  const [checkInModalOpen, setCheckInModalOpen] = useState(false);
  const [checkOutModalOpen, setCheckOutModalOpen] = useState(false);
  const [createTaskModalOpen, setCreateTaskModalOpen] = useState(false);

  // Task creation
  const createTaskMutation = useCreateTask();

  const handleCreateTask = async (taskData: TaskFormData) => {
    try {
      await createTaskMutation.mutateAsync(taskData);
      toast({
        title: "Success",
        description: "Task created successfully",
      });
      setCreateTaskModalOpen(false);
    } catch (error: unknown) {
      toast({
        title: "Error",
        description: (error as Error).message || "Failed to create task",
        variant: "destructive",
      });
    }
  };

  // Fetch schools using RPC function
  const { data: schools = [] } = useQuery<DashboardSchool[]>({
    queryKey: ['schools'],
    queryFn: async () => {
      const { data, error } = await supabase
        .rpc('get_schools_with_divisions');

      if (error) {
        console.error('Error fetching schools:', error);
        throw error;
      }
      return (data || []) as DashboardSchool[];
    },
  });

  // Fetch book distributions using RPC function
  const { data: distributions = [] } = useQuery<DashboardDistribution[]>({
    queryKey: ['distributions'],
    queryFn: async () => {
      const { data, error } = await supabase
        .rpc('get_book_distributions');

      if (error) {
        console.error('Error fetching distributions:', error);
        throw error;
      }
      return (data || []) as DashboardDistribution[];
    },
  });



  // Fetch recent tasks using optimized hook
  const { data: tasks = [], isLoading: tasksLoading } = useRecentTasks(5);

  // Performance monitoring
  React.useEffect(() => {
    if (!tasksLoading && tasks.length >= 0) {
      console.log(`✅ Dashboard tasks loaded: ${tasks.length} tasks`);
    }
  }, [tasks, tasksLoading]);

  // Calculate statistics
  const totalBooks = distributions.reduce((sum: number, dist: DashboardDistribution) => sum + (dist.quantity || 0), 0);
  const schoolsServed = new Set(distributions.map((dist: DashboardDistribution) => dist.school_id)).size;
  const thisMonth = distributions.filter((d: DashboardDistribution) => {
    const deliveryDate = new Date(d.delivery_date);
    const now = new Date();
    return deliveryDate.getMonth() === now.getMonth() &&
           deliveryDate.getFullYear() === now.getFullYear();
  }).length;

  const stats = [
    {
      title: 'Schools Served',
      value: schoolsServed,
      icon: School,
      color: 'text-ilead-green',
      bgColor: 'bg-purple-100',
      onClick: () => onViewChange?.('schools-list'),
    },
    {
      title: 'Total Books Distributed',
      value: totalBooks.toLocaleString(),
      icon: BookOpen,
      color: 'text-ilead-orange',
      bgColor: 'bg-orange-100',
      onClick: () => onViewChange?.('distributions-active'),
    },
    {
      title: 'Active Schools',
      value: schools.length,
      icon: Users,
      color: 'text-ilead-green',
      bgColor: 'bg-purple-100',
      onClick: () => onViewChange?.('schools-list'),
    },
    {
      title: 'This Month',
      value: thisMonth,
      icon: TrendingUp,
      color: 'text-ilead-orange',
      bgColor: 'bg-orange-100',
      onClick: () => onViewChange?.('distributions-active'),
    },
  ];

  // Check if user is admin or program officer - show comprehensive dashboard
  if (profile?.role === 'admin' || profile?.role === 'program_officer') {
    return <ComprehensiveAdminDashboard onViewChange={onViewChange} />;
  }

  return (
    <PageLayout>
      <PageHeader
        title="Dashboard"
        description="Monitor your field activities and book distribution impact"
        icon={Home}
        actions={profile?.role === 'field_staff' ? [
          // Quick action buttons for field staff in header
          ...(unifiedStatus?.isCheckedIn ? [{
            label: 'Check Out & Report',
            onClick: () => setCheckOutModalOpen(true),
            icon: LogOut,
            variant: 'destructive' as const,
            className: 'bg-red-600 text-white hover:bg-red-700'
          }] : [{
            label: 'Check In',
            onClick: () => setCheckInModalOpen(true),
            icon: LogIn,
            variant: 'default' as const,
            className: 'bg-green-600 text-white hover:bg-green-700'
          }]),
          {
            label: 'New Task',
            onClick: () => setCreateTaskModalOpen(true),
            icon: Plus,
            variant: 'default' as const,
            className: 'bg-ilead-green hover:bg-ilead-dark-green text-white'
          }
        ] : undefined}
      >
        <ExportManager dataType="distributions" title="Distributions" />
      </PageHeader>

      {/* Stats cards */}
      <ContentCard noPadding>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <Card
                key={index}
                className="border-l-4 border-l-ilead-green cursor-pointer hover:shadow-md transition-shadow"
                onClick={stat.onClick}
              >
                <CardContent className="p-4 flex items-center">
                  <div className={`${stat.bgColor} p-3 rounded-lg mr-4`}>
                    <Icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </ContentCard>
      {/* Quick Actions - Only for non-field staff */}
      {profile && profile.role !== 'field_staff' && (
        <ContentCard title="Quick Actions">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <FieldReportForm currentUser={profile} />
            <Button
              variant="outline"
              className="w-full bg-ilead-orange text-white hover:bg-ilead-dark-orange"
              onClick={() => onViewChange?.('distributions-schedule')}
            >
              <BookOpen className="h-4 w-4 mr-2" />
              Record Book Distribution
            </Button>
            <Button
              variant="outline"
              className="w-full bg-ilead-green hover:bg-ilead-dark-green text-white"
              onClick={() => onViewChange?.('schools-list')}
            >
              <School className="h-4 w-4 mr-2" />
              Register School
            </Button>
          </div>
        </ContentCard>
      )}
      {/* tabs and content */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="mb-4 bg-gray-100">
          <TabsTrigger value="overview" className="data-[state=active]:bg-ilead-green data-[state=active]:text-white">
            Overview
          </TabsTrigger>
          <TabsTrigger value="distributions" className="data-[state=active]:bg-ilead-orange data-[state=active]:text-white">
            Distributions
          </TabsTrigger>
          <TabsTrigger value="tasks" className="data-[state=active]:bg-ilead-green data-[state=active]:text-white">
            Tasks
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Activity Feed */}
            <Card>
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle className="flex items-center text-lg font-medium">
                  <Calendar className="h-5 w-5 mr-2 text-ilead-orange" />
                  Recent Activities
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <ActivityFeed
                  limit={8}
                  showFilters={true}
                  enableInfiniteScroll={true}
                />
              </CardContent>
            </Card>
            
            {/* Recent Tasks */}
            <Card>
              <CardHeader className="bg-gray-50 border-b">
                <CardTitle className="flex items-center justify-between text-lg font-medium">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-ilead-green" />
                    Upcoming Tasks
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onViewChange?.('tasks-assigned')}
                    className="text-ilead-green hover:text-ilead-dark-green"
                  >
                    View All
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-0">
                <div className="divide-y">
                  {tasks.slice(0, 5).map((task: DashboardTask) => (
                    <div
                      key={task.id}
                      className="p-4 flex items-center hover:bg-gray-50 cursor-pointer"
                      onClick={() => onViewChange?.('tasks-assigned')}
                    >
                      <div className={`w-2 h-2 rounded-full mr-3 ${
                        task.priority === 'urgent' ? 'bg-red-500' :
                        task.priority === 'high' ? 'bg-orange-500' :
                        task.priority === 'medium' ? 'bg-yellow-500' : 'bg-blue-500'
                      }`}></div>
                      <div className="flex-1">
                        <p className="font-medium text-gray-900">{task.title}</p>
                        <div className="flex items-center text-xs text-gray-500">
                          <Clock className="h-3 w-3 mr-1" />
                          {task.due_date ? new Date(task.due_date).toLocaleDateString() : 'No due date'}
                          {task.school_name && <span className="ml-2">• {task.school_name}</span>}
                        </div>
                      </div>
                      <div className="px-2 py-1 rounded text-xs bg-gray-200">
                        {task.status}
                      </div>
                    </div>
                  ))}
                  {tasks.length === 0 && (
                    <div className="p-6 text-center text-gray-500">
                      No tasks available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <Card>
            <CardHeader className="bg-gray-50 border-b">
              <CardTitle className="flex items-center justify-between text-lg font-medium">
                <div className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-ilead-green" />
                  Recent Book Distributions
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => onViewChange?.('distributions-active')}
                  className="text-ilead-orange hover:text-ilead-dark-orange"
                >
                  View All
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="divide-y">
                {distributions.slice(0, 5).map((distribution: DashboardDistribution) => (
                  <div
                    key={distribution.id}
                    className="p-4 flex items-center justify-between hover:bg-gray-50 cursor-pointer"
                    onClick={() => onViewChange?.('distributions-active')}
                  >
                    <div className="flex items-center">
                      <div className="bg-orange-100 p-2 rounded-lg mr-3">
                        <BookOpen className="h-4 w-4 text-ilead-orange" />
                      </div>
                      <div>
                        <p className="font-medium text-gray-900">{distribution.school_name || 'Unknown School'}</p>
                        <p className="text-sm text-gray-600">
                          {distribution.quantity} books of "{distribution.book_title}"
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-500">{new Date(distribution.delivery_date).toLocaleDateString()}</p>
                      <p className="text-xs text-gray-400">by {distribution.supervisor_name}</p>
                    </div>
                  </div>
                ))}
                {distributions.length === 0 && (
                  <div className="p-6 text-center text-gray-500">
                    No distributions recorded yet
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="distributions">
          <Card>
            <CardHeader className="bg-gray-50 border-b">
              <CardTitle className="text-lg font-medium flex items-center">
                <BookOpen className="h-5 w-5 mr-2 text-ilead-orange" />
                Book Distributions
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="text-left p-4 font-medium text-gray-600">School</th>
                      <th className="text-left p-4 font-medium text-gray-600">Book Title</th>
                      <th className="text-left p-4 font-medium text-gray-600">Quantity</th>
                      <th className="text-left p-4 font-medium text-gray-600">Date</th>
                      <th className="text-left p-4 font-medium text-gray-600">Supervisor</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {distributions.map((dist: DashboardDistribution) => (
                      <tr
                        key={dist.id}
                        className="hover:bg-gray-50 cursor-pointer"
                        onClick={() => onViewChange?.('distributions-active')}
                      >
                        <td className="p-4">{dist.school_name}</td>
                        <td className="p-4">{dist.book_title}</td>
                        <td className="p-4">{dist.quantity}</td>
                        <td className="p-4">{new Date(dist.delivery_date).toLocaleDateString()}</td>
                        <td className="p-4">{dist.supervisor_name}</td>
                      </tr>
                    ))}
                    {distributions.length === 0 && (
                      <tr>
                        <td colSpan={5} className="p-4 text-center text-gray-500">
                          No distributions recorded yet
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="tasks">
          <Card>
            <CardHeader className="bg-gray-50 border-b">
              <CardTitle className="text-lg font-medium flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-ilead-green" />
                Tasks
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50">
                      <th className="text-left p-4 font-medium text-gray-600">Title</th>
                      <th className="text-left p-4 font-medium text-gray-600">Priority</th>
                      <th className="text-left p-4 font-medium text-gray-600">Status</th>
                      <th className="text-left p-4 font-medium text-gray-600">Due Date</th>
                      <th className="text-left p-4 font-medium text-gray-600">Assigned To</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y">
                    {tasks.map((task: DashboardTask) => (
                      <tr
                        key={task.id}
                        className="hover:bg-gray-50 cursor-pointer"
                        onClick={() => onViewChange?.('tasks-assigned')}
                      >
                        <td className="p-4">{task.title}</td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded text-xs ${
                            task.priority === 'urgent' ? 'bg-red-100 text-red-800' :
                            task.priority === 'high' ? 'bg-orange-100 text-orange-800' :
                            task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {task.priority}
                          </span>
                        </td>
                        <td className="p-4">
                          <span className={`px-2 py-1 rounded text-xs ${
                            task.status === 'completed' ? 'bg-purple-100 text-purple-800' :
                            task.status === 'in_progress' ? 'bg-blue-100 text-blue-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {task.status}
                          </span>
                        </td>
                        <td className="p-4">{task.due_date ? new Date(task.due_date).toLocaleDateString() : '-'}</td>
                        <td className="p-4">{task.assigned_to_name || '-'}</td>
                      </tr>
                    ))}
                    {tasks.length === 0 && (
                      <tr>
                        <td colSpan={5} className="p-4 text-center text-gray-500">
                          No tasks available
                        </td>
                      </tr>
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Field Staff Modals */}
      {profile?.role === 'field_staff' && (
        <>
          <FieldStaffCheckInModal
            isOpen={checkInModalOpen}
            onClose={() => setCheckInModalOpen(false)}
          />
          <FieldStaffCheckOutModal
            isOpen={checkOutModalOpen}
            onClose={() => setCheckOutModalOpen(false)}
          />
          <CreateTaskDialog
            open={createTaskModalOpen}
            onOpenChange={setCreateTaskModalOpen}
            onSubmit={handleCreateTask}
            loading={createTaskMutation.isPending}
            canAssignTasks={false}
          />
        </>
      )}
    </PageLayout>
  );
};

export default Dashboard;
