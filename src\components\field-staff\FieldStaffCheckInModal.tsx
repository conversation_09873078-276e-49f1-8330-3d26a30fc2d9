import React, { useState, useEffect } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  MapPin, 
  Navigation, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Wifi,
  WifiOff,
  Smartphone,
  Target,
  Loader2
} from 'lucide-react';
import { useSchools } from '@/hooks/useSchools';
import { useFieldStaffCheckIn } from '@/hooks/field-staff/useFieldStaffAttendance';
import { useGPSLocation } from '@/hooks/field-staff/useGPSLocation';
import { getDeviceInfo, getNetworkInfo } from '@/utils/deviceInfo';
import { toast } from 'sonner';

interface FieldStaffCheckInModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const FieldStaffCheckInModal: React.FC<FieldStaffCheckInModalProps> = ({
  isOpen,
  onClose,
}) => {
  const [selectedSchoolId, setSelectedSchoolId] = useState('');
  const [notes, setNotes] = useState('');

  const { data: schools } = useSchools();
  const checkIn = useFieldStaffCheckIn();

  const {
    location,
    error: gpsError,
    loading: gpsLoading,
    getCurrentLocation,
  } = useGPSLocation();

  const isOnline = navigator.onLine;
  const isOfflineMode = !isOnline;

  // Calculate distance to selected school
  const selectedSchool = schools?.find(school => school.id === selectedSchoolId);
  const distanceFromSchool = selectedSchool && location && selectedSchool.location_coordinates
    ? Math.sqrt(
        Math.pow(location.latitude - selectedSchool.location_coordinates[0], 2) +
        Math.pow(location.longitude - selectedSchool.location_coordinates[1], 2)
      ) * 111000 // Rough conversion to meters
    : null;

  const isNearSchool = distanceFromSchool !== null && distanceFromSchool <= 100; // Within 100 meters

  const handleCheckIn = async () => {
    if (!selectedSchoolId) return;

    try {
      // Get fresh location if needed
      let currentLocation = location;
      if (!currentLocation) {
        currentLocation = await getCurrentLocation();
      }

      const deviceInfo = getDeviceInfo();
      const networkInfo = getNetworkInfo();

      await checkIn.mutateAsync({
        school_id: selectedSchoolId,
        latitude: currentLocation.latitude,
        longitude: currentLocation.longitude,
        accuracy: currentLocation.accuracy,
        verification_method: 'gps',
        device_info: { ...deviceInfo, notes },
        network_info: { ...networkInfo, online: isOnline },
        offline_sync: isOfflineMode,
      });

      setNotes('');
      setSelectedSchoolId('');
      onClose();
      toast.success('Successfully checked in!');
    } catch (error) {
      console.error('Check-in failed:', error);
      toast.error('Check-in failed. Please try again.');
    }
  };

  const handleClose = () => {
    setSelectedSchoolId('');
    setNotes('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5 text-green-600" />
            Field Work Check-In
          </DialogTitle>
          <DialogDescription>
            Check in to start your field work session. GPS location will be recorded for verification.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Connection Status */}
          <Alert className={isOnline ? 'border-green-200 bg-green-50' : 'border-yellow-200 bg-yellow-50'}>
            <div className="flex items-center gap-2">
              {isOnline ? <Wifi className="h-4 w-4" /> : <WifiOff className="h-4 w-4" />}
              <AlertDescription>
                {isOnline ? 'Online - Data will sync immediately' : 'Offline - Data will sync when connection is restored'}
              </AlertDescription>
            </div>
          </Alert>

          {/* GPS Status */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Navigation className="h-4 w-4" />
                GPS Location Status
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {gpsLoading && (
                <div className="flex items-center gap-2 text-blue-600">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm">Getting your location...</span>
                </div>
              )}
              
              {gpsError && (
                <Alert variant="destructive">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {gpsError}. Please enable GPS and refresh your location.
                  </AlertDescription>
                </Alert>
              )}
              
              {location && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="text-sm text-green-700">Location acquired</span>
                    <Badge variant="outline" className="text-xs">
                      ±{location.accuracy?.toFixed(0)}m accuracy
                    </Badge>
                  </div>
                  <div className="text-xs text-gray-600">
                    Lat: {location.latitude.toFixed(6)}, Lng: {location.longitude.toFixed(6)}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* School Selection */}
          <div className="space-y-3">
            <label className="text-sm font-medium">Select School</label>
            <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
              <SelectTrigger>
                <SelectValue placeholder="Choose the school you're visiting" />
              </SelectTrigger>
              <SelectContent>
                {schools?.map((school) => (
                  <SelectItem key={school.id} value={school.id}>
                    <div className="flex flex-col">
                      <span>{school.name}</span>
                      <span className="text-xs text-gray-500">
                        {school.district} • {school.school_type}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Distance indicator */}
            {selectedSchool && location && (
              <div className="flex items-center gap-2 text-sm">
                <Target className="h-4 w-4" />
                {distanceFromSchool !== null ? (
                  <span className={isNearSchool ? 'text-green-600' : 'text-orange-600'}>
                    {distanceFromSchool < 1000 
                      ? `${distanceFromSchool.toFixed(0)}m from school`
                      : `${(distanceFromSchool / 1000).toFixed(1)}km from school`
                    }
                  </span>
                ) : (
                  <span className="text-gray-500">School location not available</span>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-3 pt-4 border-t">
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
            <Button
              onClick={handleCheckIn}
              disabled={!selectedSchoolId || checkIn.isPending}
              className="bg-green-600 hover:bg-green-700"
            >
              {checkIn.isPending ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Checking In...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Check In
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default FieldStaffCheckInModal;
