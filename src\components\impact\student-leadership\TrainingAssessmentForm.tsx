import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  GraduationCap, 
  Plus, 
  Save, 
  Star, 
  Award,
  BookOpen,
  Users,
  CheckCircle
} from 'lucide-react';
import { useForm, useFieldArray } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { LeadershipProgramType, SatisfactionRating } from '@/types/impact';

const leadershipAssessmentSchema = z.object({
  program_name: z.string().min(1, 'Program name is required'),
  program_type: z.enum(['leadership_skills', 'communication', 'teamwork', 'problem_solving', 'critical_thinking', 'public_speaking', 'project_management', 'entrepreneurship', 'civic_engagement', 'peer_mentoring']),
  facilitator_name: z.string().optional(),
  facilitator_organization: z.string().optional(),
  start_date: z.string(),
  end_date: z.string(),
  duration_hours: z.number().min(1).optional(),
  venue: z.string().optional(),
  target_participants: z.number().min(1).optional(),
  participants: z.array(z.object({
    student_name: z.string().min(1, 'Student name is required'),
    student_id: z.string().optional(),
    school_name: z.string().min(1, 'School is required'),
    attendance_percentage: z.number().min(0).max(100).optional(),
    pre_program_score: z.number().min(0).max(100).optional(),
    post_program_score: z.number().min(0).max(100).optional(),
    certification_received: z.boolean().optional(),
    feedback_rating: z.enum(['very_dissatisfied', 'dissatisfied', 'neutral', 'satisfied', 'very_satisfied']).optional(),
    feedback_comments: z.string().optional()
  })).min(1, 'At least one participant is required')
});

type LeadershipAssessmentFormData = z.infer<typeof leadershipAssessmentSchema>;

interface LeadershipAssessmentFormProps {
  onClose: () => void;
  schoolId?: string | null;
}

const TrainingAssessmentForm: React.FC<LeadershipAssessmentFormProps> = ({ onClose, schoolId }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);

  const {
    register,
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<LeadershipAssessmentFormData>({
    resolver: zodResolver(leadershipAssessmentSchema),
    defaultValues: {
      start_date: new Date().toISOString().split('T')[0],
      end_date: new Date().toISOString().split('T')[0],
      participants: [{ student_name: '', school_name: '' }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'participants'
  });

  const onSubmit = async (data: LeadershipAssessmentFormData) => {
    setIsSubmitting(true);
    try {
      console.log('Leadership assessment data:', data);
      // In real implementation, this would save to database
      onClose();
    } finally {
      setIsSubmitting(false);
    }
  };

  const addParticipant = () => {
    append({ student_name: '', school_name: '' });
  };

  const removeParticipant = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const getSatisfactionStars = (rating: SatisfactionRating) => {
    const ratings = {
      'very_dissatisfied': 1,
      'dissatisfied': 2,
      'neutral': 3,
      'satisfied': 4,
      'very_satisfied': 5
    };
    return ratings[rating] || 0;
  };

  const renderStep = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold mb-4">Leadership Program Details</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="program_name">Program Name *</Label>
                <Input {...register('program_name')} />
                {errors.program_name && (
                  <p className="text-sm text-red-600 mt-1">{errors.program_name.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="program_type">Program Type *</Label>
                <Select
                  value={watch('program_type')}
                  onValueChange={(value) => setValue('program_type', value as LeadershipProgramType)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select program type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="leadership_skills">Leadership Skills</SelectItem>
                    <SelectItem value="communication">Communication</SelectItem>
                    <SelectItem value="teamwork">Teamwork</SelectItem>
                    <SelectItem value="problem_solving">Problem Solving</SelectItem>
                    <SelectItem value="critical_thinking">Critical Thinking</SelectItem>
                    <SelectItem value="public_speaking">Public Speaking</SelectItem>
                    <SelectItem value="project_management">Project Management</SelectItem>
                    <SelectItem value="entrepreneurship">Entrepreneurship</SelectItem>
                    <SelectItem value="civic_engagement">Civic Engagement</SelectItem>
                    <SelectItem value="peer_mentoring">Peer Mentoring</SelectItem>
                  </SelectContent>
                </Select>
                {errors.program_type && (
                  <p className="text-sm text-red-600 mt-1">{errors.program_type.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="facilitator_name">Facilitator Name</Label>
                <Input {...register('facilitator_name')} />
              </div>

              <div>
                <Label htmlFor="facilitator_organization">Facilitator Organization</Label>
                <Input {...register('facilitator_organization')} />
              </div>

              <div>
                <Label htmlFor="start_date">Start Date *</Label>
                <Input type="date" {...register('start_date')} />
                {errors.start_date && (
                  <p className="text-sm text-red-600 mt-1">{errors.start_date.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="end_date">End Date *</Label>
                <Input type="date" {...register('end_date')} />
                {errors.end_date && (
                  <p className="text-sm text-red-600 mt-1">{errors.end_date.message}</p>
                )}
              </div>

              <div>
                <Label htmlFor="duration_hours">Duration (Hours)</Label>
                <Input 
                  type="number" 
                  {...register('duration_hours', { valueAsNumber: true })} 
                />
              </div>

              <div>
                <Label htmlFor="target_participants">Target Participants</Label>
                <Input 
                  type="number" 
                  {...register('target_participants', { valueAsNumber: true })} 
                />
              </div>
            </div>

            <div>
              <Label htmlFor="venue">Venue</Label>
              <Input {...register('venue')} />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Program Participants</h3>
              <Button type="button" onClick={addParticipant} variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-2" />
                Add Participant
              </Button>
            </div>
            
            <div className="space-y-4 max-h-96 overflow-y-auto">
              {fields.map((field, index) => (
                <Card key={field.id} className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h4 className="font-medium">Participant {index + 1}</h4>
                    {fields.length > 1 && (
                      <Button
                        type="button"
                        onClick={() => removeParticipant(index)}
                        variant="outline"
                        size="sm"
                      >
                        Remove
                      </Button>
                    )}
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label>Student Name *</Label>
                      <Input {...register(`participants.${index}.student_name`)} />
                      {errors.participants?.[index]?.student_name && (
                        <p className="text-sm text-red-600 mt-1">
                          {errors.participants[index]?.student_name?.message}
                        </p>
                      )}
                    </div>

                    <div>
                      <Label>Student ID</Label>
                      <Input {...register(`participants.${index}.student_id`)} />
                    </div>
                    
                    <div>
                      <Label>School *</Label>
                      <Input {...register(`participants.${index}.school_name`)} />
                      {errors.participants?.[index]?.school_name && (
                        <p className="text-sm text-red-600 mt-1">
                          {errors.participants[index]?.school_name?.message}
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <Label>Attendance %</Label>
                      <Input 
                        type="number" 
                        min="0" 
                        max="100" 
                        {...register(`participants.${index}.attendance_percentage`, { valueAsNumber: true })} 
                      />
                    </div>
                    
                    <div>
                      <Label>Pre-Program Score</Label>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        {...register(`participants.${index}.pre_program_score`, { valueAsNumber: true })}
                      />
                    </div>

                    <div>
                      <Label>Post-Program Score</Label>
                      <Input
                        type="number"
                        min="0"
                        max="100"
                        {...register(`participants.${index}.post_program_score`, { valueAsNumber: true })}
                      />
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <div className="flex items-center space-x-2">
                      <Checkbox 
                        {...register(`participants.${index}.certification_received`)}
                      />
                      <Label>Certification Received</Label>
                    </div>
                  </div>
                  
                  <div className="mt-4">
                    <Label>Satisfaction Rating</Label>
                    <RadioGroup 
                      value={watch(`participants.${index}.feedback_rating`)}
                      onValueChange={(value) => setValue(`participants.${index}.feedback_rating`, value as SatisfactionRating)}
                    >
                      <div className="flex items-center space-x-4">
                        {[
                          { value: 'very_dissatisfied', label: 'Very Dissatisfied', stars: 1 },
                          { value: 'dissatisfied', label: 'Dissatisfied', stars: 2 },
                          { value: 'neutral', label: 'Neutral', stars: 3 },
                          { value: 'satisfied', label: 'Satisfied', stars: 4 },
                          { value: 'very_satisfied', label: 'Very Satisfied', stars: 5 }
                        ].map((rating) => (
                          <div key={rating.value} className="flex items-center space-x-2">
                            <RadioGroupItem value={rating.value} />
                            <div className="flex items-center space-x-1">
                              {Array.from({ length: rating.stars }).map((_, i) => (
                                <Star key={i} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                              ))}
                              <span className="text-sm">{rating.stars}</span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </RadioGroup>
                  </div>
                  
                  <div className="mt-4">
                    <Label>Feedback Comments</Label>
                    <Textarea {...register(`participants.${index}.feedback_comments`)} />
                  </div>
                </Card>
              ))}
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <GraduationCap className="h-5 w-5" />
            <span>Leadership Program Assessment Form</span>
          </DialogTitle>
          <DialogDescription>
            Record student leadership program details and participant assessments
          </DialogDescription>
        </DialogHeader>

        {/* Progress Indicator */}
        <div className="flex items-center space-x-4 mb-6">
          <div className={`flex items-center space-x-2 ${currentStep >= 1 ? 'text-ilead-green' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              currentStep >= 1 ? 'bg-ilead-green text-white' : 'bg-gray-200'
            }`}>
              1
            </div>
            <span className="text-sm font-medium">Program Details</span>
          </div>
          <div className={`h-px flex-1 ${currentStep >= 2 ? 'bg-ilead-green' : 'bg-gray-200'}`} />
          <div className={`flex items-center space-x-2 ${currentStep >= 2 ? 'text-ilead-green' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              currentStep >= 2 ? 'bg-ilead-green text-white' : 'bg-gray-200'
            }`}>
              2
            </div>
            <span className="text-sm font-medium">Participants</span>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {renderStep()}

          {/* Form Actions */}
          <div className="flex justify-between">
            <div>
              {currentStep > 1 && (
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={() => setCurrentStep(currentStep - 1)}
                >
                  Previous
                </Button>
              )}
            </div>
            
            <div className="flex space-x-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              
              {currentStep < 2 ? (
                <Button 
                  type="button" 
                  onClick={() => setCurrentStep(currentStep + 1)}
                  className="bg-ilead-green hover:bg-ilead-dark-green"
                >
                  Next
                </Button>
              ) : (
                <Button 
                  type="submit" 
                  disabled={isSubmitting}
                  className="bg-ilead-green hover:bg-ilead-dark-green"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4 mr-2" />
                      Save Assessment
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default TrainingAssessmentForm;
