import React, { useState, useRef } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Progress } from '@/components/ui/progress';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Download, Upload, FileSpreadsheet, AlertCircle, CheckCircle, Loader2 } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { useSchoolOperations } from '@/hooks/useSchoolOperations';

interface ImportResult {
  success: number;
  failed: number;
  errors: string[];
}

interface SchoolImportRow {
  // Required fields
  name: string;
  school_type: string;
  division_name: string; // District name is required
  student_count: string; // Now required
  champion_teacher_count: string; // Now required

  // Optional fields
  code?: string;
  school_category?: string;
  contact_phone?: string;
  email?: string;
  head_teacher_name?: string;
  head_teacher_phone?: string;
  head_teacher_email?: string;
  deputy_head_teacher_name?: string;
  deputy_head_teacher_phone?: string;
  deputy_head_teacher_email?: string;
  date_joined_ilead?: string;
  ownership_type?: string;
  location_coordinates?: string;
  is_partner_managed?: string;
  partner_name?: string;
  field_staff_name?: string;
  champion_teacher_1_name?: string;
  champion_teacher_1_phone?: string;
  champion_teacher_1_email?: string;
}

const SchoolImportTemplate: React.FC = () => {
  const [isImporting, setIsImporting] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [importResult, setImportResult] = useState<ImportResult | null>(null);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const { addSchool } = useSchoolOperations(null);

  // Fetch divisions for validation
  const { data: divisions = [] } = useQuery({
    queryKey: ['divisions'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_admin_divisions');
      if (error) throw error;
      return data || [];
    },
  });

  // Fetch field staff for validation
  const { data: fieldStaff = [] } = useQuery({
    queryKey: ['field-staff-members'],
    queryFn: async () => {
      const { data, error } = await supabase.rpc('get_field_staff_members');
      if (error) throw error;
      return data || [];
    },
  });

  const generateTemplate = () => {
    const headers = [
      'name',
      'school_type',
      'division_name',
      'student_count',
      'champion_teacher_count',
      'code',
      'school_category',
      'contact_phone',
      'email',
      'head_teacher_name',
      'head_teacher_phone',
      'head_teacher_email',
      'deputy_head_teacher_name',
      'deputy_head_teacher_phone',
      'deputy_head_teacher_email',
      'date_joined_ilead',
      'ownership_type',
      'location_coordinates',
      'is_partner_managed',
      'partner_name',
      'field_staff_name',
      'champion_teacher_1_name',
      'champion_teacher_1_phone',
      'champion_teacher_1_email'
    ];

    const sampleData = [
      'Kampala Primary School',
      'primary',
      'Kampala Central',
      '500',
      '5',
      'KPS001',
      'day',
      '+256 700 123 456',
      '<EMAIL>',
      'John Doe',
      '+256 700 111 111',
      '<EMAIL>',
      'Jane Smith',
      '+256 700 222 222',
      '<EMAIL>',
      '2023-01-15',
      'government',
      '0.3476, 32.5825',
      'false',
      '', // partner_name (empty)
      'Field Staff Name', // field_staff_name
      'Champion Teacher Name',
      '+256 700 555 666',
      '<EMAIL>'
    ];

    // Helper function to escape CSV fields that contain commas
    const escapeCSVField = (field) => {
      if (field.includes(',') || field.includes('"') || field.includes('\n')) {
        return `"${field.replace(/"/g, '""')}"`;
      }
      return field;
    };

    const csvContent = [
      headers.join(','),
      sampleData.map(escapeCSVField).join(','),
      // Add empty row for user to fill
      headers.map(() => '').join(',')
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = 'school_import_template.csv';
    link.click();
    window.URL.revokeObjectURL(url);
    
    toast.success('Template downloaded successfully!');
  };

  const validateRow = (row: SchoolImportRow, rowIndex: number): string[] => {
    const errors: string[] = [];

    // Required fields validation
    if (!row.name?.trim()) {
      errors.push(`Row ${rowIndex}: School name is required`);
    }

    if (!row.school_type?.trim()) {
      errors.push(`Row ${rowIndex}: School type is required`);
    } else if (!['primary', 'secondary', 'tertiary', 'vocational', 'university'].includes(row.school_type)) {
      errors.push(`Row ${rowIndex}: Invalid school type. Must be: primary, secondary, tertiary, vocational, or university`);
    }

    if (!row.division_name?.trim()) {
      errors.push(`Row ${rowIndex}: District name is required`);
    } else {
      const division = divisions.find(d =>
        d.district?.toLowerCase() === row.division_name.toLowerCase() ||
        d.name?.toLowerCase() === row.division_name.toLowerCase()
      );
      if (!division) {
        errors.push(`Row ${rowIndex}: District "${row.division_name}" not found`);
      }
    }

    // Required numeric fields validation
    if (!row.student_count?.trim()) {
      errors.push(`Row ${rowIndex}: Student count is required`);
    } else if (isNaN(Number(row.student_count)) || Number(row.student_count) < 0) {
      errors.push(`Row ${rowIndex}: Student count must be a valid positive number`);
    }

    if (!row.champion_teacher_count?.trim()) {
      errors.push(`Row ${rowIndex}: Champion teacher count is required`);
    } else if (isNaN(Number(row.champion_teacher_count)) || Number(row.champion_teacher_count) < 0) {
      errors.push(`Row ${rowIndex}: Champion teacher count must be a valid positive number`);
    }

    // Optional email validation (only if provided)
    if (row.email && row.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.email)) {
      errors.push(`Row ${rowIndex}: Invalid school email format`);
    }

    if (row.head_teacher_email && row.head_teacher_email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.head_teacher_email)) {
      errors.push(`Row ${rowIndex}: Invalid head teacher email format`);
    }

    if (row.deputy_head_teacher_email && row.deputy_head_teacher_email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.deputy_head_teacher_email)) {
      errors.push(`Row ${rowIndex}: Invalid deputy head teacher email format`);
    }

    if (row.champion_teacher_1_email && row.champion_teacher_1_email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(row.champion_teacher_1_email)) {
      errors.push(`Row ${rowIndex}: Invalid champion teacher email format`);
    }

    // Field staff validation
    if (row.field_staff_name?.trim()) {
      const staff = fieldStaff.find(s => s.name.toLowerCase() === row.field_staff_name.toLowerCase());
      if (!staff) {
        errors.push(`Row ${rowIndex}: Field staff "${row.field_staff_name}" not found`);
      }
    }



    return errors;
  };

  // Proper CSV parsing that handles quoted fields
  const parseCSVLine = (line: string): string[] => {
    const result: string[] = [];
    let current = '';
    let inQuotes = false;
    let i = 0;

    while (i < line.length) {
      const char = line[i];

      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i += 2;
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
          i++;
        }
      } else if (char === ',' && !inQuotes) {
        // Field separator
        result.push(current.trim());
        current = '';
        i++;
      } else {
        current += char;
        i++;
      }
    }

    // Add the last field
    result.push(current.trim());
    return result;
  };

  const parseCSV = (csvText: string): SchoolImportRow[] => {
    const lines = csvText.split('\n').filter(line => line.trim());
    if (lines.length < 2) return [];

    const headers = parseCSVLine(lines[0]).map(h => h.replace(/"/g, ''));
    const rows: SchoolImportRow[] = [];

    for (let i = 1; i < lines.length; i++) {
      const values = parseCSVLine(lines[i]).map(v => v.replace(/"/g, ''));
      if (values.some(v => v)) { // Skip empty rows
        const row: Record<string, string> = {};
        headers.forEach((header, index) => {
          row[header] = values[index] || '';
        });
        rows.push(row as SchoolImportRow);
      }
    }
    
    return rows;
  };

  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    if (!file.name.endsWith('.csv')) {
      toast.error('Please upload a CSV file');
      return;
    }

    setIsImporting(true);
    setImportProgress(0);
    setImportResult(null);

    try {
      const text = await file.text();
      const rows = parseCSV(text);
      
      if (rows.length === 0) {
        toast.error('No valid data found in the CSV file');
        setIsImporting(false);
        return;
      }

      const allErrors: string[] = [];
      let successCount = 0;
      let failedCount = 0;

      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const rowErrors = validateRow(row, i + 2); // +2 because of header and 1-based indexing
        
        if (rowErrors.length > 0) {
          allErrors.push(...rowErrors);
          failedCount++;
        } else {
          try {
            // Find division ID
            const division = divisions.find(d =>
              d.district?.toLowerCase() === row.division_name.toLowerCase() ||
              d.name?.toLowerCase() === row.division_name.toLowerCase()
            );
            
            // Find field staff ID
            let fieldStaffId = '';
            if (row.field_staff_name?.trim()) {
              const staff = fieldStaff.find(s => s.name.toLowerCase() === row.field_staff_name.toLowerCase());
              fieldStaffId = staff?.id || '';
            }

            const schoolData = {
              name: row.name,
              code: row.code || '',
              school_type: row.school_type as 'primary' | 'secondary',
              school_category: (row.school_category || 'day') as 'day' | 'boarding' | 'mixed',
              student_count: row.student_count,
              champion_teacher_count: row.champion_teacher_count,
              contact_phone: row.contact_phone || '',
              email: row.email || '',
              division_id: division?.id || '',
              head_teacher_name: row.head_teacher_name || '',
              head_teacher_phone: row.head_teacher_phone || '',
              head_teacher_email: row.head_teacher_email || '',
              deputy_head_teacher_name: row.deputy_head_teacher_name || '',
              deputy_head_teacher_phone: row.deputy_head_teacher_phone || '',
              deputy_head_teacher_email: row.deputy_head_teacher_email || '',
              date_joined_ilead: row.date_joined_ilead || '',
              ownership_type: row.ownership_type || 'government',
              location_coordinates: row.location_coordinates || '',
              is_partner_managed: row.is_partner_managed?.toLowerCase() === 'true',
              partner_name: row.partner_name || '',
              field_staff_id: fieldStaffId,
              champion_teachers: row.champion_teacher_1_name?.trim() ? [{
                id: '1',
                name: row.champion_teacher_1_name,
                phone: row.champion_teacher_1_phone || '',
                email: row.champion_teacher_1_email || ''
              }] : [],
              assistant_champion_teachers: [],
            };

            await addSchool(schoolData);
            successCount++;
          } catch (error) {
            allErrors.push(`Row ${i + 2}: Failed to import - ${error instanceof Error ? error.message : 'Unknown error'}`);
            failedCount++;
          }
        }
        
        setImportProgress(((i + 1) / rows.length) * 100);
      }

      setImportResult({
        success: successCount,
        failed: failedCount,
        errors: allErrors
      });

      if (successCount > 0) {
        toast.success(`Successfully imported ${successCount} schools`);
      }
      
      if (failedCount > 0) {
        toast.error(`Failed to import ${failedCount} schools. Check the results for details.`);
      }

    } catch (error) {
      toast.error('Failed to process the CSV file');
      console.error('Import error:', error);
    } finally {
      setIsImporting(false);
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  return (
    <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <FileSpreadsheet className="h-4 w-4" />
          Import Schools
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Import Schools from CSV</DialogTitle>
          <DialogDescription>
            Download the template, fill it with school data, and upload it to bulk import schools.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Download Template */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Step 1: Download Template</CardTitle>
              <CardDescription>
                Download the CSV template with all required fields and sample data.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Button onClick={generateTemplate} className="gap-2">
                <Download className="h-4 w-4" />
                Download CSV Template
              </Button>
            </CardContent>
          </Card>

          {/* Upload File */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm">Step 2: Upload Completed File</CardTitle>
              <CardDescription>
                Upload your completed CSV file to import schools.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <input
                ref={fileInputRef}
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="hidden"
              />
              <Button
                onClick={() => fileInputRef.current?.click()}
                disabled={isImporting}
                className="gap-2"
              >
                {isImporting ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <Upload className="h-4 w-4" />
                )}
                {isImporting ? 'Importing...' : 'Upload CSV File'}
              </Button>

              {isImporting && (
                <div className="space-y-2">
                  <Progress value={importProgress} className="w-full" />
                  <p className="text-sm text-gray-600">
                    Importing schools... {Math.round(importProgress)}%
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Import Results */}
          {importResult && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  {importResult.success > 0 ? (
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  ) : (
                    <AlertCircle className="h-4 w-4 text-red-600" />
                  )}
                  Import Results
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      {importResult.success}
                    </div>
                    <div className="text-sm text-green-700">Successful</div>
                  </div>
                  <div className="text-center p-3 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {importResult.failed}
                    </div>
                    <div className="text-sm text-red-700">Failed</div>
                  </div>
                </div>

                {importResult.errors.length > 0 && (
                  <Alert variant="destructive">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <div className="space-y-1">
                        <p className="font-medium">Import Errors:</p>
                        <div className="max-h-32 overflow-y-auto">
                          {importResult.errors.slice(0, 10).map((error, index) => (
                            <p key={index} className="text-xs">{error}</p>
                          ))}
                          {importResult.errors.length > 10 && (
                            <p className="text-xs font-medium">
                              ... and {importResult.errors.length - 10} more errors
                            </p>
                          )}
                        </div>
                      </div>
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p className="font-medium">Important Notes:</p>
                <ul className="text-sm space-y-1 ml-4 list-disc">
                  <li><strong>Required fields:</strong> School name, school type, district name, student count, and champion teacher count</li>
                  <li><strong>Optional fields:</strong> Centre number, date joined iLead, head/deputy teacher information, champion teacher email</li>
                  <li>District names must match exactly with existing Uganda districts</li>
                  <li>Field staff names must match existing field staff members</li>
                  <li>School types: primary, secondary, tertiary, vocational, university</li>
                  <li>School categories: day, boarding, both</li>
                  <li>At least 1 champion teacher is required (name field), email is optional</li>
                  <li>Coordinates format: latitude, longitude (e.g., 0.3476, 32.5825)</li>
                </ul>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default SchoolImportTemplate;
