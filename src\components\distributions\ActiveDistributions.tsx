
import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  Package,
  Clock,
  MapPin,
  User,
  Eye,
  Calendar,
  Play,
  Edit,
  CheckCircle,
  AlertCircle,
  XCircle,
  Plus
} from 'lucide-react';
import { useDistributions } from '@/hooks/useDistributions';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import DistributionDetailsModal from './DistributionDetailsModal';
import ScheduleDistributionModal from './ScheduleDistributionModal';
import CreateDistributionDialog from './CreateDistributionDialog';
import { DistributionSubmissionData } from '@/hooks/useDistributionForm';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

// Type definitions
type BookDistribution = Database['public']['Functions']['get_book_distributions']['Returns'][0];
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type BookInventory = Database['public']['Functions']['get_book_inventory']['Returns'][0];

const ActiveDistributions = () => {
  const { profile } = useAuth();
  const { toast } = useToast();
  const { distributions, schools, inventory, isLoading, addDistribution, isAdding } = useDistributions();
  const [selectedDistribution, setSelectedDistribution] = useState<BookDistribution | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [scheduleModalOpen, setScheduleModalOpen] = useState(false);

  // Filter distributions for today or in progress as "active"
  const activeDistributions = distributions.filter((dist: BookDistribution) => {
    const distDate = new Date(dist.delivery_date);
    const today = new Date();

    // Set both dates to start of day for accurate comparison
    const distDateOnly = new Date(distDate.getFullYear(), distDate.getMonth(), distDate.getDate());
    const todayOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

    // Include distributions scheduled for today OR currently in progress
    return (distDateOnly.getTime() === todayOnly.getTime()) || (dist.status === 'in_progress');
  });

  // Get planned distributions (status = 'planned')
  const { data: plannedDistributions = [], isLoading: plannedLoading } = useQuery({
    queryKey: ['planned-distributions'],
    queryFn: async (): Promise<BookDistribution[]> => {
      const { data, error } = await supabase
        .rpc('get_book_distributions');

      if (error) {
        console.error('Error fetching planned distributions:', error);
        throw error;
      }

      // Filter for planned distributions
      return (data || []).filter((dist: BookDistribution) => dist.status === 'planned');
    },
  });

  const handleViewDetails = (distribution: BookDistribution) => {
    console.log('ActiveDistributions: Opening details for distribution:', distribution);
    setSelectedDistribution(distribution);
    setDetailsModalOpen(true);
  };

  const handleCreateDistribution = (data: DistributionSubmissionData) => {
    console.log('ActiveDistributions: Handling distribution creation:', data);
    addDistribution(data);
  };

  const handleScheduleDistribution = () => {
    setScheduleModalOpen(false);
    // Refresh planned distributions after scheduling
  };

  // Execute a planned distribution immediately
  const executeDistribution = async (distributionId: string) => {
    try {
      const { error } = await supabase
        .rpc('update_distribution_status', {
          p_distribution_id: distributionId,
          p_status: 'completed'
        });

      if (error) throw error;

      toast({
        title: "Success",
        description: "Distribution executed successfully",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to execute distribution",
        variant: "destructive",
      });
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'planned':
        return <Calendar className="h-4 w-4 text-purple-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'planned':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-32 bg-gray-200 rounded"></div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Active Distributions"
        description={`Today's distributions and in-progress deliveries - ${activeDistributions.length} active`}
        icon={Package}
      >
        <div className="flex items-center gap-3">
          <Badge variant="secondary" className="bg-green-100 text-green-800">
            {activeDistributions.length} Active
          </Badge>
          <Button
            onClick={() => setScheduleModalOpen(true)}
            className="bg-green-600 hover:bg-green-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Schedule New Distribution
          </Button>
        </div>
      </PageHeader>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Main Active Distributions List - 3/4 width */}
        <div className="lg:col-span-3">
          <ContentCard noPadding>
            {activeDistributions.length === 0 ? (
              <div className="text-center py-12">
                <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No active distributions</h3>
                <p className="text-gray-600 mb-4">Today's distributions and in-progress deliveries will appear here</p>
              </div>
            ) : (
              <div className="p-6">
                <div className="space-y-4">
                  {activeDistributions.map((distribution: BookDistribution) => (
                    <div key={distribution.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <Package className="h-5 w-5 text-green-600" />
                          <div>
                            <h4 className="font-semibold text-lg">{distribution.book_title}</h4>
                            <p className="text-sm text-gray-600">{distribution.school_name}</p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge className="bg-blue-100 text-blue-800">
                            {distribution.quantity} copies
                          </Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleViewDetails(distribution)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View Details
                          </Button>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-2" />
                          <span>{distribution.supervisor_name}</span>
                        </div>
                        <div className="flex items-center">
                          <Clock className="h-4 w-4 mr-2" />
                          <span>{distribution.delivery_date}</span>
                        </div>
                        <div className="flex items-center">
                          <MapPin className="h-4 w-4 mr-2" />
                          <span>GPS Logged</span>
                        </div>
                      </div>

                      {distribution.notes && (
                        <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                          <p className="text-sm text-gray-700">{distribution.notes}</p>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </ContentCard>
        </div>

        {/* Planned Distributions Sidebar - 1/4 width */}
        <div className="lg:col-span-1">
          <Card className="h-fit">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5 text-purple-600" />
                Planned Distributions
              </CardTitle>
              <p className="text-sm text-gray-600">Upcoming scheduled distributions</p>
            </CardHeader>
            <CardContent>
              {plannedLoading ? (
                <div className="text-center py-4">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto"></div>
                  <p className="text-sm text-gray-500 mt-2">Loading...</p>
                </div>
              ) : plannedDistributions.length === 0 ? (
                <div className="text-center py-6 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-3 text-gray-300" />
                  <p className="text-sm">No planned distributions</p>
                  <p className="text-xs mt-1">Schedule one using the button above</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {plannedDistributions.slice(0, 8).map((distribution) => (
                    <div
                      key={distribution.id}
                      className="flex items-center justify-between p-2 rounded-lg bg-purple-50 hover:bg-purple-100 cursor-pointer transition-colors"
                      onClick={() => handleViewDetails(distribution)}
                    >
                      <span className="font-medium text-sm text-purple-900 truncate">
                        {distribution.school_name}
                      </span>
                      <Badge variant="outline" className="text-purple-600 border-purple-200 text-xs ml-2">
                        <Clock className="h-3 w-3 mr-1" />
                        Planned
                      </Badge>
                    </div>
                  ))}
                  {plannedDistributions.length > 8 && (
                    <p className="text-xs text-gray-500 text-center pt-2">
                      +{plannedDistributions.length - 8} more planned
                    </p>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modals */}
      <DistributionDetailsModal
        distribution={selectedDistribution}
        isOpen={detailsModalOpen}
        onClose={() => setDetailsModalOpen(false)}
      />

      <ScheduleDistributionModal
        schools={schools}
        inventory={inventory}
        onScheduled={handleScheduleDistribution}
        isOpen={scheduleModalOpen}
        onClose={() => setScheduleModalOpen(false)}
      />
    </PageLayout>
  );
};

export default ActiveDistributions;
