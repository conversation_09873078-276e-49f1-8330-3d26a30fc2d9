import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { ComprehensiveAdminDashboard } from '../ComprehensiveAdminDashboard';
import { useAuth } from '@/hooks/useAuth';

// Mock the hooks
jest.mock('@/hooks/useAuth');
jest.mock('@/hooks/dashboard/useDashboardMetrics', () => ({
  useDashboardMetrics: () => ({
    data: {
      fieldStaff: {
        totalStaff: 10,
        activeStaff: 8,
        checkedInToday: 6,
        averageHoursPerDay: 7.5,
        checkInComplianceRate: 85,
        reportSubmissionRate: 92,
      },
      programReach: {
        totalStudentsReached: 1250,
        schoolsCovered: 45,
        totalSchools: 50,
        sessionCompletionRate: 88,
        averageAttendancePerSession: 22,
        bookDistributionRate: 76,
      },
      operational: {
        taskCompletionRate: 94,
        averageTaskCompletionTime: 2.3,
        reportQualityScore: 87,
        resourceUtilization: 82,
        offlineSyncSuccessRate: 96,
      },
      quality: {
        sessionAttendanceTrend: 5.2,
        studentEngagementScore: 4.3,
        followUpCompletionRate: 89,
        challengeResolutionTime: 1.8,
        feedbackSentiment: 4.1,
      },
    },
    isLoading: false,
    refetch: jest.fn(),
  }),
  useActivitySummary: () => ({
    data: {
      todayActivities: {
        checkIns: 6,
        sessions: 12,
        reports: 8,
        visits: 6,
      },
      weeklyTrend: [
        { date: '2024-01-01', activities: 5, students: 120, hours: 35 },
        { date: '2024-01-02', activities: 8, students: 180, hours: 42 },
        { date: '2024-01-03', activities: 6, students: 150, hours: 38 },
      ],
      criticalAlerts: [
        {
          id: '1',
          type: 'warning' as const,
          message: 'GPS verification failed for 2 staff members',
          timestamp: new Date().toISOString(),
          priority: 'medium' as const,
        },
      ],
    },
    isLoading: false,
    refetch: jest.fn(),
  }),
  useStaffPerformance: () => ({
    data: [
      {
        id: '1',
        name: 'John Doe',
        role: 'field_staff',
        isCheckedIn: true,
        currentSchool: 'Test School',
        todayHours: 8,
        weeklyHours: 40,
        studentsReached: 150,
        schoolsVisited: 5,
        reportsSubmitted: 3,
        performanceScore: 95,
        lastActivity: new Date().toISOString(),
      },
    ],
    isLoading: false,
    refetch: jest.fn(),
  }),
}));

const mockUseAuth = useAuth as jest.MockedFunction<typeof useAuth>;

const createTestQueryClient = () => new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = createTestQueryClient();
  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('ComprehensiveAdminDashboard', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders admin dashboard for admin users', async () => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      profile: { 
        id: '1', 
        role: 'admin', 
        full_name: 'Admin User',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
      },
      loading: false,
      signOut: jest.fn(),
    });

    render(
      <TestWrapper>
        <ComprehensiveAdminDashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    });

    // Check for executive summary cards
    expect(screen.getByText('Field Staff Activity')).toBeInTheDocument();
    expect(screen.getByText('Schools Reached')).toBeInTheDocument();
    expect(screen.getByText('Students Impacted')).toBeInTheDocument();
    expect(screen.getByText('Program Efficiency')).toBeInTheDocument();

    // Check for quick actions
    expect(screen.getByText('Quick Actions')).toBeInTheDocument();
    expect(screen.getByText('View Staff')).toBeInTheDocument();
    expect(screen.getByText('Field Reports')).toBeInTheDocument();

    // Check for tabs
    expect(screen.getByText('Overview')).toBeInTheDocument();
    expect(screen.getByText('Analytics')).toBeInTheDocument();
    expect(screen.getByText('Performance')).toBeInTheDocument();
  });

  it('renders admin dashboard for program officers', async () => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      profile: { 
        id: '1', 
        role: 'program_officer', 
        full_name: 'Program Officer',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
      },
      loading: false,
      signOut: jest.fn(),
    });

    render(
      <TestWrapper>
        <ComprehensiveAdminDashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Admin Dashboard')).toBeInTheDocument();
    });
  });

  it('shows access denied for field staff', async () => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      profile: { 
        id: '1', 
        role: 'field_staff', 
        full_name: 'Field Staff',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
      },
      loading: false,
      signOut: jest.fn(),
    });

    render(
      <TestWrapper>
        <ComprehensiveAdminDashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
    });

    expect(screen.getByText('Admin Access Required')).toBeInTheDocument();
  });

  it('displays real-time activity data', async () => {
    mockUseAuth.mockReturnValue({
      user: { id: '1', email: '<EMAIL>' },
      profile: { 
        id: '1', 
        role: 'admin', 
        full_name: 'Admin User',
        email: '<EMAIL>',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        is_active: true,
      },
      loading: false,
      signOut: jest.fn(),
    });

    render(
      <TestWrapper>
        <ComprehensiveAdminDashboard />
      </TestWrapper>
    );

    await waitFor(() => {
      expect(screen.getByText("Today's Activities")).toBeInTheDocument();
    });

    // Check for activity metrics
    expect(screen.getByText('Check-ins')).toBeInTheDocument();
    expect(screen.getByText('Sessions')).toBeInTheDocument();
    expect(screen.getByText('Reports')).toBeInTheDocument();
  });
});
