/**
 * Offline storage management utilities
 * Handles localStorage operations, data integrity, and storage statistics
 */

import { 
  OfflineData, 
  ConflictData, 
  StorageStats, 
  STORAGE_KEYS, 
  DEFAULT_CONFIG 
} from '@/types/offlineSync.types';

/**
 * Generate a simple checksum for data integrity verification
 */
export const generateChecksum = (data: Record<string, unknown>): string => {
  const str = JSON.stringify(data, Object.keys(data).sort());
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return hash.toString(36);
};

/**
 * Load offline data from localStorage with integrity check
 */
export const loadOfflineData = (): OfflineData[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.OFFLINE_DATA);
    if (!stored) return [];

    const data = JSON.parse(stored);

    // Validate data integrity
    return data.filter((item: OfflineData) => {
      if (!item.checksum) return true; // Legacy data without checksum
      const currentChecksum = generateChecksum(item.data);
      if (currentChecksum !== item.checksum) {
        console.warn(`Data integrity check failed for item ${item.id}`);
        return false;
      }
      return true;
    });
  } catch (error) {
    console.error('Failed to load offline data:', error);
    // Clear corrupted data
    localStorage.removeItem(STORAGE_KEYS.OFFLINE_DATA);
    return [];
  }
};

/**
 * Save offline data to localStorage with integrity check
 */
export const saveOfflineData = (data: OfflineData[]): void => {
  try {
    // Add checksums to data items
    const dataWithChecksums = data.map(item => ({
      ...item,
      checksum: item.checksum || generateChecksum(item.data)
    }));
    
    localStorage.setItem(STORAGE_KEYS.OFFLINE_DATA, JSON.stringify(dataWithChecksums));
  } catch (error) {
    console.error('Failed to save offline data:', error);
    throw new Error('Storage quota exceeded or localStorage unavailable');
  }
};

/**
 * Load conflicts from localStorage
 */
export const loadConflicts = (): ConflictData[] => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.CONFLICTS);
    return stored ? JSON.parse(stored) : [];
  } catch (error) {
    console.error('Failed to load conflicts:', error);
    localStorage.removeItem(STORAGE_KEYS.CONFLICTS);
    return [];
  }
};

/**
 * Save conflicts to localStorage
 */
export const saveConflicts = (conflicts: ConflictData[]): void => {
  try {
    localStorage.setItem(STORAGE_KEYS.CONFLICTS, JSON.stringify(conflicts));
  } catch (error) {
    console.error('Failed to save conflicts:', error);
    throw new Error('Failed to save conflict data');
  }
};

/**
 * Calculate comprehensive storage statistics
 */
export const calculateStorageStats = async (): Promise<StorageStats> => {
  try {
    const offlineData = loadOfflineData();
    const conflicts = loadConflicts();

    // Calculate localStorage sizes
    const offlineDataSize = new Blob([JSON.stringify(offlineData)]).size;
    const conflictSize = new Blob([JSON.stringify(conflicts)]).size;

    // Get photo cache stats
    let photoStats = { totalItems: 0, totalSize: 0 };
    try {
      const { photoCacheManager } = await import('@/utils/photoCacheManager');
      const cacheStats = await photoCacheManager.getCacheStats();
      photoStats = {
        totalItems: cacheStats.totalItems,
        totalSize: cacheStats.totalSize
      };
    } catch (error) {
      console.warn('Could not get photo cache stats:', error);
    }

    const totalSize = offlineDataSize + conflictSize + photoStats.totalSize;
    const storageUsagePercent = (totalSize / DEFAULT_CONFIG.maxStorageSize) * 100;

    const timestamps = offlineData.map(item => item.timestamp).filter(Boolean);
    const oldestItem = timestamps.length > 0 ? Math.min(...timestamps) : 0;
    const newestItem = timestamps.length > 0 ? Math.max(...timestamps) : 0;

    return {
      totalItems: offlineData.length,
      totalSize: offlineDataSize,
      conflictItems: conflicts.length,
      conflictSize,
      photoItems: photoStats.totalItems,
      photoSize: photoStats.totalSize,
      lastCleanup: parseInt(localStorage.getItem(`${STORAGE_KEYS.STORAGE_STATS}_last_cleanup`) || '0'),
      storageUsagePercent,
      oldestItem,
      newestItem
    };
  } catch (error) {
    console.error('Error calculating storage stats:', error);
    return {
      totalItems: 0,
      totalSize: 0,
      conflictItems: 0,
      conflictSize: 0,
      photoItems: 0,
      photoSize: 0,
      lastCleanup: 0,
      storageUsagePercent: 0,
      oldestItem: 0,
      newestItem: 0
    };
  }
};

/**
 * Add item to offline queue with validation
 */
export const addToOfflineQueue = (
  type: OfflineData['type'],
  data: Record<string, unknown>,
  priority: OfflineData['priority'] = 'MEDIUM',
  maxRetries: number = DEFAULT_CONFIG.maxRetryAttempts
): string => {
  const id = `${type}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  const timestamp = Date.now();
  
  const item: OfflineData = {
    id,
    type,
    data,
    timestamp,
    retryCount: 0,
    maxRetries,
    priority,
    checksum: generateChecksum(data),
    version: 1
  };

  const existingData = loadOfflineData();
  const updatedData = [...existingData, item];
  
  saveOfflineData(updatedData);
  
  return id;
};

/**
 * Remove item from offline queue
 */
export const removeFromOfflineQueue = (id: string): boolean => {
  try {
    const existingData = loadOfflineData();
    const filteredData = existingData.filter(item => item.id !== id);
    
    if (filteredData.length === existingData.length) {
      return false; // Item not found
    }
    
    saveOfflineData(filteredData);
    return true;
  } catch (error) {
    console.error('Failed to remove item from offline queue:', error);
    return false;
  }
};

/**
 * Update item in offline queue
 */
export const updateOfflineQueueItem = (id: string, updates: Partial<OfflineData>): boolean => {
  try {
    const existingData = loadOfflineData();
    const itemIndex = existingData.findIndex(item => item.id === id);
    
    if (itemIndex === -1) {
      return false; // Item not found
    }
    
    existingData[itemIndex] = { ...existingData[itemIndex], ...updates };
    saveOfflineData(existingData);
    return true;
  } catch (error) {
    console.error('Failed to update offline queue item:', error);
    return false;
  }
};

/**
 * Clear all offline data
 */
export const clearOfflineData = (): void => {
  try {
    localStorage.removeItem(STORAGE_KEYS.OFFLINE_DATA);
    localStorage.removeItem(STORAGE_KEYS.CONFLICTS);
    localStorage.removeItem(STORAGE_KEYS.STORAGE_STATS);
  } catch (error) {
    console.error('Failed to clear offline data:', error);
  }
};

/**
 * Get storage quota information
 */
export const getStorageQuota = async (): Promise<{ used: number; available: number; total: number }> => {
  if ('storage' in navigator && 'estimate' in navigator.storage) {
    try {
      const estimate = await navigator.storage.estimate();
      return {
        used: estimate.usage || 0,
        available: (estimate.quota || 0) - (estimate.usage || 0),
        total: estimate.quota || 0
      };
    } catch (error) {
      console.warn('Could not get storage quota:', error);
    }
  }
  
  // Fallback estimation
  return {
    used: 0,
    available: DEFAULT_CONFIG.maxStorageSize,
    total: DEFAULT_CONFIG.maxStorageSize
  };
};
