import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MessageSquare, Users, Star, TrendingUp, Plus, BarChart3 } from 'lucide-react';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { MetricCard, ComingSoonCard } from '../shared';
import SurveyForm from './SurveyForm';

interface BeneficiaryFeedbackModuleProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const BeneficiaryFeedbackModule: React.FC<BeneficiaryFeedbackModuleProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [showSurveyForm, setShowSurveyForm] = useState(false);
  // Access control is now handled by AdminOnlyWrapper

  return (
    <PageLayout>
      <PageHeader
        title="Beneficiary Feedback"
        description="Collect and analyze stakeholder satisfaction and feedback"
        icon={MessageSquare}
        actions={[
          {
            label: 'Create Survey',
            onClick: () => setShowSurveyForm(true),
            icon: Plus,
          }
        ]}
      />

      {/* Feedback Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Total Responses"
          value="342"
          icon={MessageSquare}
          color="orange"
        />
        <MetricCard
          title="Avg Satisfaction"
          value="4.2/5"
          icon={Star}
          color="green"
        />
        <MetricCard
          title="Stakeholder Groups"
          value="5"
          icon={Users}
          color="blue"
        />
        <MetricCard
          title="Response Rate"
          value="78%"
          icon={TrendingUp}
          color="purple"
        />
      </div>

      {/* Coming Soon Message */}
      <ComingSoonCard
        title="Beneficiary Feedback Analytics"
        description="This module will include mobile-friendly surveys, sentiment analysis, automated feedback collection, and comprehensive stakeholder satisfaction reporting."
        icon={BarChart3}
        placeholderIcon={MessageSquare}
        features={[
          'Mobile-friendly survey forms',
          'Multi-stakeholder feedback collection',
          'Sentiment analysis and categorization',
          'Automated survey scheduling',
          'Actionable insights dashboard'
        ]}
      />

      {/* Survey Form */}
      {showSurveyForm && (
        <SurveyForm
          onClose={() => setShowSurveyForm(false)}
          schoolId={schoolId}
        />
      )}
    </PageLayout>
  );
};

export default BeneficiaryFeedbackModule;
