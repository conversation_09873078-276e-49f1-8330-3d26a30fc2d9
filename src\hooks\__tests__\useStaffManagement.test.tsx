import { renderHook, waitFor } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { vi, describe, it, expect, beforeEach } from 'vitest';
import { useStaffManagement } from '../useStaffManagement';
import { supabase } from '@/integrations/supabase/client';

// Mock Supabase
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    from: vi.fn(),
    rpc: vi.fn(),
    auth: {
      getUser: vi.fn()
    }
  }
}));

// Mock toast
vi.mock('@/hooks/use-toast', () => ({
  useToast: () => ({
    toast: vi.fn()
  })
}));

const mockStaffData = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'field_staff',
    division_id: null,
    division_name: null,
    phone: '+256700000000',
    country: 'Uganda',
    is_active: true,
    requires_password_change: false,
    last_password_change: null,
    invitation_accepted_at: '2024-01-01T00:00:00Z',
    created_at: '2024-01-01T00:00:00Z'
  }
];

const mockInvitationsData = [
  {
    id: 'inv-1',
    email: '<EMAIL>',
    name: 'Pending User',
    role: 'field_staff',
    status: 'pending',
    created_at: '2024-01-01T00:00:00Z',
    expires_at: '2024-01-08T00:00:00Z'
  }
];

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false }
    }
  });
  
  return ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  );
};

describe('useStaffManagement', () => {
  const mockSupabase = vi.mocked(supabase);

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock successful responses by default
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      insert: vi.fn().mockReturnThis(),
      update: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({ data: mockStaffData[0], error: null }),
      then: vi.fn().mockResolvedValue({ data: mockStaffData, error: null })
    } as {
      select: jest.Mock;
      eq: jest.Mock;
      order: jest.Mock;
      insert: jest.Mock;
      update: jest.Mock;
      single: jest.Mock;
      then: jest.Mock;
    });

    mockSupabase.rpc.mockResolvedValue({ data: null, error: null });
    
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'admin-1' } },
      error: null
    } as {
      data: { user: { id: string } };
      error: null;
    });
  });

  it('fetches staff members successfully', async () => {
    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    await waitFor(() => {
      expect(result.current.staffMembers).toEqual(mockStaffData);
      expect(result.current.isLoadingStaff).toBe(false);
    });
  });

  it('fetches pending invitations successfully', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      then: vi.fn().mockResolvedValue({ data: mockInvitationsData, error: null })
    } as {
      select: jest.Mock;
      eq: jest.Mock;
      order: jest.Mock;
      then: jest.Mock;
    });

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    await waitFor(() => {
      expect(result.current.pendingInvitations).toEqual(mockInvitationsData);
      expect(result.current.isLoadingInvitations).toBe(false);
    });
  });

  it('generates secure passwords with correct format', () => {
    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    const password = result.current.generateSecurePassword();
    
    expect(password).toHaveLength(16);
    expect(password).toMatch(/[A-Z]/); // Contains uppercase
    expect(password).toMatch(/[a-z]/); // Contains lowercase
    expect(password).toMatch(/\d/); // Contains digit
    expect(password).toMatch(/[!@#$%^&*]/); // Contains special character
  });

  it('creates user invitation successfully', async () => {
    mockSupabase.rpc.mockResolvedValue({ 
      data: { invitation_id: 'inv-123' }, 
      error: null 
    });

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    const invitationData = {
      email: '<EMAIL>',
      name: 'Test User',
      role: 'field_staff' as const,
      division_id: undefined,
      phone: undefined
    };

    await waitFor(() => {
      result.current.createUserInvitation(invitationData);
    });

    expect(mockSupabase.rpc).toHaveBeenCalledWith('create_user_invitation', {
      p_email: '<EMAIL>',
      p_name: 'Test User',
      p_role: 'field_staff',
      p_division_id: null,
      p_phone: null
    });
  });

  it('handles bulk invitation creation', async () => {
    mockSupabase.rpc.mockResolvedValue({ 
      data: { 
        success_count: 2, 
        failed_count: 0, 
        errors: [] 
      }, 
      error: null 
    });

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    const users = [
      {
        email: '<EMAIL>',
        name: 'User One',
        role: 'field_staff' as const
      },
      {
        email: '<EMAIL>',
        name: 'User Two',
        role: 'program_officer' as const
      }
    ];

    await waitFor(() => {
      result.current.bulkCreateInvitations(users);
    });

    expect(mockSupabase.rpc).toHaveBeenCalledWith('bulk_create_invitations', {
      p_users: users.map(user => ({
        email: user.email,
        name: user.name,
        role: user.role,
        division_id: null,
        phone: null
      }))
    });
  });

  it('updates user role successfully', async () => {
    mockSupabase.rpc.mockResolvedValue({ data: null, error: null });

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    await waitFor(() => {
      result.current.updateUserRole('user-1', 'admin');
    });

    expect(mockSupabase.rpc).toHaveBeenCalledWith('update_user_role', {
      p_user_id: 'user-1',
      p_new_role: 'admin'
    });
  });

  it('toggles user status successfully', async () => {
    mockSupabase.rpc.mockResolvedValue({ data: null, error: null });

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    await waitFor(() => {
      result.current.toggleUserStatus('user-1', false);
    });

    expect(mockSupabase.rpc).toHaveBeenCalledWith('toggle_user_status', {
      p_user_id: 'user-1',
      p_is_active: false
    });
  });

  it('handles errors gracefully', async () => {
    const mockError = new Error('Database error');
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      order: vi.fn().mockReturnThis(),
      then: vi.fn().mockRejectedValue(mockError)
    } as {
      select: jest.Mock;
      eq: jest.Mock;
      order: jest.Mock;
      then: jest.Mock;
    });

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    await waitFor(() => {
      expect(result.current.isLoadingStaff).toBe(false);
      // Error should be handled gracefully without crashing
    });
  });

  it('creates user account with password', async () => {
    mockSupabase.auth.getUser.mockResolvedValue({
      data: { user: { id: 'admin-1' } },
      error: null
    } as {
      data: { user: { id: string } };
      error: null;
    });

    // Mock auth.admin.createUser
    const mockCreateUser = vi.fn().mockResolvedValue({
      data: { user: { id: 'new-user-id' } },
      error: null
    });

    (mockSupabase as typeof mockSupabase & {
      auth: typeof mockSupabase.auth & {
        admin: { createUser: jest.Mock };
      };
    }).auth.admin = {
      createUser: mockCreateUser
    };

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    const userData = {
      email: '<EMAIL>',
      name: 'New User',
      role: 'field_staff' as const,
      password: 'SecurePass123!'
    };

    await waitFor(() => {
      result.current.createUserAccount(userData);
    });

    expect(mockCreateUser).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'SecurePass123!',
      user_metadata: {
        name: 'New User',
        role: 'field_staff'
      }
    });
  });

  it('resends invitation successfully', async () => {
    mockSupabase.from.mockReturnValue({
      select: vi.fn().mockReturnThis(),
      eq: vi.fn().mockReturnThis(),
      single: vi.fn().mockResolvedValue({
        data: {
          id: 'inv-1',
          email: '<EMAIL>',
          name: 'Test User',
          invitation_token: 'token-123',
          role: 'field_staff',
          retry_count: 0,
          expires_at: '2024-01-08T00:00:00Z'
        },
        error: null
      }),
      update: vi.fn().mockReturnThis(),
      then: vi.fn().mockResolvedValue({ data: null, error: null })
    } as {
      select: jest.Mock;
      eq: jest.Mock;
      update: jest.Mock;
      then: jest.Mock;
    });

    const { result } = renderHook(() => useStaffManagement(), {
      wrapper: createWrapper()
    });

    await waitFor(() => {
      result.current.resendInvitation('inv-1');
    });

    // Should fetch invitation and update retry count
    expect(mockSupabase.from).toHaveBeenCalledWith('user_invitations');
  });
});
