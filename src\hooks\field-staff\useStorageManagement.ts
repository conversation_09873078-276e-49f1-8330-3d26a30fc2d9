/**
 * useStorageManagement - Focused hook for managing offline storage
 * Handles storage statistics, cleanup, and optimization
 */

import { useCallback, useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  StorageStats,
  CleanupResult
} from '@/types/offlineSync.types';
import { 
  calculateStorageStats,
  clearOfflineData as clearStorage
} from '@/utils/offlineStorage';
import { 
  performComprehensiveCleanup,
  isCleanupNeeded,
  getCleanupRecommendations
} from '@/utils/storageCleanup';

export const useStorageManagement = () => {
  const [storageStats, setStorageStats] = useState<StorageStats>({
    totalSize: 0,
    usedSize: 0,
    storageUsagePercent: 0,
    itemCount: 0,
    conflictCount: 0,
    lastCleanup: null,
    needsCleanup: false,
  });

  // Update storage statistics
  const updateStorageStats = useCallback(async () => {
    try {
      const stats = await calculateStorageStats();
      const needsCleanup = await isCleanupNeeded();
      
      setStorageStats({
        ...stats,
        needsCleanup,
      });
    } catch (error) {
      console.error('Failed to update storage stats:', error);
    }
  }, []);

  // Get storage statistics
  const getStorageStats = useCallback(async (): Promise<StorageStats> => {
    return calculateStorageStats();
  }, []);

  // Perform cleanup
  const performCleanup = useCallback(async (forceCleanup = false): Promise<CleanupResult> => {
    try {
      const result = await performComprehensiveCleanup(forceCleanup);
      await updateStorageStats();
      
      if (result.itemsRemoved > 0 || result.conflictsRemoved > 0) {
        toast.success(
          `Cleanup completed: ${result.itemsRemoved} items and ${result.conflictsRemoved} conflicts removed`
        );
      }
      
      return result;
    } catch (error) {
      console.error('Cleanup failed:', error);
      toast.error('Cleanup operation failed');
      throw error;
    }
  }, [updateStorageStats]);

  // Clear all offline data
  const clearOfflineData = useCallback(() => {
    try {
      clearStorage();
      updateStorageStats();
      toast.success('Offline data cleared');
    } catch (error) {
      console.error('Failed to clear offline data:', error);
      toast.error('Failed to clear offline data');
    }
  }, [updateStorageStats]);

  // Get cleanup recommendations
  const getCleanupRecommendations = useCallback(async () => {
    try {
      return await getCleanupRecommendations();
    } catch (error) {
      console.error('Failed to get cleanup recommendations:', error);
      return [];
    }
  }, []);

  // Check if cleanup is needed
  const checkCleanupNeeded = useCallback(async (): Promise<boolean> => {
    try {
      return await isCleanupNeeded();
    } catch (error) {
      console.error('Failed to check cleanup status:', error);
      return false;
    }
  }, []);

  // Get storage usage percentage
  const getStorageUsagePercent = useCallback((): number => {
    return storageStats.storageUsagePercent;
  }, [storageStats.storageUsagePercent]);

  // Check if storage is nearly full
  const isStorageNearlyFull = useCallback((): boolean => {
    return storageStats.storageUsagePercent > 80;
  }, [storageStats.storageUsagePercent]);

  // Initialize storage stats
  useEffect(() => {
    updateStorageStats();
  }, [updateStorageStats]);

  // Auto-cleanup check every hour
  useEffect(() => {
    const checkAndCleanup = async () => {
      try {
        const needsCleanup = await isCleanupNeeded();
        if (needsCleanup) {
          await performCleanup();
        }
      } catch (error) {
        console.error('Auto-cleanup check failed:', error);
      }
    };

    // Check for cleanup every hour
    const cleanupInterval = setInterval(checkAndCleanup, 60 * 60 * 1000);
    
    return () => clearInterval(cleanupInterval);
  }, [performCleanup]);

  return {
    storageStats,
    getStorageStats,
    performCleanup,
    clearOfflineData,
    getCleanupRecommendations,
    checkCleanupNeeded,
    getStorageUsagePercent,
    isStorageNearlyFull,
    updateStorageStats,
  };
};
