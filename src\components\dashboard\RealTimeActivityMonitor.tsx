import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  MapPin, 
  Clock, 
  AlertTriangle, 
  CheckCircle, 
  Users, 
  FileText,
  RefreshCw,
  Eye
} from 'lucide-react';
import { ActivitySummary, StaffPerformance } from '@/hooks/dashboard/useDashboardMetrics';

interface RealTimeActivityMonitorProps {
  activitySummary: ActivitySummary;
  staffPerformance: StaffPerformance[];
  isLoading?: boolean;
  onRefresh?: () => void;
}

interface ActivityCardProps {
  title: string;
  value: number;
  icon: React.ElementType;
  color: string;
  subtitle?: string;
}

const ActivityCard: React.FC<ActivityCardProps> = ({
  title,
  value,
  icon: Icon,
  color,
  subtitle
}) => (
  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
    <div className={`p-2 rounded-full ${color}`}>
      <Icon className="h-4 w-4 text-white" />
    </div>
    <div>
      <p className="text-2xl font-bold text-gray-900">{value}</p>
      <p className="text-sm text-gray-600">{title}</p>
      {subtitle && <p className="text-xs text-gray-500">{subtitle}</p>}
    </div>
  </div>
);

const AlertItem: React.FC<{
  alert: ActivitySummary['criticalAlerts'][0];
}> = ({ alert }) => {
  const alertColors = {
    error: 'bg-red-100 text-red-800 border-red-200',
    warning: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    info: 'bg-blue-100 text-blue-800 border-blue-200',
  };

  const alertIcons = {
    error: AlertTriangle,
    warning: AlertTriangle,
    info: CheckCircle,
  };

  const Icon = alertIcons[alert.type];

  return (
    <div className={`p-3 rounded-lg border ${alertColors[alert.type]}`}>
      <div className="flex items-start space-x-2">
        <Icon className="h-4 w-4 mt-0.5 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium">{alert.message}</p>
          <p className="text-xs opacity-75 mt-1">
            {new Date(alert.timestamp).toLocaleTimeString()}
          </p>
        </div>
        <Badge variant="outline" className="text-xs">
          {alert.priority}
        </Badge>
      </div>
    </div>
  );
};

const StaffStatusItem: React.FC<{
  staff: StaffPerformance;
}> = ({ staff }) => (
  <div className="flex items-center justify-between p-3 bg-white border rounded-lg">
    <div className="flex items-center space-x-3">
      <div className={`w-3 h-3 rounded-full ${staff.isCheckedIn ? 'bg-green-500' : 'bg-gray-300'}`} />
      <div>
        <p className="text-sm font-medium text-gray-900">{staff.name}</p>
        <p className="text-xs text-gray-500">
          {staff.isCheckedIn ? `At ${staff.currentSchool}` : 'Not checked in'}
        </p>
      </div>
    </div>
    <div className="text-right">
      <p className="text-sm font-medium text-gray-900">{staff.todayHours}h</p>
      <p className="text-xs text-gray-500">today</p>
    </div>
  </div>
);

export const RealTimeActivityMonitor: React.FC<RealTimeActivityMonitorProps> = ({
  activitySummary,
  staffPerformance,
  isLoading,
  onRefresh
}) => {
  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Today's Activities</span>
              <div className="h-8 w-8 bg-gray-200 rounded animate-pulse" />
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              {[...Array(4)].map((_, i) => (
                <div key={i} className="h-20 bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Live Staff Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-16 bg-gray-100 rounded-lg animate-pulse" />
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const todayActivities = [
    {
      title: 'Check-ins',
      value: activitySummary.todayActivities.checkIns,
      icon: MapPin,
      color: 'bg-green-500',
    },
    {
      title: 'Sessions',
      value: activitySummary.todayActivities.sessions,
      icon: Users,
      color: 'bg-blue-500',
    },
    {
      title: 'Reports',
      value: activitySummary.todayActivities.reports,
      icon: FileText,
      color: 'bg-purple-500',
    },
    {
      title: 'School Visits',
      value: activitySummary.todayActivities.visits,
      icon: Clock,
      color: 'bg-orange-500',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Today's Activities */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Today's Activities</span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onRefresh}
              disabled={isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            {todayActivities.map((activity, index) => (
              <ActivityCard key={index} {...activity} />
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Live Staff Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Live Staff Status</span>
            <Badge variant="outline">
              {staffPerformance.filter(s => s.isCheckedIn).length} Active
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {staffPerformance.slice(0, 5).map((staff) => (
              <StaffStatusItem key={staff.id} staff={staff} />
            ))}
            {staffPerformance.length > 5 && (
              <Button variant="ghost" className="w-full text-sm">
                <Eye className="h-4 w-4 mr-2" />
                View All {staffPerformance.length} Staff
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Critical Alerts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Critical Alerts</span>
            <Badge variant="destructive">
              {activitySummary.criticalAlerts.filter(a => a.priority === 'high').length}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {activitySummary.criticalAlerts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <p>No critical alerts</p>
                <p className="text-sm">All systems operating normally</p>
              </div>
            ) : (
              activitySummary.criticalAlerts.map((alert) => (
                <AlertItem key={alert.id} alert={alert} />
              ))
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
