import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Bar<PERSON>hart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { TrendingUp, BarChart3, <PERSON><PERSON><PERSON> as PieChartIcon, Filter } from 'lucide-react';
import { StudentLearningOutcome } from '@/types/impact';

interface LearningProgressChartProps {
  data: StudentLearningOutcome[];
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
}

const LearningProgressChart: React.FC<LearningProgressChartProps> = ({
  data,
  schoolId,
  dateRange
}) => {
  const [chartType, setChartType] = useState<'bar' | 'line' | 'pie'>('bar');
  const [selectedSubject, setSelectedSubject] = useState<string>('all');
  const [selectedSchool, setSelectedSchool] = useState<string>('all');

  // Get unique subjects and schools for filtering
  const subjects = Array.from(new Set(data.map(item => item.subject)));
  const schools = Array.from(new Set(data.map(item => ({ id: item.school_id, name: item.school_name }))));

  // Filter data based on selections
  const filteredData = data.filter(item => {
    const subjectMatch = selectedSubject === 'all' || item.subject === selectedSubject;
    const schoolMatch = selectedSchool === 'all' || item.school_id === selectedSchool;
    return subjectMatch && schoolMatch;
  });

  // Prepare data for different chart types
  const prepareBarChartData = () => {
    return filteredData.map(item => ({
      name: `${item.school_name} - ${item.subject}`,
      school: item.school_name,
      subject: item.subject,
      preScore: item.avg_pre_score,
      postScore: item.avg_post_score,
      improvement: item.avg_improvement,
      students: item.total_students,
      improvementRate: item.improvement_rate
    }));
  };

  const prepareLineChartData = () => {
    // Group by subject and show progression
    const subjectGroups = filteredData.reduce((acc, item) => {
      if (!acc[item.subject]) {
        acc[item.subject] = [];
      }
      acc[item.subject].push(item);
      return acc;
    }, {} as Record<string, StudentLearningOutcome[]>);

    return Object.entries(subjectGroups).map(([subject, items]) => ({
      subject: subject.charAt(0).toUpperCase() + subject.slice(1),
      avgPreScore: items.reduce((sum, item) => sum + item.avg_pre_score, 0) / items.length,
      avgPostScore: items.reduce((sum, item) => sum + item.avg_post_score, 0) / items.length,
      avgImprovement: items.reduce((sum, item) => sum + item.avg_improvement, 0) / items.length,
      totalStudents: items.reduce((sum, item) => sum + item.total_students, 0)
    }));
  };

  const preparePieChartData = () => {
    const improvementRanges = [
      { name: 'Excellent (>20%)', min: 20, max: 100, color: '#10B981' },
      { name: 'Good (15-20%)', min: 15, max: 20, color: '#3B82F6' },
      { name: 'Fair (10-15%)', min: 10, max: 15, color: '#F59E0B' },
      { name: 'Needs Improvement (<10%)', min: 0, max: 10, color: '#EF4444' }
    ];

    return improvementRanges.map(range => ({
      name: range.name,
      value: filteredData.filter(item => 
        item.avg_improvement >= range.min && item.avg_improvement < range.max
      ).length,
      color: range.color
    })).filter(item => item.value > 0);
  };

  const barChartData = prepareBarChartData();
  const lineChartData = prepareLineChartData();
  const pieChartData = preparePieChartData();

  const renderChart = () => {
    switch (chartType) {
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={barChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="name" 
                angle={-45}
                textAnchor="end"
                height={100}
                fontSize={12}
              />
              <YAxis />
              <Tooltip 
                formatter={(value, name) => [
                  typeof value === 'number' ? value.toFixed(1) : value,
                  name === 'preScore' ? 'Pre-Assessment' :
                  name === 'postScore' ? 'Post-Assessment' :
                  name === 'improvement' ? 'Improvement %' : name
                ]}
                labelFormatter={(label) => `Assessment: ${label}`}
              />
              <Legend />
              <Bar dataKey="preScore" fill="#94A3B8" name="Pre-Assessment Score" />
              <Bar dataKey="postScore" fill="#10B981" name="Post-Assessment Score" />
              <Bar dataKey="improvement" fill="#3B82F6" name="Improvement %" />
            </BarChart>
          </ResponsiveContainer>
        );

      case 'line':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <LineChart data={lineChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="subject" />
              <YAxis />
              <Tooltip 
                formatter={(value) => [
                  typeof value === 'number' ? value.toFixed(1) : value,
                  'Score'
                ]}
              />
              <Legend />
              <Line 
                type="monotone" 
                dataKey="avgPreScore" 
                stroke="#94A3B8" 
                strokeWidth={2}
                name="Pre-Assessment Average"
              />
              <Line 
                type="monotone" 
                dataKey="avgPostScore" 
                stroke="#10B981" 
                strokeWidth={2}
                name="Post-Assessment Average"
              />
              <Line 
                type="monotone" 
                dataKey="avgImprovement" 
                stroke="#3B82F6" 
                strokeWidth={2}
                name="Average Improvement %"
              />
            </LineChart>
          </ResponsiveContainer>
        );

      case 'pie':
        return (
          <ResponsiveContainer width="100%" height={400}>
            <PieChart>
              <Pie
                data={pieChartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, value, percent }) => `${name}: ${value} (${(percent * 100).toFixed(0)}%)`}
                outerRadius={120}
                fill="#8884d8"
                dataKey="value"
              >
                {pieChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        );

      default:
        return null;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5" />
              <span>Learning Progress Visualization</span>
            </CardTitle>
            <CardDescription>
              Visual analysis of student learning outcomes and improvement trends
            </CardDescription>
          </div>
          
          <div className="flex items-center space-x-2">
            <Select value={chartType} onValueChange={(value: 'bar' | 'line' | 'pie') => setChartType(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="bar">
                  <div className="flex items-center space-x-2">
                    <BarChart3 className="h-4 w-4" />
                    <span>Bar Chart</span>
                  </div>
                </SelectItem>
                <SelectItem value="line">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-4 w-4" />
                    <span>Line Chart</span>
                  </div>
                </SelectItem>
                <SelectItem value="pie">
                  <div className="flex items-center space-x-2">
                    <PieChartIcon className="h-4 w-4" />
                    <span>Pie Chart</span>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {/* Filters */}
        <div className="flex items-center space-x-4 mb-6">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium">Filters:</span>
          </div>
          
          <Select value={selectedSubject} onValueChange={setSelectedSubject}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="All Subjects" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Subjects</SelectItem>
              {subjects.map(subject => (
                <SelectItem key={subject} value={subject}>
                  {subject.charAt(0).toUpperCase() + subject.slice(1)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {!schoolId && (
            <Select value={selectedSchool} onValueChange={setSelectedSchool}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Schools" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Schools</SelectItem>
                {schools.map(school => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>

        {/* Chart */}
        {filteredData.length > 0 ? (
          <div>
            {renderChart()}
            
            {/* Summary Statistics */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center p-4 bg-blue-50 rounded-lg">
                <h4 className="font-semibold text-blue-800">Total Students</h4>
                <p className="text-2xl font-bold text-blue-600">
                  {filteredData.reduce((sum, item) => sum + item.total_students, 0)}
                </p>
              </div>
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <h4 className="font-semibold text-green-800">Avg Improvement</h4>
                <p className="text-2xl font-bold text-green-600">
                  {(filteredData.reduce((sum, item) => sum + item.avg_improvement, 0) / filteredData.length).toFixed(1)}%
                </p>
              </div>
              <div className="text-center p-4 bg-purple-50 rounded-lg">
                <h4 className="font-semibold text-purple-800">Success Rate</h4>
                <p className="text-2xl font-bold text-purple-600">
                  {(filteredData.reduce((sum, item) => sum + item.improvement_rate, 0) / filteredData.length).toFixed(1)}%
                </p>
              </div>
              <div className="text-center p-4 bg-orange-50 rounded-lg">
                <h4 className="font-semibold text-orange-800">Assessments</h4>
                <p className="text-2xl font-bold text-orange-600">
                  {filteredData.length}
                </p>
              </div>
            </div>
          </div>
        ) : (
          <div className="text-center py-12">
            <TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No Data Available</h3>
            <p className="text-gray-600">
              No learning outcomes data matches your current filters.
            </p>
            <Button 
              onClick={() => {
                setSelectedSubject('all');
                setSelectedSchool('all');
              }}
              variant="outline"
              className="mt-4"
            >
              Clear Filters
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default LearningProgressChart;
