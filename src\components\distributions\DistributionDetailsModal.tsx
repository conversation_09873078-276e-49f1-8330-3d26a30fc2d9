import React from 'react';
import { <PERSON>alog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  MapPin, 
  User, 
  Calendar, 
  Package, 
  FileText, 
  Camera,
  Edit,
  CheckCircle,
  Clock,
  AlertCircle,
  XCircle
} from 'lucide-react';
import { Database } from '@/integrations/supabase/types';

type BookDistribution = Database['public']['Functions']['get_book_distributions']['Returns'][0];

type DistributionPhoto = {
  photo_url: string;
  caption?: string;
};

interface DistributionDetailsModalProps {
  distribution: BookDistribution | null;
  isOpen: boolean;
  onClose: () => void;
  onEdit?: (distribution: BookDistribution) => void;
}

const DistributionDetailsModal = ({ 
  distribution, 
  isOpen, 
  onClose, 
  onEdit 
}: DistributionDetailsModalProps) => {
  // Since we removed the get_distribution_details function, we'll use the basic distribution data
  // and fetch photos separately if needed
  const detailedDistribution = distribution;
  const isLoading = false;

  if (!distribution) {
    console.log('DistributionDetailsModal: No distribution provided');
    return null;
  }

  console.log('DistributionDetailsModal: Rendering with distribution:', distribution);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'planned':
        return <Calendar className="h-4 w-4 text-purple-600" />;
      case 'cancelled':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'planned':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'cancelled':
        return 'bg-red-100 text-red-800 border-red-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // For now, we'll show no photos since we don't have a separate photos query
  // This can be enhanced later if photo functionality is needed
  const photos: DistributionPhoto[] = [];

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Distribution Details
          </DialogTitle>
          <DialogDescription>
            View complete information about this book distribution
          </DialogDescription>
        </DialogHeader>

        {isLoading ? (
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </div>
        ) : (
          <div className="space-y-6">
            {/* Status and Actions */}
            <div className="flex items-center justify-between">
              <Badge variant="outline" className={`flex items-center gap-1 ${getStatusColor(distribution.status || 'completed')}`}>
                {getStatusIcon(distribution.status || 'completed')}
                {(distribution.status || 'completed').replace('_', ' ').toUpperCase()}
              </Badge>
              {onEdit && (
                <Button variant="outline" size="sm" onClick={() => onEdit(distribution)}>
                  <Edit className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              )}
            </div>

            {/* Book Information */}
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                  <Package className="h-5 w-5 text-blue-600" />
                  Book Information
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Title</p>
                    <p className="font-medium">{distribution.book_title}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-600">Quantity</p>
                    <p className="font-medium text-lg text-blue-600">{distribution.quantity} books</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* School and Supervisor Information */}
            <Card>
              <CardContent className="p-4">
                <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-green-600" />
                  Distribution Details
                </h3>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">School</p>
                      <p className="font-medium">{distribution.school_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <User className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Supervisor</p>
                      <p className="font-medium">{distribution.supervisor_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Delivery Date</p>
                      <p className="font-medium">
                        {distribution.delivery_date ? 
                          new Date(distribution.delivery_date).toLocaleDateString('en-US', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          }) : 'Not specified'
                        }
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Notes */}
            {distribution.notes && (
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                    <FileText className="h-5 w-5 text-purple-600" />
                    Notes
                  </h3>
                  <p className="text-gray-700 whitespace-pre-wrap">{distribution.notes}</p>
                </CardContent>
              </Card>
            )}

            {/* Photos */}
            {photos.length > 0 && (
              <Card>
                <CardContent className="p-4">
                  <h3 className="font-semibold text-lg mb-3 flex items-center gap-2">
                    <Camera className="h-5 w-5 text-orange-600" />
                    Photos ({photos.length})
                  </h3>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {photos.map((photo: DistributionPhoto, index: number) => (
                      <div key={index} className="relative group">
                        <img
                          src={photo.photo_url}
                          alt={photo.caption || `Distribution photo ${index + 1}`}
                          className="w-full h-32 object-cover rounded-lg border"
                        />
                        {photo.caption && (
                          <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-75 text-white text-xs p-2 rounded-b-lg">
                            {photo.caption}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end gap-2 pt-4 border-t">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              {onEdit && (
                <Button onClick={() => onEdit(distribution)}>
                  <Edit className="h-4 w-4 mr-1" />
                  Edit Distribution
                </Button>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default DistributionDetailsModal;
