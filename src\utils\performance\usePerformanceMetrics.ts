
import { useState, useEffect } from 'react';
import { performanceMonitor } from './monitor';
import type { PerformanceMetrics, QuerySummary } from './monitor';

export const usePerformanceMetrics = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics[]>([]);
  const [summary, setSummary] = useState<QuerySummary[]>([]);
  const [slowQueries, setSlowQueries] = useState<PerformanceMetrics[]>([]);
  const [cacheHitRate, setCacheHitRate] = useState<number>(0);

  useEffect(() => {
    const updateMetrics = () => {
      setMetrics(performanceMonitor.getMetrics());
      setSummary(performanceMonitor.getSummary());
      setSlowQueries(performanceMonitor.getSlowQueries());
      setCacheHitRate(performanceMonitor.getCacheHitRate());
    };

    // Update metrics immediately
    updateMetrics();

    // Set up interval to update metrics periodically
    const interval = setInterval(updateMetrics, 1000);

    return () => clearInterval(interval);
  }, []);

  const clear = () => {
    performanceMonitor.clear();
    setMetrics([]);
    setSummary([]);
    setSlowQueries([]);
    setCacheHitRate(0);
  };

  return {
    metrics,
    summary,
    slowQueries,
    cacheHitRate,
    clear,
  };
};
