-- Migration to restructure teacher training system to student leadership training
-- This migration renames tables and updates field names to reflect iLEAD's actual mission

-- First, update the training_type enum to leadership program types
DROP TYPE IF EXISTS leadership_program_type CASCADE;
CREATE TYPE leadership_program_type AS ENUM (
    'leadership_skills',
    'communication',
    'teamwork',
    'problem_solving',
    'critical_thinking',
    'public_speaking',
    'project_management',
    'entrepreneurship',
    'civic_engagement',
    'peer_mentoring'
);

-- Rename teacher_training_programs table to student_leadership_programs
ALTER TABLE teacher_training_programs RENAME TO student_leadership_programs;

-- Update column names and types in student_leadership_programs
ALTER TABLE student_leadership_programs 
    RENAME COLUMN training_type TO program_type;

-- Update the column type to use the new enum
ALTER TABLE student_leadership_programs 
    ALTER COLUMN program_type TYPE leadership_program_type 
    USING CASE 
        WHEN program_type::text = 'leadership' THEN 'leadership_skills'::leadership_program_type
        WHEN program_type::text = 'pedagogy' THEN 'communication'::leadership_program_type
        WHEN program_type::text = 'subject_specific' THEN 'critical_thinking'::leadership_program_type
        WHEN program_type::text = 'technology' THEN 'problem_solving'::leadership_program_type
        WHEN program_type::text = 'classroom_management' THEN 'teamwork'::leadership_program_type
        WHEN program_type::text = 'assessment' THEN 'project_management'::leadership_program_type
        WHEN program_type::text = 'inclusive_education' THEN 'civic_engagement'::leadership_program_type
        ELSE 'leadership_skills'::leadership_program_type
    END;

-- Rename teacher_training_participation table to student_leadership_participation
ALTER TABLE teacher_training_participation RENAME TO student_leadership_participation;

-- Update column names in student_leadership_participation
ALTER TABLE student_leadership_participation 
    RENAME COLUMN training_program_id TO leadership_program_id;

ALTER TABLE student_leadership_participation 
    RENAME COLUMN teacher_name TO student_name;

ALTER TABLE student_leadership_participation 
    RENAME COLUMN teacher_id TO student_id;

ALTER TABLE student_leadership_participation 
    RENAME COLUMN pre_training_score TO pre_program_score;

ALTER TABLE student_leadership_participation 
    RENAME COLUMN post_training_score TO post_program_score;

-- Update foreign key constraint
ALTER TABLE student_leadership_participation 
    DROP CONSTRAINT teacher_training_participation_training_program_id_fkey;

ALTER TABLE student_leadership_participation 
    ADD CONSTRAINT student_leadership_participation_leadership_program_id_fkey 
    FOREIGN KEY (leadership_program_id) REFERENCES student_leadership_programs(id);

-- Update indexes
DROP INDEX IF EXISTS idx_teacher_training_programs_type;
CREATE INDEX idx_student_leadership_programs_type ON student_leadership_programs(program_type);

DROP INDEX IF EXISTS idx_teacher_training_programs_dates;
CREATE INDEX idx_student_leadership_programs_dates ON student_leadership_programs(start_date, end_date);

DROP INDEX IF EXISTS idx_teacher_training_participation_program;
CREATE INDEX idx_student_leadership_participation_program ON student_leadership_participation(leadership_program_id);

DROP INDEX IF EXISTS idx_teacher_training_participation_school;
CREATE INDEX idx_student_leadership_participation_school ON student_leadership_participation(school_id);

-- Update trigger names
DROP TRIGGER IF EXISTS update_training_programs_updated_at ON student_leadership_programs;
CREATE TRIGGER update_leadership_programs_updated_at
    BEFORE UPDATE ON student_leadership_programs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_training_participation_updated_at ON student_leadership_participation;
CREATE TRIGGER update_leadership_participation_updated_at
    BEFORE UPDATE ON student_leadership_participation
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Update RLS policies
DROP POLICY IF EXISTS "Users can view training programs" ON student_leadership_programs;
CREATE POLICY "Users can view leadership programs" ON student_leadership_programs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

DROP POLICY IF EXISTS "Program officers and admins can manage training programs" ON student_leadership_programs;
CREATE POLICY "Program officers and admins can manage leadership programs" ON student_leadership_programs
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

DROP POLICY IF EXISTS "Users can view training participation" ON student_leadership_participation;
CREATE POLICY "Users can view leadership participation" ON student_leadership_participation
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer', 'field_staff')
        )
    );

DROP POLICY IF EXISTS "Program officers and admins can manage training participation" ON student_leadership_participation;
CREATE POLICY "Program officers and admins can manage leadership participation" ON student_leadership_participation
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM profiles
            WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
        )
    );

-- Enable RLS on renamed tables
ALTER TABLE student_leadership_programs ENABLE ROW LEVEL SECURITY;
ALTER TABLE student_leadership_participation ENABLE ROW LEVEL SECURITY;

-- Drop the old training_type enum (this will be done after updating functions)
-- DROP TYPE IF EXISTS training_type CASCADE;
