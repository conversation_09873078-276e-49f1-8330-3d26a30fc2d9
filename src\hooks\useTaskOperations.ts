
import { useState } from 'react';
import { Database } from '@/integrations/supabase/types';

interface UseTaskOperationsProps {
  onViewDetails?: (taskId: string) => void;
  onUpdateStatus?: (taskId: string, status: Database['public']['Enums']['task_status']) => void;
  onEdit?: (taskId: string) => void;
}

export const useTaskOperations = ({
  onViewDetails,
  onUpdateStatus,
  onEdit
}: UseTaskOperationsProps = {}) => {
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);

  const handleViewDetails = (taskId: string) => {
    setSelectedTaskId(taskId);
    if (onViewDetails) {
      onViewDetails(taskId);
    }
  };

  const handleUpdateStatus = (taskId: string, status: Database['public']['Enums']['task_status']) => {
    if (onUpdateStatus) {
      onUpdateStatus(taskId, status);
    }
  };

  const handleEdit = (taskId: string) => {
    if (onEdit) {
      onEdit(taskId);
    }
  };

  const clearSelection = () => {
    setSelectedTaskId(null);
  };

  return {
    selectedTaskId,
    handleViewDetails,
    handleUpdateStatus,
    handleEdit,
    clearSelection
  };
};
