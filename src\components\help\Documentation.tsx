import React, { useState } from 'react';
import { Search, FileText, Book, Users, Settings, HelpCircle, ChevronRight, ChevronDown, Shield } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';
import { useAuth } from '@/hooks/useAuth';
import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface DocumentationSection {
  id: string;
  title: string;
  description: string;
  icon: React.ComponentType<{ className?: string }>;
  content: DocumentationItem[];
  adminOnly?: boolean;
}

interface DocumentationItem {
  id: string;
  title: string;
  content: string;
  tags: string[];
  lastUpdated: string;
}

const Documentation: React.FC = () => {
  const { profile } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [openSections, setOpenSections] = useState<string[]>(['getting-started']);

  // Check if user is admin
  const isAdmin = profile?.role === 'admin';

  // If not admin, show access denied
  if (!isAdmin) {
    return (
      <PageLayout>
        <PageHeader
          title="Documentation"
          description="System documentation and guides"
          icon={FileText}
        />
        <ContentCard>
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Access to documentation is restricted to administrators only. Please contact your system administrator if you need access to this section.
            </AlertDescription>
          </Alert>
        </ContentCard>
      </PageLayout>
    );
  }

  const documentationSections: DocumentationSection[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      description: 'Essential guides for new users and system setup',
      icon: Book,
      content: [
        {
          id: 'system-overview',
          title: 'System Overview',
          content: 'iLEAD Field Tracker is a comprehensive platform for managing educational programs, tracking student progress, and coordinating field activities across multiple schools.',
          tags: ['overview', 'introduction'],
          lastUpdated: '2024-01-15'
        },
        {
          id: 'user-roles',
          title: 'User Roles and Permissions',
          content: 'The system supports three main user roles: Admin (full system access), Program Officer (program management and oversight), and Field Staff (field operations and data collection).',
          tags: ['roles', 'permissions', 'access'],
          lastUpdated: '2024-01-15'
        }
      ]
    },
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Managing users, roles, and permissions',
      icon: Users,
      content: [
        {
          id: 'creating-users',
          title: 'Creating New Users',
          content: 'To create new users, navigate to Settings > User Management. Fill in the required information including name, email, role, and assigned division.',
          tags: ['users', 'creation', 'setup'],
          lastUpdated: '2024-01-15'
        },
        {
          id: 'role-assignment',
          title: 'Role Assignment and Changes',
          content: 'User roles can be modified by administrators. Changes take effect immediately and will update the user\'s access permissions across the system.',
          tags: ['roles', 'permissions', 'management'],
          lastUpdated: '2024-01-15'
        }
      ]
    },
    {
      id: 'task-management',
      title: 'Task Management',
      description: 'Creating, assigning, and tracking tasks',
      icon: FileText,
      content: [
        {
          id: 'creating-tasks',
          title: 'Creating and Assigning Tasks',
          content: 'Tasks can be created by administrators and program officers. Include clear descriptions, due dates, priority levels, and assign to appropriate field staff.',
          tags: ['tasks', 'creation', 'assignment'],
          lastUpdated: '2024-01-15'
        },
        {
          id: 'task-tracking',
          title: 'Task Progress Tracking',
          content: 'Monitor task progress through the task dashboard. View completion rates, overdue tasks, and generate reports for program oversight.',
          tags: ['tasks', 'tracking', 'monitoring'],
          lastUpdated: '2024-01-15'
        }
      ]
    },
    {
      id: 'system-administration',
      title: 'System Administration',
      description: 'Advanced system configuration and maintenance',
      icon: Settings,
      adminOnly: true,
      content: [
        {
          id: 'database-management',
          title: 'Database Management',
          content: 'Regular database maintenance includes backup procedures, performance monitoring, and data integrity checks. Contact technical support for advanced operations.',
          tags: ['database', 'maintenance', 'backup'],
          lastUpdated: '2024-01-15'
        },
        {
          id: 'system-configuration',
          title: 'System Configuration',
          content: 'System-wide settings can be configured through the admin panel. This includes notification settings, data retention policies, and integration configurations.',
          tags: ['configuration', 'settings', 'admin'],
          lastUpdated: '2024-01-15'
        }
      ]
    }
  ];

  const toggleSection = (sectionId: string) => {
    setOpenSections(prev => 
      prev.includes(sectionId) 
        ? prev.filter(id => id !== sectionId)
        : [...prev, sectionId]
    );
  };

  const filteredSections = documentationSections.filter(section => {
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return section.title.toLowerCase().includes(query) ||
             section.description.toLowerCase().includes(query) ||
             section.content.some(item => 
               item.title.toLowerCase().includes(query) ||
               item.content.toLowerCase().includes(query) ||
               item.tags.some(tag => tag.toLowerCase().includes(query))
             );
    }
    return true;
  });

  return (
    <PageLayout>
      <PageHeader
        title="Documentation"
        description="Comprehensive guides and documentation for the iLEAD Field Tracker system"
        icon={FileText}
      />

      {/* Search */}
      <ContentCard>
        <div className="relative mb-6">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search documentation..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>

        {/* Documentation Sections */}
        <div className="space-y-4">
          {filteredSections.map((section) => {
            const Icon = section.icon;
            const isOpen = openSections.includes(section.id);

            return (
              <Card key={section.id}>
                <Collapsible open={isOpen} onOpenChange={() => toggleSection(section.id)}>
                  <CollapsibleTrigger asChild>
                    <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Icon className="h-5 w-5 text-ilead-green" />
                          <div>
                            <CardTitle className="text-lg">{section.title}</CardTitle>
                            <CardDescription>{section.description}</CardDescription>
                          </div>
                          {section.adminOnly && (
                            <Badge variant="secondary" className="ml-2">
                              Admin Only
                            </Badge>
                          )}
                        </div>
                        {isOpen ? (
                          <ChevronDown className="h-4 w-4" />
                        ) : (
                          <ChevronRight className="h-4 w-4" />
                        )}
                      </div>
                    </CardHeader>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <CardContent className="pt-0">
                      <div className="space-y-4">
                        {section.content.map((item) => (
                          <div key={item.id} className="border-l-2 border-ilead-green pl-4">
                            <h4 className="font-semibold text-gray-900 mb-2">{item.title}</h4>
                            <p className="text-gray-600 mb-3">{item.content}</p>
                            <div className="flex items-center justify-between">
                              <div className="flex flex-wrap gap-1">
                                {item.tags.map((tag) => (
                                  <Badge key={tag} variant="outline" className="text-xs">
                                    {tag}
                                  </Badge>
                                ))}
                              </div>
                              <span className="text-xs text-gray-400">
                                Updated: {item.lastUpdated}
                              </span>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Collapsible>
              </Card>
            );
          })}
        </div>

        {filteredSections.length === 0 && (
          <Card>
            <CardContent className="text-center py-8">
              <HelpCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-gray-900 mb-2">No documentation found</h3>
              <p className="text-gray-600">
                Try adjusting your search terms or browse all available sections.
              </p>
            </CardContent>
          </Card>
        )}
      </ContentCard>
    </PageLayout>
  );
};

export default Documentation;
