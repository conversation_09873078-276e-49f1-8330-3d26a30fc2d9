import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface InvitationEmailData {
  to: string;
  name: string;
  inviterName: string;
  invitationToken: string;
  role: string;
  organizationName?: string;
}

interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Parse request body
    const { invitationData }: { invitationData: InvitationEmailData } = await req.json()

    if (!invitationData) {
      return new Response(
        JSON.stringify({ error: 'Missing invitation data' }),
        { 
          status: 400, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    // Generate email template
    const template = generateInvitationTemplate(invitationData)

    // Send email using Resend (you'll need to set up Resend API key)
    const resendApiKey = Deno.env.get('RESEND_API_KEY')
    
    if (!resendApiKey) {
      console.error('RESEND_API_KEY not configured')
      return new Response(
        JSON.stringify({ error: 'Email service not configured' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const emailResponse = await fetch('https://api.resend.com/emails', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${resendApiKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        from: '<EMAIL>', // Configure your domain
        to: [invitationData.to],
        subject: template.subject,
        html: template.html,
        text: template.text,
      }),
    })

    if (!emailResponse.ok) {
      const errorData = await emailResponse.text()
      console.error('Failed to send email:', errorData)
      
      // Update invitation status to 'failed'
      await supabaseClient
        .from('user_invitations')
        .update({ 
          status: 'failed',
          failed_reason: `Email service error: ${errorData}`
        })
        .eq('invitation_token', invitationData.invitationToken)

      return new Response(
        JSON.stringify({ error: 'Failed to send email' }),
        { 
          status: 500, 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
        }
      )
    }

    const emailResult = await emailResponse.json()
    console.log('Email sent successfully:', emailResult)

    // Update invitation status to 'sent'
    const { error: updateError } = await supabaseClient
      .from('user_invitations')
      .update({ 
        status: 'sent', 
        sent_at: new Date().toISOString() 
      })
      .eq('invitation_token', invitationData.invitationToken)

    if (updateError) {
      console.error('Error updating invitation status:', updateError)
      // Don't fail the request since email was sent successfully
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        emailId: emailResult.id,
        message: 'Invitation email sent successfully' 
      }),
      { 
        status: 200, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )

  } catch (error) {
    console.error('Error in send-invitation-email function:', error)
    
    return new Response(
      JSON.stringify({ 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      }),
      { 
        status: 500, 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' } 
      }
    )
  }
})

function generateInvitationTemplate(data: InvitationEmailData): EmailTemplate {
  const baseUrl = Deno.env.get('SITE_URL') || 'http://localhost:5173'
  const organizationName = data.organizationName || 'iLead Field Track'
  const acceptUrl = `${baseUrl}/accept-invitation?token=${data.invitationToken}`
  const roleDisplay = data.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())

  const subject = `Invitation to join ${organizationName}`

  const html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Invitation to ${organizationName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background-color: #2563eb; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background-color: #f9fafb; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { display: inline-block; background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; margin: 20px 0; }
        .footer { margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; font-size: 14px; color: #6b7280; }
        .warning { background-color: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>${organizationName}</h1>
          <p>You've been invited to join our team</p>
        </div>
        
        <div class="content">
          <h2>Hello ${data.name},</h2>
          
          <p>You have been invited by <strong>${data.inviterName}</strong> to join ${organizationName} as a <strong>${roleDisplay}</strong>.</p>
          
          <p>To accept this invitation and set up your account, please click the button below:</p>
          
          <div style="text-align: center;">
            <a href="${acceptUrl}" class="button">Accept Invitation</a>
          </div>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${acceptUrl}
          </p>
          
          <div class="warning">
            <strong>Important:</strong> This invitation will expire in 7 days. If you don't accept it by then, you'll need to request a new invitation.
          </div>
          
          <h3>What happens next?</h3>
          <ol>
            <li>Click the invitation link above</li>
            <li>Create a secure password for your account</li>
            <li>Complete your profile setup</li>
            <li>Start using ${organizationName}</li>
          </ol>
          
          <p>If you have any questions or need assistance, please contact your administrator.</p>
          
          <p>Welcome to the team!</p>
        </div>
        
        <div class="footer">
          <p>This invitation was sent to ${data.to}. If you weren't expecting this invitation, you can safely ignore this email.</p>
          <p>&copy; ${new Date().getFullYear()} ${organizationName}. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
    ${organizationName} - Invitation to Join

    Hello ${data.name},

    You have been invited by ${data.inviterName} to join ${organizationName} as a ${roleDisplay}.

    To accept this invitation and set up your account, please visit:
    ${acceptUrl}

    This invitation will expire in 7 days.

    What happens next:
    1. Click the invitation link above
    2. Create a secure password for your account
    3. Complete your profile setup
    4. Start using ${organizationName}

    If you have any questions or need assistance, please contact your administrator.

    Welcome to the team!

    ---
    This invitation was sent to ${data.to}. If you weren't expecting this invitation, you can safely ignore this email.
    © ${new Date().getFullYear()} ${organizationName}. All rights reserved.
  `

  return { subject, html, text }
}
