
import { useState } from 'react';
import { useTasks } from '@/hooks/tasks';
import { useTaskOperations } from '@/hooks/useTaskOperations';
import { Database } from '@/integrations/supabase/types';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

interface UseTaskListProps {
  propTasks?: Task[];
  propLoading?: boolean;
  onViewDetails?: (taskId: string) => void;
  onUpdateStatus?: (taskId: string, status: Database['public']['Enums']['task_status']) => void;
}

export const useTaskList = ({
  propTasks,
  propLoading = false,
  onViewDetails,
  onUpdateStatus
}: UseTaskListProps) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [taskDetailsOpen, setTaskDetailsOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'cards' | 'list'>('list');
  const [currentPage, setCurrentPage] = useState(1);

  // If no tasks are provided as props, fetch them
  const { data: fetchedTasks = [], isLoading: fetchLoading } = useTasks({
    includeComments: true
  });

  // Use prop tasks if provided, otherwise use fetched tasks
  const tasks = propTasks || fetchedTasks;
  const loading = propLoading || fetchLoading;

  const {
    selectedTaskId,
    handleViewDetails,
    handleUpdateStatus,
    clearSelection
  } = useTaskOperations({
    onViewDetails,
    onUpdateStatus
  });

  const handleTaskViewDetails = (taskId: string) => {
    handleViewDetails(taskId);
    setTaskDetailsOpen(true);
  };

  const handleTaskUpdateStatus = (taskId: string, status: Database['public']['Enums']['task_status']) => {
    handleUpdateStatus(taskId, status);
  };

  const getStatusStats = () => {
    return {
      pending: tasks.filter(t => t.status === 'pending').length,
      in_progress: tasks.filter(t => t.status === 'in_progress').length,
      completed: tasks.filter(t => t.status === 'completed').length,
      overdue: tasks.filter(t => 
        t.due_date && 
        new Date(t.due_date) < new Date() && 
        t.status !== 'completed'
      ).length,
    };
  };

  // Reset to first page when filters change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setCurrentPage(1);
  };

  const handlePriorityFilterChange = (value: string) => {
    setPriorityFilter(value);
    setCurrentPage(1);
  };

  const handleViewModeChange = (mode: 'cards' | 'list') => {
    setViewMode(mode);
    setCurrentPage(1);
  };

  return {
    tasks,
    loading,
    searchTerm,
    statusFilter,
    priorityFilter,
    taskDetailsOpen,
    viewMode,
    currentPage,
    selectedTaskId,
    handleTaskViewDetails,
    handleTaskUpdateStatus,
    getStatusStats,
    handleSearchChange,
    handleStatusFilterChange,
    handlePriorityFilterChange,
    handleViewModeChange,
    setCurrentPage,
    setTaskDetailsOpen,
    clearSelection
  };
};
