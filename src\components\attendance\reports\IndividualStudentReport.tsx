import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  User, 
  Calendar, 
  TrendingUp, 
  TrendingDown, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Download,
  Printer
} from 'lucide-react';
import { useIndividualStudentReport } from '@/hooks/attendance/useAttendanceReports';

interface IndividualStudentReportProps {
  studentId: string;
  startDate: Date;
  endDate: Date;
  schoolId?: string;
}

const IndividualStudentReport: React.FC<IndividualStudentReportProps> = ({
  studentId,
  startDate,
  endDate,
  schoolId,
}) => {
  const { data: report, isLoading, error } = useIndividualStudentReport({
    studentId,
    startDate,
    endDate,
    schoolId,
    reportType: 'individual',
  });

  const handlePrint = () => {
    window.print();
  };

  const handleExport = () => {
    // TODO: Implement PDF export
    console.log('Exporting report...');
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </CardContent>
      </Card>
    );
  }

  if (error || !report) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error Loading Report</h3>
          <p className="text-gray-600">
            {error?.message || 'Failed to load student report. Please try again.'}
          </p>
        </CardContent>
      </Card>
    );
  }

  const getRiskBadgeColor = (level: string) => {
    switch (level) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const getAttendanceColor = (rate: number) => {
    if (rate >= 90) return 'text-green-600';
    if (rate >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6 print:space-y-4">
      {/* Header */}
      <Card className="print:shadow-none">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl flex items-center gap-2">
                <User className="h-6 w-6" />
                Individual Student Attendance Report
              </CardTitle>
              <CardDescription>
                Comprehensive attendance analysis for {report.student.first_name} {report.student.last_name}
              </CardDescription>
            </div>
            <div className="flex gap-2 print:hidden">
              <Button variant="outline" size="sm" onClick={handlePrint}>
                <Printer className="h-4 w-4 mr-2" />
                Print
              </Button>
              <Button variant="outline" size="sm" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                Export PDF
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div>
              <div className="text-sm font-medium text-gray-600">Student</div>
              <div className="text-lg font-semibold">
                {report.student.first_name} {report.student.last_name}
              </div>
              <div className="text-sm text-gray-600">
                {report.student.student_number} • Grade {report.student.grade_level}
              </div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600">School</div>
              <div className="text-lg font-semibold">{report.school.name}</div>
              <div className="text-sm text-gray-600">{report.school.location}</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600">Report Period</div>
              <div className="text-lg font-semibold">
                {new Date(report.period.start_date).toLocaleDateString()} - {new Date(report.period.end_date).toLocaleDateString()}
              </div>
              <div className="text-sm text-gray-600">{report.period.total_days} days</div>
            </div>
            <div>
              <div className="text-sm font-medium text-gray-600">Risk Level</div>
              <Badge className={getRiskBadgeColor(report.risk_assessment.level)}>
                {report.risk_assessment.level.toUpperCase()} RISK
              </Badge>
              {report.risk_assessment.intervention_needed && (
                <div className="text-sm text-red-600 mt-1">Intervention Required</div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Attendance Summary */}
      <Card className="print:shadow-none">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Attendance Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">
                {report.attendance_summary.total_sessions}
              </div>
              <div className="text-sm text-gray-600">Total Sessions</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {report.attendance_summary.sessions_attended}
              </div>
              <div className="text-sm text-gray-600">Attended</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-red-600">
                {report.attendance_summary.sessions_absent}
              </div>
              <div className="text-sm text-gray-600">Absent</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-yellow-600">
                {report.attendance_summary.sessions_late}
              </div>
              <div className="text-sm text-gray-600">Late</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {report.attendance_summary.sessions_excused}
              </div>
              <div className="text-sm text-gray-600">Excused</div>
            </div>
            <div className="text-center">
              <div className={`text-3xl font-bold ${getAttendanceColor(report.attendance_summary.attendance_rate)}`}>
                {report.attendance_summary.attendance_rate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Attendance Rate</div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6 pt-6 border-t">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {report.attendance_summary.punctuality_rate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-600">Punctuality Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">
                {report.attendance_summary.consecutive_absences}
              </div>
              <div className="text-sm text-gray-600">Current Consecutive Absences</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {report.attendance_summary.longest_absence_streak}
              </div>
              <div className="text-sm text-gray-600">Longest Absence Streak</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Session Type Breakdown */}
      <Card className="print:shadow-none">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Session Type Breakdown
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(report.session_breakdown).map(([sessionType, data]) => (
              <div key={sessionType} className="border rounded-lg p-4">
                <div className="font-medium capitalize mb-2">
                  {sessionType.replace('_', ' ')}
                </div>
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span>Total Sessions:</span>
                    <span className="font-medium">{data.total}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Attended:</span>
                    <span className="font-medium">{data.attended}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span>Attendance Rate:</span>
                    <span className={`font-medium ${getAttendanceColor(data.rate)}`}>
                      {data.rate.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Participation Analysis */}
      {report.participation_scores.sessions_with_scores > 0 && (
        <Card className="print:shadow-none">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Participation Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">
                  {report.participation_scores.average_score.toFixed(1)}
                </div>
                <div className="text-sm text-gray-600">Average Participation Score</div>
                <div className="text-xs text-gray-500 mt-1">
                  Based on {report.participation_scores.sessions_with_scores} sessions
                </div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-700">
                  {report.participation_scores.sessions_with_scores}
                </div>
                <div className="text-sm text-gray-600">Sessions with Scores</div>
              </div>
              <div className="text-center">
                <Badge variant="outline" className="text-lg px-3 py-1">
                  {report.participation_scores.score_trend}
                </Badge>
                <div className="text-sm text-gray-600 mt-1">Trend</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Risk Assessment & Recommendations */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="print:shadow-none">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5" />
              Risk Assessment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Badge className={getRiskBadgeColor(report.risk_assessment.level)}>
                    {report.risk_assessment.level.toUpperCase()} RISK
                  </Badge>
                  {report.risk_assessment.intervention_needed && (
                    <Badge variant="destructive">
                      Intervention Required
                    </Badge>
                  )}
                </div>
              </div>
              
              {report.risk_assessment.factors.length > 0 && (
                <div>
                  <div className="font-medium mb-2">Risk Factors:</div>
                  <ul className="space-y-1">
                    {report.risk_assessment.factors.map((factor, index) => (
                      <li key={index} className="text-sm text-gray-600 flex items-start gap-2">
                        <span className="text-red-500 mt-1">•</span>
                        {factor}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="print:shadow-none">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5" />
              Recommendations
            </CardTitle>
          </CardHeader>
          <CardContent>
            {report.recommendations.length > 0 ? (
              <ul className="space-y-2">
                {report.recommendations.map((recommendation, index) => (
                  <li key={index} className="text-sm flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    {recommendation}
                  </li>
                ))}
              </ul>
            ) : (
              <div className="text-center py-4 text-gray-500">
                <CheckCircle className="h-8 w-8 text-green-500 mx-auto mb-2" />
                No specific recommendations at this time. Student is maintaining good attendance.
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Guardian Information */}
      {(report.student.guardian_name || report.student.guardian_contact) && (
        <Card className="print:shadow-none">
          <CardHeader>
            <CardTitle>Guardian Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {report.student.guardian_name && (
                <div>
                  <div className="text-sm font-medium text-gray-600">Guardian Name</div>
                  <div className="text-lg">{report.student.guardian_name}</div>
                </div>
              )}
              {report.student.guardian_contact && (
                <div>
                  <div className="text-sm font-medium text-gray-600">Contact Information</div>
                  <div className="text-lg">{report.student.guardian_contact}</div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Report Footer */}
      <Card className="print:shadow-none">
        <CardContent className="text-center py-4">
          <div className="text-sm text-gray-500">
            Report generated on {new Date().toLocaleDateString()} at {new Date().toLocaleTimeString()}
          </div>
          <div className="text-xs text-gray-400 mt-1">
            iLEAD Field Tracker - Attendance Management System
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default IndividualStudentReport;
