
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { performanceMonitor } from '@/utils/performance';
import { queryKeys, mutationKeys } from '@/lib/queryClient';
import { ActivityFormData } from './types';

// Mutation for logging activities manually
export const useLogActivity = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationKey: mutationKeys.activities.log,
    mutationFn: async (activityData: ActivityFormData) => {
      const endTimer = performanceMonitor.startTimer('log_activity_mutation');
      
      try {
        const { data, error } = await supabase.rpc('log_activity', {
          p_activity_type: activityData.activity_type,
          p_user_id: activityData.user_id,
          p_entity_type: activityData.entity_type,
          p_entity_id: activityData.entity_id,
          p_description: activityData.description,
          p_metadata: activityData.metadata || {},
        });
        
        if (error) {
          endTimer(false, error.message);
          throw error;
        }

        endTimer(true);
        return data;
      } catch (error) {
        endTimer(false, (error as Error).message);
        throw error;
      }
    },
    onSuccess: () => {
      // Invalidate all activity queries to refresh the data
      queryClient.invalidateQueries({ queryKey: queryKeys.activities.all });
    },
  });
};
