-- Sample Book Inventory Data
-- This migration adds sample book inventory data for testing and demonstration
-- Priority: MEDIUM - Helpful for testing but not critical for functionality

-- Insert sample book inventory data
INSERT INTO book_inventory (
    book_title, 
    quantity_available, 
    language, 
    grade_level, 
    subject, 
    isbn, 
    publisher, 
    publication_year, 
    cost_per_unit,
    description
) VALUES
-- Primary Mathematics Books
('Mathematics Grade 1 - Numbers and Counting', 150, 'English', 'Grade 1', 'Mathematics', '978-9970-123-45-6', 'Uganda Education Publishers', 2023, 8500.00, 'Introduction to numbers, counting, and basic arithmetic for Grade 1 students'),
('Mathematics Grade 2 - Addition and Subtraction', 120, 'English', 'Grade 2', 'Mathematics', '978-9970-123-46-3', 'Uganda Education Publishers', 2023, 9000.00, 'Building on Grade 1 concepts with addition and subtraction'),
('Mathematics Grade 3 - Multiplication and Division', 100, 'English', 'Grade 3', 'Mathematics', '978-9970-123-47-0', 'Uganda Education Publishers', 2023, 9500.00, 'Introduction to multiplication and division concepts'),
('Mathematics Grade 4 - Fractions and Decimals', 85, 'English', 'Grade 4', 'Mathematics', '978-9970-123-48-7', 'Uganda Education Publishers', 2023, 10000.00, 'Understanding fractions and decimal numbers'),

-- English Language Books
('English Reader Grade 1 - My First Words', 180, 'English', 'Grade 1', 'English', '978-9970-234-56-7', 'East African Educational Publishers', 2023, 7500.00, 'Basic vocabulary and reading skills for beginners'),
('English Reader Grade 2 - Stories and Poems', 140, 'English', 'Grade 2', 'English', '978-9970-234-57-4', 'East African Educational Publishers', 2023, 8000.00, 'Simple stories and poems to develop reading comprehension'),
('English Grammar Grade 3', 110, 'English', 'Grade 3', 'English', '978-9970-234-58-1', 'East African Educational Publishers', 2023, 8500.00, 'Basic grammar rules and sentence construction'),
('English Composition Grade 4', 95, 'English', 'Grade 4', 'English', '978-9970-234-59-8', 'East African Educational Publishers', 2023, 9000.00, 'Writing skills and composition techniques'),

-- Science Books
('Science Grade 3 - Our Environment', 90, 'English', 'Grade 3', 'Science', '978-9970-345-67-8', 'African Science Publishers', 2023, 11000.00, 'Introduction to environmental science and basic scientific concepts'),
('Science Grade 4 - Living and Non-Living Things', 75, 'English', 'Grade 4', 'Science', '978-9970-345-68-5', 'African Science Publishers', 2023, 11500.00, 'Understanding the difference between living and non-living things'),
('Science Grade 5 - Plants and Animals', 60, 'English', 'Grade 5', 'Science', '978-9970-345-69-2', 'African Science Publishers', 2023, 12000.00, 'Study of plant and animal life cycles'),

-- Social Studies Books
('Social Studies Grade 4 - Our Community', 80, 'English', 'Grade 4', 'Social Studies', '978-9970-456-78-9', 'Uganda Social Publishers', 2023, 9500.00, 'Understanding community roles and responsibilities'),
('Social Studies Grade 5 - Our Country Uganda', 70, 'English', 'Grade 5', 'Social Studies', '978-9970-456-79-6', 'Uganda Social Publishers', 2023, 10000.00, 'Geography and history of Uganda'),
('Social Studies Grade 6 - East Africa', 55, 'English', 'Grade 6', 'Social Studies', '978-9970-456-80-2', 'Uganda Social Publishers', 2023, 10500.00, 'Regional studies of East African countries'),

-- Local Language Books (Luganda)
('Luganda Reader P1 - Ebigambo Byaffe', 100, 'Luganda', 'Primary 1', 'Local Language', '978-9970-567-89-0', 'Luganda Publishers Ltd', 2023, 6500.00, 'Basic Luganda vocabulary and reading for Primary 1'),
('Luganda Reader P2 - Emboozi Ennungi', 85, 'Luganda', 'Primary 2', 'Local Language', '978-9970-567-90-6', 'Luganda Publishers Ltd', 2023, 7000.00, 'Simple stories in Luganda for Primary 2 students'),
('Luganda Grammar P3', 70, 'Luganda', 'Primary 3', 'Local Language', '978-9970-567-91-3', 'Luganda Publishers Ltd', 2023, 7500.00, 'Basic Luganda grammar and sentence structure'),

-- Religious Education
('Christian Religious Education Grade 4', 65, 'English', 'Grade 4', 'Religious Education', '978-9970-678-90-1', 'Faith Publishers Uganda', 2023, 8000.00, 'Introduction to Christian values and Bible stories'),
('Islamic Religious Education Grade 4', 45, 'English', 'Grade 4', 'Religious Education', '978-9970-678-91-8', 'Islamic Education Publishers', 2023, 8000.00, 'Basic Islamic teachings and values'),

-- Physical Education and Health
('Physical Education Grade 5', 40, 'English', 'Grade 5', 'Physical Education', '978-9970-789-01-2', 'Sports Education Publishers', 2023, 9000.00, 'Physical fitness, sports, and health education'),

-- Art and Craft
('Art and Craft Grade 3', 50, 'English', 'Grade 3', 'Art', '978-9970-890-12-3', 'Creative Arts Publishers', 2023, 8500.00, 'Introduction to drawing, painting, and craft making'),

-- Music and Dance
('Music and Dance Grade 4', 35, 'English', 'Grade 4', 'Music', '978-9970-901-23-4', 'Cultural Arts Publishers', 2023, 8000.00, 'Traditional and modern music and dance'),

-- Special Needs Education
('Special Needs Mathematics Grade 2', 25, 'English', 'Grade 2', 'Mathematics', '978-9970-012-34-5', 'Inclusive Education Publishers', 2023, 12000.00, 'Mathematics adapted for students with special learning needs'),

-- Teacher Guides
('Mathematics Teacher Guide Grade 1-3', 30, 'English', 'Teacher Resource', 'Mathematics', '978-9970-111-22-3', 'Teacher Resource Publishers', 2023, 15000.00, 'Comprehensive teaching guide for primary mathematics'),
('English Teacher Guide Grade 1-3', 25, 'English', 'Teacher Resource', 'English', '978-9970-111-23-0', 'Teacher Resource Publishers', 2023, 15000.00, 'Teaching methodology and resources for English language'),

-- Supplementary Reading
('African Folk Tales Collection', 60, 'English', 'All Grades', 'Literature', '978-9970-222-33-4', 'African Literature Publishers', 2023, 6000.00, 'Collection of traditional African stories for all primary grades'),
('Uganda History for Children', 45, 'English', 'Grade 4-7', 'History', '978-9970-333-44-5', 'Historical Publishers Uganda', 2023, 11000.00, 'Child-friendly introduction to Ugandan history'),

-- Workbooks and Exercise Books
('Mathematics Workbook Grade 1', 200, 'English', 'Grade 1', 'Mathematics', '978-9970-444-55-6', 'Workbook Publishers', 2023, 3500.00, 'Practice exercises and activities for Grade 1 mathematics'),
('English Workbook Grade 2', 180, 'English', 'Grade 2', 'English', '978-9970-444-56-3', 'Workbook Publishers', 2023, 3500.00, 'Writing and reading practice for Grade 2 students'),

-- Bilingual Books
('Mathematics Grade 2 (English-Luganda)', 40, 'Bilingual', 'Grade 2', 'Mathematics', '978-9970-555-66-7', 'Bilingual Education Publishers', 2023, 10000.00, 'Mathematics concepts taught in both English and Luganda'),
('Science Grade 3 (English-Runyankole)', 30, 'Bilingual', 'Grade 3', 'Science', '978-9970-555-67-4', 'Bilingual Education Publishers', 2023, 11000.00, 'Science concepts in English and Runyankole');

-- Add some sample distribution records for demonstration (optional)
-- Note: These will only work if there are existing schools and profiles in the database
-- Uncomment the following lines if you want sample distribution data

/*
-- Sample distributions (only if sample schools and profiles exist)
INSERT INTO book_distributions (
    school_id, 
    inventory_id, 
    quantity, 
    supervisor_id, 
    notes, 
    delivery_date,
    status
) 
SELECT 
    s.id as school_id,
    bi.id as inventory_id,
    CASE 
        WHEN bi.book_title LIKE '%Grade 1%' THEN 25
        WHEN bi.book_title LIKE '%Grade 2%' THEN 20
        ELSE 15
    END as quantity,
    p.id as supervisor_id,
    'Sample distribution for testing purposes' as notes,
    NOW() - INTERVAL '7 days' as delivery_date,
    'completed' as status
FROM schools s
CROSS JOIN book_inventory bi
CROSS JOIN profiles p
WHERE s.name LIKE '%Primary%'
  AND bi.book_title LIKE '%Grade 1%'
  AND p.role IN ('admin', 'program_officer')
LIMIT 5;
*/
