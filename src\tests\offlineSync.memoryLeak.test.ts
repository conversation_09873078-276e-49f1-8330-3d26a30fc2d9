import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import { useOfflineSync } from '@/hooks/field-staff/useOfflineSync';

// Mock dependencies
vi.mock('@/integrations/supabase/client', () => ({
  supabase: {
    rpc: vi.fn(),
    from: vi.fn(),
  },
}));

vi.mock('sonner', () => ({
  toast: {
    info: vi.fn(),
    warning: vi.fn(),
    error: vi.fn(),
    success: vi.fn(),
  },
}));

vi.mock('@/utils/photoCacheManager', () => ({
  photoCacheManager: {
    getCacheStats: vi.fn().mockResolvedValue({
      totalItems: 0,
      totalSize: 0,
    }),
    performCleanup: vi.fn().mockResolvedValue(undefined),
  },
}));

// Mock localStorage
const localStorageMock = (() => {
  let store: Record<string, string> = {};

  return {
    getItem: (key: string) => store[key] || null,
    setItem: (key: string, value: string) => {
      store[key] = value;
    },
    removeItem: (key: string) => {
      delete store[key];
    },
    clear: () => {
      store = {};
    },
    get length() {
      return Object.keys(store).length;
    },
    key: (index: number) => Object.keys(store)[index] || null,
  };
})();

describe('Offline Sync Memory Leak Prevention', () => {
  beforeEach(() => {
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock,
      writable: true,
    });

    Object.defineProperty(navigator, 'onLine', {
      value: true,
      writable: true,
    });

    localStorageMock.clear();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllTimers();
  });

  describe('Storage Limits Enforcement', () => {
    it('should enforce maximum offline items limit', async () => {
      const { result } = renderHook(() => useOfflineSync());

      // Add items beyond the limit (500)
      await act(async () => {
        for (let i = 0; i < 600; i++) {
          result.current.addToOfflineQueue(
            'check_in',
            { test: `data_${i}` },
            'MEDIUM'
          );
        }
      });

      const offlineData = result.current.loadOfflineData();
      expect(offlineData.length).toBeLessThanOrEqual(500);
    });

    it('should prioritize items by priority when enforcing limits', async () => {
      const { result } = renderHook(() => useOfflineSync());

      await act(async () => {
        // Add low priority items
        for (let i = 0; i < 300; i++) {
          result.current.addToOfflineQueue(
            'check_in',
            { test: `low_${i}` },
            'LOW'
          );
        }

        // Add high priority items
        for (let i = 0; i < 300; i++) {
          result.current.addToOfflineQueue(
            'check_in',
            { test: `high_${i}` },
            'HIGH'
          );
        }
      });

      const offlineData = result.current.loadOfflineData();
      const highPriorityItems = offlineData.filter(item => item.priority === 'HIGH');
      const lowPriorityItems = offlineData.filter(item => item.priority === 'LOW');

      // Should keep more high priority items
      expect(highPriorityItems.length).toBeGreaterThan(lowPriorityItems.length);
    });

    it('should handle storage quota exceeded errors', async () => {
      const { result } = renderHook(() => useOfflineSync());

      // Mock localStorage to throw quota exceeded error
      const originalSetItem = localStorageMock.setItem;
      localStorageMock.setItem = vi.fn().mockImplementation(() => {
        const error = new Error('Storage quota exceeded');
        error.name = 'QuotaExceededError';
        throw error;
      });

      await act(async () => {
        result.current.addToOfflineQueue(
          'check_in',
          { test: 'data' },
          'MEDIUM'
        );
      });

      // Should handle the error gracefully
      expect(vi.mocked(localStorageMock.setItem)).toHaveBeenCalled();

      // Restore original method
      localStorageMock.setItem = originalSetItem;
    });
  });

  describe('Automatic Cleanup', () => {
    it('should remove old successful items automatically', async () => {
      const { result } = renderHook(() => useOfflineSync());

      const now = Date.now();
      const oldTimestamp = now - (25 * 60 * 60 * 1000); // 25 hours ago

      // Add old successful items (retryCount = 0 means successful)
      const oldItems = [
        {
          id: 'old_1',
          type: 'check_in' as const,
          data: { test: 'old_data_1' },
          timestamp: oldTimestamp,
          retryCount: 0,
          maxRetries: 5,
          priority: 'MEDIUM' as const,
        },
        {
          id: 'old_2',
          type: 'check_in' as const,
          data: { test: 'old_data_2' },
          timestamp: oldTimestamp,
          retryCount: 0,
          maxRetries: 5,
          priority: 'MEDIUM' as const,
        },
      ];

      localStorageMock.setItem('field_staff_offline_data', JSON.stringify(oldItems));

      await act(async () => {
        const cleanupResult = await result.current.performComprehensiveCleanup(true);
        expect(cleanupResult.itemsRemoved).toBeGreaterThan(0);
      });

      const remainingData = result.current.loadOfflineData();
      expect(remainingData.length).toBeLessThan(oldItems.length);
    });

    it('should keep failed items longer than successful items', async () => {
      const { result } = renderHook(() => useOfflineSync());

      const now = Date.now();
      const oldTimestamp = now - (25 * 60 * 60 * 1000); // 25 hours ago

      const mixedItems = [
        {
          id: 'old_successful',
          type: 'check_in' as const,
          data: { test: 'successful' },
          timestamp: oldTimestamp,
          retryCount: 0, // Successful
          maxRetries: 5,
          priority: 'MEDIUM' as const,
        },
        {
          id: 'old_failed',
          type: 'check_in' as const,
          data: { test: 'failed' },
          timestamp: oldTimestamp,
          retryCount: 5, // Failed (reached max retries)
          maxRetries: 5,
          priority: 'MEDIUM' as const,
        },
      ];

      localStorageMock.setItem('field_staff_offline_data', JSON.stringify(mixedItems));

      await act(async () => {
        await result.current.performComprehensiveCleanup(true);
      });

      const remainingData = result.current.loadOfflineData();
      const hasSuccessful = remainingData.some(item => item.id === 'old_successful');
      const hasFailed = remainingData.some(item => item.id === 'old_failed');

      // Failed items should be kept longer
      expect(hasSuccessful).toBe(false);
      expect(hasFailed).toBe(true);
    });

    it('should clean up old conflicts', async () => {
      const { result } = renderHook(() => useOfflineSync());

      const now = Date.now();
      const oldTimestamp = now - (31 * 24 * 60 * 60 * 1000); // 31 days ago

      const conflicts = [
        {
          id: 'old_conflict',
          localData: { test: 'local' },
          serverData: { test: 'server' },
          conflictFields: ['test'],
          timestamp: oldTimestamp,
        },
        {
          id: 'recent_conflict',
          localData: { test: 'local' },
          serverData: { test: 'server' },
          conflictFields: ['test'],
          timestamp: now - (1 * 60 * 60 * 1000), // 1 hour ago
        },
      ];

      localStorageMock.setItem('field_staff_sync_conflicts', JSON.stringify(conflicts));

      await act(async () => {
        const cleanupResult = await result.current.performComprehensiveCleanup(true);
        expect(cleanupResult.conflictsRemoved).toBeGreaterThan(0);
      });

      const conflictsData = localStorageMock.getItem('field_staff_sync_conflicts');
      const remainingConflicts = conflictsData ? JSON.parse(conflictsData) : [];

      expect(remainingConflicts.length).toBeLessThan(conflicts.length);
      expect(remainingConflicts.some((c: { id: string }) => c.id === 'recent_conflict')).toBe(true);
      expect(remainingConflicts.some((c: { id: string }) => c.id === 'old_conflict')).toBe(false);
    });
  });

  describe('Storage Statistics', () => {
    it('should calculate storage statistics correctly', async () => {
      const { result } = renderHook(() => useOfflineSync());

      // Add test data
      await act(async () => {
        result.current.addToOfflineQueue('check_in', { test: 'data1' }, 'HIGH');
        result.current.addToOfflineQueue('check_in', { test: 'data2' }, 'MEDIUM');
        result.current.addToOfflineQueue('check_in', { test: 'data3' }, 'LOW');
      });

      const stats = await act(async () => {
        return await result.current.getStorageStats();
      });

      expect(stats.totalItems).toBe(3);
      expect(stats.totalSize).toBeGreaterThan(0);
      expect(stats.storageUsagePercent).toBeGreaterThanOrEqual(0);
      expect(stats.oldestItem).toBeGreaterThan(0);
      expect(stats.newestItem).toBeGreaterThan(0);
    });

    it('should track storage usage percentage', async () => {
      const { result } = renderHook(() => useOfflineSync());

      // Add significant amount of data
      await act(async () => {
        for (let i = 0; i < 100; i++) {
          result.current.addToOfflineQueue(
            'check_in',
            { test: `large_data_${i}`.repeat(100) }, // Make data larger
            'MEDIUM'
          );
        }
      });

      const stats = await act(async () => {
        return await result.current.getStorageStats();
      });

      expect(stats.storageUsagePercent).toBeGreaterThan(0);
      expect(stats.totalSize).toBeGreaterThan(1000); // Should be substantial
    });
  });

  describe('Periodic Cleanup', () => {
    it('should trigger periodic cleanup based on storage usage', async () => {
      vi.useFakeTimers();

      const { result } = renderHook(() => useOfflineSync());

      // Add data to trigger cleanup threshold
      await act(async () => {
        for (let i = 0; i < 200; i++) {
          result.current.addToOfflineQueue(
            'check_in',
            { test: `data_${i}`.repeat(50) },
            'MEDIUM'
          );
        }
      });

      const initialStats = await act(async () => {
        return await result.current.getStorageStats();
      });

      // Fast-forward time to trigger periodic cleanup
      await act(async () => {
        vi.advanceTimersByTime(30 * 60 * 1000); // 30 minutes
      });

      const finalStats = await act(async () => {
        return await result.current.getStorageStats();
      });

      // Should have cleaned up some data
      expect(finalStats.totalItems).toBeLessThanOrEqual(initialStats.totalItems);

      vi.useRealTimers();
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should not accumulate unlimited data over time', async () => {
      const { result } = renderHook(() => useOfflineSync());

      // Simulate continuous operation over time
      const iterations = 10;
      const itemsPerIteration = 100;

      for (let iteration = 0; iteration < iterations; iteration++) {
        await act(async () => {
          // Add items
          for (let i = 0; i < itemsPerIteration; i++) {
            result.current.addToOfflineQueue(
              'check_in',
              { test: `iteration_${iteration}_item_${i}` },
              'MEDIUM'
            );
          }

          // Simulate some items being processed (successful)
          const currentData = result.current.loadOfflineData();
          const processedData = currentData.map((item, index) => {
            if (index % 3 === 0) {
              // Mark every 3rd item as processed (successful)
              return { ...item, retryCount: 0, timestamp: item.timestamp - (25 * 60 * 60 * 1000) };
            }
            return item;
          });

          localStorageMock.setItem('field_staff_offline_data', JSON.stringify(processedData));

          // Trigger cleanup
          await result.current.performComprehensiveCleanup();
        });
      }

      const finalData = result.current.loadOfflineData();

      // Should not have accumulated all items (should be cleaned up)
      expect(finalData.length).toBeLessThan(iterations * itemsPerIteration);
      expect(finalData.length).toBeLessThanOrEqual(500); // Should respect max limit
    });

    it('should handle rapid successive additions without memory explosion', async () => {
      const { result } = renderHook(() => useOfflineSync());

      // Rapidly add many items
      await act(async () => {
        const promises = [];
        for (let i = 0; i < 1000; i++) {
          promises.push(
            result.current.addToOfflineQueue(
              'check_in',
              { test: `rapid_${i}` },
              'MEDIUM'
            )
          );
        }
        await Promise.all(promises);
      });

      const data = result.current.loadOfflineData();

      // Should enforce limits even with rapid additions
      expect(data.length).toBeLessThanOrEqual(500);
    });
  });
});
