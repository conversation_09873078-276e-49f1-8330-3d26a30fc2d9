
import React, { useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Clock, Database, Zap, BarChart3, Refresh<PERSON>w, Trash2 } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useRecentTasks, useTasks } from '@/hooks/tasks';
import { usePerformanceMetrics, getPerformanceRating, getPerformanceColor, getPerformanceIcon } from '@/utils/performance';

interface PerformanceMetric {
  name: string;
  duration: number;
  status: 'success' | 'error' | 'pending';
  error?: string;
}

const PerformanceTest = () => {
  const { profile } = useAuth();
  const [metrics, setMetrics] = useState<PerformanceMetric[]>([]);
  const [isRunning, setIsRunning] = useState(false);
  const { metrics: performanceMetrics, summary, slowQueries, cacheHitRate, clear } = usePerformanceMetrics();

  // Test the old get_tasks RPC function
  const { refetch: refetchOldTasks } = useQuery({
    queryKey: ['performance-test-old'],
    queryFn: async () => {
      const start = performance.now();
      const { data, error } = await supabase.rpc('get_tasks', {
        p_user_id: null,
        p_status_filter: null,
        p_assigned_filter: null
      });
      const end = performance.now();
      
      const metric: PerformanceMetric = {
        name: 'Old get_tasks RPC',
        duration: end - start,
        status: error ? 'error' : 'success',
        error: error?.message
      };
      
      setMetrics(prev => [...prev, metric]);
      
      if (error) throw error;
      return data;
    },
    enabled: false,
  });

  // Test the optimized hooks
  const { refetch: refetchOptimizedTasks } = useRecentTasks(10);
  const { refetch: refetchAllTasks } = useTasks();

  const runPerformanceTest = async () => {
    setIsRunning(true);
    setMetrics([]);
    
    try {
      // Test 1: Old RPC function
      console.time('Old get_tasks RPC');
      await refetchOldTasks();
      console.timeEnd('Old get_tasks RPC');
      
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Test 2: Optimized recent tasks
      console.time('Optimized recent tasks');
      const start2 = performance.now();
      await refetchOptimizedTasks();
      const end2 = performance.now();
      console.timeEnd('Optimized recent tasks');
      
      setMetrics(prev => [...prev, {
        name: 'Optimized recent tasks',
        duration: end2 - start2,
        status: 'success'
      }]);
      
      // Wait a bit between tests
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Test 3: Optimized all tasks
      console.time('Optimized all tasks');
      const start3 = performance.now();
      await refetchAllTasks();
      const end3 = performance.now();
      console.timeEnd('Optimized all tasks');
      
      setMetrics(prev => [...prev, {
        name: 'Optimized all tasks',
        duration: end3 - start3,
        status: 'success'
      }]);
      
    } catch (error) {
      console.error('Performance test error:', error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600';
      case 'error': return 'text-red-600';
      default: return 'text-yellow-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success': return '✅';
      case 'error': return '❌';
      default: return '⏳';
    }
  };

  if (!profile) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="p-6 text-center">
            <p>Please log in to run performance tests</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5 text-yellow-500" />
            Task Query Performance Test
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <p className="text-gray-600">
            This test compares the performance of different task query approaches.
          </p>
          
          <Button 
            onClick={runPerformanceTest}
            disabled={isRunning}
            className="bg-ilead-green hover:bg-ilead-dark-green"
          >
            {isRunning ? (
              <>
                <Clock className="h-4 w-4 mr-2 animate-spin" />
                Running Tests...
              </>
            ) : (
              <>
                <Database className="h-4 w-4 mr-2" />
                Run Performance Test
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Real-time Performance Metrics */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-500" />
              Real-time Performance Metrics
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                Refresh
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={clear}
              >
                <Trash2 className="h-4 w-4 mr-1" />
                Clear
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-3 bg-blue-50 rounded-lg">
              <p className="text-sm text-blue-600 font-medium">Total Queries</p>
              <p className="text-2xl font-bold text-blue-800">{performanceMetrics.length}</p>
            </div>
            <div className="p-3 bg-green-50 rounded-lg">
              <p className="text-sm text-green-600 font-medium">Cache Hit Rate</p>
              <p className="text-2xl font-bold text-green-800">{cacheHitRate.toFixed(1)}%</p>
            </div>
            <div className="p-3 bg-orange-50 rounded-lg">
              <p className="text-sm text-orange-600 font-medium">Slow Queries</p>
              <p className="text-2xl font-bold text-orange-800">{slowQueries.length}</p>
            </div>
          </div>

          {/* Query Summary */}
          {summary.length > 0 && (
            <div>
              <h4 className="font-medium mb-3">Query Performance Summary</h4>
              <div className="space-y-2">
                {summary.map((item, index) => {
                  const rating = getPerformanceRating(item.averageTime);
                  return (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center gap-3">
                        <span className="text-lg">{getPerformanceIcon(rating)}</span>
                        <div>
                          <p className="font-medium">{item.queryName}</p>
                          <p className="text-sm text-gray-600">
                            {item.totalCalls} calls • {item.errorRate.toFixed(1)}% errors
                          </p>
                        </div>
                      </div>
                      <div className={`text-right ${getPerformanceColor(rating)}`}>
                        <p className="font-mono text-lg">
                          {item.averageTime.toFixed(2)}ms
                        </p>
                        <p className="text-xs text-gray-500">
                          {item.fastestQuery.toFixed(0)}-{item.slowestQuery.toFixed(0)}ms range
                        </p>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {/* Recent Queries */}
          {performanceMetrics.length > 0 && (
            <div>
              <h4 className="font-medium mb-3">Recent Queries (Last 10)</h4>
              <div className="space-y-2">
                {performanceMetrics.slice(-10).reverse().map((metric, index) => {
                  const rating = getPerformanceRating(metric.duration);
                  return (
                    <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center gap-2">
                        <span>{getPerformanceIcon(rating)}</span>
                        <span className="text-sm font-medium">{metric.queryName}</span>
                        {metric.resultCount !== undefined && (
                          <span className="text-xs text-gray-500">({metric.resultCount} items)</span>
                        )}
                      </div>
                      <div className={`text-sm ${getPerformanceColor(rating)}`}>
                        {metric.duration.toFixed(2)}ms
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Manual Test Results */}
      {metrics.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Manual Test Results</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {metrics.map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{getStatusIcon(metric.status)}</span>
                    <div>
                      <p className="font-medium">{metric.name}</p>
                      {metric.error && (
                        <p className="text-sm text-red-600">{metric.error}</p>
                      )}
                    </div>
                  </div>
                  <div className={`text-right ${getStatusColor(metric.status)}`}>
                    <p className="font-mono text-lg">
                      {metric.duration.toFixed(2)}ms
                    </p>
                    <p className="text-xs text-gray-500">
                      {metric.duration < 100 ? 'Fast' :
                       metric.duration < 500 ? 'Moderate' : 'Slow'}
                    </p>
                  </div>
                </div>
              ))}
            </div>

            {metrics.length >= 2 && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <p className="text-sm text-blue-800">
                  <strong>Performance Improvement:</strong> {' '}
                  {metrics.length >= 3 && (
                    <>
                      Recent tasks are {((metrics[0]?.duration || 0) / (metrics[1]?.duration || 1)).toFixed(1)}x faster,
                      All tasks are {((metrics[0]?.duration || 0) / (metrics[2]?.duration || 1)).toFixed(1)}x faster
                    </>
                  )}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default PerformanceTest;
