-- Optimize task queries for better performance
-- This migration adds optimized RPC functions for different use cases

-- Optimized function for dashboard - gets recent tasks without comment counts
CREATE OR REPLACE FUNCTION get_recent_tasks(
    p_limit INTEGER DEFAULT 10
)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    description TEXT,
    priority task_priority,
    status task_status,
    due_date TIMESTAMP WITH TIME ZONE,
    assigned_to U<PERSON><PERSON>,
    assigned_to_name <PERSON><PERSON><PERSON><PERSON>,
    created_by U<PERSON><PERSON>,
    created_by_name VARCHAR,
    school_id UUID,
    school_name VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.title,
        t.description,
        t.priority,
        t.status,
        t.due_date,
        t.assigned_to,
        p_assigned.name as assigned_to_name,
        t.created_by,
        p_created.name as created_by_name,
        t.school_id,
        s.name as school_name,
        t.created_at,
        t.updated_at
    FROM tasks t
    LEFT JOIN profiles p_assigned ON t.assigned_to = p_assigned.id
    LEFT JOIN profiles p_created ON t.created_by = p_created.id
    LEFT JOIN schools s ON t.school_id = s.id
    WHERE 
        -- Apply RLS: user can see tasks they're assigned to, created, or if they're admin/program_officer
        (t.assigned_to = auth.uid() OR 
         t.created_by = auth.uid() OR
         EXISTS (
             SELECT 1 FROM profiles 
             WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
         ))
    ORDER BY 
        CASE t.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        t.due_date ASC NULLS LAST,
        t.created_at DESC
    LIMIT p_limit;
END;
$$;

-- Optimized function for task lists with optional comment counts
CREATE OR REPLACE FUNCTION get_tasks_optimized(
    p_user_id UUID DEFAULT NULL,
    p_status_filter task_status DEFAULT NULL,
    p_assigned_filter UUID DEFAULT NULL,
    p_include_comments BOOLEAN DEFAULT FALSE,
    p_limit INTEGER DEFAULT NULL
)
RETURNS TABLE (
    id UUID,
    title VARCHAR,
    description TEXT,
    priority task_priority,
    status task_status,
    due_date TIMESTAMP WITH TIME ZONE,
    assigned_to UUID,
    assigned_to_name VARCHAR,
    created_by UUID,
    created_by_name VARCHAR,
    school_id UUID,
    school_name VARCHAR,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE,
    comment_count BIGINT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.id,
        t.title,
        t.description,
        t.priority,
        t.status,
        t.due_date,
        t.assigned_to,
        p_assigned.name as assigned_to_name,
        t.created_by,
        p_created.name as created_by_name,
        t.school_id,
        s.name as school_name,
        t.created_at,
        t.updated_at,
        CASE 
            WHEN p_include_comments THEN COALESCE(comment_counts.count, 0)
            ELSE 0
        END as comment_count
    FROM tasks t
    LEFT JOIN profiles p_assigned ON t.assigned_to = p_assigned.id
    LEFT JOIN profiles p_created ON t.created_by = p_created.id
    LEFT JOIN schools s ON t.school_id = s.id
    LEFT JOIN LATERAL (
        SELECT COUNT(*) as count
        FROM task_comments tc
        WHERE tc.task_id = t.id AND p_include_comments = TRUE
    ) comment_counts ON p_include_comments = TRUE
    WHERE 
        -- Apply RLS: user can see tasks they're assigned to, created, or if they're admin/program_officer
        (t.assigned_to = auth.uid() OR 
         t.created_by = auth.uid() OR
         EXISTS (
             SELECT 1 FROM profiles 
             WHERE id = auth.uid() AND role IN ('admin', 'program_officer')
         ))
        -- Apply filters
        AND (p_user_id IS NULL OR t.assigned_to = p_user_id OR t.created_by = p_user_id)
        AND (p_status_filter IS NULL OR t.status = p_status_filter)
        AND (p_assigned_filter IS NULL OR t.assigned_to = p_assigned_filter)
    ORDER BY 
        CASE t.priority
            WHEN 'urgent' THEN 1
            WHEN 'high' THEN 2
            WHEN 'medium' THEN 3
            WHEN 'low' THEN 4
        END,
        t.due_date ASC NULLS LAST,
        t.created_at DESC
    LIMIT p_limit;
END;
$$;

-- Add composite indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_tasks_composite_assigned_status ON tasks(assigned_to, status) WHERE assigned_to IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_tasks_composite_created_status ON tasks(created_by, status);
CREATE INDEX IF NOT EXISTS idx_tasks_priority_due_date ON tasks(priority, due_date);
CREATE INDEX IF NOT EXISTS idx_tasks_created_at_desc ON tasks(created_at DESC);

-- Add index for profiles role lookup (used in RLS)
CREATE INDEX IF NOT EXISTS idx_profiles_role ON profiles(role) WHERE role IN ('admin', 'program_officer');

-- Add partial index for active profiles
CREATE INDEX IF NOT EXISTS idx_profiles_active ON profiles(id) WHERE is_active = true;
