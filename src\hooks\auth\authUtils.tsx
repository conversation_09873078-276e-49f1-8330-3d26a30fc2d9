
import { supabase } from '@/integrations/supabase/client';

// Clean up auth state function to prevent limbo issues
export const cleanupAuthState = () => {
  try {
    // Remove standard auth tokens
    localStorage.removeItem('supabase.auth.token');
    // Remove all Supabase auth keys from localStorage
    Object.keys(localStorage).forEach((key) => {
      if (key.startsWith('supabase.auth.') || key.includes('sb-')) {
        localStorage.removeItem(key);
      }
    });
    // Remove from sessionStorage if in use
    Object.keys(sessionStorage || {}).forEach((key) => {
      if (key.startsWith('supabase.auth.') || key.includes('sb-')) {
        sessionStorage.removeItem(key);
      }
    });
  } catch (error) {
    console.error('Error cleaning up auth state:', error);
  }
};

export const signIn = async (email: string, password: string) => {
  try {
    console.log('Starting sign in process...');
    
    // Clean up existing state first
    cleanupAuthState();
    
    // Attempt global sign out to ensure clean state
    try {
      await supabase.auth.signOut({ scope: 'global' });
    } catch (err) {
      console.log('Global signout failed, continuing...', err);
      // Continue even if this fails
    }

    // Small delay to ensure cleanup is complete
    await new Promise(resolve => setTimeout(resolve, 100));
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    
    if (error) {
      console.error('Sign in error:', error);
      return { error };
    }

    if (data.user) {
      console.log('Sign in successful, user:', data.user.id);
      // Force page reload for clean state
      setTimeout(() => {
        window.location.href = '/';
      }, 100);
    }
    
    return { error: null };
  } catch (error: unknown) {
    console.error('Unexpected sign in error:', error);
    return { error };
  }
};

export const signOut = async () => {
  try {
    console.log('Starting sign out process...');

    // Clean up auth state first
    cleanupAuthState();

    // Attempt global sign out
    const { error } = await supabase.auth.signOut({ scope: 'global' });

    if (error) {
      console.error('Sign out error:', error);
    }

    // Force page reload for a clean state regardless of errors
    setTimeout(() => {
      window.location.href = '/';
    }, 100);

    return { error };
  } catch (error: unknown) {
    console.error('Unexpected sign out error:', error);
    // Still force reload even on error
    setTimeout(() => {
      window.location.href = '/';
    }, 100);
    return { error };
  }
};
