
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { BarChart3 } from 'lucide-react';

const Analytics = () => {
  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <BarChart3 className="h-6 w-6 mr-2 text-ilead-green" />
        <h1 className="text-2xl font-bold text-gray-900">Analytics</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Data Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">Analytics dashboard and charts will be displayed here.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default Analytics;
