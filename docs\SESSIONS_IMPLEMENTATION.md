# Sessions Implementation Guide

## Overview

The Sessions system is a comprehensive attendance and session management solution for field staff in educational settings. It provides tools for creating, managing, and tracking attendance sessions across multiple schools.

## Architecture

### Core Components

1. **Data Layer**
   - `useAttendanceSessions.ts` - Main hook for session CRUD operations
   - `useStudentAttendance.ts` - Student attendance tracking
   - `useSchools.ts` - School data management

2. **UI Components**
   - `SessionsOverview.tsx` - Main dashboard for all sessions
   - `SessionDetails.tsx` - Detailed view of individual sessions
   - `CreateSessionDialog.tsx` - Session creation interface
   - `AttendanceTracker.tsx` - Real-time attendance tracking

3. **Database Schema**
   - `attendance_sessions` - Core session data
   - `student_attendance` - Individual attendance records
   - `schools` - School information
   - `students` - Student data

## Key Features

### 1. Session Management

#### Session Types
- **Class** - Regular classroom sessions
- **Leadership Program** - Leadership training sessions
- **Training** - Staff training sessions
- **Assessment** - Student assessments
- **Meeting** - Administrative meetings
- **Other** - Custom session types

#### Session States
- **Upcoming** - Scheduled for future
- **Ready** - Can be started (today, past start time)
- **Active** - Currently in progress
- **Completed** - Finished with end time recorded
- **Missed** - Past due without being started

### 2. Attendance Tracking

#### Attendance Status Options
- **Present** - Student attended
- **Absent** - Student did not attend
- **Late** - Student arrived late
- **Excused** - Excused absence

#### Features
- Real-time attendance marking
- Bulk attendance operations
- Round table seating arrangements
- Attendance statistics and reporting

### 3. Offline Capabilities

#### Data Synchronization
- Offline session creation
- Offline attendance tracking
- Automatic sync when connection restored
- Conflict resolution for concurrent edits

## Implementation Details

### Session Creation Flow

```typescript
// 1. Create session data
const sessionData: CreateSessionData = {
  session_name: "Math Class - Grade 5",
  session_type: "class",
  school_id: "school-uuid",
  grade_level: "Grade 5",
  subject: "Mathematics",
  teacher_name: "John Doe",
  session_date: "2024-01-15",
  start_time: "09:00",
  end_time: "10:30",
  planned_duration_minutes: 90,
  location: "Classroom A",
  max_capacity: 30,
  round_tables_count: 4,
  session_description: "Introduction to fractions",
  learning_objectives: [
    "Understand basic fraction concepts",
    "Identify numerator and denominator"
  ],
  materials_needed: [
    "Fraction worksheets",
    "Visual aids"
  ]
};

// 2. Create session using hook
const createSession = useCreateAttendanceSession();
await createSession.mutateAsync(sessionData);
```

### Attendance Tracking Flow

```typescript
// 1. Get session attendance
const { data: attendance } = useSessionAttendance(sessionId);

// 2. Mark student attendance
const markAttendance = useMarkStudentAttendance();
await markAttendance.mutateAsync({
  sessionId,
  studentId,
  status: 'present',
  checkInTime: new Date().toISOString(),
  notes: 'Arrived on time'
});

// 3. Bulk attendance operations
const bulkMarkAttendance = useBulkMarkAttendance();
await bulkMarkAttendance.mutateAsync({
  sessionId,
  attendanceRecords: [
    { studentId: 'student1', status: 'present' },
    { studentId: 'student2', status: 'absent' },
    { studentId: 'student3', status: 'late' }
  ]
});
```

### Session State Management

```typescript
// Session lifecycle management
const sessionControls = useSessionControls(sessionId);

// Start session
await sessionControls.startSession({
  actualStartTime: new Date().toISOString(),
  location: "Updated location if needed"
});

// End session
await sessionControls.endSession({
  actualEndTime: new Date().toISOString(),
  sessionNotes: "Session completed successfully"
});

// Update session
await sessionControls.updateSession({
  sessionNotes: "Additional notes",
  actualDurationMinutes: 85
});
```

## Error Handling

### Robust Error Recovery

```typescript
// Schools loading with fallback
export const useSchools = () => {
  return useQuery({
    queryKey: ['schools'],
    queryFn: async () => {
      // Try RPC function first
      try {
        const { data: rpcData, error: rpcError } = await supabase
          .rpc('get_schools_with_divisions');
        if (!rpcError && rpcData) return rpcData;
      } catch (rpcError) {
        console.warn('RPC function failed, falling back to direct query');
      }

      // Fallback to direct table query
      const { data, error } = await supabase
        .from('schools')
        .select('*')
        .eq('registration_status', 'active');
      
      if (error) throw error;
      return data;
    },
    retry: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  });
};
```

### Session Creation with Fallback

```typescript
// Create session with RPC fallback to direct insert
const createSession = async (sessionData) => {
  try {
    // Try RPC function first
    const { data, error } = await supabase.rpc('create_attendance_session', params);
    if (!error && data) return data;
  } catch (rpcError) {
    console.warn('RPC function failed, using direct insert');
  }

  // Fallback to direct insert
  const { data, error } = await supabase
    .from('attendance_sessions')
    .insert(sessionData)
    .select()
    .single();
  
  if (error) throw error;
  return data.id;
};
```

## Performance Optimizations

### Query Optimization

```typescript
// Efficient session queries with proper relationships
const sessionsQuery = supabase
  .from('attendance_sessions')
  .select(`
    *,
    school:schools(name, location_coordinates),
    facilitator:profiles!attendance_sessions_facilitator_id_fkey(name),
    student_attendance(
      id,
      student_id,
      attendance_status,
      student:students(first_name, last_name)
    )
  `)
  .order('session_date', { ascending: false });
```

### Caching Strategy

```typescript
// Smart caching with appropriate stale times
export const useAttendanceSessions = (schoolId, dateRange) => {
  return useQuery({
    queryKey: ['attendance-sessions', schoolId, dateRange],
    queryFn: fetchSessions,
    staleTime: 30 * 1000, // 30 seconds for active data
    cacheTime: 5 * 60 * 1000, // 5 minutes cache retention
  });
};
```

## Security Considerations

### Row Level Security (RLS)

```sql
-- Sessions can only be viewed by facilitators and admins
CREATE POLICY "Users can view their sessions" ON attendance_sessions
  FOR SELECT USING (
    facilitator_id = auth.uid() OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role IN ('admin', 'supervisor')
    )
  );

-- Attendance can only be marked by session facilitators
CREATE POLICY "Facilitators can mark attendance" ON student_attendance
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM attendance_sessions 
      WHERE id = student_attendance.session_id 
      AND facilitator_id = auth.uid()
    )
  );
```

### Data Validation

```typescript
// Client-side validation
const sessionSchema = z.object({
  session_name: z.string().min(1, "Session name is required"),
  session_type: z.enum(['class', 'leadership_program', 'training', 'assessment', 'meeting', 'other']),
  school_id: z.string().uuid("Valid school ID required"),
  session_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Valid date required"),
  start_time: z.string().regex(/^\d{2}:\d{2}$/, "Valid time required"),
  max_capacity: z.number().min(1).max(200).optional(),
});
```

## Testing Strategy

### Unit Tests

```typescript
// Test session creation
describe('useCreateAttendanceSession', () => {
  it('should create session successfully', async () => {
    const { result } = renderHook(() => useCreateAttendanceSession());
    
    await act(async () => {
      await result.current.mutateAsync(mockSessionData);
    });
    
    expect(result.current.isSuccess).toBe(true);
  });

  it('should handle RPC failure gracefully', async () => {
    // Mock RPC failure
    mockSupabase.rpc.mockRejectedValueOnce(new Error('RPC failed'));
    
    const { result } = renderHook(() => useCreateAttendanceSession());
    
    await act(async () => {
      await result.current.mutateAsync(mockSessionData);
    });
    
    // Should still succeed via fallback
    expect(result.current.isSuccess).toBe(true);
  });
});
```

### Integration Tests

```typescript
// Test complete session workflow
describe('Session Workflow', () => {
  it('should complete full session lifecycle', async () => {
    // 1. Create session
    const session = await createSession(mockData);
    expect(session.id).toBeDefined();
    
    // 2. Start session
    await startSession(session.id);
    
    // 3. Mark attendance
    await markAttendance(session.id, studentId, 'present');
    
    // 4. End session
    await endSession(session.id);
    
    // 5. Verify final state
    const finalSession = await getSession(session.id);
    expect(finalSession.actual_end_time).toBeDefined();
  });
});
```

## Deployment Considerations

### Database Migrations

```sql
-- Ensure proper indexes for performance
CREATE INDEX idx_attendance_sessions_date_school 
ON attendance_sessions(session_date, school_id);

CREATE INDEX idx_student_attendance_session 
ON student_attendance(session_id, attendance_status);

-- Ensure RPC functions exist
CREATE OR REPLACE FUNCTION create_attendance_session(...)
RETURNS uuid AS $$
BEGIN
  -- Implementation
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### Environment Configuration

```typescript
// Feature flags for gradual rollout
const FEATURES = {
  ENHANCED_SESSIONS: process.env.REACT_APP_ENHANCED_SESSIONS === 'true',
  OFFLINE_SYNC: process.env.REACT_APP_OFFLINE_SYNC === 'true',
  ROUND_TABLES: process.env.REACT_APP_ROUND_TABLES === 'true',
};
```

## Monitoring and Analytics

### Performance Metrics

```typescript
// Track session creation success rate
const trackSessionCreation = (success: boolean, method: 'rpc' | 'direct') => {
  analytics.track('session_created', {
    success,
    method,
    timestamp: new Date().toISOString(),
  });
};

// Monitor attendance marking performance
const trackAttendanceMarking = (duration: number, studentCount: number) => {
  analytics.track('attendance_marked', {
    duration,
    studentCount,
    efficiency: studentCount / duration,
  });
};
```

### Error Tracking

```typescript
// Comprehensive error logging
const logSessionError = (error: Error, context: any) => {
  console.error('Session error:', error);
  
  // Send to error tracking service
  errorTracker.captureException(error, {
    tags: { component: 'sessions' },
    extra: context,
  });
};
```

## Future Enhancements

### Planned Features

1. **Advanced Analytics**
   - Attendance trends analysis
   - Session effectiveness metrics
   - Student engagement tracking

2. **Mobile Optimizations**
   - Offline-first architecture
   - Progressive Web App features
   - Push notifications

3. **Integration Capabilities**
   - Calendar system integration
   - SMS notifications for parents
   - Learning management system sync

4. **AI-Powered Features**
   - Attendance prediction
   - Optimal session scheduling
   - Personalized learning recommendations

This implementation provides a robust, scalable foundation for session management while maintaining flexibility for future enhancements and ensuring reliable operation in challenging field conditions.
