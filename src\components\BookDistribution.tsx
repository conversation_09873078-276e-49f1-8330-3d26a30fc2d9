
import React, { useState } from 'react';
import { BookOpen } from 'lucide-react';
import CreateDistributionDialog from './distributions/CreateDistributionDialog';
import DistributionList from './distributions/DistributionList';
import ExportManager from './ExportManager';
import { useDistributions } from '@/hooks/useDistributions';
import { DistributionSubmissionData } from '@/hooks/useDistributionForm';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

// Use Supabase generated types
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface BookDistributionProps {
  currentUser: Profile;
}

const BookDistribution = ({ currentUser }: BookDistributionProps) => {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const {
    distributions,
    schools,
    inventory,
    isLoading,
    addDistribution,
    isAdding,
  } = useDistributions();

  const handleCreateDistribution = (data: DistributionSubmissionData) => {
    console.log('BookDistribution: Handling distribution creation:', data);
    addDistribution(data);
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          <div className="space-y-3">
            <div className="h-32 bg-gray-200 rounded"></div>
            <div className="h-32 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Book Distributions"
        description="Track and manage book deliveries to schools"
        icon={BookOpen}
      >
        <ExportManager dataType="distributions" title="Distributions" />
        <CreateDistributionDialog
          schools={schools}
          inventory={inventory}
          currentUser={currentUser}
          onSubmit={handleCreateDistribution}
          isSubmitting={isAdding}
        />
      </PageHeader>

      <ContentCard noPadding>
        <DistributionList
          distributions={distributions}
          currentUser={currentUser}
          onCreateClick={() => setCreateDialogOpen(true)}
        />
      </ContentCard>
    </PageLayout>
  );
};

export default BookDistribution;
