-- Enhanced Field Reports Schema Migration
-- Migration 026: Adopt Activity Report format while maintaining existing functionality
-- Adds facilitator information, enhanced participant data, structured feedback, and recommendations

-- Add facilitator information fields
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS facilitators JSONB DEFAULT '[]';
-- Structure: [{"name": "string", "mobile": "string", "email": "string"}]

-- Add enhanced participant information
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS male_participants INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS female_participants INTEGER DEFAULT 0;
-- Flexible student breakdown by education level
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS students_primary INTEGER DEFAULT 0; -- Primary students
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS students_s1 INTEGER DEFAULT 0; -- Senior 1 students
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS students_s2 INTEGER DEFAULT 0; -- Senior 2 students
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS students_s3 INTEGER DEFAULT 0; -- Senior 3 students
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS students_s4 INTEGER DEFAULT 0; -- Senior 4 students
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS students_other INTEGER DEFAULT 0; -- Other levels (A-level, etc.)
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS champions_count INTEGER DEFAULT 0;

-- Flexible round table breakdown by education level
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS round_tables_primary INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS round_tables_s1 INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS round_tables_s2 INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS round_tables_s3 INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS round_tables_s4 INTEGER DEFAULT 0;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS round_tables_other INTEGER DEFAULT 0;

-- Add structured activity feedback
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS activity_feedback JSONB DEFAULT '[]';
-- Structure: [{"topic": "string", "what_worked_well": "string", "participant_comments": "string"}]

-- Add introduction and structured recommendations
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS introduction TEXT;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS recommendations TEXT;
ALTER TABLE field_reports ADD COLUMN IF NOT EXISTS way_forward TEXT;

-- Create function to validate facilitator data
CREATE OR REPLACE FUNCTION validate_facilitator_data(facilitators_data JSONB)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    -- Check if it's an array
    IF jsonb_typeof(facilitators_data) != 'array' THEN
        RETURN FALSE;
    END IF;
    
    -- Check each facilitator object has required fields
    IF EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(facilitators_data) AS facilitator
        WHERE NOT (
            facilitator ? 'name' AND 
            facilitator ? 'mobile' AND 
            facilitator ? 'email'
        )
    ) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$;

-- Create function to validate activity feedback data
CREATE OR REPLACE FUNCTION validate_activity_feedback(feedback_data JSONB)
RETURNS BOOLEAN
LANGUAGE plpgsql
AS $$
BEGIN
    -- Check if it's an array
    IF jsonb_typeof(feedback_data) != 'array' THEN
        RETURN FALSE;
    END IF;
    
    -- Check each feedback object has required fields
    IF EXISTS (
        SELECT 1 
        FROM jsonb_array_elements(feedback_data) AS feedback
        WHERE NOT (
            feedback ? 'topic' AND 
            feedback ? 'what_worked_well' AND 
            feedback ? 'participant_comments'
        )
    ) THEN
        RETURN FALSE;
    END IF;
    
    RETURN TRUE;
END;
$$;

-- Add constraints to ensure data integrity
ALTER TABLE field_reports ADD CONSTRAINT check_facilitators_format 
    CHECK (facilitators IS NULL OR validate_facilitator_data(facilitators));

ALTER TABLE field_reports ADD CONSTRAINT check_activity_feedback_format 
    CHECK (activity_feedback IS NULL OR validate_activity_feedback(activity_feedback));

-- Add constraints for participant counts
ALTER TABLE field_reports ADD CONSTRAINT check_participant_counts_positive
    CHECK (
        male_participants >= 0 AND
        female_participants >= 0 AND
        students_primary >= 0 AND
        students_s1 >= 0 AND
        students_s2 >= 0 AND
        students_s3 >= 0 AND
        students_s4 >= 0 AND
        students_other >= 0 AND
        champions_count >= 0 AND
        round_tables_primary >= 0 AND
        round_tables_s1 >= 0 AND
        round_tables_s2 >= 0 AND
        round_tables_s3 >= 0 AND
        round_tables_s4 >= 0 AND
        round_tables_other >= 0
    );

-- Create helper function to calculate total participants
CREATE OR REPLACE FUNCTION calculate_total_participants(report_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
AS $$
DECLARE
    total_count INTEGER;
BEGIN
    SELECT COALESCE(male_participants, 0) + COALESCE(female_participants, 0)
    INTO total_count
    FROM field_reports
    WHERE id = report_id;
    
    RETURN COALESCE(total_count, 0);
END;
$$;

-- Create view for enhanced field reports with calculated fields
CREATE OR REPLACE VIEW enhanced_field_reports AS
SELECT 
    fr.*,
    -- Calculate total participants
    COALESCE(fr.male_participants, 0) + COALESCE(fr.female_participants, 0) AS total_participants_calculated,
    -- Calculate total students across all levels
    COALESCE(fr.students_primary, 0) + COALESCE(fr.students_s1, 0) + COALESCE(fr.students_s2, 0) +
    COALESCE(fr.students_s3, 0) + COALESCE(fr.students_s4, 0) + COALESCE(fr.students_other, 0) AS total_students_calculated,
    -- Calculate total round tables across all levels
    COALESCE(fr.round_tables_primary, 0) + COALESCE(fr.round_tables_s1, 0) + COALESCE(fr.round_tables_s2, 0) +
    COALESCE(fr.round_tables_s3, 0) + COALESCE(fr.round_tables_s4, 0) + COALESCE(fr.round_tables_other, 0) AS total_round_tables_calculated,
    -- Get staff and school information
    p.name AS staff_name,
    s.name AS school_name,
    ad.district AS school_district,
    ad.sub_county AS school_sub_county,
    -- Get attendance information
    fsa.check_in_time,
    fsa.check_out_time,
    fsa.check_in_address,
    fsa.check_out_address
FROM field_reports fr
LEFT JOIN profiles p ON fr.staff_id = p.id
LEFT JOIN schools s ON fr.school_id = s.id
LEFT JOIN administrative_divisions ad ON s.division_id = ad.id
LEFT JOIN field_staff_attendance fsa ON fr.attendance_id = fsa.id;

-- Grant appropriate permissions
GRANT SELECT ON enhanced_field_reports TO authenticated;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_field_reports_facilitators ON field_reports USING GIN (facilitators);
CREATE INDEX IF NOT EXISTS idx_field_reports_activity_feedback ON field_reports USING GIN (activity_feedback);
CREATE INDEX IF NOT EXISTS idx_field_reports_participant_counts ON field_reports (male_participants, female_participants);
CREATE INDEX IF NOT EXISTS idx_field_reports_student_counts ON field_reports (students_primary, students_s1, students_s2, students_s3, students_s4, students_other);
CREATE INDEX IF NOT EXISTS idx_field_reports_round_table_counts ON field_reports (round_tables_primary, round_tables_s1, round_tables_s2, round_tables_s3, round_tables_s4, round_tables_other);

-- Add comments for documentation
COMMENT ON COLUMN field_reports.facilitators IS 'JSON array of facilitator information with name, mobile, and email fields';
COMMENT ON COLUMN field_reports.male_participants IS 'Number of male participants';
COMMENT ON COLUMN field_reports.female_participants IS 'Number of female participants';
COMMENT ON COLUMN field_reports.students_primary IS 'Number of Primary level students';
COMMENT ON COLUMN field_reports.students_s1 IS 'Number of Senior 1 students';
COMMENT ON COLUMN field_reports.students_s2 IS 'Number of Senior 2 students';
COMMENT ON COLUMN field_reports.students_s3 IS 'Number of Senior 3 students';
COMMENT ON COLUMN field_reports.students_s4 IS 'Number of Senior 4 students';
COMMENT ON COLUMN field_reports.students_other IS 'Number of students from other levels (A-level, etc.)';
COMMENT ON COLUMN field_reports.champions_count IS 'Number of champion teachers/students involved';
COMMENT ON COLUMN field_reports.round_tables_primary IS 'Number of round tables for Primary level';
COMMENT ON COLUMN field_reports.round_tables_s1 IS 'Number of round tables for Senior 1';
COMMENT ON COLUMN field_reports.round_tables_s2 IS 'Number of round tables for Senior 2';
COMMENT ON COLUMN field_reports.round_tables_s3 IS 'Number of round tables for Senior 3';
COMMENT ON COLUMN field_reports.round_tables_s4 IS 'Number of round tables for Senior 4';
COMMENT ON COLUMN field_reports.round_tables_other IS 'Number of round tables for other levels';
COMMENT ON COLUMN field_reports.activity_feedback IS 'JSON array of structured feedback for each topic/activity';
COMMENT ON COLUMN field_reports.introduction IS 'Introduction or context for the activity';
COMMENT ON COLUMN field_reports.recommendations IS 'Structured recommendations based on the activity';
COMMENT ON COLUMN field_reports.way_forward IS 'Strategic next steps and way forward';

COMMENT ON VIEW enhanced_field_reports IS 'Enhanced view of field reports with calculated fields and joined staff/school information';
