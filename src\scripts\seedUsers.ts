import { supabase } from '@/integrations/supabase/client';

interface SeedResult {
  email: string;
  status: 'complete' | 'failed';
  password?: string;
  error?: string;
}

interface SeedResponse {
  success: boolean;
  results: SeedResult[];
  summary: {
    total: number;
    completed: number;
    failed: number;
  };
}

/**
 * Seed users using Supabase Edge Function
 */
export const seedUsers = async (): Promise<SeedResponse> => {
  try {
    console.log('🌱 Starting user seeding process...');

    // Call the Edge Function
    const { data, error } = await supabase.functions.invoke('seed-users', {
      body: {}
    });

    if (error) {
      console.error('❌ Error calling seed-users function:', error);
      throw new Error(`Edge Function error: ${error.message}`);
    }

    if (!data?.success) {
      console.error('❌ Seed function returned error:', data);
      throw new Error(data?.error || 'Unknown error from seed function');
    }

    console.log('✅ User seeding completed successfully');
    return data as SeedResponse;

  } catch (error) {
    console.error('❌ Error in seedUsers:', error);
    throw error;
  }
};

/**
 * Format and display seeding results
 */
export const displaySeedResults = (response: SeedResponse): void => {
  console.log('\n📊 SEEDING RESULTS SUMMARY');
  console.log('=' .repeat(50));
  console.log(`Total Users: ${response.summary.total}`);
  console.log(`✅ Completed: ${response.summary.completed}`);
  console.log(`❌ Failed: ${response.summary.failed}`);
  console.log('=' .repeat(50));

  console.log('\n📋 DETAILED RESULTS:');
  response.results.forEach((result, index) => {
    console.log(`\n${index + 1}. ${result.email}`);
    console.log(`   Status: ${result.status === 'complete' ? '✅ COMPLETE' : '❌ FAILED'}`);
    
    if (result.status === 'complete' && result.password) {
      console.log(`   Password: ${result.password}`);
      console.log(`   Note: User must change password on first login`);
    } else if (result.status === 'failed' && result.error) {
      console.log(`   Error: ${result.error}`);
    }
  });

  if (response.summary.completed > 0) {
    console.log('\n🔐 PASSWORD SUMMARY:');
    console.log('=' .repeat(50));
    response.results
      .filter(r => r.status === 'complete')
      .forEach(result => {
        console.log(`${result.email}: ${result.password}`);
      });
  }

  console.log('\n⚠️  IMPORTANT NOTES:');
  console.log('- All users must change their password on first login');
  console.log('- Passwords should be shared securely with users');
  console.log('- Users are created with is_active: true');
  console.log('- Users are assigned to Uganda country by default');
};

/**
 * Run the seeding process (for development/testing)
 */
export const runSeedingProcess = async (): Promise<void> => {
  try {
    const response = await seedUsers();
    displaySeedResults(response);
  } catch (error) {
    console.error('❌ Seeding process failed:', error);
    throw error;
  }
};

// Export for use in browser console or scripts
if (typeof window !== 'undefined') {
  (window as typeof window & { seedUsers: typeof runSeedingProcess }).seedUsers = runSeedingProcess;
  console.log('🌱 User seeding function available as window.seedUsers()');
}
