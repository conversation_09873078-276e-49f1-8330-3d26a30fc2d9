
import React, { useState } from 'react';
import { CheckSquare } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import TaskList from '../TaskList';
import TaskDetailsDialog from './TaskDetailsDialog';
import { useCompletedTasks, useUpdateTaskStatus } from '@/hooks/tasks';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

const CompletedTasks = () => {
  const { profile } = useAuth();
  const [selectedTaskId, setSelectedTaskId] = useState<string | null>(null);

  // Use optimized hook for completed tasks
  const { data: completedTasks = [], isLoading } = useCompletedTasks();
  const updateStatusMutation = useUpdateTaskStatus();

  const handleViewDetails = (taskId: string) => {
    setSelectedTaskId(taskId);
  };

  const handleUpdateStatus = (taskId: string, status: Database['public']['Enums']['task_status']) => {
    updateStatusMutation.mutate({ taskId, status });
  };

  return (
    <PageLayout>
      <PageHeader
        title="Completed Tasks"
        description={`View all completed tasks (${completedTasks.length} total)`}
        icon={CheckSquare}
      />

      <ContentCard noPadding>
        <TaskList
          tasks={completedTasks}
          loading={isLoading}
          currentUserId={profile?.id}
          onViewDetails={handleViewDetails}
          onUpdateStatus={handleUpdateStatus}
        />
      </ContentCard>

      {/* Task Details Dialog */}
      <TaskDetailsDialog
        taskId={selectedTaskId}
        open={!!selectedTaskId}
        onOpenChange={(open) => {
          if (!open) {
            setSelectedTaskId(null);
          }
        }}
      />
    </PageLayout>
  );
};

export default CompletedTasks;
