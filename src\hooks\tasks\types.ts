
import { Database } from '@/integrations/supabase/types';

export type TaskFormData = {
  title: string;
  description?: string;
  priority: Database['public']['Enums']['task_priority'];
  due_date?: Date;
  assigned_to?: string;
  school_id?: string;
};

export type TaskQueryParams = {
  userId?: string;
  statusFilter?: Database['public']['Enums']['task_status'] | 'all';
  assignedFilter?: string;
  includeComments?: boolean;
  limit?: number;
};
