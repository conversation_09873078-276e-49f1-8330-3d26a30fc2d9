export interface PerformanceMetrics {
  operationName: string;
  startTime: number;
  endTime?: number;
  duration?: number;
  success: boolean;
  errorMessage?: string;
  metadata?: Record<string, unknown>;
  queryName?: string;
  resultCount?: number;
}

export interface QuerySummary {
  queryName: string;
  totalCalls: number;
  averageTime: number;
  fastestQuery: number;
  slowestQuery: number;
  errorRate: number;
}

export class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private activeTimers: Map<string, number> = new Map();

  startTimer(operationName: string): (success: boolean, errorMessage?: string) => void {
    const startTime = performance.now();
    const timerId = `${operationName}_${startTime}`;
    
    this.activeTimers.set(timerId, startTime);

    // Return the endTimer function
    return (success: boolean = true, errorMessage?: string) => {
      const endTime = performance.now();
      const startTimeFromMap = this.activeTimers.get(timerId);
      
      if (startTimeFromMap !== undefined) {
        const duration = endTime - startTimeFromMap;
        
        const metric: PerformanceMetrics = {
          operationName,
          queryName: operationName,
          startTime: startTimeFromMap,
          endTime,
          duration,
          success,
          errorMessage,
        };

        this.metrics.push(metric);
        this.activeTimers.delete(timerId);

        // Log performance metrics in development
        if (process.env.NODE_ENV === 'development') {
          const status = success ? '✅' : '❌';
          console.log(
            `${status} ${operationName}: ${duration.toFixed(2)}ms`,
            errorMessage ? `(Error: ${errorMessage})` : ''
          );
        }

        // Keep only the last 100 metrics to prevent memory leaks
        if (this.metrics.length > 100) {
          this.metrics = this.metrics.slice(-50);
        }
      }
    };
  }

  getMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  getAverageTime(operationName: string): number {
    const operationMetrics = this.metrics.filter(m => m.operationName === operationName && m.duration);
    if (operationMetrics.length === 0) return 0;
    
    const totalTime = operationMetrics.reduce((sum, metric) => sum + (metric.duration || 0), 0);
    return totalTime / operationMetrics.length;
  }

  getSuccessRate(operationName: string): number {
    const operationMetrics = this.metrics.filter(m => m.operationName === operationName);
    if (operationMetrics.length === 0) return 0;
    
    const successCount = operationMetrics.filter(m => m.success).length;
    return (successCount / operationMetrics.length) * 100;
  }

  clear(): void {
    this.metrics = [];
    this.activeTimers.clear();
  }

  // Helper method for debugging
  logSummary(): void {
    const operations = [...new Set(this.metrics.map(m => m.operationName))];
    
    console.group('Performance Summary');
    operations.forEach(op => {
      const avgTime = this.getAverageTime(op);
      const successRate = this.getSuccessRate(op);
      console.log(`${op}: ${avgTime.toFixed(2)}ms avg, ${successRate.toFixed(1)}% success`);
    });
    console.groupEnd();
  }

  getSummary(): QuerySummary[] {
    const queryGroups = this.metrics.reduce((acc, metric) => {
      const queryName = metric.queryName || metric.operationName;
      if (!acc[queryName]) {
        acc[queryName] = [];
      }
      acc[queryName].push(metric);
      return acc;
    }, {} as Record<string, PerformanceMetrics[]>);

    return Object.entries(queryGroups).map(([queryName, metrics]) => {
      const validMetrics = metrics.filter(m => m.duration !== undefined);
      const durations = validMetrics.map(m => m.duration!);
      const errors = metrics.filter(m => !m.success).length;

      return {
        queryName,
        totalCalls: metrics.length,
        averageTime: durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0,
        fastestQuery: durations.length > 0 ? Math.min(...durations) : 0,
        slowestQuery: durations.length > 0 ? Math.max(...durations) : 0,
        errorRate: metrics.length > 0 ? (errors / metrics.length) * 100 : 0,
      };
    });
  }

  getSlowQueries(threshold: number = 500): PerformanceMetrics[] {
    return this.metrics.filter(m => m.duration && m.duration > threshold);
  }

  getCacheHitRate(): number {
    // Mock cache hit rate for now
    return 75.5;
  }
}

// Export a singleton instance
export const performanceMonitor = new PerformanceMonitor();
