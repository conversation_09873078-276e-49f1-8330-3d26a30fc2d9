import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import {
  Clock,
  CheckCircle,
  MapPin,
  Calendar,
  User,
  Building,
  Timer,
  AlertCircle,
  ArrowRight,
  Activity,
  Wifi,
  WifiOff
} from 'lucide-react';
import { useUnifiedCheckInStatus } from '@/hooks/attendance/useUnifiedCheckInStatus';
import { useAuth } from '@/hooks/useAuth';
import FieldStaffCheckIn from './FieldStaffCheckIn';
import FieldStaffCheckOut from './FieldStaffCheckOut';
import OfflineStatusIndicator from './OfflineStatusIndicator';
import SyncStatusIndicator from './SyncStatusIndicator';
import PhotoUploadQueueStatus from './PhotoUploadQueueStatus';
import { withFieldErrorBoundary } from '@/utils/fieldErrorBoundaryUtils';
import { PageLayout, PageHeader } from '@/components/layout';
import { format } from 'date-fns';

const FieldStaffAttendance: React.FC = () => {
  const { profile } = useAuth();
  const { data: unifiedStatus, isLoading } = useUnifiedCheckInStatus();

  // Show loading state
  if (isLoading) {
    return (
      <PageLayout>
        <PageHeader
          title="Field Attendance"
          description="Manage your field work check-in and reporting"
        />
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading attendance status...</span>
        </div>
      </PageLayout>
    );
  }

  // Determine current status and next action
  const isCheckedIn = unifiedStatus?.isCheckedIn ?? false;
  const isOnline = navigator.onLine;

  const statusInfo = {
    status: isCheckedIn ? 'checked_in' : 'checked_out',
    statusLabel: isCheckedIn ? 'Active Field Work' : 'Ready to Start',
    statusColor: isCheckedIn ? 'bg-green-100 text-green-800 border-green-200' : 'bg-blue-100 text-blue-800 border-blue-200',
    nextAction: isCheckedIn ? 'Complete your field work and submit report' : 'Check in to begin your field work session',
    actionButtonText: isCheckedIn ? 'Check Out & Submit Report' : 'Start Field Work',
    actionButtonColor: isCheckedIn ? 'bg-orange-600 hover:bg-orange-700' : 'bg-green-600 hover:bg-green-700',
    icon: isCheckedIn ? Activity : Clock,
    bgGradient: isCheckedIn ? 'from-green-50 to-emerald-50' : 'from-blue-50 to-indigo-50'
  };

  const StatusIcon = statusInfo.icon;

  return (
    <PageLayout>
      <PageHeader
        title="Field Visits"
        description="Manage your field work check-in and reporting"
      />

      {/* Sync Status and Connection Banner */}
      <div className="mb-6 space-y-4">
        <div className={`flex items-center gap-3 p-3 rounded-lg border ${
          isOnline
            ? 'bg-green-50 border-green-200 text-green-800'
            : 'bg-yellow-50 border-yellow-200 text-yellow-800'
        }`}>
          {isOnline ? (
            <Wifi className="h-4 w-4" />
          ) : (
            <WifiOff className="h-4 w-4" />
          )}
          <span className="text-sm font-medium">
            {isOnline ? 'Online - Data will sync immediately' : 'Offline - Data will sync when connection is restored'}
          </span>
        </div>

        {/* Enhanced Sync Status */}
        <SyncStatusIndicator showDetails={false} />

        {/* Photo Upload Queue Status */}
        <PhotoUploadQueueStatus compact={true} />
      </div>

      {/* Enhanced Status Card */}
      <Card className={`mb-6 border-2 bg-gradient-to-r ${statusInfo.bgGradient}`}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`p-3 rounded-full ${isCheckedIn ? 'bg-green-100' : 'bg-blue-100'}`}>
                <StatusIcon className={`h-6 w-6 ${isCheckedIn ? 'text-green-600' : 'text-blue-600'}`} />
              </div>
              <div>
                <CardTitle className="text-xl">
                  {statusInfo.statusLabel}
                </CardTitle>
                <CardDescription className="text-base">
                  {statusInfo.nextAction}
                </CardDescription>
              </div>
            </div>
            <Badge className={`${statusInfo.statusColor} text-sm px-3 py-1 border`}>
              {isCheckedIn ? 'IN FIELD' : 'AVAILABLE'}
            </Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">

            {/* Current Check-in Details */}
            {isCheckedIn && currentCheckIn && (
              <div className="bg-white border border-green-200 rounded-lg p-5 shadow-sm">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="font-semibold text-green-900 flex items-center gap-2">
                    <MapPin className="h-5 w-5" />
                    Active Field Session
                  </h4>
                  <div className="flex items-center gap-2 text-green-700">
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                    <span className="text-sm font-medium">Live</span>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Calendar className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <div>
                        <div className="text-xs text-gray-500 uppercase tracking-wide">Check-in Time</div>
                        <div className="font-medium text-gray-900">
                          {format(new Date(currentCheckIn.check_in_time), 'MMM d, yyyy h:mm a')}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <Building className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <div>
                        <div className="text-xs text-gray-500 uppercase tracking-wide">Location</div>
                        <div className="font-medium text-gray-900">
                          {currentCheckIn.address_description || 'GPS Location Recorded'}
                        </div>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <Timer className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <div>
                        <div className="text-xs text-gray-500 uppercase tracking-wide">Session Duration</div>
                        <div className="font-medium text-gray-900">
                          {(() => {
                            const checkInTime = new Date(currentCheckIn.check_in_time);
                            const now = new Date();
                            const diffMs = now.getTime() - checkInTime.getTime();
                            const hours = Math.floor(diffMs / (1000 * 60 * 60));
                            const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
                            return `${hours}h ${minutes}m`;
                          })()}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-3">
                      <User className="h-4 w-4 text-green-600 flex-shrink-0" />
                      <div>
                        <div className="text-xs text-gray-500 uppercase tracking-wide">Field Staff</div>
                        <div className="font-medium text-gray-900">
                          {profile?.name || 'Current User'}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Ready to Check In Message */}
            {!isCheckedIn && (
              <div className="bg-white border border-blue-200 rounded-lg p-5 shadow-sm">
                <div className="flex items-center gap-3 mb-3">
                  <div className="p-2 bg-blue-100 rounded-full">
                    <AlertCircle className="h-4 w-4 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-900">Ready to Start Field Work</h4>
                    <p className="text-sm text-blue-700">
                      You are not currently checked in to any field location.
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <span>Next step:</span>
                  <ArrowRight className="h-4 w-4" />
                  <span className="font-medium">Use the form below to check in and begin your session</span>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Conditional Interface */}
      {isCheckedIn ? (
        <FieldStaffCheckOut />
      ) : (
        <FieldStaffCheckIn />
      )}
    </PageLayout>
  );
};

// Export the wrapped component with a proper name
const FieldStaffAttendanceWithErrorBoundary = withFieldErrorBoundary(FieldStaffAttendance);
FieldStaffAttendanceWithErrorBoundary.displayName = 'FieldStaffAttendance';

export default FieldStaffAttendanceWithErrorBoundary;
