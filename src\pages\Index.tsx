
import React from 'react';
import Login from '@/components/Login';
import AuthenticatedApp from '@/components/AuthenticatedApp';
import { useAuth } from '@/hooks/useAuth';

const Index = () => {
  const { user, loading, signIn } = useAuth();

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-purple-50 to-orange-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return <Login onLogin={signIn} />;
  }

  return <AuthenticatedApp />;
};

export default Index;
