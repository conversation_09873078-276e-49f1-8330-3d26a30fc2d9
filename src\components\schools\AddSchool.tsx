
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Plus, School } from 'lucide-react';
import { useSchoolOperations } from '@/hooks/useSchoolOperations';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';
import { useSchoolForm } from '@/hooks/useSchoolForm';
import BasicInformationSection from './form/BasicInformationSection';
import AcademicStructureSection from './form/AcademicStructureSection';
import LeadershipSection from './form/LeadershipSection';
import ContactInformationSection from './form/ContactInformationSection';
import LocationInfrastructureSection from './form/LocationInfrastructureSection';

const AddSchool = () => {
  const { profile } = useAuth();
  const { divisions, addSchool, isAddingSchool } = useSchoolOperations(profile);
  const { toast } = useToast();
  const { formData, setFormData, resetForm } = useSchoolForm();

  const canManageSchools = profile?.role === 'admin' || profile?.role === 'program_officer';

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!canManageSchools) {
      toast({
        title: "Error",
        description: "You don't have permission to add schools",
        variant: "destructive",
      });
      return;
    }

    if (!formData.division_id) {
      toast({
        title: "Error",
        description: "Please select a district",
        variant: "destructive",
      });
      return;
    }
    
    addSchool(formData);
    resetForm();
  };

  if (!canManageSchools) {
    return (
      <div className="p-6">
        <Card>
          <CardContent className="text-center py-12">
            <School className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Access Restricted</h3>
            <p className="text-gray-600">You don't have permission to add new schools.</p>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="flex items-center mb-6">
        <Plus className="h-6 w-6 mr-2 text-purple-600" />
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Add New School</h1>
          <p className="text-gray-600 mt-1">Register a new school in the iLead program</p>
        </div>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>School Registration Form</CardTitle>
          <CardDescription>
            Please provide complete information about the school according to the Ugandan education system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <BasicInformationSection 
              formData={formData} 
              divisions={divisions} 
              onFormDataChange={setFormData} 
            />
            
            <AcademicStructureSection 
              formData={formData} 
              onFormDataChange={setFormData} 
            />
            
            <LeadershipSection 
              formData={formData} 
              onFormDataChange={setFormData} 
            />
            
            <ContactInformationSection 
              formData={formData} 
              onFormDataChange={setFormData} 
            />
            
            <LocationInfrastructureSection 
              formData={formData} 
              onFormDataChange={setFormData} 
            />

            <div className="flex justify-end space-x-4 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={() => window.history.back()}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="bg-purple-600 hover:bg-purple-700"
                disabled={isAddingSchool}
              >
                {isAddingSchool ? 'Adding School...' : 'Add School'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  );
};

export default AddSchool;
