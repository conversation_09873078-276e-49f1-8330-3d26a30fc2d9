/**
 * <PERSON><PERSON><PERSON> to verify a user's password by attempting to sign in
 * Usage: node scripts/verify-password.js
 */

import { createClient } from '@supabase/supabase-js';

// Use the same configuration as the client
const supabaseUrl = "https://bygrspebofyofymivmib.supabase.co";
const supabaseAnonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ5Z3JzcGVib2Z5b2Z5bWl2bWliIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDkwMzIxODgsImV4cCI6MjA2NDYwODE4OH0.xxfeix-6F42NmVWaQHE19nnDCxZmiMDs1_fyLb0-lgE";

// Create Supabase client for authentication
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function verifyPassword(email, password) {
  try {
    console.log(`Attempting to sign in with email: ${email}`);
    
    const { data, error } = await supabase.auth.signInWithPassword({
      email: email,
      password: password
    });

    if (error) {
      console.error('Sign in failed:', error.message);
      return false;
    }

    if (data.user) {
      console.log('✅ Sign in successful!');
      console.log(`User ID: ${data.user.id}`);
      console.log(`Email: ${data.user.email}`);
      
      // Sign out after verification
      await supabase.auth.signOut();
      console.log('Signed out successfully');
      return true;
    }

    return false;
  } catch (error) {
    console.error('Unexpected error:', error);
    return false;
  }
}

// Test credentials
const testEmail = "<EMAIL>";
const testPassword = "Xzt4q87m";

async function main() {
  console.log('=== Password Verification Script ===');
  console.log(`Testing credentials for: ${testEmail}`);
  console.log(`Password: ${testPassword}`);
  console.log('');

  const success = await verifyPassword(testEmail, testPassword);
  
  if (success) {
    console.log('✅ Password verification successful!');
    console.log('The user can now log in with the new password.');
  } else {
    console.log('❌ Password verification failed!');
    console.log('The password may not have been set correctly.');
  }
}

// Run the script
main().catch(console.error);
