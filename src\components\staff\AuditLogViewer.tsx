import React, { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  Calendar,
  Search,
  Filter,
  Download,
  Eye,
  User,
  Shield,
  UserPlus,
  UserMinus,
  Settings,
  AlertTriangle
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

type AuditAction = Database['public']['Enums']['audit_action'];

interface AuditLogDetails {
  old_role?: string;
  new_role?: string;
  reason?: string;
  [key: string]: unknown;
}

interface AuditLog {
  id: string;
  action: AuditAction;
  target_user_id: string | null;
  target_user_email: string | null;
  performed_by: string;
  performed_by_name: string;
  details: AuditLogDetails | null;
  ip_address: string | null;
  user_agent: string | null;
  created_at: string;
}

interface AuditLogViewerProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const AuditLogViewer: React.FC<AuditLogViewerProps> = ({ open, onOpenChange }) => {
  const [filters, setFilters] = useState({
    action: 'all',
    performedBy: '',
    targetUser: '',
    dateFrom: '',
    dateTo: '',
    search: ''
  });

  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 50;

  const { data: auditLogs, isLoading, error } = useQuery({
    queryKey: ['auditLogs', filters, currentPage],
    queryFn: async () => {
      let query = supabase
        .from('staff_audit_logs')
        .select(`
          *,
          performed_by_profile:profiles!staff_audit_logs_performed_by_fkey(name),
          target_user_profile:profiles!staff_audit_logs_target_user_id_fkey(name, email)
        `)
        .order('created_at', { ascending: false })
        .range((currentPage - 1) * pageSize, currentPage * pageSize - 1);

      // Apply filters
      if (filters.action && filters.action !== 'all') {
        query = query.eq('action', filters.action);
      }
      if (filters.performedBy) {
        query = query.eq('performed_by', filters.performedBy);
      }
      if (filters.targetUser) {
        query = query.eq('target_user_id', filters.targetUser);
      }
      if (filters.dateFrom) {
        query = query.gte('created_at', filters.dateFrom);
      }
      if (filters.dateTo) {
        query = query.lte('created_at', filters.dateTo);
      }

      const { data, error } = await query;
      if (error) throw error;

      // Transform data to include names
      return data.map(log => ({
        ...log,
        performed_by_name: log.performed_by_profile?.name || 'Unknown User',
        target_user_email: log.target_user_profile?.email || null
      })) as AuditLog[];
    },
    enabled: open
  });

  const getActionIcon = (action: AuditAction) => {
    switch (action) {
      case 'user_created':
        return <UserPlus className="h-4 w-4" />;
      case 'user_updated':
        return <Settings className="h-4 w-4" />;
      case 'user_deleted':
        return <UserMinus className="h-4 w-4" />;
      case 'role_changed':
        return <Shield className="h-4 w-4" />;
      case 'status_changed':
        return <User className="h-4 w-4" />;
      case 'invitation_sent':
        return <UserPlus className="h-4 w-4" />;
      case 'bulk_operation':
        return <Settings className="h-4 w-4" />;
      default:
        return <Eye className="h-4 w-4" />;
    }
  };

  const getActionBadgeVariant = (action: AuditAction) => {
    switch (action) {
      case 'user_created':
      case 'invitation_sent':
        return 'default';
      case 'user_updated':
      case 'role_changed':
      case 'status_changed':
        return 'secondary';
      case 'user_deleted':
        return 'destructive';
      case 'bulk_operation':
        return 'outline';
      default:
        return 'outline';
    }
  };

  const formatActionLabel = (action: AuditAction) => {
    return action.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  };

  const formatDetails = (action: AuditAction, details: AuditLogDetails | null) => {
    if (!details) return '';

    switch (action) {
      case 'role_changed':
        return `From ${details.old_role || 'unknown'} to ${details.new_role || 'unknown'}`;
      case 'status_changed':
        return `Status changed to ${details.new_status ? 'active' : 'inactive'}`;
      case 'bulk_operation':
        return `${details.operation_type || 'Unknown operation'}: ${details.affected_count || 0} users`;
      case 'user_created':
        return `Role: ${details.role || 'unknown'}`;
      default:
        return JSON.stringify(details);
    }
  };

  const exportAuditLogs = () => {
    if (!auditLogs) return;

    const csvHeaders = ['Date', 'Action', 'Performed By', 'Target User', 'Details', 'IP Address'];
    const csvData = auditLogs.map(log => [
      new Date(log.created_at).toLocaleString(),
      formatActionLabel(log.action),
      log.performed_by_name,
      log.target_user_email || '',
      formatDetails(log.action, log.details),
      log.ip_address || ''
    ]);

    const csvContent = [
      csvHeaders.join(','),
      ...csvData.map(row => row.map(cell => `"${cell}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    window.URL.revokeObjectURL(url);
  };

  const clearFilters = () => {
    setFilters({
      action: 'all',
      performedBy: '',
      targetUser: '',
      dateFrom: '',
      dateTo: '',
      search: ''
    });
    setCurrentPage(1);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-6xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Eye className="h-5 w-5 mr-2" />
            Audit Log Viewer
          </DialogTitle>
          <DialogDescription>
            View and filter staff management audit logs for security and compliance
          </DialogDescription>
        </DialogHeader>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <Filter className="h-5 w-5 mr-2" />
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Action</Label>
                <Select value={filters.action} onValueChange={(value) => setFilters(prev => ({ ...prev, action: value }))}>
                  <SelectTrigger>
                    <SelectValue placeholder="All actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All actions</SelectItem>
                    <SelectItem value="user_created">User Created</SelectItem>
                    <SelectItem value="user_updated">User Updated</SelectItem>
                    <SelectItem value="user_deleted">User Deleted</SelectItem>
                    <SelectItem value="role_changed">Role Changed</SelectItem>
                    <SelectItem value="status_changed">Status Changed</SelectItem>
                    <SelectItem value="invitation_sent">Invitation Sent</SelectItem>
                    <SelectItem value="bulk_operation">Bulk Operation</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Date From</Label>
                <Input
                  type="date"
                  value={filters.dateFrom}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateFrom: e.target.value }))}
                />
              </div>

              <div className="space-y-2">
                <Label>Date To</Label>
                <Input
                  type="date"
                  value={filters.dateTo}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateTo: e.target.value }))}
                />
              </div>
            </div>

            <div className="flex items-center space-x-2 mt-4">
              <Button onClick={clearFilters} variant="outline" size="sm">
                Clear Filters
              </Button>
              <Button onClick={exportAuditLogs} variant="outline" size="sm" disabled={!auditLogs?.length}>
                <Download className="h-4 w-4 mr-2" />
                Export CSV
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Audit Logs */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            </div>
          ) : error ? (
            <Card>
              <CardContent className="pt-6">
                <div className="flex items-center text-red-600">
                  <AlertTriangle className="h-5 w-5 mr-2" />
                  Failed to load audit logs
                </div>
              </CardContent>
            </Card>
          ) : !auditLogs?.length ? (
            <Card>
              <CardContent className="pt-6">
                <div className="text-center text-gray-500">
                  No audit logs found matching the current filters
                </div>
              </CardContent>
            </Card>
          ) : (
            <div className="space-y-2">
              {auditLogs.map((log) => (
                <Card key={log.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="pt-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          {getActionIcon(log.action)}
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2 mb-1">
                            <Badge variant={getActionBadgeVariant(log.action)}>
                              {formatActionLabel(log.action)}
                            </Badge>
                            <span className="text-sm text-gray-500">
                              {new Date(log.created_at).toLocaleString()}
                            </span>
                          </div>
                          
                          <div className="text-sm">
                            <span className="font-medium">{log.performed_by_name}</span>
                            {log.target_user_email && (
                              <>
                                <span className="text-gray-500"> performed action on </span>
                                <span className="font-medium">{log.target_user_email}</span>
                              </>
                            )}
                          </div>
                          
                          {log.details && (
                            <div className="text-sm text-gray-600 mt-1">
                              {formatDetails(log.action, log.details)}
                            </div>
                          )}
                          
                          {log.ip_address && (
                            <div className="text-xs text-gray-400 mt-1">
                              IP: {log.ip_address}
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {/* Pagination */}
        {auditLogs && auditLogs.length === pageSize && (
          <div className="flex justify-center space-x-2">
            <Button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              variant="outline"
              size="sm"
            >
              Previous
            </Button>
            <span className="flex items-center px-3 text-sm">
              Page {currentPage}
            </span>
            <Button
              onClick={() => setCurrentPage(prev => prev + 1)}
              disabled={auditLogs.length < pageSize}
              variant="outline"
              size="sm"
            >
              Next
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default AuditLogViewer;
