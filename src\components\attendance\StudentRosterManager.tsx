import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Users, 
  Search, 
  Filter,
  UserPlus,
  UserMinus,
  Shuffle,
  Grid,
  List,
  Download,
  Upload
} from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Database } from '@/integrations/supabase/types';

type Student = Database['public']['Tables']['students']['Row'];

interface StudentRosterManagerProps {
  sessionId: string;
  schoolId: string;
  roundTablesCount?: number;
  studentsPerTable?: number;
  maxCapacity?: number;
}

interface StudentWithTable extends Student {
  table_number?: number;
  is_enrolled?: boolean;
}

const StudentRosterManager: React.FC<StudentRosterManagerProps> = ({
  sessionId,
  schoolId,
  roundTablesCount = 0,
  studentsPerTable = 8,
  maxCapacity,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [gradeFilter, setGradeFilter] = useState<string>('all');
  const [genderFilter, setGenderFilter] = useState<string>('all');
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set());
  const [viewMode, setViewMode] = useState<'list' | 'tables'>('list');

  // Fetch all students from the school
  const { data: allStudents, isLoading: studentsLoading } = useQuery({
    queryKey: ['school-students', schoolId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('students')
        .select('*')
        .eq('school_id', schoolId)
        .eq('status', 'active')
        .order('last_name', { ascending: true });

      if (error) {
        console.error('Error fetching students:', error);
        throw error;
      }

      return data as Student[];
    },
    enabled: !!schoolId,
  });

  // Fetch current session enrollment
  const { data: sessionAttendance, isLoading: attendanceLoading } = useQuery({
    queryKey: ['session-students', sessionId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('student_attendance')
        .select('student_id, table_number')
        .eq('session_id', sessionId);

      if (error) {
        console.error('Error fetching session students:', error);
        throw error;
      }

      return data;
    },
    enabled: !!sessionId,
  });

  // Combine student data with enrollment status
  const studentsWithEnrollment: StudentWithTable[] = allStudents?.map(student => ({
    ...student,
    is_enrolled: sessionAttendance?.some(att => att.student_id === student.id) || false,
    table_number: sessionAttendance?.find(att => att.student_id === student.id)?.table_number || undefined,
  })) || [];

  // Filter students
  const filteredStudents = studentsWithEnrollment.filter(student => {
    if (searchTerm && !`${student.first_name} ${student.last_name}`.toLowerCase().includes(searchTerm.toLowerCase())) {
      return false;
    }
    if (gradeFilter && gradeFilter !== 'all' && student.grade_level.toString() !== gradeFilter) {
      return false;
    }
    if (genderFilter && genderFilter !== 'all' && student.gender !== genderFilter) {
      return false;
    }
    return true;
  });

  const enrolledStudents = filteredStudents.filter(s => s.is_enrolled);
  const availableStudents = filteredStudents.filter(s => !s.is_enrolled);

  // Get unique grades and genders for filters
  const availableGrades = [...new Set(allStudents?.map(s => s.grade_level) || [])].sort();
  const availableGenders = [...new Set(allStudents?.map(s => s.gender).filter(Boolean) || [])];

  const handleStudentToggle = (studentId: string, enroll: boolean) => {
    // TODO: Implement enrollment/unenrollment logic
    console.log(`${enroll ? 'Enrolling' : 'Unenrolling'} student ${studentId}`);
  };

  const handleBulkEnroll = () => {
    selectedStudents.forEach(studentId => {
      handleStudentToggle(studentId, true);
    });
    setSelectedStudents(new Set());
  };

  const handleBulkUnenroll = () => {
    selectedStudents.forEach(studentId => {
      handleStudentToggle(studentId, false);
    });
    setSelectedStudents(new Set());
  };

  const handleAutoAssignTables = () => {
    if (roundTablesCount === 0) return;
    
    const studentsToAssign = enrolledStudents.filter(s => !s.table_number);
    const assignments: Record<string, number> = {};
    
    studentsToAssign.forEach((student, index) => {
      const tableNumber = (index % roundTablesCount) + 1;
      assignments[student.id] = tableNumber;
    });

    // TODO: Implement table assignment logic
    console.log('Auto-assigning tables:', assignments);
  };

  const renderTableView = () => {
    if (roundTablesCount === 0) return null;

    const tables = Array.from({ length: roundTablesCount }, (_, i) => i + 1);
    
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {tables.map(tableNumber => {
          const tableStudents = enrolledStudents.filter(s => s.table_number === tableNumber);
          const remainingSeats = studentsPerTable - tableStudents.length;
          
          return (
            <Card key={tableNumber} className="border-2">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex items-center justify-between">
                  <span>Table {tableNumber}</span>
                  <Badge variant={tableStudents.length === studentsPerTable ? 'default' : 'secondary'}>
                    {tableStudents.length}/{studentsPerTable}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {tableStudents.map(student => (
                    <div key={student.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="text-sm">
                        <div className="font-medium">{student.first_name} {student.last_name}</div>
                        <div className="text-gray-600">Grade {student.grade_level} • {student.gender}</div>
                      </div>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => handleStudentToggle(student.id, false)}
                      >
                        <UserMinus className="h-3 w-3" />
                      </Button>
                    </div>
                  ))}
                  
                  {remainingSeats > 0 && (
                    <div className="text-center py-4 border-2 border-dashed border-gray-300 rounded">
                      <div className="text-sm text-gray-500">
                        {remainingSeats} seat{remainingSeats !== 1 ? 's' : ''} available
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
        
        {/* Unassigned Students */}
        {enrolledStudents.some(s => !s.table_number) && (
          <Card className="border-2 border-yellow-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <span>Unassigned</span>
                <Badge variant="outline" className="text-yellow-600 border-yellow-300">
                  {enrolledStudents.filter(s => !s.table_number).length}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {enrolledStudents
                  .filter(s => !s.table_number)
                  .map(student => (
                    <div key={student.id} className="flex items-center justify-between p-2 bg-yellow-50 rounded">
                      <div className="text-sm">
                        <div className="font-medium">{student.first_name} {student.last_name}</div>
                        <div className="text-gray-600">Grade {student.grade_level} • {student.gender}</div>
                      </div>
                      <Select
                        onValueChange={(value) => {
                          // TODO: Assign student to table
                          console.log(`Assigning student ${student.id} to table ${value}`);
                        }}
                      >
                        <SelectTrigger className="w-20">
                          <SelectValue placeholder="Table" />
                        </SelectTrigger>
                        <SelectContent>
                          {tables.map(tableNum => (
                            <SelectItem key={tableNum} value={tableNum.toString()}>
                              {tableNum}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderListView = () => {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Enrolled Students */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Enrolled Students ({enrolledStudents.length})</span>
              {maxCapacity && (
                <Badge variant={enrolledStudents.length >= maxCapacity ? 'destructive' : 'secondary'}>
                  {enrolledStudents.length}/{maxCapacity}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {enrolledStudents.map(student => (
                <div key={student.id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={selectedStudents.has(student.id)}
                      onCheckedChange={(checked) => {
                        const newSelected = new Set(selectedStudents);
                        if (checked) {
                          newSelected.add(student.id);
                        } else {
                          newSelected.delete(student.id);
                        }
                        setSelectedStudents(newSelected);
                      }}
                    />
                    <div>
                      <div className="font-medium">{student.first_name} {student.last_name}</div>
                      <div className="text-sm text-gray-600">
                        {student.student_number} • Grade {student.grade_level} • {student.gender}
                        {student.table_number && ` • Table ${student.table_number}`}
                      </div>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleStudentToggle(student.id, false)}
                  >
                    <UserMinus className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              {enrolledStudents.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No students enrolled yet
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Available Students */}
        <Card>
          <CardHeader>
            <CardTitle>Available Students ({availableStudents.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {availableStudents.map(student => (
                <div key={student.id} className="flex items-center justify-between p-3 border rounded">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={selectedStudents.has(student.id)}
                      onCheckedChange={(checked) => {
                        const newSelected = new Set(selectedStudents);
                        if (checked) {
                          newSelected.add(student.id);
                        } else {
                          newSelected.delete(student.id);
                        }
                        setSelectedStudents(newSelected);
                      }}
                    />
                    <div>
                      <div className="font-medium">{student.first_name} {student.last_name}</div>
                      <div className="text-sm text-gray-600">
                        {student.student_number} • Grade {student.grade_level} • {student.gender}
                      </div>
                    </div>
                  </div>
                  <Button
                    size="sm"
                    onClick={() => handleStudentToggle(student.id, true)}
                    disabled={maxCapacity ? enrolledStudents.length >= maxCapacity : false}
                  >
                    <UserPlus className="h-4 w-4" />
                  </Button>
                </div>
              ))}
              
              {availableStudents.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No available students
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  };

  if (studentsLoading || attendanceLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Student Roster Management
          </CardTitle>
          <CardDescription>
            Manage student enrollment and table assignments for this session
          </CardDescription>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex items-center gap-4 mb-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search students..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            
            <Select value={gradeFilter} onValueChange={setGradeFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Grade" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All grades</SelectItem>
                {availableGrades.map(grade => (
                  <SelectItem key={grade} value={grade.toString()}>
                    Grade {grade}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={genderFilter} onValueChange={setGenderFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Gender" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All genders</SelectItem>
                {availableGenders.map(gender => (
                  <SelectItem key={gender} value={gender}>
                    {gender}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {roundTablesCount > 0 && (
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setViewMode(viewMode === 'list' ? 'tables' : 'list')}
                >
                  {viewMode === 'list' ? <Grid className="h-4 w-4" /> : <List className="h-4 w-4" />}
                </Button>
              </div>
            )}
          </div>

          {/* Bulk Actions */}
          {selectedStudents.size > 0 && (
            <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 rounded-lg">
              <span className="text-sm font-medium">{selectedStudents.size} selected</span>
              <Button size="sm" onClick={handleBulkEnroll}>
                <UserPlus className="h-4 w-4 mr-2" />
                Enroll Selected
              </Button>
              <Button size="sm" variant="outline" onClick={handleBulkUnenroll}>
                <UserMinus className="h-4 w-4 mr-2" />
                Unenroll Selected
              </Button>
              {roundTablesCount > 0 && (
                <Button size="sm" variant="outline" onClick={handleAutoAssignTables}>
                  <Shuffle className="h-4 w-4 mr-2" />
                  Auto-assign Tables
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Student Lists/Tables */}
      {viewMode === 'tables' && roundTablesCount > 0 ? renderTableView() : renderListView()}
    </div>
  );
};

export default StudentRosterManager;
