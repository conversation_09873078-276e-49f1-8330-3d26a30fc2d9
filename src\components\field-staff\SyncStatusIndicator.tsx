import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Wifi, 
  WifiOff, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  Database,
  Zap,
  AlertCircle
} from 'lucide-react';
import { useOfflineSync } from '@/hooks/field-staff/useOfflineSync';
import { toast } from 'sonner';

interface SyncStatusIndicatorProps {
  showDetails?: boolean;
  compact?: boolean;
  className?: string;
}

const SyncStatusIndicator: React.FC<SyncStatusIndicatorProps> = ({
  showDetails = false,
  compact = false,
  className = '',
}) => {
  const [showConflicts, setShowConflicts] = useState(false);
  const { 
    syncStatus, 
    conflicts, 
    syncOfflineData, 
    clearOfflineData, 
    resolveConflict,
    getSyncStats 
  } = useOfflineSync();

  const stats = getSyncStats();

  const handleManualSync = async () => {
    try {
      await syncOfflineData();
      toast.success('Manual sync completed');
    } catch (error) {
      toast.error('Manual sync failed');
    }
  };

  const handleClearData = () => {
    if (window.confirm('Are you sure you want to clear all offline data? This cannot be undone.')) {
      clearOfflineData();
    }
  };

  const getStatusColor = () => {
    if (!syncStatus.isOnline) return 'bg-red-500';
    if (syncStatus.isSyncing) return 'bg-blue-500';
    if (syncStatus.conflictItems > 0) return 'bg-yellow-500';
    if (syncStatus.failedItems > 0) return 'bg-orange-500';
    if (syncStatus.pendingItems > 0) return 'bg-blue-400';
    return 'bg-green-500';
  };

  const getStatusText = () => {
    if (!syncStatus.isOnline) return 'Offline';
    if (syncStatus.isSyncing) return 'Syncing...';
    if (syncStatus.conflictItems > 0) return 'Conflicts';
    if (syncStatus.failedItems > 0) return 'Sync Issues';
    if (syncStatus.pendingItems > 0) return 'Pending Sync';
    return 'Synced';
  };

  const formatLastSync = () => {
    if (!syncStatus.lastSyncTime) return 'Never';
    const now = new Date();
    const diff = now.getTime() - syncStatus.lastSyncTime.getTime();
    const minutes = Math.floor(diff / 60000);
    
    if (minutes < 1) return 'Just now';
    if (minutes < 60) return `${minutes}m ago`;
    const hours = Math.floor(minutes / 60);
    if (hours < 24) return `${hours}h ago`;
    const days = Math.floor(hours / 24);
    return `${days}d ago`;
  };

  if (compact) {
    return (
      <div className={`flex items-center gap-2 ${className}`}>
        <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
        <span className="text-sm text-gray-600">{getStatusText()}</span>
        {syncStatus.pendingItems > 0 && (
          <Badge variant="secondary" className="text-xs">
            {syncStatus.pendingItems}
          </Badge>
        )}
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {syncStatus.isOnline ? (
              <Wifi className="h-4 w-4 text-green-500" />
            ) : (
              <WifiOff className="h-4 w-4 text-red-500" />
            )}
            <CardTitle className="text-sm">Sync Status</CardTitle>
          </div>
          <div className={`w-3 h-3 rounded-full ${getStatusColor()}`} />
        </div>
        <CardDescription className="text-xs">
          {getStatusText()} • Last sync: {formatLastSync()}
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Sync Progress */}
        {syncStatus.isSyncing && (
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Syncing data...</span>
              <span>{syncStatus.syncProgress}%</span>
            </div>
            <Progress value={syncStatus.syncProgress} className="h-2" />
          </div>
        )}

        {/* Status Summary */}
        <div className="grid grid-cols-2 gap-2 text-xs">
          <div className="flex items-center gap-1">
            <Database className="h-3 w-3 text-blue-500" />
            <span>{syncStatus.pendingItems} pending</span>
          </div>
          <div className="flex items-center gap-1">
            <CheckCircle className="h-3 w-3 text-green-500" />
            <span>Last: {formatLastSync()}</span>
          </div>
          {syncStatus.failedItems > 0 && (
            <div className="flex items-center gap-1">
              <AlertTriangle className="h-3 w-3 text-orange-500" />
              <span>{syncStatus.failedItems} failed</span>
            </div>
          )}
          {syncStatus.conflictItems > 0 && (
            <div className="flex items-center gap-1">
              <AlertCircle className="h-3 w-3 text-yellow-500" />
              <span>{syncStatus.conflictItems} conflicts</span>
            </div>
          )}
        </div>

        {/* Alerts */}
        {!syncStatus.isOnline && (
          <Alert variant="destructive">
            <WifiOff className="h-4 w-4" />
            <AlertDescription className="text-xs">
              Device is offline. Data will be saved locally and synced when connection is restored.
            </AlertDescription>
          </Alert>
        )}

        {syncStatus.conflictItems > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              {syncStatus.conflictItems} sync conflicts need resolution.
              <Button 
                variant="link" 
                size="sm" 
                className="p-0 h-auto text-xs"
                onClick={() => setShowConflicts(!showConflicts)}
              >
                View conflicts
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <Button 
            size="sm" 
            variant="outline" 
            onClick={handleManualSync}
            disabled={syncStatus.isSyncing || !syncStatus.isOnline}
            className="text-xs"
          >
            <RefreshCw className={`h-3 w-3 mr-1 ${syncStatus.isSyncing ? 'animate-spin' : ''}`} />
            Sync Now
          </Button>
          
          {process.env.NODE_ENV === 'development' && (
            <Button 
              size="sm" 
              variant="ghost" 
              onClick={handleClearData}
              className="text-xs text-red-600"
            >
              Clear Data
            </Button>
          )}
        </div>

        {/* Detailed Stats */}
        {showDetails && (
          <div className="pt-2 border-t space-y-2">
            <h4 className="text-xs font-medium">Sync Statistics</h4>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div>Total pending: {stats.totalPending}</div>
              <div>High priority: {stats.highPriority}</div>
              <div>Failed items: {stats.failed}</div>
              <div>Conflicts: {stats.conflicts}</div>
              {stats.oldestItem && (
                <div className="col-span-2">
                  Oldest: {new Date(stats.oldestItem).toLocaleString()}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Conflict Resolution */}
        {showConflicts && conflicts.length > 0 && (
          <div className="pt-2 border-t space-y-2">
            <h4 className="text-xs font-medium">Sync Conflicts</h4>
            {conflicts.slice(0, 3).map((conflict) => (
              <div key={conflict.id} className="p-2 bg-yellow-50 rounded text-xs">
                <div className="font-medium">Data conflict detected</div>
                <div className="text-gray-600 mb-2">
                  Fields: {conflict.conflictFields.join(', ')}
                </div>
                <div className="flex gap-1">
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => resolveConflict(conflict.id, 'CLIENT')}
                    className="text-xs h-6"
                  >
                    Use Local
                  </Button>
                  <Button 
                    size="sm" 
                    variant="outline" 
                    onClick={() => resolveConflict(conflict.id, 'SERVER')}
                    className="text-xs h-6"
                  >
                    Use Server
                  </Button>
                </div>
              </div>
            ))}
            {conflicts.length > 3 && (
              <div className="text-xs text-gray-500">
                +{conflicts.length - 3} more conflicts
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default SyncStatusIndicator;
