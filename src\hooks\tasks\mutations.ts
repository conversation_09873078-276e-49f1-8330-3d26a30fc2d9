
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { Database } from '@/integrations/supabase/types';
import { TaskFormData } from './types';

// Hook for creating a new task
export const useCreateTask = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (taskData: TaskFormData) => {
      console.log('🔨 Creating task with data:', taskData);
      
      const { data, error } = await supabase
        .rpc('create_task', {
          p_title: taskData.title,
          p_description: taskData.description || null,
          p_priority: taskData.priority,
          p_due_date: taskData.due_date ? taskData.due_date.toISOString() : null,
          p_assigned_to: taskData.assigned_to || null,
          p_school_id: taskData.school_id || null
        });

      if (error) {
        console.error('❌ Error creating task:', error);
        throw error;
      }
      
      console.log('✅ Task created successfully:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['my-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['managed-tasks'] });
      console.log('✅ Task queries invalidated after creation');
      toast({
        title: "Success",
        description: "Task created successfully",
      });
    },
    onError: (error: Error) => {
      console.error('❌ Task creation failed:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to create task",
        variant: "destructive",
      });
    },
  });
};

// Hook for updating task status
export const useUpdateTaskStatus = () => {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ taskId, status }: { taskId: string; status: Database['public']['Enums']['task_status'] }) => {
      console.log('🔄 Updating task status:', taskId, 'to', status);
      
      const { data, error } = await supabase
        .from('tasks')
        .update({ status })
        .eq('id', taskId)
        .select();

      if (error) {
        console.error('❌ Error updating task status:', error);
        throw error;
      }
      
      console.log('✅ Task status updated successfully:', data);
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tasks'] });
      queryClient.invalidateQueries({ queryKey: ['my-tasks'] });
      queryClient.invalidateQueries({ queryKey: ['managed-tasks'] });
      console.log('✅ Task queries invalidated after status update');
      toast({
        title: "Success",
        description: "Task status updated successfully",
      });
    },
    onError: (error: Error) => {
      console.error('❌ Task status update failed:', error);
      toast({
        title: "Error",
        description: error.message || "Failed to update task status",
        variant: "destructive",
      });
    },
  });
};
