// Test utilities for field staff functionality
// This file provides utilities to test the enhanced GPS, error handling, and offline sync features

import { FieldOperationError, ErrorHandler } from './errorHandling';

// Mock GPS position for testing
export const createMockGPSPosition = (
  latitude: number = 0.3476,
  longitude: number = 32.5825,
  accuracy: number = 10
): GeolocationPosition => ({
  coords: {
    latitude,
    longitude,
    accuracy,
    altitude: null,
    altitudeAccuracy: null,
    heading: null,
    speed: null,
  },
  timestamp: Date.now(),
});

// Mock GPS error for testing
export const createMockGPSError = (code: number): GeolocationPositionError => ({
  code,
  message: `Mock GPS error ${code}`,
  PERMISSION_DENIED: 1,
  POSITION_UNAVAILABLE: 2,
  TIMEOUT: 3,
});

// Test GPS performance optimization
export const testGPSPerformance = async () => {
  console.log('🧪 Testing GPS Performance Optimization...');
  
  const results = {
    adaptivePolling: false,
    batteryOptimization: false,
    movementDetection: false,
    errorRecovery: false,
  };

  try {
    // Test adaptive polling
    const startTime = Date.now();
    // Simulate stationary state
    await new Promise(resolve => setTimeout(resolve, 1000));
    const endTime = Date.now();
    
    if (endTime - startTime >= 1000) {
      results.adaptivePolling = true;
      console.log('✅ Adaptive polling working');
    }

    // Test battery optimization
    if ('getBattery' in navigator) {
      try {
        const battery = await (navigator as Navigator & { getBattery(): Promise<{ level: number }> }).getBattery();
        if (battery.level !== undefined) {
          results.batteryOptimization = true;
          console.log('✅ Battery optimization available');
        }
      } catch (error) {
        console.log('⚠️ Battery API not available');
      }
    }

    // Test movement detection
    const pos1 = createMockGPSPosition(0.3476, 32.5825);
    const pos2 = createMockGPSPosition(0.3477, 32.5826);
    
    // Calculate distance (should be > 0)
    const R = 6371e3;
    const φ1 = (pos1.coords.latitude * Math.PI) / 180;
    const φ2 = (pos2.coords.latitude * Math.PI) / 180;
    const Δφ = ((pos2.coords.latitude - pos1.coords.latitude) * Math.PI) / 180;
    const Δλ = ((pos2.coords.longitude - pos1.coords.longitude) * Math.PI) / 180;
    
    const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
              Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    if (distance > 0) {
      results.movementDetection = true;
      console.log('✅ Movement detection working');
    }

    // Test error recovery
    const mockError = createMockGPSError(3); // Timeout error
    const fieldError = FieldOperationError.fromGPSError(mockError);
    
    if (fieldError.recovery?.type === 'RETRY') {
      results.errorRecovery = true;
      console.log('✅ Error recovery mechanisms working');
    }

  } catch (error) {
    console.error('❌ GPS performance test failed:', error);
  }

  return results;
};

// Test error handling enhancement
export const testErrorHandling = async () => {
  console.log('🧪 Testing Error Handling Enhancement...');
  
  const results = {
    gpsErrorHandling: false,
    networkErrorHandling: false,
    retryMechanism: false,
    userFeedback: false,
  };

  try {
    // Test GPS error handling
    const gpsError = createMockGPSError(1); // Permission denied
    const fieldError = FieldOperationError.fromGPSError(gpsError);
    
    if (fieldError.fieldError.code === 'GPS_001' && !fieldError.fieldError.recoverable) {
      results.gpsErrorHandling = true;
      console.log('✅ GPS error handling working');
    }

    // Test network error handling
    const networkError = new Error('Network timeout');
    const networkFieldError = FieldOperationError.fromNetworkError(networkError, true);
    
    if (networkFieldError.recovery?.type === 'RETRY') {
      results.networkErrorHandling = true;
      console.log('✅ Network error handling working');
    }

    // Test retry mechanism
    if (networkFieldError.recovery?.maxAttempts && networkFieldError.recovery.maxAttempts > 1) {
      results.retryMechanism = true;
      console.log('✅ Retry mechanism working');
    }

    // Test user feedback
    if (fieldError.recovery?.userPrompt) {
      results.userFeedback = true;
      console.log('✅ User feedback working');
    }

  } catch (error) {
    console.error('❌ Error handling test failed:', error);
  }

  return results;
};

// Test offline sync reliability
export const testOfflineSync = async () => {
  console.log('🧪 Testing Offline Sync Reliability...');
  
  const results = {
    dataIntegrity: false,
    conflictDetection: false,
    priorityQueue: false,
    adaptiveRetry: false,
  };

  try {
    // Test data integrity (checksum)
    const testData = { id: '123', name: 'test', timestamp: Date.now() };
    const checksum1 = generateTestChecksum(testData);
    const checksum2 = generateTestChecksum(testData);
    
    if (checksum1 === checksum2) {
      results.dataIntegrity = true;
      console.log('✅ Data integrity checks working');
    }

    // Test conflict detection
    const localData = { id: '123', name: 'local', updated_at: '2024-01-01' };
    const serverData = { id: '123', name: 'server', updated_at: '2024-01-02' };
    
    const hasConflict = detectTestConflict(localData, serverData);
    if (hasConflict) {
      results.conflictDetection = true;
      console.log('✅ Conflict detection working');
    }

    // Test priority queue
    const items = [
      { priority: 'LOW', timestamp: 1 },
      { priority: 'HIGH', timestamp: 2 },
      { priority: 'CRITICAL', timestamp: 3 },
    ];
    
    const sorted = sortByPriority(items);
    if (sorted[0].priority === 'CRITICAL') {
      results.priorityQueue = true;
      console.log('✅ Priority queue working');
    }

    // Test adaptive retry
    const retryDelay = calculateTestBackoff(3, 1000);
    if (retryDelay > 1000) {
      results.adaptiveRetry = true;
      console.log('✅ Adaptive retry working');
    }

  } catch (error) {
    console.error('❌ Offline sync test failed:', error);
  }

  return results;
};

// Helper functions for testing
const generateTestChecksum = (data: Record<string, unknown>): string => {
  const str = JSON.stringify(data, Object.keys(data).sort());
  let hash = 0;
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash;
  }
  return hash.toString(36);
};

const detectTestConflict = (local: Record<string, unknown>, server: Record<string, unknown>): boolean => {
  const localTime = new Date(local.updated_at as string).getTime();
  const serverTime = new Date(server.updated_at as string).getTime();
  
  return serverTime > localTime && local.name !== server.name;
};

const sortByPriority = (items: Array<{ priority: string; timestamp: number }>) => {
  const priorityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
  return items.sort((a, b) => {
    const priorityDiff = (priorityOrder as Record<string, number>)[b.priority] - (priorityOrder as Record<string, number>)[a.priority];
    return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
  });
};

const calculateTestBackoff = (attempt: number, baseDelay: number): number => {
  return Math.min(baseDelay * Math.pow(2, attempt), 30000);
};

// Run all tests
export const runFieldStaffTests = async () => {
  console.log('🚀 Running Field Staff Enhancement Tests...');
  
  const gpsResults = await testGPSPerformance();
  const errorResults = await testErrorHandling();
  const syncResults = await testOfflineSync();
  
  const allResults = {
    gps: gpsResults,
    errorHandling: errorResults,
    offlineSync: syncResults,
  };
  
  // Calculate overall success rate
  const totalTests = Object.values(allResults).reduce((acc, category) => 
    acc + Object.keys(category).length, 0
  );
  
  const passedTests = Object.values(allResults).reduce((acc, category) => 
    acc + Object.values(category).filter(Boolean).length, 0
  );
  
  const successRate = Math.round((passedTests / totalTests) * 100);
  
  console.log(`\n📊 Test Results Summary:`);
  console.log(`✅ Passed: ${passedTests}/${totalTests} (${successRate}%)`);
  console.log(`🎯 GPS Performance: ${Object.values(gpsResults).filter(Boolean).length}/${Object.keys(gpsResults).length}`);
  console.log(`🛡️ Error Handling: ${Object.values(errorResults).filter(Boolean).length}/${Object.keys(errorResults).length}`);
  console.log(`🔄 Offline Sync: ${Object.values(syncResults).filter(Boolean).length}/${Object.keys(syncResults).length}`);
  
  if (successRate >= 80) {
    console.log('🎉 Field staff enhancements are working well!');
  } else if (successRate >= 60) {
    console.log('⚠️ Field staff enhancements need some attention');
  } else {
    console.log('❌ Field staff enhancements need significant work');
  }
  
  return allResults;
};

// Export for use in development console
if (typeof window !== 'undefined') {
  (window as Window & { fieldStaffTests: Record<string, unknown> }).fieldStaffTests = {
    runAll: runFieldStaffTests,
    testGPS: testGPSPerformance,
    testErrors: testErrorHandling,
    testSync: testOfflineSync,
  };
}
