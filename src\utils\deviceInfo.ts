/**
 * Device Information Utilities
 * 
 * Provides functions to collect device and network information
 * for field staff attendance tracking and system diagnostics.
 */

export interface DeviceInfo {
  userAgent: string;
  platform: string;
  language: string;
  cookieEnabled: boolean;
  onLine: boolean;
  timestamp: number;
  screen?: {
    width: number;
    height: number;
    colorDepth: number;
  };
}

export interface NetworkInfo {
  effectiveType: string;
  downlink: number;
  rtt: number;
  saveData: boolean;
  online: boolean;
  timestamp: number;
}

/**
 * Collects comprehensive device information
 * @returns DeviceInfo object with browser and device details
 */
export const getDeviceInfo = (): DeviceInfo => {
  return {
    userAgent: navigator.userAgent,
    platform: navigator.platform,
    language: navigator.language,
    cookieEnabled: navigator.cookieEnabled,
    onLine: navigator.onLine,
    timestamp: Date.now(),
    screen: {
      width: screen.width,
      height: screen.height,
      colorDepth: screen.colorDepth,
    },
  };
};

/**
 * Collects network connection information
 * @returns NetworkInfo object with connection details
 */
export const getNetworkInfo = (): NetworkInfo => {
  // Access the Network Information API with proper typing
  const navigatorWithConnection = navigator as Navigator & {
    connection?: {
      effectiveType?: string;
      downlink?: number;
      rtt?: number;
      saveData?: boolean;
    };
    mozConnection?: {
      effectiveType?: string;
      downlink?: number;
      rtt?: number;
      saveData?: boolean;
    };
    webkitConnection?: {
      effectiveType?: string;
      downlink?: number;
      rtt?: number;
      saveData?: boolean;
    };
  };

  const connection = navigatorWithConnection.connection ||
                    navigatorWithConnection.mozConnection ||
                    navigatorWithConnection.webkitConnection;
  
  return {
    effectiveType: connection?.effectiveType || 'unknown',
    downlink: connection?.downlink || 0,
    rtt: connection?.rtt || 0,
    saveData: connection?.saveData || false,
    online: navigator.onLine,
    timestamp: Date.now(),
  };
};

/**
 * Gets a simplified device fingerprint for tracking purposes
 * @returns string representing a basic device fingerprint
 */
export const getDeviceFingerprint = (): string => {
  const deviceInfo = getDeviceInfo();
  const networkInfo = getNetworkInfo();
  
  const fingerprint = [
    deviceInfo.platform,
    deviceInfo.language,
    deviceInfo.screen?.width,
    deviceInfo.screen?.height,
    networkInfo.effectiveType,
  ].join('|');
  
  // Create a simple hash of the fingerprint
  let hash = 0;
  for (let i = 0; i < fingerprint.length; i++) {
    const char = fingerprint.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  
  return Math.abs(hash).toString(36);
};

/**
 * Checks if the device supports required features for field staff operations
 * @returns object indicating feature support
 */
export const checkDeviceCapabilities = () => {
  return {
    geolocation: 'geolocation' in navigator,
    camera: 'mediaDevices' in navigator && 'getUserMedia' in navigator.mediaDevices,
    storage: 'localStorage' in window,
    serviceWorker: 'serviceWorker' in navigator,
    notifications: 'Notification' in window,
    online: navigator.onLine,
    touchScreen: 'ontouchstart' in window || navigator.maxTouchPoints > 0,
    accelerometer: 'DeviceMotionEvent' in window,
    orientation: 'DeviceOrientationEvent' in window,
  };
};

/**
 * Gets battery information if available
 * @returns Promise with battery info or null if not supported
 */
export const getBatteryInfo = async (): Promise<{
  charging: boolean;
  level: number;
  chargingTime: number;
  dischargingTime: number;
} | null> => {
  try {
    const navigatorWithBattery = navigator as Navigator & {
      getBattery?: () => Promise<{
        charging: boolean;
        level: number;
        chargingTime: number;
        dischargingTime: number;
      }>;
    };

    if ('getBattery' in navigator && navigatorWithBattery.getBattery) {
      const battery = await navigatorWithBattery.getBattery();
      return {
        charging: battery.charging,
        level: battery.level,
        chargingTime: battery.chargingTime,
        dischargingTime: battery.dischargingTime,
      };
    }
  } catch (error) {
    console.warn('Battery API not available:', error);
  }
  return null;
};

/**
 * Formats device info for display purposes
 * @param deviceInfo - Device information object
 * @returns formatted string for display
 */
export const formatDeviceInfo = (deviceInfo: DeviceInfo): string => {
  const parts = [
    deviceInfo.platform,
    deviceInfo.language,
    `${deviceInfo.screen?.width}x${deviceInfo.screen?.height}`,
    deviceInfo.onLine ? 'Online' : 'Offline'
  ];
  
  return parts.filter(Boolean).join(' • ');
};

/**
 * Formats network info for display purposes
 * @param networkInfo - Network information object
 * @returns formatted string for display
 */
export const formatNetworkInfo = (networkInfo: NetworkInfo): string => {
  const parts = [
    networkInfo.effectiveType !== 'unknown' ? networkInfo.effectiveType.toUpperCase() : 'Unknown',
    networkInfo.downlink > 0 ? `${networkInfo.downlink} Mbps` : '',
    networkInfo.rtt > 0 ? `${networkInfo.rtt}ms RTT` : '',
    networkInfo.online ? 'Connected' : 'Disconnected'
  ];
  
  return parts.filter(Boolean).join(' • ');
};

/**
 * Collects comprehensive system information for diagnostics
 * @returns Promise with complete system information
 */
export const getSystemInfo = async () => {
  const deviceInfo = getDeviceInfo();
  const networkInfo = getNetworkInfo();
  const capabilities = checkDeviceCapabilities();
  const batteryInfo = await getBatteryInfo();
  
  return {
    device: deviceInfo,
    network: networkInfo,
    capabilities,
    battery: batteryInfo,
    timestamp: Date.now(),
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    locale: Intl.DateTimeFormat().resolvedOptions().locale,
  };
};
