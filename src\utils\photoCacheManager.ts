/**
 * Photo cache management system for field report photos
 * Handles storage limits, cleanup strategies, and cache optimization
 */

export interface PhotoCacheItem {
  id: string;
  blob: Blob;
  fileName: string;
  timestamp: number;
  size: number;
  fieldReportId?: string;
  uploadStatus: 'pending' | 'uploading' | 'uploaded' | 'failed';
  lastAccessed: number;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
}

export interface CacheStats {
  totalItems: number;
  totalSize: number;
  availableSpace: number;
  oldestItem: number;
  newestItem: number;
  pendingUploads: number;
  failedUploads: number;
}

const CACHE_DB_NAME = 'field_staff_photo_cache';
const CACHE_DB_VERSION = 1;
const CACHE_STORE_NAME = 'photos';
const MAX_CACHE_SIZE = 50 * 1024 * 1024; // 50MB max cache size
const MAX_CACHE_ITEMS = 200; // Maximum number of cached photos
const CACHE_CLEANUP_THRESHOLD = 0.8; // Clean up when 80% full
const MAX_ITEM_AGE = 7 * 24 * 60 * 60 * 1000; // 7 days in milliseconds

class PhotoCacheManager {
  private db: IDBDatabase | null = null;
  private isInitialized = false;

  /**
   * Initialize the cache database
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    return new Promise((resolve, reject) => {
      const request = indexedDB.open(CACHE_DB_NAME, CACHE_DB_VERSION);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        this.db = request.result;
        this.isInitialized = true;
        resolve();
      };

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        
        if (!db.objectStoreNames.contains(CACHE_STORE_NAME)) {
          const store = db.createObjectStore(CACHE_STORE_NAME, { keyPath: 'id' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
          store.createIndex('lastAccessed', 'lastAccessed', { unique: false });
          store.createIndex('uploadStatus', 'uploadStatus', { unique: false });
          store.createIndex('priority', 'priority', { unique: false });
        }
      };
    });
  }

  /**
   * Add photo to cache
   */
  async addPhoto(
    id: string,
    blob: Blob,
    fileName: string,
    options: {
      fieldReportId?: string;
      priority?: PhotoCacheItem['priority'];
      uploadStatus?: PhotoCacheItem['uploadStatus'];
    } = {}
  ): Promise<void> {
    await this.initialize();
    if (!this.db) throw new Error('Cache database not initialized');

    const now = Date.now();
    const cacheItem: PhotoCacheItem = {
      id,
      blob,
      fileName,
      timestamp: now,
      size: blob.size,
      fieldReportId: options.fieldReportId,
      uploadStatus: options.uploadStatus || 'pending',
      lastAccessed: now,
      priority: options.priority || 'MEDIUM',
    };

    // Check if cache cleanup is needed before adding
    const stats = await this.getCacheStats();
    if (this.shouldCleanup(stats, blob.size)) {
      await this.performCleanup();
    }

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readwrite');
    const store = transaction.objectStore(CACHE_STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.put(cacheItem);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get photo from cache
   */
  async getPhoto(id: string): Promise<PhotoCacheItem | null> {
    await this.initialize();
    if (!this.db) return null;

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readwrite');
    const store = transaction.objectStore(CACHE_STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.get(id);
      request.onsuccess = () => {
        const result = request.result;
        if (result) {
          // Update last accessed time
          result.lastAccessed = Date.now();
          store.put(result);
        }
        resolve(result || null);
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Update photo status
   */
  async updatePhotoStatus(
    id: string,
    status: PhotoCacheItem['uploadStatus']
  ): Promise<void> {
    await this.initialize();
    if (!this.db) return;

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readwrite');
    const store = transaction.objectStore(CACHE_STORE_NAME);

    return new Promise((resolve, reject) => {
      const getRequest = store.get(id);
      getRequest.onsuccess = () => {
        const item = getRequest.result;
        if (item) {
          item.uploadStatus = status;
          item.lastAccessed = Date.now();
          const putRequest = store.put(item);
          putRequest.onsuccess = () => resolve();
          putRequest.onerror = () => reject(putRequest.error);
        } else {
          resolve(); // Item not found, nothing to update
        }
      };
      getRequest.onerror = () => reject(getRequest.error);
    });
  }

  /**
   * Remove photo from cache
   */
  async removePhoto(id: string): Promise<void> {
    await this.initialize();
    if (!this.db) return;

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readwrite');
    const store = transaction.objectStore(CACHE_STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.delete(id);
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get cache statistics
   */
  async getCacheStats(): Promise<CacheStats> {
    await this.initialize();
    if (!this.db) {
      return {
        totalItems: 0,
        totalSize: 0,
        availableSpace: MAX_CACHE_SIZE,
        oldestItem: 0,
        newestItem: 0,
        pendingUploads: 0,
        failedUploads: 0,
      };
    }

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readonly');
    const store = transaction.objectStore(CACHE_STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => {
        const items: PhotoCacheItem[] = request.result;
        
        const totalSize = items.reduce((sum, item) => sum + item.size, 0);
        const timestamps = items.map(item => item.timestamp);
        const pendingUploads = items.filter(item => item.uploadStatus === 'pending' || item.uploadStatus === 'uploading').length;
        const failedUploads = items.filter(item => item.uploadStatus === 'failed').length;

        resolve({
          totalItems: items.length,
          totalSize,
          availableSpace: MAX_CACHE_SIZE - totalSize,
          oldestItem: timestamps.length > 0 ? Math.min(...timestamps) : 0,
          newestItem: timestamps.length > 0 ? Math.max(...timestamps) : 0,
          pendingUploads,
          failedUploads,
        });
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Check if cleanup is needed
   */
  private shouldCleanup(stats: CacheStats, newItemSize: number): boolean {
    const wouldExceedSize = (stats.totalSize + newItemSize) > (MAX_CACHE_SIZE * CACHE_CLEANUP_THRESHOLD);
    const wouldExceedCount = stats.totalItems >= MAX_CACHE_ITEMS;
    
    return wouldExceedSize || wouldExceedCount;
  }

  /**
   * Perform cache cleanup
   */
  async performCleanup(): Promise<void> {
    await this.initialize();
    if (!this.db) return;

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readwrite');
    const store = transaction.objectStore(CACHE_STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.getAll();
      request.onsuccess = () => {
        const items: PhotoCacheItem[] = request.result;
        const now = Date.now();

        // Sort items by cleanup priority (oldest, uploaded, lowest priority first)
        const sortedItems = items.sort((a, b) => {
          // Don't remove pending or uploading items
          if (a.uploadStatus === 'pending' || a.uploadStatus === 'uploading') return 1;
          if (b.uploadStatus === 'pending' || b.uploadStatus === 'uploading') return -1;

          // Remove uploaded items before failed ones
          if (a.uploadStatus === 'uploaded' && b.uploadStatus === 'failed') return -1;
          if (a.uploadStatus === 'failed' && b.uploadStatus === 'uploaded') return 1;

          // Remove older items first
          if (a.lastAccessed !== b.lastAccessed) {
            return a.lastAccessed - b.lastAccessed;
          }

          // Remove lower priority items first
          const priorityOrder = { LOW: 1, MEDIUM: 2, HIGH: 3, CRITICAL: 4 };
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        });

        // Remove items until we're under the threshold
        const targetSize = MAX_CACHE_SIZE * 0.6; // Clean to 60% capacity
        const targetCount = Math.floor(MAX_CACHE_ITEMS * 0.8); // Clean to 80% count
        
        let currentSize = items.reduce((sum, item) => sum + item.size, 0);
        let currentCount = items.length;
        const itemsToRemove: string[] = [];

        for (const item of sortedItems) {
          if (currentSize <= targetSize && currentCount <= targetCount) {
            break;
          }

          // Don't remove items that are too new or currently uploading
          if (
            (now - item.timestamp) < (24 * 60 * 60 * 1000) || // Less than 1 day old
            item.uploadStatus === 'pending' ||
            item.uploadStatus === 'uploading'
          ) {
            continue;
          }

          itemsToRemove.push(item.id);
          currentSize -= item.size;
          currentCount--;
        }

        // Remove old items (older than MAX_ITEM_AGE)
        for (const item of items) {
          if (
            (now - item.timestamp) > MAX_ITEM_AGE &&
            item.uploadStatus === 'uploaded'
          ) {
            itemsToRemove.push(item.id);
          }
        }

        // Execute removals
        let removedCount = 0;
        const removeNext = () => {
          if (removedCount >= itemsToRemove.length) {
            resolve();
            return;
          }

          const deleteRequest = store.delete(itemsToRemove[removedCount]);
          deleteRequest.onsuccess = () => {
            removedCount++;
            removeNext();
          };
          deleteRequest.onerror = () => reject(deleteRequest.error);
        };

        if (itemsToRemove.length > 0) {
          removeNext();
        } else {
          resolve();
        }
      };
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Get pending uploads
   */
  async getPendingUploads(): Promise<PhotoCacheItem[]> {
    await this.initialize();
    if (!this.db) return [];

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readonly');
    const store = transaction.objectStore(CACHE_STORE_NAME);
    const index = store.index('uploadStatus');

    return new Promise((resolve, reject) => {
      const request = index.getAll('pending');
      request.onsuccess = () => resolve(request.result);
      request.onerror = () => reject(request.error);
    });
  }

  /**
   * Clear all cache data
   */
  async clearCache(): Promise<void> {
    await this.initialize();
    if (!this.db) return;

    const transaction = this.db.transaction([CACHE_STORE_NAME], 'readwrite');
    const store = transaction.objectStore(CACHE_STORE_NAME);

    return new Promise((resolve, reject) => {
      const request = store.clear();
      request.onsuccess = () => resolve();
      request.onerror = () => reject(request.error);
    });
  }
}

// Export singleton instance
export const photoCacheManager = new PhotoCacheManager();

// Initialize on module load
photoCacheManager.initialize().catch(console.error);

// Periodic cleanup
setInterval(() => {
  photoCacheManager.performCleanup().catch(console.error);
}, 60 * 60 * 1000); // Every hour
