
import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { TrendingUp } from 'lucide-react';

const Performance = () => {
  return (
    <div className="p-6">
      <div className="flex items-center mb-6">
        <TrendingUp className="h-6 w-6 mr-2 text-ilead-green" />
        <h1 className="text-2xl font-bold text-gray-900">Performance Reports</h1>
      </div>
      
      <Card>
        <CardHeader>
          <CardTitle>Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600">Performance reports and metrics will be displayed here.</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default Performance;
