
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { SchoolFormData } from '@/types/school';

interface AcademicStructureSectionProps {
  formData: SchoolFormData;
  onFormDataChange: (data: SchoolFormData) => void;
}

const AcademicStructureSection = ({ formData, onFormDataChange }: AcademicStructureSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Academic Structure</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="space-y-2">
          <Label htmlFor="student_count">Total Students (Optional)</Label>
          <Input
            id="student_count"
            type="number"
            value={formData.student_count || ''}
            onChange={(e) => onFormDataChange({ ...formData, student_count: e.target.value })}
            placeholder="Number of students"
            min="0"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="teacher_count">Total Teachers (Optional)</Label>
          <Input
            id="teacher_count"
            type="number"
            value={formData.teacher_count || ''}
            onChange={(e) => onFormDataChange({ ...formData, teacher_count: e.target.value })}
            placeholder="Number of teachers"
            min="0"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="classes_count">Number of Classes</Label>
          <Input
            id="classes_count"
            type="number"
            value={formData.classes_count}
            onChange={(e) => onFormDataChange({ ...formData, classes_count: e.target.value })}
            placeholder="e.g., 7 (P1-P7)"
            min="1"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="streams_per_class">Streams per Class</Label>
          <Input
            id="streams_per_class"
            type="number"
            value={formData.streams_per_class}
            onChange={(e) => onFormDataChange({ ...formData, streams_per_class: e.target.value })}
            placeholder="e.g., 2 (A, B)"
            min="1"
          />
        </div>
      </div>
    </div>
  );
};

export default AcademicStructureSection;
