
import React from 'react';
import { Calendar } from 'lucide-react';
import { useAuth } from '@/hooks/useAuth';
import TaskList from '../TaskList';
import { useOverdueTasks } from '@/hooks/tasks';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

type Task = {
  id: string;
  title: string;
  description: string | null;
  priority: Database['public']['Enums']['task_priority'];
  status: Database['public']['Enums']['task_status'];
  due_date: string | null;
  assigned_to: string | null;
  assigned_to_name: string | null;
  created_by: string;
  created_by_name: string;
  school_id: string | null;
  school_name: string | null;
  created_at: string;
  updated_at: string;
  comment_count: number;
};

const OverdueTasks = () => {
  const { profile } = useAuth();

  // Use optimized hook for overdue tasks
  const { data: overdueTasks = [], isLoading, refetch } = useOverdueTasks();

  const handleViewDetails = (taskId: string) => {
    // This would open task details - for now just log
    console.log('View task details:', taskId);
  };

  const handleUpdateStatus = (taskId: string, status: Database['public']['Enums']['task_status']) => {
    // This would update task status - for now just log
    console.log('Update task status:', taskId, status);
  };

  return (
    <PageLayout>
      <PageHeader
        title="Overdue Tasks"
        description={`Tasks that have passed their due date (${overdueTasks.length} total)`}
        icon={Calendar}
      />

      {overdueTasks.length > 0 && (
        <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-700 text-sm">
            ⚠️ These tasks require immediate attention as they are past their due dates.
          </p>
        </div>
      )}

      <ContentCard noPadding>
        <TaskList
          tasks={overdueTasks}
          loading={isLoading}
          currentUserId={profile?.id}
          onViewDetails={handleViewDetails}
          onUpdateStatus={handleUpdateStatus}
        />
      </ContentCard>
    </PageLayout>
  );
};

export default OverdueTasks;
