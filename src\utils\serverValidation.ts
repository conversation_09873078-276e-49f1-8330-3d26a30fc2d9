import { supabase } from '@/integrations/supabase/client';
import { validateISBN } from './bookValidation';

export interface DuplicateCheckResult {
  isDuplicate: boolean;
  existingBook?: {
    id: string;
    title: string;
    author: string;
    isbn: string;
  };
  message?: string;
}

/**
 * Check if ISBN already exists in the database
 */
export async function checkISBNDuplicate(isbn: string, excludeBookId?: string): Promise<DuplicateCheckResult> {
  if (!isbn || isbn.trim() === '') {
    return { isDuplicate: false };
  }

  // Validate ISBN format first
  const isbnValidation = validateISBN(isbn);
  if (!isbnValidation.isValid) {
    return { 
      isDuplicate: false, 
      message: 'Invalid ISBN format - cannot check for duplicates' 
    };
  }

  try {
    // Clean the ISBN for comparison (remove hyphens and spaces)
    const cleanISBN = isbn.replace(/[-\s]/g, '');
    
    // Query the database for existing books with this ISBN
    let query = supabase
      .from('books')
      .select('id, title, author, isbn')
      .not('isbn', 'is', null);

    // Use ilike for case-insensitive partial matching to handle different ISBN formats
    query = query.or(`isbn.ilike.%${cleanISBN}%`);

    if (excludeBookId) {
      query = query.neq('id', excludeBookId);
    }

    const { data: existingBooks, error } = await query;

    if (error) {
      console.error('Error checking ISBN duplicate:', error);
      return { 
        isDuplicate: false, 
        message: 'Unable to check for duplicate ISBN' 
      };
    }

    // Check if any of the returned books have the exact same ISBN (after cleaning)
    const duplicateBook = existingBooks?.find(book => {
      if (!book.isbn) return false;
      const existingCleanISBN = book.isbn.replace(/[-\s]/g, '');
      return existingCleanISBN === cleanISBN;
    });

    if (duplicateBook) {
      return {
        isDuplicate: true,
        existingBook: duplicateBook,
        message: `ISBN already exists for "${duplicateBook.title}" by ${duplicateBook.author}`
      };
    }

    return { isDuplicate: false };

  } catch (error) {
    console.error('Error checking ISBN duplicate:', error);
    return { 
      isDuplicate: false, 
      message: 'Unable to check for duplicate ISBN' 
    };
  }
}

/**
 * Check if a book title and author combination already exists
 */
export async function checkBookDuplicate(title: string, author: string, excludeBookId?: string): Promise<DuplicateCheckResult> {
  if (!title || !author || title.trim() === '' || author.trim() === '') {
    return { isDuplicate: false };
  }

  try {
    let query = supabase
      .from('books')
      .select('id, title, author, isbn')
      .ilike('title', title.trim())
      .ilike('author', author.trim());

    if (excludeBookId) {
      query = query.neq('id', excludeBookId);
    }

    const { data: existingBooks, error } = await query;

    if (error) {
      console.error('Error checking book duplicate:', error);
      return { 
        isDuplicate: false, 
        message: 'Unable to check for duplicate book' 
      };
    }

    if (existingBooks && existingBooks.length > 0) {
      const duplicateBook = existingBooks[0];
      return {
        isDuplicate: true,
        existingBook: duplicateBook,
        message: `A book with this title and author already exists${duplicateBook.isbn ? ` (ISBN: ${duplicateBook.isbn})` : ''}`
      };
    }

    return { isDuplicate: false };

  } catch (error) {
    console.error('Error checking book duplicate:', error);
    return { 
      isDuplicate: false, 
      message: 'Unable to check for duplicate book' 
    };
  }
}

/**
 * Comprehensive server-side validation for book creation/update
 */
export async function validateBookServerSide(
  bookData: {
    title: string;
    author: string;
    isbn?: string;
  },
  excludeBookId?: string
): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Check for ISBN duplicate if ISBN is provided
    if (bookData.isbn && bookData.isbn.trim() !== '') {
      const isbnCheck = await checkISBNDuplicate(bookData.isbn, excludeBookId);
      if (isbnCheck.isDuplicate) {
        errors.push(isbnCheck.message || 'ISBN already exists');
      } else if (isbnCheck.message) {
        warnings.push(isbnCheck.message);
      }
    }

    // Check for title/author duplicate
    const bookCheck = await checkBookDuplicate(bookData.title, bookData.author, excludeBookId);
    if (bookCheck.isDuplicate) {
      warnings.push(bookCheck.message || 'Similar book already exists');
    } else if (bookCheck.message) {
      warnings.push(bookCheck.message);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };

  } catch (error) {
    console.error('Error in server-side validation:', error);
    return {
      isValid: false,
      errors: ['Unable to validate book data'],
      warnings: []
    };
  }
}

/**
 * Validate inventory constraints
 */
export async function validateInventoryConstraints(
  bookId: string,
  newTotalQuantity: number,
  newAvailableQuantity: number
): Promise<{
  isValid: boolean;
  errors: string[];
  warnings: string[];
}> {
  const errors: string[] = [];
  const warnings: string[] = [];

  try {
    // Get current distribution data for this book
    const { data: distributions, error: distError } = await supabase
      .from('book_distributions')
      .select('quantity, status')
      .eq('book_id', bookId)
      .in('status', ['planned', 'in_progress']);

    if (distError) {
      warnings.push('Unable to check distribution constraints');
    } else if (distributions && distributions.length > 0) {
      const plannedQuantity = distributions.reduce((sum, dist) => sum + dist.quantity, 0);
      
      if (newAvailableQuantity < plannedQuantity) {
        errors.push(`Cannot reduce available quantity below planned distributions (${plannedQuantity} books planned)`);
      }
    }

    // Check for reasonable quantity limits
    if (newTotalQuantity > 100000) {
      warnings.push('Total quantity seems very large - please verify');
    }

    if (newAvailableQuantity > newTotalQuantity) {
      errors.push('Available quantity cannot exceed total quantity');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };

  } catch (error) {
    console.error('Error validating inventory constraints:', error);
    return {
      isValid: false,
      errors: ['Unable to validate inventory constraints'],
      warnings: []
    };
  }
}

/**
 * Validate distribution request
 */
export async function validateDistributionRequest(
  bookId: string,
  requestedQuantity: number
): Promise<{
  isValid: boolean;
  errors: string[];
  availableQuantity: number;
}> {
  const errors: string[] = [];

  try {
    // Get current inventory for the book
    const { data: inventory, error: invError } = await supabase
      .from('book_inventory')
      .select('available_quantity')
      .eq('book_id', bookId)
      .maybeSingle();

    if (invError || !inventory) {
      return {
        isValid: false,
        errors: ['Unable to check book availability'],
        availableQuantity: 0
      };
    }

    const availableQuantity = inventory.available_quantity;

    if (requestedQuantity > availableQuantity) {
      errors.push(`Insufficient inventory. Available: ${availableQuantity}, Requested: ${requestedQuantity}`);
    }

    if (requestedQuantity <= 0) {
      errors.push('Requested quantity must be greater than 0');
    }

    return {
      isValid: errors.length === 0,
      errors,
      availableQuantity
    };

  } catch (error) {
    console.error('Error validating distribution request:', error);
    return {
      isValid: false,
      errors: ['Unable to validate distribution request'],
      availableQuantity: 0
    };
  }
}
