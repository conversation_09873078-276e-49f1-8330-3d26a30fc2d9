import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Bell, 
  Send, 
  Check, 
  X, 
  Clock, 
  AlertTriangle,
  MessageSquare,
  Filter,
  Search,
  Plus,
  Settings,
  BarChart3
} from 'lucide-react';
import { 
  useAttendanceNotifications, 
  useCreateAttendanceNotification,
  useMarkNotificationRead,
  useSendNotification,
  useNotificationStats
} from '@/hooks/attendance/useAttendanceNotifications';
import { useSchools } from '@/hooks/useSchools';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

interface AttendanceNotificationCenterProps {
  defaultSchoolId?: string;
}

const AttendanceNotificationCenter: React.FC<AttendanceNotificationCenterProps> = ({
  defaultSchoolId,
}) => {
  const { profile } = useAuth();
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || 'all');
  const [filterType, setFilterType] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showUnreadOnly, setShowUnreadOnly] = useState(false);

  const { data: schools } = useSchools();
  const { data: notifications, isLoading } = useAttendanceNotifications(
    selectedSchoolId || undefined,
    undefined,
    showUnreadOnly
  );
  const { data: stats } = useNotificationStats(selectedSchoolId || undefined);
  const createNotification = useCreateAttendanceNotification();
  const markAsRead = useMarkNotificationRead();
  const sendNotification = useSendNotification();

  // Filter notifications
  const filteredNotifications = notifications?.filter(notification => {
    if (filterType && filterType !== 'all' && notification.notification_type !== filterType) return false;
    if (filterStatus && filterStatus !== 'all' && notification.delivery_status !== filterStatus) return false;
    if (searchTerm && !notification.message_title.toLowerCase().includes(searchTerm.toLowerCase()) &&
        !notification.message_content.toLowerCase().includes(searchTerm.toLowerCase())) return false;
    return true;
  }) || [];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return <Check className="h-4 w-4 text-green-600" />;
      case 'failed':
        return <X className="h-4 w-4 text-red-600" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <MessageSquare className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
      case 'delivered':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'failed':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'normal':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'absence_alert':
        return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'late_alert':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'low_attendance':
        return <BarChart3 className="h-4 w-4 text-orange-600" />;
      case 'session_reminder':
        return <Bell className="h-4 w-4 text-blue-600" />;
      case 'check_in_confirmation':
      case 'check_out_confirmation':
        return <Check className="h-4 w-4 text-green-600" />;
      case 'attendance_summary':
        return <BarChart3 className="h-4 w-4 text-purple-600" />;
      default:
        return <MessageSquare className="h-4 w-4 text-gray-400" />;
    }
  };

  const handleMarkAsRead = async (notificationId: string) => {
    try {
      await markAsRead.mutateAsync(notificationId);
    } catch (error) {
      console.error('Failed to mark notification as read:', error);
    }
  };

  const handleSendNotification = async (notificationId: string) => {
    try {
      await sendNotification.mutateAsync(notificationId);
    } catch (error) {
      console.error('Failed to send notification:', error);
    }
  };

  return (
    <PageLayout>
      <PageHeader
        title="Attendance Notifications"
        description="Manage attendance alerts, reminders, and automated notifications"
      />

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.total || 0}
                </p>
                <p className="text-sm text-gray-600">Total Notifications</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <Bell className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-yellow-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.pending || 0}
                </p>
                <p className="text-sm text-gray-600">Pending</p>
              </div>
              <div className="bg-yellow-100 p-3 rounded-lg">
                <Clock className="h-6 w-6 text-yellow-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.recent_24h || 0}
                </p>
                <p className="text-sm text-gray-600">Last 24 Hours</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <Send className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-red-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats?.failed || 0}
                </p>
                <p className="text-sm text-gray-600">Failed</p>
              </div>
              <div className="bg-red-100 p-3 rounded-lg">
                <X className="h-6 w-6 text-red-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Controls */}
      <ContentCard>
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search notifications..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            
            <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All schools" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All schools</SelectItem>
                {schools?.map((school) => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All types" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All types</SelectItem>
                <SelectItem value="absence_alert">Absence Alert</SelectItem>
                <SelectItem value="late_alert">Late Alert</SelectItem>
                <SelectItem value="low_attendance">Low Attendance</SelectItem>
                <SelectItem value="session_reminder">Session Reminder</SelectItem>
                <SelectItem value="check_in_confirmation">Check-in Confirmation</SelectItem>
                <SelectItem value="check_out_confirmation">Check-out Confirmation</SelectItem>
                <SelectItem value="attendance_summary">Attendance Summary</SelectItem>
              </SelectContent>
            </Select>

            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="All status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="sent">Sent</SelectItem>
                <SelectItem value="delivered">Delivered</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="flex gap-2">
            <Button
              variant={showUnreadOnly ? "default" : "outline"}
              size="sm"
              onClick={() => setShowUnreadOnly(!showUnreadOnly)}
            >
              <Bell className="h-4 w-4 mr-2" />
              {showUnreadOnly ? 'Show All' : 'Unread Only'}
            </Button>
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Create
            </Button>
            <Button variant="outline" size="sm">
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Clear Filters */}
        {(searchTerm || (selectedSchoolId && selectedSchoolId !== 'all') || (filterType && filterType !== 'all') || (filterStatus && filterStatus !== 'all')) && (
          <div className="mb-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                setSearchTerm('');
                setSelectedSchoolId('all');
                setFilterType('all');
                setFilterStatus('all');
              }}
            >
              <Filter className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
          </div>
        )}
      </ContentCard>

      {/* Notifications List */}
      <ContentCard>
        <CardHeader>
          <CardTitle>Notifications</CardTitle>
          <CardDescription>
            {filteredNotifications.length} notification{filteredNotifications.length !== 1 ? 's' : ''} found
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="text-center py-8">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No notifications found</h3>
              <p className="text-gray-600">
                {notifications?.length === 0 
                  ? "No notifications have been created yet."
                  : "No notifications match your current filters."
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredNotifications.map((notification) => (
                <div 
                  key={notification.id} 
                  className={`border rounded-lg p-4 ${!notification.read_at ? 'bg-blue-50 border-blue-200' : ''}`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-start gap-3">
                      <div className="p-2 rounded-lg bg-gray-100">
                        {getTypeIcon(notification.notification_type)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <div className="font-medium">{notification.message_title}</div>
                          {!notification.read_at && (
                            <Badge variant="secondary" className="text-xs">
                              New
                            </Badge>
                          )}
                        </div>
                        <div className="text-sm text-gray-600 mb-2">
                          {notification.message_content}
                        </div>
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>
                            {new Date(notification.created_at).toLocaleString()}
                          </span>
                          {notification.student && (
                            <span>
                              Student: {notification.student.first_name} {notification.student.last_name}
                            </span>
                          )}
                          {notification.session && (
                            <span>
                              Session: {notification.session.session_name}
                            </span>
                          )}
                          <span className="capitalize">
                            To: {notification.recipient_type}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Badge className={getPriorityColor(notification.priority_level)}>
                        {notification.priority_level}
                      </Badge>
                      <div className="flex items-center gap-1">
                        {getStatusIcon(notification.delivery_status)}
                        <Badge className={getStatusColor(notification.delivery_status)}>
                          {notification.delivery_status}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-3 border-t">
                    <div className="flex items-center gap-2">
                      {!notification.read_at && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleMarkAsRead(notification.id)}
                        >
                          <Check className="h-4 w-4 mr-2" />
                          Mark Read
                        </Button>
                      )}
                      
                      {notification.delivery_status === 'pending' && (
                        <Button
                          size="sm"
                          onClick={() => handleSendNotification(notification.id)}
                          disabled={sendNotification.isPending}
                        >
                          <Send className="h-4 w-4 mr-2" />
                          Send Now
                        </Button>
                      )}

                      {notification.delivery_status === 'failed' && (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleSendNotification(notification.id)}
                        >
                          <Send className="h-4 w-4 mr-2" />
                          Retry
                        </Button>
                      )}
                    </div>

                    <div className="text-xs text-gray-500">
                      {notification.delivery_method && (
                        <span className="capitalize">
                          via {notification.delivery_method}
                        </span>
                      )}
                      {notification.sent_at && (
                        <span className="ml-2">
                          • Sent {new Date(notification.sent_at).toLocaleString()}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </ContentCard>
    </PageLayout>
  );
};

export default AttendanceNotificationCenter;
