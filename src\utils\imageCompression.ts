/**
 * Image compression utilities for field report photos
 * Optimizes images for offline storage and upload while maintaining quality
 */

export interface CompressionOptions {
  maxWidth?: number;
  maxHeight?: number;
  quality?: number; // 0.1 to 1.0
  format?: 'jpeg' | 'webp' | 'png';
  maxSizeKB?: number;
}

export interface CompressedImageResult {
  blob: Blob;
  dataUrl: string;
  originalSize: number;
  compressedSize: number;
  compressionRatio: number;
  width: number;
  height: number;
}

/**
 * Default compression settings optimized for field documentation
 */
export const DEFAULT_COMPRESSION_OPTIONS: CompressionOptions = {
  maxWidth: 1920,
  maxHeight: 1080,
  quality: 0.8,
  format: 'jpeg',
  maxSizeKB: 500, // 500KB max for offline storage efficiency
};

/**
 * Compresses an image file using Canvas API
 */
export const compressImage = async (
  file: File,
  options: CompressionOptions = DEFAULT_COMPRESSION_OPTIONS
): Promise<CompressedImageResult> => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    if (!ctx) {
      reject(new Error('Canvas context not available'));
      return;
    }

    img.onload = () => {
      try {
        // Calculate new dimensions while maintaining aspect ratio
        const { width: newWidth, height: newHeight } = calculateDimensions(
          img.width,
          img.height,
          options.maxWidth || DEFAULT_COMPRESSION_OPTIONS.maxWidth!,
          options.maxHeight || DEFAULT_COMPRESSION_OPTIONS.maxHeight!
        );

        canvas.width = newWidth;
        canvas.height = newHeight;

        // Draw and compress image
        ctx.drawImage(img, 0, 0, newWidth, newHeight);

        const quality = options.quality || DEFAULT_COMPRESSION_OPTIONS.quality!;
        const format = options.format || DEFAULT_COMPRESSION_OPTIONS.format!;
        const mimeType = `image/${format}`;

        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('Failed to compress image'));
              return;
            }

            const dataUrl = canvas.toDataURL(mimeType, quality);
            const compressionRatio = (file.size - blob.size) / file.size;

            resolve({
              blob,
              dataUrl,
              originalSize: file.size,
              compressedSize: blob.size,
              compressionRatio,
              width: newWidth,
              height: newHeight,
            });
          },
          mimeType,
          quality
        );
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => {
      reject(new Error('Failed to load image'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * Calculate optimal dimensions while maintaining aspect ratio
 */
const calculateDimensions = (
  originalWidth: number,
  originalHeight: number,
  maxWidth: number,
  maxHeight: number
): { width: number; height: number } => {
  let { width, height } = { width: originalWidth, height: originalHeight };

  // Scale down if larger than max dimensions
  if (width > maxWidth) {
    height = (height * maxWidth) / width;
    width = maxWidth;
  }

  if (height > maxHeight) {
    width = (width * maxHeight) / height;
    height = maxHeight;
  }

  return { width: Math.round(width), height: Math.round(height) };
};

/**
 * Validates if image meets compression requirements
 */
export const validateCompressedImage = (
  result: CompressedImageResult,
  options: CompressionOptions
): { isValid: boolean; reason?: string } => {
  const maxSizeKB = options.maxSizeKB || DEFAULT_COMPRESSION_OPTIONS.maxSizeKB!;
  const maxSizeBytes = maxSizeKB * 1024;

  if (result.compressedSize > maxSizeBytes) {
    return {
      isValid: false,
      reason: `Image size ${Math.round(result.compressedSize / 1024)}KB exceeds limit of ${maxSizeKB}KB`,
    };
  }

  return { isValid: true };
};

/**
 * Progressive compression - tries multiple quality levels to meet size requirements
 */
export const progressiveCompress = async (
  file: File,
  options: CompressionOptions = DEFAULT_COMPRESSION_OPTIONS
): Promise<CompressedImageResult> => {
  const qualityLevels = [0.8, 0.6, 0.4, 0.2];
  
  for (const quality of qualityLevels) {
    const result = await compressImage(file, { ...options, quality });
    const validation = validateCompressedImage(result, options);
    
    if (validation.isValid) {
      return result;
    }
  }

  // If still too large, try WebP format
  if (options.format !== 'webp') {
    return progressiveCompress(file, { ...options, format: 'webp' });
  }

  // Last resort - return best compression achieved
  return compressImage(file, { ...options, quality: 0.2 });
};

/**
 * Batch compress multiple images
 */
export const batchCompressImages = async (
  files: File[],
  options: CompressionOptions = DEFAULT_COMPRESSION_OPTIONS,
  onProgress?: (completed: number, total: number) => void
): Promise<CompressedImageResult[]> => {
  const results: CompressedImageResult[] = [];
  
  for (let i = 0; i < files.length; i++) {
    const result = await progressiveCompress(files[i], options);
    results.push(result);
    onProgress?.(i + 1, files.length);
  }
  
  return results;
};

/**
 * Convert compressed image to File object for upload
 */
export const compressedImageToFile = (
  result: CompressedImageResult,
  originalFileName: string
): File => {
  const extension = result.blob.type.split('/')[1];
  const fileName = originalFileName.replace(/\.[^/.]+$/, `.${extension}`);
  
  return new File([result.blob], fileName, {
    type: result.blob.type,
    lastModified: Date.now(),
  });
};
