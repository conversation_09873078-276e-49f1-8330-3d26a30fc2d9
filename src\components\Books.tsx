import React, { useState } from 'react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { BookOpen, Package } from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';
import BookManagement from './BookManagement';
import ConsolidatedDistributionManagement from './distributions/ConsolidatedDistributionManagement';
import { useAuth } from '@/hooks/useAuth';

const Books = () => {
  const { profile } = useAuth();
  const [activeTab, setActiveTab] = useState('management');

  // Only admins and program officers can access book management
  if (profile?.role !== 'admin' && profile?.role !== 'program_officer') {
    return (
      <PageLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <BookOpen className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 mb-2">Access Restricted</h2>
            <p className="text-gray-600">Only administrators and program officers can access book management.</p>
          </div>
        </div>
      </PageLayout>
    );
  }

  return (
    <PageLayout>
      <PageHeader
        title="Books"
        description="Manage books, inventory, and distributions"
        icon={BookOpen}
      />

      <div className="space-y-6">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="management" className="flex items-center gap-2">
              <Package className="h-4 w-4" />
              Book Management
            </TabsTrigger>
            <TabsTrigger value="distributions" className="flex items-center gap-2">
              <BookOpen className="h-4 w-4" />
              Distributions
            </TabsTrigger>
          </TabsList>

          <TabsContent value="management" className="space-y-6">
            <BookManagement />
          </TabsContent>

          <TabsContent value="distributions" className="space-y-6">
            <ConsolidatedDistributionManagement />
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
};

export default Books;
