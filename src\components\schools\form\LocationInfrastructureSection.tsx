
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { SchoolFormData } from '@/types/school';

interface LocationInfrastructureSectionProps {
  formData: SchoolFormData;
  onFormDataChange: (data: SchoolFormData) => void;
}

const LocationInfrastructureSection = ({ formData, onFormDataChange }: LocationInfrastructureSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">Location & Infrastructure</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="location_description">Location Description</Label>
          <Textarea
            id="location_description"
            value={formData.location_description}
            onChange={(e) => onFormDataChange({ ...formData, location_description: e.target.value })}
            placeholder="Describe the school's location and surroundings"
            rows={3}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="infrastructure_notes">Infrastructure Notes</Label>
          <Textarea
            id="infrastructure_notes"
            value={formData.infrastructure_notes}
            onChange={(e) => onFormDataChange({ ...formData, infrastructure_notes: e.target.value })}
            placeholder="Building conditions, facilities, water, electricity, etc."
            rows={3}
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="nearest_health_center">Nearest Health Center</Label>
          <Input
            id="nearest_health_center"
            value={formData.nearest_health_center}
            onChange={(e) => onFormDataChange({ ...formData, nearest_health_center: e.target.value })}
            placeholder="Name and distance to nearest health facility"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="distance_to_main_road">Distance to Main Road</Label>
          <Input
            id="distance_to_main_road"
            value={formData.distance_to_main_road}
            onChange={(e) => onFormDataChange({ ...formData, distance_to_main_road: e.target.value })}
            placeholder="e.g., 2 km from tarmac road"
          />
        </div>
      </div>
    </div>
  );
};

export default LocationInfrastructureSection;
