/**
 * Refactored useOfflineSync hook - simplified and modular
 * Orchestrates smaller, focused hooks for offline sync functionality
 */

import { useMemo } from 'react';

// Import types
import {
  OfflineSyncStatus
} from '@/types/offlineSync.types';

// Import focused hooks
import { useOfflineQueue } from './useOfflineQueue';
import { useConflictResolution } from './useConflictResolution';
import { useStorageManagement } from './useStorageManagement';
import { useSyncOperations } from './useSyncOperations';

// Import utilities for direct access
import { loadOfflineData } from '@/utils/offlineStorage';

export const useOfflineSync = () => {
  // Use focused hooks for different concerns
  const queueHook = useOfflineQueue();
  const conflictHook = useConflictResolution(queueHook.addToOfflineQueue);
  const storageHook = useStorageManagement();
  const syncHook = useSyncOperations();

  // Combine sync status from different hooks
  const syncStatus = useMemo((): OfflineSyncStatus => ({
    isOnline: syncHook.syncStatus.isOnline,
    pendingItems: queueHook.queueStats.totalPending,
    lastSyncTime: syncHook.syncStatus.lastSyncTime,
    isSyncing: syncHook.syncStatus.isSyncing,
    syncProgress: syncHook.syncStatus.syncProgress,
    failedItems: queueHook.queueStats.failed,
    conflictItems: conflictHook.getConflictCount(),
    storageUsagePercent: storageHook.storageStats.storageUsagePercent,
    lastCleanup: storageHook.storageStats.lastCleanup ? new Date(storageHook.storageStats.lastCleanup) : null,
    totalStorageSize: storageHook.storageStats.totalSize,
  }), [
    syncHook.syncStatus,
    queueHook.queueStats,
    conflictHook,
    storageHook.storageStats
  ]);

  // Return the combined interface from all hooks
  return {
    // Sync status (combined from all hooks)
    syncStatus,

    // Conflicts (from conflict hook)
    conflicts: conflictHook.conflicts,

    // Queue operations (from queue hook)
    addToOfflineQueue: queueHook.addToOfflineQueue,
    removeFromOfflineQueue: queueHook.removeFromOfflineQueue,

    // Sync operations (from sync hook)
    syncOfflineData: syncHook.syncOfflineData,

    // Storage operations (from storage hook)
    clearOfflineData: storageHook.clearOfflineData,

    // Direct data access
    loadOfflineData,

    // Conflict resolution (from conflict hook)
    resolveConflict: conflictHook.resolveConflict,

    // Statistics (from queue hook)
    getSyncStats: () => queueHook.queueStats,

    // Storage statistics (from storage hook)
    getStorageStats: storageHook.getStorageStats,

    // Cleanup (from storage hook)
    performComprehensiveCleanup: storageHook.performCleanup,
  };
};


