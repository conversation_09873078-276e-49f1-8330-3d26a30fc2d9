import React, { useState } from 'react';
import { Play, Download, BookOpen, Video, FileText, Users, Clock, Star, Search } from 'lucide-react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useAuth } from '@/hooks/useAuth';
import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface TrainingResource {
  id: string;
  title: string;
  description: string;
  type: 'video' | 'document' | 'interactive';
  category: string;
  duration?: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  rating: number;
  downloadUrl?: string;
  videoUrl?: string;
  completedBy?: string[];
  tags: string[];
  lastUpdated: string;
  targetRoles: UserRole[];
}

const TrainingMaterials: React.FC = () => {
  const { profile } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedType, setSelectedType] = useState('all');

  // Mock training resources
  const trainingResources: TrainingResource[] = [
    {
      id: '1',
      title: 'Getting Started with iLEAD Field Tracker',
      description: 'Complete introduction to the platform covering basic navigation, user roles, and core features.',
      type: 'video',
      category: 'getting-started',
      duration: '15 min',
      difficulty: 'beginner',
      rating: 4.8,
      videoUrl: '#',
      completedBy: ['user1', 'user2'],
      tags: ['introduction', 'basics', 'navigation'],
      lastUpdated: '2024-01-15',
      targetRoles: ['admin', 'program_officer', 'field_staff']
    },
    {
      id: '2',
      title: 'Task Management Best Practices',
      description: 'Learn how to effectively create, assign, and track tasks for maximum productivity.',
      type: 'document',
      category: 'task-management',
      duration: '10 min read',
      difficulty: 'intermediate',
      rating: 4.6,
      downloadUrl: '#',
      completedBy: ['user1'],
      tags: ['tasks', 'productivity', 'management'],
      lastUpdated: '2024-01-12',
      targetRoles: ['admin', 'program_officer']
    },
    {
      id: '3',
      title: 'Field Data Collection Guide',
      description: 'Comprehensive guide for field staff on collecting and submitting accurate data.',
      type: 'interactive',
      category: 'field-operations',
      duration: '20 min',
      difficulty: 'beginner',
      rating: 4.9,
      completedBy: [],
      tags: ['field-work', 'data-collection', 'mobile'],
      lastUpdated: '2024-01-10',
      targetRoles: ['field_staff']
    },
    {
      id: '4',
      title: 'Impact Measurement & Reporting',
      description: 'Advanced training on using the impact measurement tools and generating reports.',
      type: 'video',
      category: 'impact-measurement',
      duration: '25 min',
      difficulty: 'advanced',
      rating: 4.7,
      videoUrl: '#',
      completedBy: [],
      tags: ['impact', 'reporting', 'analytics'],
      lastUpdated: '2024-01-08',
      targetRoles: ['admin', 'program_officer']
    },
    {
      id: '5',
      title: 'Mobile App User Manual',
      description: 'Complete user manual for the mobile application including offline functionality.',
      type: 'document',
      category: 'mobile-app',
      duration: '15 min read',
      difficulty: 'beginner',
      rating: 4.5,
      downloadUrl: '#',
      completedBy: ['user2'],
      tags: ['mobile', 'offline', 'manual'],
      lastUpdated: '2024-01-05',
      targetRoles: ['field_staff']
    }
  ];

  const categories = [
    { value: 'all', label: 'All Categories' },
    { value: 'getting-started', label: 'Getting Started' },
    { value: 'task-management', label: 'Task Management' },
    { value: 'field-operations', label: 'Field Operations' },
    { value: 'impact-measurement', label: 'Impact Measurement' },
    { value: 'mobile-app', label: 'Mobile App' }
  ];

  const resourceTypes = [
    { value: 'all', label: 'All Types' },
    { value: 'video', label: 'Videos' },
    { value: 'document', label: 'Documents' },
    { value: 'interactive', label: 'Interactive' }
  ];

  const filteredResources = trainingResources.filter(resource => {
    // Filter by user role
    if (!resource.targetRoles.includes(profile?.role as UserRole)) {
      return false;
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      const matchesSearch = resource.title.toLowerCase().includes(query) ||
                           resource.description.toLowerCase().includes(query) ||
                           resource.tags.some(tag => tag.toLowerCase().includes(query));
      if (!matchesSearch) return false;
    }

    // Filter by category
    if (selectedCategory !== 'all' && resource.category !== selectedCategory) {
      return false;
    }

    // Filter by type
    if (selectedType !== 'all' && resource.type !== selectedType) {
      return false;
    }

    return true;
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'video': return <Video className="h-4 w-4" />;
      case 'document': return <FileText className="h-4 w-4" />;
      case 'interactive': return <Users className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-800';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const isCompleted = (resource: TrainingResource) => {
    return resource.completedBy?.includes(profile?.id || '') || false;
  };

  const handleResourceAction = (resource: TrainingResource) => {
    if (resource.type === 'video' && resource.videoUrl) {
      // In a real implementation, this would open a video player
      window.open(resource.videoUrl, '_blank');
    } else if (resource.type === 'document' && resource.downloadUrl) {
      // In a real implementation, this would download the document
      window.open(resource.downloadUrl, '_blank');
    } else {
      // For interactive content, navigate to the training module
      console.log('Opening interactive training:', resource.id);
    }
  };

  return (
    <div className="max-w-6xl mx-auto">

      {/* Filters */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search training materials..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={selectedCategory} onValueChange={setSelectedCategory}>
          <SelectTrigger>
            <SelectValue placeholder="Select category" />
          </SelectTrigger>
          <SelectContent>
            {categories.map(category => (
              <SelectItem key={category.value} value={category.value}>
                {category.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select value={selectedType} onValueChange={setSelectedType}>
          <SelectTrigger>
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            {resourceTypes.map(type => (
              <SelectItem key={type.value} value={type.value}>
                {type.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Training Resources Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredResources.map((resource) => (
          <Card key={resource.id} className={`transition-all hover:shadow-lg ${isCompleted(resource) ? 'ring-2 ring-green-200' : ''}`}>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-2">
                  {getTypeIcon(resource.type)}
                  <Badge variant="outline" className={getDifficultyColor(resource.difficulty)}>
                    {resource.difficulty}
                  </Badge>
                </div>
                {isCompleted(resource) && (
                  <Badge className="bg-green-100 text-green-800">
                    Completed
                  </Badge>
                )}
              </div>
              <CardTitle className="text-lg">{resource.title}</CardTitle>
              <CardDescription>{resource.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm text-gray-600">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-4 w-4" />
                    <span>{resource.duration}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                    <span>{resource.rating}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-1">
                  {resource.tags.slice(0, 3).map((tag) => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <Button 
                  onClick={() => handleResourceAction(resource)}
                  className="w-full"
                  variant={isCompleted(resource) ? "outline" : "default"}
                >
                  {resource.type === 'video' && (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Watch Video
                    </>
                  )}
                  {resource.type === 'document' && (
                    <>
                      <Download className="h-4 w-4 mr-2" />
                      Download Guide
                    </>
                  )}
                  {resource.type === 'interactive' && (
                    <>
                      <BookOpen className="h-4 w-4 mr-2" />
                      Start Training
                    </>
                  )}
                </Button>

                <p className="text-xs text-gray-500">
                  Updated: {resource.lastUpdated}
                </p>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredResources.length === 0 && (
        <Card>
          <CardContent className="text-center py-8">
            <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">No training materials found</h3>
            <p className="text-gray-600">
              Try adjusting your search terms or filters to find relevant training resources.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TrainingMaterials;
