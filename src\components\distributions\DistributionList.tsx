
import React, { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { BookOpen, Plus, Calendar, MapPin, Eye } from 'lucide-react';
import { Database } from '@/integrations/supabase/types';
import DistributionDetailsModal from './DistributionDetailsModal';

// Type definitions
type BookDistribution = Database['public']['Functions']['get_book_distributions']['Returns'][0];
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface DistributionListProps {
  distributions: BookDistribution[];
  currentUser: Profile | null;
  onCreateClick: () => void;
}

const DistributionList = ({ distributions, currentUser, onCreateClick }: DistributionListProps) => {
  const [selectedDistribution, setSelectedDistribution] = useState<BookDistribution | null>(null);
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false);

  const handleViewDetails = (distribution: BookDistribution) => {
    setSelectedDistribution(distribution);
    setIsDetailsModalOpen(true);
  };

  if (distributions.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No distributions logged yet</h3>
          <p className="text-gray-600 mb-4">Start by logging your first book distribution</p>
          <Button onClick={onCreateClick} className="bg-purple-600 hover:bg-purple-700">
            <Plus className="h-4 w-4 mr-2" />
            Log First Distribution
          </Button>
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
      case 'in_progress':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case 'planned':
        return <Badge variant="outline">Planned</Badge>;
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  return (
    <>
    <Card>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Book Title</TableHead>
              <TableHead>School</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Delivery Date</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {distributions.map((distribution: BookDistribution) => (
              <TableRow key={distribution.id} className="hover:bg-gray-50">
                <TableCell className="font-medium">
                  <div className="flex items-center">
                    <BookOpen className="h-4 w-4 text-purple-600 mr-2" />
                    {distribution.book_title || 'Unknown Book'}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <MapPin className="h-4 w-4 text-gray-500 mr-2" />
                    {distribution.school_name || 'Unknown School'}
                  </div>
                </TableCell>
                <TableCell>
                  <span className="font-medium">{distribution.quantity}</span> copies
                </TableCell>
                <TableCell>
                  <div className="flex items-center">
                    <Calendar className="h-4 w-4 text-gray-500 mr-2" />
                    {distribution.delivery_date}
                  </div>
                </TableCell>
                <TableCell>
                  {getStatusBadge(distribution.status)}
                </TableCell>
                <TableCell>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleViewDetails(distribution)}
                    className="flex items-center gap-2"
                  >
                    <Eye className="h-4 w-4" />
                    View Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>

    {/* Distribution Details Modal */}
    {selectedDistribution && (
      <DistributionDetailsModal
        distribution={selectedDistribution}
        isOpen={isDetailsModalOpen}
        onClose={() => {
          setIsDetailsModalOpen(false);
          setSelectedDistribution(null);
        }}
      />
    )}
  </>
  );
};

export default DistributionList;
