import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';


import {
  Users,
  UserPlus,
  Upload,
  Download,
  Search,
  Filter,
  MoreHorizontal,
  Shield,
  UserCheck,
  UserX,
  Mail,
  Phone,
  MapPin,
  Eye
} from 'lucide-react';
import { useStaffManagement } from '@/hooks/useStaffManagement';
import { useAuth } from '@/hooks/useAuth';
import FieldStaffAccessControl from '@/components/field-staff/FieldStaffAccessControl';
import StaffTable from './StaffTable';
import BulkUserCreation from './BulkUserCreation';
import CreateUserDialog from './CreateUserDialog';
import AuditLogViewer from './AuditLogViewer';
import { Database } from '@/integrations/supabase/types';

type UserRole = Database['public']['Enums']['user_role'];

interface StaffFilters {
  search: string;
  role: UserRole | 'all';
  status: 'all' | 'active' | 'inactive';
  division: string | 'all';
}

const StaffManagement = () => {
  const { profile } = useAuth();
  const {
    staffMembers,
    isLoadingStaff,
    refetchStaff
  } = useStaffManagement();

  const [filters, setFilters] = useState<StaffFilters>({
    search: '',
    role: 'all',
    status: 'all',
    division: 'all'
  });

  const [showBulkCreation, setShowBulkCreation] = useState(false);
  const [showCreateUser, setShowCreateUser] = useState(false);
  const [showAuditLog, setShowAuditLog] = useState(false);

  // Filter staff members based on current filters
  const filteredStaffMembers = useMemo(() => {
    return staffMembers.filter(member => {
      const matchesSearch = filters.search === '' || 
        member.name.toLowerCase().includes(filters.search.toLowerCase()) ||
        member.email.toLowerCase().includes(filters.search.toLowerCase());
      
      const matchesRole = filters.role === 'all' || member.role === filters.role;
      
      const matchesStatus = filters.status === 'all' || 
        (filters.status === 'active' && member.is_active) ||
        (filters.status === 'inactive' && !member.is_active);
      
      const matchesDivision = filters.division === 'all' || 
        member.division_id === filters.division;

      return matchesSearch && matchesRole && matchesStatus && matchesDivision;
    });
  }, [staffMembers, filters]);

  // Get unique divisions for filter dropdown
  const divisions = useMemo(() => {
    const uniqueDivisions = new Map();
    staffMembers.forEach(member => {
      if (member.division_id && member.division_name) {
        uniqueDivisions.set(member.division_id, member.division_name);
      }
    });
    return Array.from(uniqueDivisions.entries()).map(([id, name]) => ({ id, name }));
  }, [staffMembers]);

  // Calculate statistics
  const stats = useMemo(() => {
    const total = staffMembers.length;
    const active = staffMembers.filter(m => m.is_active).length;
    const inactive = total - active;
    const admins = staffMembers.filter(m => m.role === 'admin').length;
    const programOfficers = staffMembers.filter(m => m.role === 'program_officer').length;
    const fieldStaff = staffMembers.filter(m => m.role === 'field_staff').length;

    return {
      total,
      active,
      inactive,
      admins,
      programOfficers,
      fieldStaff
    };
  }, [staffMembers]);

  const handleFilterChange = (key: keyof StaffFilters, value: string) => {
    setFilters(prev => ({ ...prev, [key]: value }));
  };

  const handleExportCSV = () => {
    const csvData = filteredStaffMembers.map(member => ({
      Name: member.name,
      Email: member.email,
      Role: member.role,
      Division: member.division_name || '',
      Phone: member.phone || '',
      Status: member.is_active ? 'Active' : 'Inactive',
      'Created At': new Date(member.created_at).toLocaleDateString(),
      'Requires Password Change': member.requires_password_change ? 'Yes' : 'No'
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `staff-export-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  return (
    <FieldStaffAccessControl requiredRoles={['admin', 'program_officer']}>
      <div className="p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Staff Management</h1>
            <p className="text-gray-600 mt-1">
              Manage user accounts, roles, and permissions
            </p>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={() => setShowCreateUser(true)}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <UserPlus className="h-4 w-4 mr-2" />
              Add User
            </Button>

            {/* Admin-only features */}
            {profile?.role === 'admin' && (
              <>
                <Button
                  onClick={() => setShowBulkCreation(true)}
                  variant="outline"
                >
                  <Upload className="h-4 w-4 mr-2" />
                  Bulk Import
                </Button>
                <Button
                  onClick={handleExportCSV}
                  variant="outline"
                  disabled={filteredStaffMembers.length === 0}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export CSV
                </Button>
                <Button
                  onClick={() => setShowAuditLog(true)}
                  variant="outline"
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Audit Log
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Staff</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.total}</div>
              <p className="text-xs text-muted-foreground">
                {stats.active} active, {stats.inactive} inactive
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Admins</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.admins}</div>
              <p className="text-xs text-muted-foreground">
                System administrators
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Program Officers</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.programOfficers}</div>
              <p className="text-xs text-muted-foreground">
                Program management
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Field Staff</CardTitle>
              <UserX className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{stats.fieldStaff}</div>
              <p className="text-xs text-muted-foreground">
                Field operations
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Filters</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Search</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search by name or email..."
                    value={filters.search}
                    onChange={(e) => handleFilterChange('search', e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Role</label>
                <Select value={filters.role} onValueChange={(value) => handleFilterChange('role', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All roles" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Roles</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                    <SelectItem value="program_officer">Program Officer</SelectItem>
                    <SelectItem value="field_staff">Field Staff</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Division</label>
                <Select value={filters.division} onValueChange={(value) => handleFilterChange('division', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="All divisions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Divisions</SelectItem>
                    {divisions.map((division) => (
                      <SelectItem key={division.id} value={division.id}>
                        {division.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="space-y-4">
          <StaffTable
            staffMembers={filteredStaffMembers}
            isLoading={isLoadingStaff}
            onRefresh={refetchStaff}
          />
        </div>



        {/* Dialogs */}
        <CreateUserDialog 
          open={showCreateUser}
          onOpenChange={setShowCreateUser}
        />
        
        <BulkUserCreation
          open={showBulkCreation}
          onOpenChange={setShowBulkCreation}
        />

        <AuditLogViewer
          open={showAuditLog}
          onOpenChange={setShowAuditLog}
        />
      </div>
    </FieldStaffAccessControl>
  );
};

export default StaffManagement;
