/**
 * Dedicated photo upload queue system
 * Operates independently from other sync operations to prevent blocking
 */

import { toast } from 'sonner';
import { supabase } from '@/integrations/supabase/client';

export interface PhotoUploadQueueItem {
  id: string;
  photoId: string;
  fileName: string;
  blob: Blob;
  fieldReportId?: string;
  distributionId?: string;
  priority: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'pending' | 'uploading' | 'completed' | 'failed' | 'paused';
  uploadProgress: number;
  retryCount: number;
  maxRetries: number;
  timestamp: number;
  lastAttempt?: number;
  error?: string;
  uploadUrl?: string;
  metadata?: {
    originalSize: number;
    compressedSize: number;
    compressionRatio: number;
  };
}

export interface PhotoUploadProgress {
  queueId: string;
  photoId: string;
  progress: number;
  status: PhotoUploadQueueItem['status'];
  error?: string;
}

export interface PhotoUploadQueueStats {
  totalItems: number;
  pendingItems: number;
  uploadingItems: number;
  completedItems: number;
  failedItems: number;
  totalSize: number;
  uploadedSize: number;
  estimatedTimeRemaining?: number;
}

class PhotoUploadQueueManager {
  private queue: Map<string, PhotoUploadQueueItem> = new Map();
  private activeUploads: Set<string> = new Set();
  private progressCallbacks: Map<string, (progress: PhotoUploadProgress) => void> = new Map();
  private isProcessing = false;
  private processingInterval?: NodeJS.Timeout;
  
  // Configuration
  private readonly MAX_CONCURRENT_UPLOADS = 2;
  private readonly RETRY_DELAY_BASE = 2000; // 2 seconds
  private readonly PROCESSING_INTERVAL = 1000; // 1 second
  private readonly STORAGE_KEY = 'photo_upload_queue';

  constructor() {
    this.loadQueueFromStorage();
    this.startProcessing();
    
    // Listen for online/offline events
    window.addEventListener('online', () => this.resumeProcessing());
    window.addEventListener('offline', () => this.pauseProcessing());
  }

  /**
   * Add photo to upload queue
   */
  addToQueue(
    photoId: string,
    fileName: string,
    blob: Blob,
    options: {
      fieldReportId?: string;
      distributionId?: string;
      priority?: PhotoUploadQueueItem['priority'];
      maxRetries?: number;
      metadata?: PhotoUploadQueueItem['metadata'];
    } = {}
  ): string {
    const queueId = `photo_queue_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const queueItem: PhotoUploadQueueItem = {
      id: queueId,
      photoId,
      fileName,
      blob,
      fieldReportId: options.fieldReportId,
      distributionId: options.distributionId,
      priority: options.priority || 'MEDIUM',
      status: 'pending',
      uploadProgress: 0,
      retryCount: 0,
      maxRetries: options.maxRetries || 3,
      timestamp: Date.now(),
      metadata: options.metadata,
    };

    this.queue.set(queueId, queueItem);
    this.saveQueueToStorage();
    
    toast.info(`Photo queued for upload (${this.queue.size} in queue)`);
    return queueId;
  }

  /**
   * Remove item from queue
   */
  removeFromQueue(queueId: string): boolean {
    const item = this.queue.get(queueId);
    if (!item) return false;

    // Cancel active upload if in progress
    if (item.status === 'uploading') {
      this.activeUploads.delete(queueId);
    }

    this.queue.delete(queueId);
    this.progressCallbacks.delete(queueId);
    this.saveQueueToStorage();
    
    return true;
  }

  /**
   * Get queue statistics
   */
  getQueueStats(): PhotoUploadQueueStats {
    const items = Array.from(this.queue.values());
    const totalSize = items.reduce((sum, item) => sum + item.blob.size, 0);
    const uploadedSize = items.reduce((sum, item) => {
      return sum + (item.blob.size * (item.uploadProgress / 100));
    }, 0);

    return {
      totalItems: items.length,
      pendingItems: items.filter(item => item.status === 'pending').length,
      uploadingItems: items.filter(item => item.status === 'uploading').length,
      completedItems: items.filter(item => item.status === 'completed').length,
      failedItems: items.filter(item => item.status === 'failed').length,
      totalSize,
      uploadedSize,
      estimatedTimeRemaining: this.calculateEstimatedTime(items),
    };
  }

  /**
   * Subscribe to progress updates for a specific item
   */
  subscribeToProgress(queueId: string, callback: (progress: PhotoUploadProgress) => void): void {
    this.progressCallbacks.set(queueId, callback);
  }

  /**
   * Unsubscribe from progress updates
   */
  unsubscribeFromProgress(queueId: string): void {
    this.progressCallbacks.delete(queueId);
  }

  /**
   * Pause all uploads
   */
  pauseProcessing(): void {
    this.isProcessing = false;
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
      this.processingInterval = undefined;
    }
    
    // Mark uploading items as paused
    this.queue.forEach(item => {
      if (item.status === 'uploading') {
        item.status = 'paused';
        this.notifyProgress(item.id, item);
      }
    });
  }

  /**
   * Resume processing uploads
   */
  resumeProcessing(): void {
    if (!this.isProcessing) {
      this.isProcessing = true;
      this.startProcessing();
      
      // Resume paused items
      this.queue.forEach(item => {
        if (item.status === 'paused') {
          item.status = 'pending';
        }
      });
      
      toast.success('Photo upload queue resumed');
    }
  }

  /**
   * Retry failed uploads
   */
  retryFailedUploads(): void {
    let retriedCount = 0;
    
    this.queue.forEach(item => {
      if (item.status === 'failed' && item.retryCount < item.maxRetries) {
        item.status = 'pending';
        item.retryCount++;
        item.error = undefined;
        retriedCount++;
      }
    });
    
    if (retriedCount > 0) {
      toast.info(`Retrying ${retriedCount} failed photo uploads`);
      this.saveQueueToStorage();
    }
  }

  /**
   * Clear completed uploads from queue
   */
  clearCompleted(): void {
    const completedItems = Array.from(this.queue.entries())
      .filter(([_, item]) => item.status === 'completed');
    
    completedItems.forEach(([queueId, _]) => {
      this.queue.delete(queueId);
      this.progressCallbacks.delete(queueId);
    });
    
    if (completedItems.length > 0) {
      toast.success(`Cleared ${completedItems.length} completed uploads`);
      this.saveQueueToStorage();
    }
  }

  /**
   * Get all queue items
   */
  getAllItems(): PhotoUploadQueueItem[] {
    return Array.from(this.queue.values()).sort((a, b) => {
      // Sort by priority first, then by timestamp
      const priorityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
      const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
      return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
    });
  }

  /**
   * Start processing queue
   */
  private startProcessing(): void {
    if (this.processingInterval) return;
    
    this.isProcessing = true;
    this.processingInterval = setInterval(() => {
      this.processQueue();
    }, this.PROCESSING_INTERVAL);
  }

  /**
   * Process queue items
   */
  private async processQueue(): Promise<void> {
    if (!this.isProcessing || !navigator.onLine) return;

    const pendingItems = Array.from(this.queue.values())
      .filter(item => item.status === 'pending')
      .sort((a, b) => {
        const priorityOrder = { CRITICAL: 4, HIGH: 3, MEDIUM: 2, LOW: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        return priorityDiff !== 0 ? priorityDiff : a.timestamp - b.timestamp;
      });

    const availableSlots = this.MAX_CONCURRENT_UPLOADS - this.activeUploads.size;
    const itemsToProcess = pendingItems.slice(0, availableSlots);

    for (const item of itemsToProcess) {
      this.uploadPhoto(item);
    }
  }

  /**
   * Upload a single photo
   */
  private async uploadPhoto(item: PhotoUploadQueueItem): Promise<void> {
    if (this.activeUploads.has(item.id)) return;

    this.activeUploads.add(item.id);
    item.status = 'uploading';
    item.lastAttempt = Date.now();
    this.notifyProgress(item.id, item);

    try {
      // Determine storage bucket based on context with fallback
      let bucket = item.fieldReportId ? 'field-report-photos' : 'distribution-photos';
      const prefix = item.fieldReportId || item.distributionId || 'temp';
      const fileName = `${prefix}_${Date.now()}_${item.fileName}`;
      let filePath = `${bucket}/${fileName}`;

      // Upload to Supabase Storage with fallback to general-files bucket
      let uploadError: Error | null;
      let uploadResult = await supabase.storage
        .from(bucket)
        .upload(filePath, item.blob, {
          onUploadProgress: (progress) => {
            item.uploadProgress = Math.round((progress.loaded / progress.total) * 100);
            this.notifyProgress(item.id, item);
          }
        });

      uploadError = uploadResult.error;

      // If the primary bucket doesn't exist, fallback to general-files bucket
      if (uploadError && uploadError.message?.includes('Bucket not found')) {
        console.warn(`Bucket ${bucket} not found, falling back to general-files bucket`);
        bucket = 'general-files';
        filePath = `${bucket}/${fileName}`;

        uploadResult = await supabase.storage
          .from(bucket)
          .upload(filePath, item.blob, {
            onUploadProgress: (progress) => {
              item.uploadProgress = Math.round((progress.loaded / progress.total) * 100);
              this.notifyProgress(item.id, item);
            }
          });

        uploadError = uploadResult.error;
      }

      if (uploadError) {
        throw uploadError;
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(filePath);

      item.uploadUrl = publicUrl;
      item.status = 'completed';
      item.uploadProgress = 100;
      
      this.notifyProgress(item.id, item);
      this.saveQueueToStorage();
      
    } catch (error) {
      console.error('Photo upload failed:', error);
      
      item.retryCount++;
      item.error = error instanceof Error ? error.message : 'Upload failed';
      
      if (item.retryCount >= item.maxRetries) {
        item.status = 'failed';
        toast.error(`Photo upload failed after ${item.maxRetries} attempts`);
      } else {
        item.status = 'pending';
        // Add exponential backoff delay
        setTimeout(() => {
          // Item will be picked up in next processing cycle
        }, this.RETRY_DELAY_BASE * Math.pow(2, item.retryCount));
      }
      
      this.notifyProgress(item.id, item);
      this.saveQueueToStorage();
    } finally {
      this.activeUploads.delete(item.id);
    }
  }

  /**
   * Notify progress callback
   */
  private notifyProgress(queueId: string, item: PhotoUploadQueueItem): void {
    const callback = this.progressCallbacks.get(queueId);
    if (callback) {
      callback({
        queueId,
        photoId: item.photoId,
        progress: item.uploadProgress,
        status: item.status,
        error: item.error,
      });
    }
  }

  /**
   * Calculate estimated time remaining
   */
  private calculateEstimatedTime(items: PhotoUploadQueueItem[]): number | undefined {
    const uploadingItems = items.filter(item => item.status === 'uploading');
    if (uploadingItems.length === 0) return undefined;

    // Simple estimation based on current upload speeds
    const avgSpeed = uploadingItems.reduce((sum, item) => {
      const elapsed = Date.now() - (item.lastAttempt || item.timestamp);
      const uploaded = item.blob.size * (item.uploadProgress / 100);
      return sum + (uploaded / elapsed);
    }, 0) / uploadingItems.length;

    const remainingBytes = items
      .filter(item => item.status !== 'completed')
      .reduce((sum, item) => sum + item.blob.size * (1 - item.uploadProgress / 100), 0);

    return avgSpeed > 0 ? remainingBytes / avgSpeed : undefined;
  }

  /**
   * Save queue to localStorage
   */
  private saveQueueToStorage(): void {
    try {
      const queueData = Array.from(this.queue.entries()).map(([id, item]) => ({
        id,
        ...item,
        blob: undefined, // Don't store blob in localStorage
      }));
      
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(queueData));
    } catch (error) {
      console.error('Failed to save photo upload queue:', error);
    }
  }

  /**
   * Load queue from localStorage
   */
  private loadQueueFromStorage(): void {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (!stored) return;

      const queueData = JSON.parse(stored);
      // Note: Blobs are not restored from localStorage
      // They should be re-added when the app restarts
      queueData.forEach((item: Partial<PhotoUploadQueueItem>) => {
        if (item.status === 'uploading') {
          item.status = 'pending'; // Reset uploading items to pending
        }
        // Skip items without blobs (they'll need to be re-added)
        if (item.blob) {
          this.queue.set(item.id, item);
        }
      });
    } catch (error) {
      console.error('Failed to load photo upload queue:', error);
    }
  }
}

// Export singleton instance
export const photoUploadQueue = new PhotoUploadQueueManager();
