-- Migration 019: Simplify GPS tracking to check-in only
-- Remove continuous GPS tracking requirements and make check-out location optional

-- Update field_staff_checkout function to make location parameters optional
CREATE OR REPLACE FUNCTION field_staff_checkout(
    p_attendance_id UUID,
    p_latitude DECIMAL(10,8) DEFAULT NULL,
    p_longitude DECIMAL(11,8) DEFAULT NULL,
    p_accuracy DECIMAL(8,2) DEFAULT NULL,
    p_address TEXT DEFAULT NULL,
    p_notes TEXT DEFAULT NULL,
    -- Field report data
    p_activity_type field_activity_type,
    p_round_table_sessions INTEGER DEFAULT 0,
    p_total_students INTEGER DEFAULT 0,
    p_students_per_session INTEGER DEFAULT 8,
    p_activities_conducted TEXT[] DEFAULT '{}',
    p_topics_covered TEXT[] DEFAULT '{}',
    p_challenges TEXT DEFAULT NULL,
    p_wins TEXT DEFAULT NULL,
    p_observations TEXT DEFAULT NULL,
    p_lessons_learned TEXT DEFAULT NULL,
    p_follow_up_required BOOLEAN DEFAULT false,
    p_follow_up_actions TEXT DEFAULT NULL,
    p_photos TEXT[] DEFAULT '{}',
    p_offline_sync BOOLEAN DEFAULT false
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    attendance_record field_staff_attendance%ROWTYPE;
    duration_minutes INTEGER;
    field_report_id UUID;
BEGIN
    -- Get attendance record and verify ownership
    SELECT * INTO attendance_record
    FROM field_staff_attendance
    WHERE id = p_attendance_id AND staff_id = auth.uid() AND status = 'active';

    IF attendance_record IS NULL THEN
        RAISE EXCEPTION 'Invalid attendance record or already checked out';
    END IF;

    -- Calculate duration
    duration_minutes := EXTRACT(EPOCH FROM (NOW() - attendance_record.check_in_time)) / 60;

    -- Update attendance record with check-out info (location optional)
    UPDATE field_staff_attendance
    SET 
        check_out_time = NOW(),
        check_out_location = CASE 
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL 
            THEN POINT(p_longitude, p_latitude) 
            ELSE NULL 
        END,
        check_out_accuracy = p_accuracy,
        check_out_address = p_address,
        total_duration_minutes = duration_minutes,
        status = 'completed',
        notes = p_notes,
        sync_timestamp = CASE WHEN p_offline_sync THEN NOW() ELSE NULL END,
        updated_at = NOW()
    WHERE id = p_attendance_id;

    -- Create field report
    INSERT INTO field_reports (
        attendance_id, staff_id, school_id, report_date, activity_type,
        round_table_sessions_count, total_students_attended, students_per_session,
        activities_conducted, topics_covered, challenges_encountered,
        wins_achieved, general_observations, lessons_learned,
        follow_up_required, follow_up_actions, photos,
        report_location, location_verified, offline_created,
        sync_timestamp, report_status
    )
    VALUES (
        p_attendance_id, auth.uid(), attendance_record.school_id, CURRENT_DATE, p_activity_type,
        p_round_table_sessions, p_total_students, p_students_per_session,
        p_activities_conducted, p_topics_covered, p_challenges,
        p_wins, p_observations, p_lessons_learned,
        p_follow_up_required, p_follow_up_actions, p_photos,
        CASE 
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL 
            THEN POINT(p_longitude, p_latitude) 
            ELSE NULL 
        END, 
        CASE 
            WHEN p_latitude IS NOT NULL AND p_longitude IS NOT NULL 
            THEN true 
            ELSE false 
        END, 
        p_offline_sync,
        CASE WHEN p_offline_sync THEN NOW() ELSE NULL END, 'submitted'
    )
    RETURNING id INTO field_report_id;

    -- Update or create daily timesheet
    PERFORM update_daily_timesheet(auth.uid(), CURRENT_DATE);

    -- Return the field report ID
    RETURN field_report_id;
END;
$$;

-- Add comment explaining the change
COMMENT ON FUNCTION field_staff_checkout IS 'Field staff check-out function with optional location capture. Location parameters are now optional to support check-out from any location while maintaining check-in location verification for school arrival confirmation.';

-- Update any existing views or functions that might depend on check-out location
-- This ensures backward compatibility while allowing location-optional check-outs

-- Create a view for attendance records that handles optional check-out locations
CREATE OR REPLACE VIEW field_staff_attendance_summary AS
SELECT 
    fsa.id,
    fsa.staff_id,
    fsa.school_id,
    s.name as school_name,
    fsa.attendance_date,
    fsa.check_in_time,
    fsa.check_out_time,
    fsa.total_duration_minutes,
    -- Check-in location (always required)
    ST_X(fsa.check_in_location) as check_in_longitude,
    ST_Y(fsa.check_in_location) as check_in_latitude,
    fsa.check_in_accuracy,
    fsa.check_in_address,
    -- Check-out location (optional)
    CASE 
        WHEN fsa.check_out_location IS NOT NULL 
        THEN ST_X(fsa.check_out_location) 
        ELSE NULL 
    END as check_out_longitude,
    CASE 
        WHEN fsa.check_out_location IS NOT NULL 
        THEN ST_Y(fsa.check_out_location) 
        ELSE NULL 
    END as check_out_latitude,
    fsa.check_out_accuracy,
    fsa.check_out_address,
    fsa.distance_from_school,
    fsa.location_verified,
    fsa.verification_method,
    fsa.status,
    fsa.notes,
    fsa.created_at,
    fsa.updated_at,
    -- Indicate if check-out location was captured
    CASE 
        WHEN fsa.check_out_location IS NOT NULL 
        THEN true 
        ELSE false 
    END as has_checkout_location
FROM field_staff_attendance fsa
JOIN schools s ON fsa.school_id = s.id
ORDER BY fsa.check_in_time DESC;

-- Grant appropriate permissions
GRANT SELECT ON field_staff_attendance_summary TO authenticated;

-- Add index for performance on the new optional check-out location queries
CREATE INDEX IF NOT EXISTS idx_field_staff_attendance_checkout_location 
ON field_staff_attendance USING GIST (check_out_location) 
WHERE check_out_location IS NOT NULL;

-- Update RLS policies if needed to handle optional check-out locations
-- (Existing policies should continue to work as they primarily focus on staff_id)
