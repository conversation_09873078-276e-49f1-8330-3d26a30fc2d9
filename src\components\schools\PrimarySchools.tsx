
import React from 'react';
import { School } from 'lucide-react';
import SchoolManagement from '../SchoolManagement';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

const PrimarySchools = () => {
  const { profile } = useAuth();

  return (
    <PageLayout>
      <PageHeader
        title="Primary Schools"
        description="View and manage primary schools (P1-P7)"
        icon={School}
      />

      <ContentCard noPadding>
        <SchoolManagement currentUser={profile} schoolTypeFilter="primary" />
      </ContentCard>
    </PageLayout>
  );
};

export default PrimarySchools;
