import { useQuery } from '@tanstack/react-query';
import { useAuth } from '@/hooks/useAuth';
import { useCurrentCheckInStatus } from './useGPSTracking';
import { useCurrentAttendance } from '@/hooks/field-staff/useFieldStaffAttendance';

export interface UnifiedCheckInStatus {
  isCheckedIn: boolean;
  checkInTime: string | null;
  checkOutTime: string | null;
  schoolId: string | null;
  schoolName: string | null;
  attendanceId: string | null;
  locationLogId: string | null;
  source: 'gps_tracking' | 'field_attendance' | 'none';
  lastUpdated: string | null;
}

/**
 * Unified hook for determining check-in status across the application.
 * This hook consolidates multiple data sources to provide a single source of truth
 * for check-in status, preventing inconsistencies between different components.
 *
 * Priority order:
 * 1. Field staff attendance system (field_staff_attendance) - primary system
 * 2. GPS tracking system (staff_location_logs) - legacy fallback
 * 3. None - user is not checked in
 */
export const useUnifiedCheckInStatus = () => {
  const { profile } = useAuth();
  const { data: gpsCheckIn, isLoading: gpsLoading } = useCurrentCheckInStatus();
  const { data: fieldAttendance, isLoading: attendanceLoading } = useCurrentAttendance();

  return useQuery({
    queryKey: ['unified-checkin-status', profile?.id, gpsCheckIn?.id, fieldAttendance?.id],
    queryFn: async (): Promise<UnifiedCheckInStatus> => {
      // Default state - not checked in
      const defaultStatus: UnifiedCheckInStatus = {
        isCheckedIn: false,
        checkInTime: null,
        checkOutTime: null,
        schoolId: null,
        schoolName: null,
        attendanceId: null,
        locationLogId: null,
        source: 'none',
        lastUpdated: null,
      };

      // Priority 1: Field staff attendance system (primary system)
      // Only consider field attendance from today to avoid stale data
      if (fieldAttendance && fieldAttendance.status === 'active' && !fieldAttendance.check_out_time) {
        const checkInDate = new Date(fieldAttendance.check_in_time);
        const today = new Date();
        const isToday = checkInDate.toDateString() === today.toDateString();

        if (isToday) {
          return {
            isCheckedIn: true,
            checkInTime: fieldAttendance.check_in_time,
            checkOutTime: fieldAttendance.check_out_time,
            schoolId: fieldAttendance.school_id,
            schoolName: fieldAttendance.school_name || null,
            attendanceId: fieldAttendance.id,
            locationLogId: null,
            source: 'field_attendance',
            lastUpdated: fieldAttendance.updated_at || fieldAttendance.check_in_time,
          };
        }
      }

      // Priority 2: GPS tracking system (legacy fallback)
      // Only consider GPS check-ins from today to avoid stale data
      if (gpsCheckIn && gpsCheckIn.check_in_status === 'checked_in') {
        const checkInDate = new Date(gpsCheckIn.check_in_time);
        const today = new Date();
        const isToday = checkInDate.toDateString() === today.toDateString();

        if (isToday) {
          return {
            isCheckedIn: true,
            checkInTime: gpsCheckIn.check_in_time,
            checkOutTime: gpsCheckIn.check_out_time,
            schoolId: gpsCheckIn.school_id,
            schoolName: gpsCheckIn.school_name || null,
            attendanceId: null,
            locationLogId: gpsCheckIn.id,
            source: 'gps_tracking',
            lastUpdated: gpsCheckIn.updated_at || gpsCheckIn.check_in_time,
          };
        }
      }

      // No active check-in found
      return defaultStatus;
    },
    enabled: !!profile?.id && !gpsLoading && !attendanceLoading,
    staleTime: 10000, // 10 seconds - balance between freshness and performance
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
  });
};

/**
 * Hook to get just the boolean check-in status for simple use cases
 */
export const useIsCheckedIn = () => {
  const { data: status, isLoading } = useUnifiedCheckInStatus();
  return {
    isCheckedIn: status?.isCheckedIn ?? false,
    isLoading,
  };
};

/**
 * Hook to get detailed check-in information for display purposes
 */
export const useCheckInDetails = () => {
  const { data: status, isLoading } = useUnifiedCheckInStatus();
  
  return {
    ...status,
    isLoading,
    displayLocation: status?.schoolName || 'Unknown location',
    displayTime: status?.checkInTime ? new Date(status.checkInTime).toLocaleTimeString() : null,
    duration: status?.checkInTime ? 
      Math.floor((Date.now() - new Date(status.checkInTime).getTime()) / (1000 * 60)) : null,
  };
};
