import React from 'react';
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, User, School, Clock } from 'lucide-react';
import TaskStatusBadge from '../TaskStatusBadge';
import TaskPriorityBadge from '../TaskPriorityBadge';
import { Database } from '@/integrations/supabase/types';

interface TaskDetailsOverviewProps {
  taskDetails: {
    title: string;
    description: string | null;
    priority: Database['public']['Enums']['task_priority'];
    status: Database['public']['Enums']['task_status'];
    due_date: string | null;
    assigned_to_name: string | null;
    created_by_name: string;
    school_name: string | null;
    created_at: string;
    updated_at: string;
  };
}

const TaskDetailsOverview: React.FC<TaskDetailsOverviewProps> = ({ taskDetails }) => {
  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const isOverdue = taskDetails.due_date && 
    new Date(taskDetails.due_date) < new Date() && 
    taskDetails.status !== 'completed';

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Task Overview</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          <TaskPriorityBadge priority={taskDetails.priority} />
          <TaskStatusBadge status={taskDetails.status} />
          {isOverdue && (
            <Badge variant="destructive">Overdue</Badge>
          )}
        </div>

        {taskDetails.description && (
          <div>
            <h4 className="font-medium text-gray-900 mb-2">Description</h4>
            <p className="text-gray-600 whitespace-pre-wrap">
              {taskDetails.description}
            </p>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          {taskDetails.assigned_to_name && (
            <div className="flex items-center gap-2">
              <User className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">Assigned to:</span>
              <span className="font-medium">{taskDetails.assigned_to_name}</span>
            </div>
          )}

          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Created by:</span>
            <span className="font-medium">{taskDetails.created_by_name}</span>
          </div>

          {taskDetails.school_name && (
            <div className="flex items-center gap-2">
              <School className="h-4 w-4 text-gray-400" />
              <span className="text-gray-600">School:</span>
              <span className="font-medium">{taskDetails.school_name}</span>
            </div>
          )}

          <div className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Due date:</span>
            <span className={`font-medium ${isOverdue ? 'text-red-600' : ''}`}>
              {formatDate(taskDetails.due_date)}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Created:</span>
            <span className="font-medium">{formatDate(taskDetails.created_at)}</span>
          </div>

          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-gray-400" />
            <span className="text-gray-600">Updated:</span>
            <span className="font-medium">{formatDate(taskDetails.updated_at)}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskDetailsOverview;
