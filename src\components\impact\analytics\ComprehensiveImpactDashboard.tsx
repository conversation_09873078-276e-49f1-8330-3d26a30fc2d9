import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import {
  Target,
  TrendingUp,
  Users,
  School,
  Award,
  MessageSquare,
  Calendar,
  Download,
  Filter,
  BarChart3,
  Activity
} from 'lucide-react';
import { PageLayout, PageHeader } from '@/components/layout';
import { MetricCard } from '../shared';

interface ComprehensiveImpactDashboardProps {
  schoolId?: string | null;
  dateRange: {
    start: Date;
    end: Date;
  };
  canViewAllData: boolean;
}

const ComprehensiveImpactDashboard: React.FC<ComprehensiveImpactDashboardProps> = ({
  schoolId,
  dateRange,
  canViewAllData
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('quarterly');
  const [selectedMetric, setSelectedMetric] = useState('all');

  // Mock comprehensive impact data
  const overallImpactMetrics = [
    { 
      category: 'Student Outcomes', 
      current: 85, 
      target: 90, 
      baseline: 65,
      trend: '+12%',
      color: '#10B981'
    },
    { 
      category: 'School Performance', 
      current: 78, 
      target: 85, 
      baseline: 60,
      trend: '+8%',
      color: '#3B82F6'
    },
    { 
      category: 'Teacher Training', 
      current: 92, 
      target: 90, 
      baseline: 70,
      trend: '+15%',
      color: '#8B5CF6'
    },
    { 
      category: 'Community Satisfaction', 
      current: 88, 
      target: 85, 
      baseline: 72,
      trend: '+10%',
      color: '#F59E0B'
    },
    { 
      category: 'Infrastructure', 
      current: 75, 
      target: 80, 
      baseline: 45,
      trend: '+18%',
      color: '#EF4444'
    }
  ];

  const monthlyTrends = [
    { month: 'Jan', students: 82, schools: 75, teachers: 88, community: 85 },
    { month: 'Feb', students: 84, schools: 77, teachers: 90, community: 86 },
    { month: 'Mar', students: 86, schools: 78, teachers: 91, community: 87 },
    { month: 'Apr', students: 85, schools: 80, teachers: 92, community: 88 },
    { month: 'May', students: 87, schools: 82, teachers: 93, community: 89 },
    { month: 'Jun', students: 89, schools: 84, teachers: 94, community: 90 }
  ];

  const impactDistribution = [
    { name: 'Direct Beneficiaries', value: 2847, color: '#10B981' },
    { name: 'Indirect Beneficiaries', value: 8541, color: '#3B82F6' },
    { name: 'Community Members', value: 15623, color: '#8B5CF6' },
    { name: 'Stakeholders', value: 456, color: '#F59E0B' }
  ];

  const radarData = [
    {
      subject: 'Learning Outcomes',
      current: 85,
      target: 90,
      baseline: 65
    },
    {
      subject: 'Attendance',
      current: 88,
      target: 90,
      baseline: 70
    },
    {
      subject: 'Infrastructure',
      current: 75,
      target: 80,
      baseline: 45
    },
    {
      subject: 'Teacher Quality',
      current: 92,
      target: 90,
      baseline: 70
    },
    {
      subject: 'Community Engagement',
      current: 88,
      target: 85,
      baseline: 72
    },
    {
      subject: 'Resource Availability',
      current: 78,
      target: 85,
      baseline: 55
    }
  ];



  // Access control is now handled by AdminOnlyWrapper

  return (
    <PageLayout>
      <PageHeader
        title="Comprehensive Impact Dashboard"
        description="Holistic view of program impact across all dimensions"
        icon={Target}
        actions={[
          {
            label: 'Filters',
            onClick: () => {},
            icon: Filter,
            variant: 'outline'
          },
          {
            label: 'Export Report',
            onClick: () => {},
            icon: Download,
          }
        ]}
      >
        <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
          <SelectTrigger className="w-32">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="monthly">Monthly</SelectItem>
            <SelectItem value="quarterly">Quarterly</SelectItem>
            <SelectItem value="annual">Annual</SelectItem>
          </SelectContent>
        </Select>
      </PageHeader>

      {/* Key Achievements */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard
          title="Students Impacted"
          value="2,847"
          icon={Users}
          color="blue"
        />
        <MetricCard
          title="Schools Improved"
          value="23"
          icon={School}
          color="green"
        />
        <MetricCard
          title="Teachers Trained"
          value="156"
          icon={Award}
          color="purple"
        />
        <MetricCard
          title="Satisfaction Rate"
          value="89%"
          icon={MessageSquare}
          color="orange"
        />
      </div>

      {/* Impact Overview Radar Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="h-5 w-5" />
            <span>Impact Overview</span>
          </CardTitle>
          <CardDescription>
            Current performance vs targets and baseline across all impact areas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <RadarChart data={radarData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="subject" />
              <PolarRadiusAxis angle={90} domain={[0, 100]} />
              <Radar
                name="Current"
                dataKey="current"
                stroke="#10B981"
                fill="#10B981"
                fillOpacity={0.3}
                strokeWidth={2}
              />
              <Radar
                name="Target"
                dataKey="target"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.1}
                strokeWidth={2}
                strokeDasharray="5 5"
              />
              <Radar
                name="Baseline"
                dataKey="baseline"
                stroke="#94A3B8"
                fill="#94A3B8"
                fillOpacity={0.1}
                strokeWidth={1}
              />
              <Legend />
            </RadarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Trends and Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Impact Trends</CardTitle>
            <CardDescription>
              Monthly progress across key impact areas
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={monthlyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis domain={[70, 100]} />
                <Tooltip />
                <Legend />
                <Line type="monotone" dataKey="students" stroke="#10B981" strokeWidth={2} name="Student Outcomes" />
                <Line type="monotone" dataKey="schools" stroke="#3B82F6" strokeWidth={2} name="School Performance" />
                <Line type="monotone" dataKey="teachers" stroke="#8B5CF6" strokeWidth={2} name="Teacher Training" />
                <Line type="monotone" dataKey="community" stroke="#F59E0B" strokeWidth={2} name="Community Satisfaction" />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Beneficiary Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Beneficiary Reach</CardTitle>
            <CardDescription>
              Distribution of program beneficiaries
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={impactDistribution}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, value }) => `${name}: ${value.toLocaleString()}`}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {impactDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => value.toLocaleString()} />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Impact Metrics Comparison */}
      <Card>
        <CardHeader>
          <CardTitle>Impact Metrics Performance</CardTitle>
          <CardDescription>
            Current performance vs targets across all impact categories
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ResponsiveContainer width="100%" height={400}>
            <BarChart data={overallImpactMetrics} layout="horizontal">
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis type="number" domain={[0, 100]} />
              <YAxis dataKey="category" type="category" width={120} />
              <Tooltip />
              <Legend />
              <Bar dataKey="baseline" fill="#94A3B8" name="Baseline" />
              <Bar dataKey="current" fill="#10B981" name="Current" />
              <Bar dataKey="target" fill="#3B82F6" name="Target" />
            </BarChart>
          </ResponsiveContainer>
        </CardContent>
      </Card>

      {/* Summary Insights */}
      <Card>
        <CardHeader>
          <CardTitle>Key Insights & Recommendations</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-green-800 mb-3">Achievements</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <p className="text-sm">Teacher training exceeded targets by 2%</p>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <p className="text-sm">Community satisfaction above target</p>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <p className="text-sm">Significant improvement from baseline across all areas</p>
                </div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-orange-800 mb-3">Areas for Focus</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <p className="text-sm">Infrastructure improvements need acceleration</p>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <p className="text-sm">Student outcomes approaching but not yet at target</p>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                  <p className="text-sm">School performance metrics need targeted interventions</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </PageLayout>
  );
};

export default ComprehensiveImpactDashboard;
