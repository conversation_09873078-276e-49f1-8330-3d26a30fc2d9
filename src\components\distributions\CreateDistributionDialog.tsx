
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Plus, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useDistributionForm, DistributionSubmissionData } from '@/hooks/useDistributionForm';
import { Database } from '@/integrations/supabase/types';

// Type definitions
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type BookInventory = Database['public']['Functions']['get_book_inventory']['Returns'][0];
type Profile = Database['public']['Functions']['get_user_profile']['Returns'][0];

interface CreateDistributionDialogProps {
  schools: SchoolWithDivision[];
  inventory: BookInventory[];
  currentUser: Profile;
  onSubmit: (data: DistributionSubmissionData) => void;
  isSubmitting: boolean;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

const CreateDistributionDialog = ({
  schools,
  inventory,
  currentUser,
  onSubmit,
  isSubmitting,
  open,
  onOpenChange
}: CreateDistributionDialogProps) => {
  const [internalOpen, setInternalOpen] = useState(false);

  // Use external state if provided, otherwise use internal state
  const isOpen = open !== undefined ? open : internalOpen;
  const setIsOpen = onOpenChange || setInternalOpen;
  const { formData, resetForm, updateField, validateForm } = useDistributionForm();
  const { toast } = useToast();
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  // Get selected inventory item details
  const selectedInventory = inventory.find((item: BookInventory) => item.id === formData.inventory_id);
  const availableQuantity = selectedInventory?.quantity_available || 0;
  const requestedQuantity = parseInt(formData.quantity) || 0;

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const validation = validateForm();
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      return;
    }

    // Check if selected inventory exists
    if (!selectedInventory) {
      toast({
        title: "Error",
        description: "Please select a valid book from inventory",
        variant: "destructive",
      });
      return;
    }

    // Check inventory availability
    if (requestedQuantity > availableQuantity) {
      toast({
        title: "Insufficient Inventory",
        description: `Only ${availableQuantity} copies available. You requested ${requestedQuantity}.`,
        variant: "destructive",
      });
      return;
    }

    // Prepare submission data
    const submissionData: DistributionSubmissionData = {
      school_id: formData.school_id,
      inventory_id: formData.inventory_id,
      quantity: requestedQuantity,
      supervisor_id: currentUser?.id || '',
      notes: formData.notes || '',
      book_title: selectedInventory.book_title, // For compatibility with existing RPC
      status: formData.status,
      distribution_date: formData.distribution_date,
    };

    onSubmit(submissionData);
    resetForm();
    setIsOpen(false);
    setValidationErrors([]);
  };

  const handleDialogChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setValidationErrors([]);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogChange}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Log Book Distribution</DialogTitle>
          <DialogDescription>
            Record a new book delivery to a school. Select books from available inventory.
          </DialogDescription>
        </DialogHeader>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* School Selection */}
          <div className="space-y-2">
            <Label htmlFor="school">School *</Label>
            <Select
              value={formData.school_id}
              onValueChange={(value) => updateField('school_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a school" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                {schools.filter((school: SchoolWithDivision) => school.id && school.id.trim() !== '').map((school: SchoolWithDivision) => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          {/* Book Selection */}
          <div className="space-y-2">
            <Label htmlFor="book">Book *</Label>
            <Select
              value={formData.inventory_id}
              onValueChange={(value) => updateField('inventory_id', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a book from inventory" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                {inventory.filter((item: BookInventory) => item.id && item.id.trim() !== '').map((item: BookInventory) => (
                  <SelectItem key={item.id} value={item.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{item.book_title}</span>
                      <span className="text-sm text-gray-500">
                        {item.grade_level} • {item.subject} • {item.quantity_available} available
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Show selected book details */}
            {selectedInventory && (
              <div className="p-3 bg-blue-50 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-blue-900">{selectedInventory.book_title}</p>
                    <p className="text-sm text-blue-700">
                      {selectedInventory.grade_level} • {selectedInventory.subject} • {selectedInventory.language}
                    </p>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {selectedInventory.quantity_available} available
                  </Badge>
                </div>
              </div>
            )}
          </div>
          {/* Quantity Input */}
          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity *</Label>
            <Input
              id="quantity"
              type="number"
              value={formData.quantity}
              onChange={(e) => updateField('quantity', e.target.value)}
              placeholder="Number of books"
              required
              min="1"
              max={availableQuantity}
            />
            {selectedInventory && requestedQuantity > 0 && (
              <div className="text-sm">
                {requestedQuantity <= availableQuantity ? (
                  <span className="text-green-600">
                    ✓ {requestedQuantity} of {availableQuantity} available
                  </span>
                ) : (
                  <span className="text-red-600">
                    ⚠ Only {availableQuantity} available, you requested {requestedQuantity}
                  </span>
                )}
              </div>
            )}
          </div>

          {/* Status Selection */}
          <div className="space-y-2">
            <Label htmlFor="status">Status *</Label>
            <Select
              value={formData.status}
              onValueChange={(value: 'planned' | 'in_progress' | 'completed' | 'cancelled') => updateField('status', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select distribution status" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                <SelectItem value="completed">Completed (Delivered)</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="planned">Planned</SelectItem>
                <SelectItem value="cancelled">Cancelled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Distribution Date */}
          <div className="space-y-2">
            <Label htmlFor="distribution_date">Distribution Date *</Label>
            <Input
              id="distribution_date"
              type="date"
              value={formData.distribution_date || ''}
              onChange={(e) => updateField('distribution_date', e.target.value)}
              required
            />
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => updateField('notes', e.target.value)}
              placeholder="Any additional notes about the delivery"
              rows={3}
            />
          </div>

          <Button
            type="submit"
            className="w-full bg-green-600 hover:bg-green-700"
            disabled={isSubmitting || (selectedInventory && requestedQuantity > availableQuantity)}
          >
            {isSubmitting ? 'Logging Distribution...' : 'Log Distribution'}
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default CreateDistributionDialog;
