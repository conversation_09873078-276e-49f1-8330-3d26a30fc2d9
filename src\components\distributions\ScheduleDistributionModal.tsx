import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Calendar, Plus, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useDistributions } from '@/hooks/useDistributions';
import { DistributionSubmissionData } from '@/hooks/useDistributionForm';
import { Database } from '@/integrations/supabase/types';
import { useAuth } from '@/hooks/useAuth';

// Type definitions
type SchoolWithDivision = Database['public']['Functions']['get_schools_with_divisions']['Returns'][0];
type BookInventory = Database['public']['Functions']['get_book_inventory']['Returns'][0];

interface ScheduleFormData {
  school_id: string;
  inventory_id: string;
  quantity: string;
  scheduled_date: string;
  priority: 'low' | 'medium' | 'high';
  notes: string;
}

interface ScheduleDistributionModalProps {
  schools: SchoolWithDivision[];
  inventory: BookInventory[];
  onScheduled?: () => void;
  isOpen?: boolean;
  onClose?: () => void;
}

const ScheduleDistributionModal = ({
  schools,
  inventory,
  onScheduled,
  isOpen: externalIsOpen = false,
  onClose: externalOnClose
}: ScheduleDistributionModalProps) => {
  const { profile } = useAuth();
  const { addDistribution, isAdding } = useDistributions();
  const { toast } = useToast();
  const [internalIsOpen, setInternalIsOpen] = useState(false);

  // Use external control if provided, otherwise use internal state
  const isOpen = externalOnClose ? externalIsOpen : internalIsOpen;
  const setIsOpen = externalOnClose ? (open: boolean) => {
    if (!open) externalOnClose();
  } : setInternalIsOpen;
  const [scheduleData, setScheduleData] = useState<ScheduleFormData>({
    school_id: '',
    inventory_id: '',
    quantity: '',
    scheduled_date: '',
    priority: 'medium',
    notes: '',
  });
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const selectedInventory = inventory.find((item: BookInventory) => item.id === scheduleData.inventory_id);
  const availableQuantity = selectedInventory?.quantity_available || 0;
  const requestedQuantity = parseInt(scheduleData.quantity) || 0;

  const validateForm = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = [];

    if (!scheduleData.school_id) {
      errors.push('Please select a school');
    }

    if (!scheduleData.inventory_id) {
      errors.push('Please select a book from inventory');
    }

    if (!scheduleData.quantity || requestedQuantity <= 0) {
      errors.push('Please enter a valid quantity greater than 0');
    }

    if (!scheduleData.scheduled_date) {
      errors.push('Please select a scheduled date');
    }

    if (selectedInventory && requestedQuantity > availableQuantity) {
      errors.push(`Only ${availableQuantity} copies available, you requested ${requestedQuantity}`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  };

  const handleSchedule = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate form
    const validation = validateForm();
    if (!validation.isValid) {
      setValidationErrors(validation.errors);
      return;
    }

    if (!selectedInventory) {
      toast({
        title: "Error",
        description: "Please select a valid book from inventory",
        variant: "destructive",
      });
      return;
    }

    // Create a planned distribution
    const distributionData: DistributionSubmissionData = {
      school_id: scheduleData.school_id,
      inventory_id: scheduleData.inventory_id,
      quantity: requestedQuantity,
      supervisor_id: profile?.id || '',
      notes: `Scheduled for ${scheduleData.scheduled_date}. Priority: ${scheduleData.priority}. ${scheduleData.notes}`.trim(),
      book_title: selectedInventory.book_title,
      status: 'planned', // Set status to planned for scheduled distributions
    };

    addDistribution(distributionData);
    
    // Reset form and close modal
    setScheduleData({
      school_id: '',
      inventory_id: '',
      quantity: '',
      scheduled_date: '',
      priority: 'medium',
      notes: '',
    });
    setValidationErrors([]);
    setIsOpen(false);
    
    if (onScheduled) {
      onScheduled();
    }
  };

  const handleDialogChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setValidationErrors([]);
    }
  };

  const modalContent = (
    <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Schedule New Distribution</DialogTitle>
          <DialogDescription>
            Plan a distribution for future execution. This will create a planned distribution.
          </DialogDescription>
        </DialogHeader>

        {/* Validation Errors */}
        {validationErrors.length > 0 && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {validationErrors.map((error, index) => (
                  <li key={index}>{error}</li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSchedule} className="space-y-4">
          {/* School Selection */}
          <div className="space-y-2">
            <Label htmlFor="school">School *</Label>
            <Select 
              value={scheduleData.school_id} 
              onValueChange={(value) => setScheduleData({ ...scheduleData, school_id: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a school" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                {schools.map((school: SchoolWithDivision) => (
                  <SelectItem key={school.id} value={school.id}>
                    {school.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Book Selection */}
          <div className="space-y-2">
            <Label htmlFor="book">Book *</Label>
            <Select 
              value={scheduleData.inventory_id} 
              onValueChange={(value) => setScheduleData({ ...scheduleData, inventory_id: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select a book from inventory" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                {inventory.map((item: BookInventory) => (
                  <SelectItem key={item.id} value={item.id}>
                    <div className="flex flex-col">
                      <span className="font-medium">{item.book_title}</span>
                      <span className="text-sm text-gray-500">
                        {item.grade_level} • {item.subject} • {item.quantity_available} available
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Show selected book details */}
            {selectedInventory && (
              <div className="p-3 bg-blue-50 rounded-lg border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium text-blue-900">{selectedInventory.book_title}</p>
                    <p className="text-sm text-blue-700">
                      {selectedInventory.grade_level} • {selectedInventory.subject} • {selectedInventory.language}
                    </p>
                  </div>
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800">
                    {selectedInventory.quantity_available} available
                  </Badge>
                </div>
              </div>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            {/* Quantity */}
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity *</Label>
              <Input
                id="quantity"
                type="number"
                value={scheduleData.quantity}
                onChange={(e) => setScheduleData({ ...scheduleData, quantity: e.target.value })}
                placeholder="Number of books"
                required
                min="1"
                max={availableQuantity}
              />
              {selectedInventory && requestedQuantity > 0 && (
                <div className="text-sm">
                  {requestedQuantity <= availableQuantity ? (
                    <span className="text-green-600">
                      ✓ {requestedQuantity} of {availableQuantity} available
                    </span>
                  ) : (
                    <span className="text-red-600">
                      ⚠ Only {availableQuantity} available, you requested {requestedQuantity}
                    </span>
                  )}
                </div>
              )}
            </div>

            {/* Scheduled Date */}
            <div className="space-y-2">
              <Label htmlFor="scheduled_date">Scheduled Date *</Label>
              <Input
                id="scheduled_date"
                type="date"
                value={scheduleData.scheduled_date}
                onChange={(e) => setScheduleData({ ...scheduleData, scheduled_date: e.target.value })}
                required
                min={new Date().toISOString().split('T')[0]} // Prevent past dates
              />
            </div>
          </div>

          {/* Priority */}
          <div className="space-y-2">
            <Label htmlFor="priority">Priority</Label>
            <Select 
              value={scheduleData.priority} 
              onValueChange={(value: 'low' | 'medium' | 'high') => setScheduleData({ ...scheduleData, priority: value })}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select priority level" />
              </SelectTrigger>
              <SelectContent className="bg-white z-50">
                <SelectItem value="high">High Priority</SelectItem>
                <SelectItem value="medium">Medium Priority</SelectItem>
                <SelectItem value="low">Low Priority</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Notes */}
          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={scheduleData.notes}
              onChange={(e) => setScheduleData({ ...scheduleData, notes: e.target.value })}
              placeholder="Any additional notes about the scheduled distribution"
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              className="bg-green-600 hover:bg-green-700"
              disabled={isAdding || (selectedInventory && requestedQuantity > availableQuantity)}
            >
              <Plus className="h-4 w-4 mr-2" />
              {isAdding ? 'Scheduling...' : 'Schedule Distribution'}
            </Button>
          </div>
        </form>
    </DialogContent>
  );

  return (
    <>
      {!externalOnClose && (
        <Dialog open={isOpen} onOpenChange={handleDialogChange}>
          <DialogTrigger asChild>
            <Button className="bg-purple-600 hover:bg-purple-700">
              <Calendar className="h-4 w-4 mr-2" />
              Schedule New
            </Button>
          </DialogTrigger>
          {modalContent}
        </Dialog>
      )}

      {externalOnClose && (
        <Dialog open={isOpen} onOpenChange={handleDialogChange}>
          {modalContent}
        </Dialog>
      )}
    </>
  );
};

export default ScheduleDistributionModal;
