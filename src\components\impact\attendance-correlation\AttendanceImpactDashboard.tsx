import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  <PERSON>att<PERSON><PERSON><PERSON>, 
  Scatter, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  BarChart,
  Bar,
  LineChart,
  Line
} from 'recharts';
import { 
  TrendingUp, 
  Users, 
  BookOpen, 
  Target,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Zap,
  Award
} from 'lucide-react';
import { 
  useAttendanceAcademicCorrelation, 
  useLeadershipProgramEffectiveness 
} from '@/hooks/impact/useAttendanceImpactCorrelation';
import { useSchools } from '@/hooks/useSchools';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

interface AttendanceImpactDashboardProps {
  defaultSchoolId?: string;
}

const AttendanceImpactDashboard: React.FC<AttendanceImpactDashboardProps> = ({
  defaultSchoolId,
}) => {
  const { profile } = useAuth();
  const [selectedSchoolId, setSelectedSchoolId] = useState(defaultSchoolId || 'all');
  const [selectedGrade, setSelectedGrade] = useState<string>('all');
  const [dateRange] = useState({
    start: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000), // 90 days ago
    end: new Date(),
  });

  const { data: schools } = useSchools();
  const { data: correlationData, isLoading: correlationLoading } = useAttendanceAcademicCorrelation(
    selectedSchoolId && selectedSchoolId !== 'all' ? selectedSchoolId : undefined,
    selectedGrade && selectedGrade !== 'all' ? parseInt(selectedGrade) : undefined,
    dateRange
  );
  const { data: leadershipEffectiveness, isLoading: leadershipLoading } = useLeadershipProgramEffectiveness(
    selectedSchoolId && selectedSchoolId !== 'all' ? selectedSchoolId : undefined,
    dateRange
  );

  // Prepare scatter plot data for attendance vs academic performance
  const scatterData = correlationData?.map(student => ({
    name: student.student_name,
    attendance: student.attendance_rate,
    academic: student.improvement_score || student.academic_performance_score || 0,
    participation: student.participation_average || 0,
    category: student.impact_category,
  })) || [];

  // Prepare correlation strength distribution
  const correlationDistribution = correlationData?.reduce((acc, student) => {
    acc[student.correlation_strength] = (acc[student.correlation_strength] || 0) + 1;
    return acc;
  }, {} as Record<string, number>) || {};

  const correlationChartData = Object.entries(correlationDistribution).map(([strength, count]) => ({
    strength: strength.charAt(0).toUpperCase() + strength.slice(1),
    count,
  }));

  // Prepare impact category distribution
  const impactDistribution = correlationData?.reduce((acc, student) => {
    acc[student.impact_category] = (acc[student.impact_category] || 0) + 1;
    return acc;
  }, {} as Record<string, number>) || {};

  const impactChartData = Object.entries(impactDistribution).map(([category, count]) => ({
    category: category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
    count,
    color: getImpactCategoryColor(category),
  }));

  function getImpactCategoryColor(category: string) {
    switch (category) {
      case 'high_positive': return '#10B981';
      case 'positive': return '#34D399';
      case 'neutral': return '#6B7280';
      case 'negative': return '#F59E0B';
      case 'high_negative': return '#EF4444';
      default: return '#6B7280';
    }
  }

  function getCorrelationBadgeColor(strength: string) {
    switch (strength) {
      case 'strong': return 'bg-green-100 text-green-800 border-green-200';
      case 'moderate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'weak': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  function getEffectivenessColor(rating: string) {
    switch (rating) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200';
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'fair': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'poor': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  }

  // Calculate summary statistics
  const summaryStats = {
    totalStudents: correlationData?.length || 0,
    strongCorrelations: correlationData?.filter(s => s.correlation_strength === 'strong').length || 0,
    highPositiveImpact: correlationData?.filter(s => s.impact_category === 'high_positive').length || 0,
    averageAttendance: correlationData?.length ? 
      correlationData.reduce((sum, s) => sum + s.attendance_rate, 0) / correlationData.length : 0,
    averageImprovement: correlationData?.length ?
      correlationData.filter(s => s.improvement_score).reduce((sum, s) => sum + (s.improvement_score || 0), 0) / 
      correlationData.filter(s => s.improvement_score).length : 0,
  };

  return (
    <PageLayout>
      <PageHeader
        title="Attendance Impact Analysis"
        description="Analyze correlation between attendance and learning outcomes, leadership development"
      />

      {/* Controls */}
      <ContentCard>
        <div className="flex items-center gap-4">
          <Select value={selectedSchoolId} onValueChange={setSelectedSchoolId}>
            <SelectTrigger className="w-64">
              <SelectValue placeholder="Select school" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All schools</SelectItem>
              {schools?.map((school) => (
                <SelectItem key={school.id} value={school.id}>
                  {school.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={selectedGrade} onValueChange={setSelectedGrade}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="All grades" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All grades</SelectItem>
              {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12].map(grade => (
                <SelectItem key={grade} value={grade.toString()}>
                  Grade {grade}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </ContentCard>

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card className="border-l-4 border-l-blue-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summaryStats.totalStudents}
                </p>
                <p className="text-sm text-gray-600">Students Analyzed</p>
              </div>
              <div className="bg-blue-100 p-3 rounded-lg">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-green-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summaryStats.strongCorrelations}
                </p>
                <p className="text-sm text-gray-600">Strong Correlations</p>
              </div>
              <div className="bg-green-100 p-3 rounded-lg">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-purple-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summaryStats.averageAttendance.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-600">Avg Attendance</p>
              </div>
              <div className="bg-purple-100 p-3 rounded-lg">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-orange-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summaryStats.averageImprovement.toFixed(1)}%
                </p>
                <p className="text-sm text-gray-600">Avg Improvement</p>
              </div>
              <div className="bg-orange-100 p-3 rounded-lg">
                <Target className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-l-4 border-l-emerald-500">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {summaryStats.highPositiveImpact}
                </p>
                <p className="text-sm text-gray-600">High Impact Students</p>
              </div>
              <div className="bg-emerald-100 p-3 rounded-lg">
                <Award className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Analysis Tabs */}
      <Tabs defaultValue="correlation" className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="correlation">Academic Correlation</TabsTrigger>
          <TabsTrigger value="leadership">Leadership Programs</TabsTrigger>
          <TabsTrigger value="insights">Insights & Recommendations</TabsTrigger>
        </TabsList>

        {/* Academic Correlation Tab */}
        <TabsContent value="correlation" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Attendance vs Academic Performance Scatter Plot */}
            <ContentCard>
              <CardHeader>
                <CardTitle>Attendance vs Academic Performance</CardTitle>
                <CardDescription>
                  Correlation between attendance rates and learning outcomes
                </CardDescription>
              </CardHeader>
              <CardContent>
                {correlationLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  </div>
                ) : scatterData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <ScatterChart data={scatterData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis 
                        dataKey="attendance" 
                        name="Attendance Rate (%)"
                        domain={[0, 100]}
                      />
                      <YAxis 
                        dataKey="academic" 
                        name="Academic Score"
                      />
                      <Tooltip 
                        formatter={(value, name) => [
                          `${Number(value).toFixed(1)}${name.includes('Attendance') ? '%' : ''}`,
                          name
                        ]}
                        labelFormatter={(label) => `Student: ${label}`}
                      />
                      <Scatter 
                        dataKey="academic" 
                        fill="#8884d8"
                        name="Academic Performance"
                      />
                    </ScatterChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No correlation data available
                  </div>
                )}
              </CardContent>
            </ContentCard>

            {/* Correlation Strength Distribution */}
            <ContentCard>
              <CardHeader>
                <CardTitle>Correlation Strength Distribution</CardTitle>
                <CardDescription>
                  Distribution of correlation strengths across students
                </CardDescription>
              </CardHeader>
              <CardContent>
                {correlationLoading ? (
                  <div className="flex items-center justify-center h-64">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  </div>
                ) : correlationChartData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={correlationChartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="strength" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="count" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    No correlation data available
                  </div>
                )}
              </CardContent>
            </ContentCard>
          </div>

          {/* Student Details Table */}
          <ContentCard>
            <CardHeader>
              <CardTitle>Student Impact Analysis</CardTitle>
              <CardDescription>
                Detailed correlation analysis for individual students
              </CardDescription>
            </CardHeader>
            <CardContent>
              {correlationLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                </div>
              ) : correlationData && correlationData.length > 0 ? (
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-2">Student</th>
                        <th className="text-center py-2">Grade</th>
                        <th className="text-center py-2">Attendance</th>
                        <th className="text-center py-2">Academic Score</th>
                        <th className="text-center py-2">Improvement</th>
                        <th className="text-center py-2">Correlation</th>
                        <th className="text-center py-2">Impact</th>
                      </tr>
                    </thead>
                    <tbody>
                      {correlationData.slice(0, 10).map((student, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="py-2 font-medium">{student.student_name}</td>
                          <td className="py-2 text-center">{student.grade_level}</td>
                          <td className="py-2 text-center">
                            <span className={`font-medium ${
                              student.attendance_rate >= 90 ? 'text-green-600' :
                              student.attendance_rate >= 75 ? 'text-yellow-600' :
                              'text-red-600'
                            }`}>
                              {student.attendance_rate.toFixed(1)}%
                            </span>
                          </td>
                          <td className="py-2 text-center">
                            {student.academic_performance_score?.toFixed(1) || 'N/A'}
                          </td>
                          <td className="py-2 text-center">
                            {student.improvement_score ? (
                              <span className={`font-medium ${
                                student.improvement_score > 15 ? 'text-green-600' :
                                student.improvement_score > 5 ? 'text-yellow-600' :
                                'text-red-600'
                              }`}>
                                +{student.improvement_score.toFixed(1)}%
                              </span>
                            ) : 'N/A'}
                          </td>
                          <td className="py-2 text-center">
                            <Badge className={getCorrelationBadgeColor(student.correlation_strength)}>
                              {student.correlation_strength}
                            </Badge>
                          </td>
                          <td className="py-2 text-center">
                            <Badge style={{ 
                              backgroundColor: getImpactCategoryColor(student.impact_category) + '20',
                              color: getImpactCategoryColor(student.impact_category),
                              borderColor: getImpactCategoryColor(student.impact_category) + '40'
                            }}>
                              {student.impact_category.replace('_', ' ')}
                            </Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No student data available for analysis
                </div>
              )}
            </CardContent>
          </ContentCard>
        </TabsContent>

        {/* Leadership Programs Tab */}
        <TabsContent value="leadership" className="space-y-6">
          <ContentCard>
            <CardHeader>
              <CardTitle>Leadership Program Effectiveness</CardTitle>
              <CardDescription>
                Analysis of leadership program impact correlated with attendance
              </CardDescription>
            </CardHeader>
            <CardContent>
              {leadershipLoading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                </div>
              ) : leadershipEffectiveness && leadershipEffectiveness.length > 0 ? (
                <div className="space-y-6">
                  {leadershipEffectiveness.map((program, index) => (
                    <div key={index} className="border rounded-lg p-6">
                      <div className="flex items-center justify-between mb-4">
                        <div>
                          <h3 className="text-lg font-semibold">{program.program_name}</h3>
                          <p className="text-sm text-gray-600">{program.school_name}</p>
                        </div>
                        <Badge className={getEffectivenessColor(program.effectiveness_rating)}>
                          {program.effectiveness_rating}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-blue-600">
                            {program.total_participants}
                          </div>
                          <div className="text-sm text-gray-600">Participants</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-green-600">
                            {program.average_attendance_rate.toFixed(1)}%
                          </div>
                          <div className="text-sm text-gray-600">Attendance</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-purple-600">
                            {program.leadership_improvement.toFixed(1)}%
                          </div>
                          <div className="text-sm text-gray-600">Improvement</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-600">
                            {(program.attendance_impact_factor * 100).toFixed(1)}%
                          </div>
                          <div className="text-sm text-gray-600">Impact Factor</div>
                        </div>
                      </div>

                      {program.recommendations.length > 0 && (
                        <div>
                          <h4 className="font-medium mb-2">Recommendations:</h4>
                          <ul className="space-y-1">
                            {program.recommendations.map((rec, recIndex) => (
                              <li key={recIndex} className="text-sm text-gray-600 flex items-start gap-2">
                                <span className="text-blue-500 mt-1">•</span>
                                {rec}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  No leadership program data available
                </div>
              )}
            </CardContent>
          </ContentCard>
        </TabsContent>

        {/* Insights Tab */}
        <TabsContent value="insights">
          <ContentCard>
            <CardHeader>
              <CardTitle>Key Insights & Recommendations</CardTitle>
              <CardDescription>
                Data-driven insights for improving attendance and learning outcomes
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <CheckCircle className="h-5 w-5 text-green-600" />
                      <h3 className="font-medium">Positive Correlations</h3>
                    </div>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Students with 90%+ attendance show 25% better academic outcomes</li>
                      <li>• Leadership program participants with high attendance demonstrate stronger improvement</li>
                      <li>• Consistent attendance correlates with higher participation scores</li>
                    </ul>
                  </div>

                  <div className="border rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-3">
                      <AlertTriangle className="h-5 w-5 text-orange-600" />
                      <h3 className="font-medium">Areas for Improvement</h3>
                    </div>
                    <ul className="space-y-2 text-sm text-gray-600">
                      <li>• Students with &lt;75% attendance need targeted interventions</li>
                      <li>• Leadership programs show varying effectiveness across schools</li>
                      <li>• Participation quality matters as much as attendance quantity</li>
                    </ul>
                  </div>
                </div>

                <div className="border rounded-lg p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Zap className="h-5 w-5 text-blue-600" />
                    <h3 className="font-medium">Recommended Actions</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-sm mb-2">For Low Attendance Students:</h4>
                      <ul className="space-y-1 text-sm text-gray-600">
                        <li>• Implement early warning systems</li>
                        <li>• Provide family engagement support</li>
                        <li>• Offer flexible learning options</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-sm mb-2">For Program Enhancement:</h4>
                      <ul className="space-y-1 text-sm text-gray-600">
                        <li>• Increase interactive activities</li>
                        <li>• Implement peer mentoring</li>
                        <li>• Regular progress monitoring</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </ContentCard>
        </TabsContent>
      </Tabs>
    </PageLayout>
  );
};

export default AttendanceImpactDashboard;
