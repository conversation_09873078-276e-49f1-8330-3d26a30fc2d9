import { BookCondition, BookLanguage } from '@/types/book';

export interface BookFormData {
  title: string;
  language: BookLanguage;
  description: string;
  total_quantity: string;
  condition: BookCondition;
  minimum_threshold: string;
  notes: string;
}

export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  fieldErrors: Record<string, string>;
}

export interface ISBNValidationResult {
  isValid: boolean;
  type: 'ISBN-10' | 'ISBN-13' | 'invalid';
  formatted: string;
}

/**
 * Validates ISBN-10 format and checksum
 */
export function validateISBN10(isbn: string): boolean {
  // Remove any hyphens or spaces
  const cleanISBN = isbn.replace(/[-\s]/g, '');
  
  // Check if it's exactly 10 characters
  if (cleanISBN.length !== 10) return false;
  
  // Check if first 9 are digits and last is digit or X
  if (!/^\d{9}[\dX]$/.test(cleanISBN)) return false;
  
  // Calculate checksum
  let sum = 0;
  for (let i = 0; i < 9; i++) {
    sum += parseInt(cleanISBN[i]) * (10 - i);
  }
  
  const checkDigit = cleanISBN[9];
  const calculatedCheck = (11 - (sum % 11)) % 11;
  
  if (calculatedCheck === 10) {
    return checkDigit === 'X';
  } else {
    return checkDigit === calculatedCheck.toString();
  }
}

/**
 * Validates ISBN-13 format and checksum
 */
export function validateISBN13(isbn: string): boolean {
  // Remove any hyphens or spaces
  const cleanISBN = isbn.replace(/[-\s]/g, '');
  
  // Check if it's exactly 13 digits
  if (cleanISBN.length !== 13 || !/^\d{13}$/.test(cleanISBN)) return false;
  
  // Calculate checksum
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    const digit = parseInt(cleanISBN[i]);
    sum += i % 2 === 0 ? digit : digit * 3;
  }
  
  const checkDigit = parseInt(cleanISBN[12]);
  const calculatedCheck = (10 - (sum % 10)) % 10;
  
  return checkDigit === calculatedCheck;
}

/**
 * Comprehensive ISBN validation
 */
export function validateISBN(isbn: string): ISBNValidationResult {
  if (!isbn || isbn.trim() === '') {
    return { isValid: true, type: 'invalid', formatted: '' }; // ISBN is optional
  }
  
  const cleanISBN = isbn.replace(/[-\s]/g, '');
  
  if (cleanISBN.length === 10) {
    const isValid = validateISBN10(cleanISBN);
    return {
      isValid,
      type: 'ISBN-10',
      formatted: isValid ? formatISBN10(cleanISBN) : isbn
    };
  } else if (cleanISBN.length === 13) {
    const isValid = validateISBN13(cleanISBN);
    return {
      isValid,
      type: 'ISBN-13',
      formatted: isValid ? formatISBN13(cleanISBN) : isbn
    };
  }
  
  return { isValid: false, type: 'invalid', formatted: isbn };
}

/**
 * Format ISBN-10 with hyphens
 */
export function formatISBN10(isbn: string): string {
  const clean = isbn.replace(/[-\s]/g, '');
  if (clean.length !== 10) return isbn;
  
  // Basic formatting: X-XXX-XXXXX-X
  return `${clean.slice(0, 1)}-${clean.slice(1, 4)}-${clean.slice(4, 9)}-${clean.slice(9)}`;
}

/**
 * Format ISBN-13 with hyphens
 */
export function formatISBN13(isbn: string): string {
  const clean = isbn.replace(/[-\s]/g, '');
  if (clean.length !== 13) return isbn;
  
  // Basic formatting: XXX-X-XXX-XXXXX-X
  return `${clean.slice(0, 3)}-${clean.slice(3, 4)}-${clean.slice(4, 7)}-${clean.slice(7, 12)}-${clean.slice(12)}`;
}

/**
 * Validates publication year
 */
export function validatePublicationYear(year: string): { isValid: boolean; error?: string } {
  if (!year || year.trim() === '') {
    return { isValid: true }; // Publication year is optional
  }
  
  const yearNum = parseInt(year);
  const currentYear = new Date().getFullYear();
  
  if (isNaN(yearNum)) {
    return { isValid: false, error: 'Publication year must be a valid number' };
  }
  
  if (yearNum < 1800) {
    return { isValid: false, error: 'Publication year cannot be before 1800' };
  }
  
  if (yearNum > currentYear + 5) {
    return { isValid: false, error: `Publication year cannot be more than 5 years in the future (${currentYear + 5})` };
  }
  
  return { isValid: true };
}

/**
 * Validates quantity fields
 */
export function validateQuantity(quantity: string, fieldName: string, allowZero: boolean = false): { isValid: boolean; error?: string } {
  if (!quantity || quantity.trim() === '') {
    return { isValid: true }; // Most quantity fields are optional
  }
  
  const quantityNum = parseInt(quantity);
  
  if (isNaN(quantityNum)) {
    return { isValid: false, error: `${fieldName} must be a valid number` };
  }
  
  if (!allowZero && quantityNum < 0) {
    return { isValid: false, error: `${fieldName} cannot be negative` };
  }
  
  if (allowZero && quantityNum < 0) {
    return { isValid: false, error: `${fieldName} cannot be negative` };
  }
  
  if (quantityNum > 1000000) {
    return { isValid: false, error: `${fieldName} seems unreasonably large (max: 1,000,000)` };
  }
  
  return { isValid: true };
}

/**
 * Validates cost per unit
 */
export function validateCostPerUnit(cost: string): { isValid: boolean; error?: string } {
  if (!cost || cost.trim() === '') {
    return { isValid: true }; // Cost is optional
  }
  
  const costNum = parseFloat(cost);
  
  if (isNaN(costNum)) {
    return { isValid: false, error: 'Cost per unit must be a valid number' };
  }
  
  if (costNum < 0) {
    return { isValid: false, error: 'Cost per unit cannot be negative' };
  }
  
  if (costNum > 10000000) {
    return { isValid: false, error: 'Cost per unit seems unreasonably large (max: 10,000,000 UGX)' };
  }
  
  return { isValid: true };
}

/**
 * Validates text fields
 */
export function validateTextField(text: string, fieldName: string, required: boolean = false, maxLength: number = 255): { isValid: boolean; error?: string } {
  if (required && (!text || text.trim() === '')) {
    return { isValid: false, error: `${fieldName} is required` };
  }
  
  if (text && text.length > maxLength) {
    return { isValid: false, error: `${fieldName} cannot exceed ${maxLength} characters` };
  }
  
  // Check for potentially harmful content
  if (text && /<script|javascript:|data:/i.test(text)) {
    return { isValid: false, error: `${fieldName} contains invalid content` };
  }
  
  return { isValid: true };
}

/**
 * Comprehensive book form validation
 */
export function validateBookForm(formData: BookFormData): ValidationResult {
  const errors: string[] = [];
  const fieldErrors: Record<string, string> = {};
  
  // Required field validations
  const titleValidation = validateTextField(formData.title, 'Title', true, 255);
  if (!titleValidation.isValid) {
    errors.push(titleValidation.error!);
    fieldErrors.title = titleValidation.error!;
  }
  

  
  const descriptionValidation = validateTextField(formData.description, 'Description', false, 1000);
  if (!descriptionValidation.isValid) {
    errors.push(descriptionValidation.error!);
    fieldErrors.description = descriptionValidation.error!;
  }
  
  const quantityValidation = validateQuantity(formData.total_quantity, 'Total quantity', true);
  if (!quantityValidation.isValid) {
    errors.push(quantityValidation.error!);
    fieldErrors.total_quantity = quantityValidation.error!;
  }
  
  const thresholdValidation = validateQuantity(formData.minimum_threshold, 'Minimum threshold', true);
  if (!thresholdValidation.isValid) {
    errors.push(thresholdValidation.error!);
    fieldErrors.minimum_threshold = thresholdValidation.error!;
  }
  

  
  const notesValidation = validateTextField(formData.notes, 'Notes', false, 1000);
  if (!notesValidation.isValid) {
    errors.push(notesValidation.error!);
    fieldErrors.notes = notesValidation.error!;
  }
  
  // Cross-field validations
  if (formData.total_quantity && formData.minimum_threshold) {
    const totalQty = parseInt(formData.total_quantity);
    const minThreshold = parseInt(formData.minimum_threshold);
    
    if (!isNaN(totalQty) && !isNaN(minThreshold) && minThreshold > totalQty) {
      errors.push('Minimum threshold cannot be greater than total quantity');
      fieldErrors.minimum_threshold = 'Cannot exceed total quantity';
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    fieldErrors
  };
}

/**
 * Real-time field validation for better UX
 */
export function validateField(fieldName: keyof BookFormData, value: string, formData?: Partial<BookFormData>): { isValid: boolean; error?: string } {
  switch (fieldName) {
    case 'title':
      return validateTextField(value, 'Title', true, 255);
    case 'author':
      return validateTextField(value, 'Author', true, 255);
    case 'isbn': {
      const isbnResult = validateISBN(value);
      return { isValid: isbnResult.isValid || value.trim() === '', error: isbnResult.isValid ? undefined : 'Invalid ISBN format' };
    }
    case 'publication_year':
      return validatePublicationYear(value);
    case 'publisher':
      return validateTextField(value, 'Publisher', false, 255);
    case 'description':
      return validateTextField(value, 'Description', false, 1000);
    case 'total_quantity':
      return validateQuantity(value, 'Total quantity', true);
    case 'minimum_threshold': {
      const thresholdResult = validateQuantity(value, 'Minimum threshold', true);
      if (thresholdResult.isValid && formData?.total_quantity) {
        const totalQty = parseInt(formData.total_quantity);
        const threshold = parseInt(value);
        if (!isNaN(totalQty) && !isNaN(threshold) && threshold > totalQty) {
          return { isValid: false, error: 'Cannot exceed total quantity' };
        }
      }
      return thresholdResult;
    }
    case 'cost_per_unit':
      return validateCostPerUnit(value);
    case 'storage_location':
      return validateTextField(value, 'Storage location', false, 255);
    case 'notes':
      return validateTextField(value, 'Notes', false, 1000);
    default:
      return { isValid: true };
  }
}
