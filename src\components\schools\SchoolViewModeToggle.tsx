
import React from 'react';
import { Button } from '@/components/ui/button';
import { Table, List } from 'lucide-react';

export type ViewMode = 'table' | 'list';

interface SchoolViewModeToggleProps {
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
}

const SchoolViewModeToggle = ({ viewMode, onViewModeChange }: SchoolViewModeToggleProps) => {
  return (
    <div className="flex items-center bg-gray-100 rounded-lg p-1">
      <Button
        variant={viewMode === 'table' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewModeChange('table')}
        className="h-8"
      >
        <Table className="h-4 w-4" />
      </Button>
      <Button
        variant={viewMode === 'list' ? 'default' : 'ghost'}
        size="sm"
        onClick={() => onViewModeChange('list')}
        className="h-8"
      >
        <List className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default SchoolViewModeToggle;
