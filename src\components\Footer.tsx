import React from 'react';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import {
  Heart,
  ExternalLink,
  Mail,
  Phone,
  MapPin,
  Globe,
  Twitter,
  Facebook,
} from 'lucide-react';

interface FooterProps {
  onViewChange?: (view: string) => void;
}

const Footer = ({ onViewChange }: FooterProps) => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { label: 'Dashboard', view: 'dashboard' },
    { label: 'Schools', view: 'schools-list' },
    { label: 'Tasks', view: 'tasks-assigned' },
    { label: 'Distributions', view: 'distributions-active' },
    { label: 'Reports', view: 'reports-analytics' },
  ];

  const supportLinks = [
    { label: 'Help & Support', view: 'help' },
    { label: 'Documentation', view: 'help-docs' },
    { label: 'System Status', href: '#' },
  ];

  const legalLinks = [
    { label: 'Privacy Policy', href: '#' },
    { label: 'Terms of Service', href: '#' },
    { label: 'Data Protection', href: '#' },
    { label: 'Cookie Policy', href: '#' },
  ];

  return (
    <footer className="bg-white border-t border-gray-200 mt-auto">
      {/* Footer */}
      <div className="px-6 py-4">
        <div className="flex flex-col md:flex-row justify-between items-center space-y-2 md:space-y-0">
          <div className="flex items-center space-x-1 text-sm text-gray-600">
            <span>© {currentYear} iLEAD Uganda. All rights reserved.</span>
          </div>

          <div className="text-sm text-gray-500">
            Version 1.0.0
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
