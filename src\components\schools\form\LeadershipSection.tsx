
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { SchoolFormData } from '@/types/school';

interface LeadershipSectionProps {
  formData: SchoolFormData;
  onFormDataChange: (data: SchoolFormData) => void;
}

const LeadershipSection = ({ formData, onFormDataChange }: LeadershipSectionProps) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">School Leadership</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="head_teacher_name">Head Teacher Name</Label>
          <Input
            id="head_teacher_name"
            value={formData.head_teacher_name}
            onChange={(e) => onFormDataChange({ ...formData, head_teacher_name: e.target.value })}
            placeholder="Full name of head teacher"
          />
        </div>
        <div className="space-y-2">
          <Label htmlFor="deputy_head_teacher_name">Deputy Head Teacher Name</Label>
          <Input
            id="deputy_head_teacher_name"
            value={formData.deputy_head_teacher_name}
            onChange={(e) => onFormDataChange({ ...formData, deputy_head_teacher_name: e.target.value })}
            placeholder="Full name of deputy head teacher"
          />
        </div>
      </div>
    </div>
  );
};

export default LeadershipSection;
