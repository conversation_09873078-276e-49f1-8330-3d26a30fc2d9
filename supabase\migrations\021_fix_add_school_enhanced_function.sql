-- Fix the add_school_enhanced function to match frontend requirements
-- This migration updates the function signature to include all the parameters
-- that the frontend is trying to pass

DROP FUNCTION IF EXISTS add_school_enhanced(text, text, uuid, text, text, text, integer, integer, date, jsonb, jsonb);

CREATE OR REPLACE FUNCTION add_school_enhanced(
    p_name VARCHAR(255),
    p_school_type school_type,
    p_division_id UUID,
    p_student_count INTEGER,
    p_champion_teacher_count INTEGER,
    p_code VARCHAR(50) DEFAULT NULL,
    p_school_category school_category DEFAULT 'day',
    p_contact_phone VARCHAR(50) DEFAULT NULL,
    p_email VARCHAR(255) DEFAULT NULL,
    p_head_teacher_name VARCHAR(255) DEFAULT NULL,
    p_head_teacher_phone VARCHAR(50) DEFAULT NULL,
    p_head_teacher_email VARCHAR(255) DEFAULT NULL,
    p_deputy_head_teacher_name <PERSON><PERSON><PERSON><PERSON>(255) DEFAULT NULL,
    p_deputy_head_teacher_phone VARCHAR(50) DEFAULT NULL,
    p_deputy_head_teacher_email VARCHAR(255) DEFAULT NULL,
    p_date_joined_ilead DATE DEFAULT NULL,
    p_ownership_type VARCHAR(50) DEFAULT NULL,
    p_location_coordinates POINT DEFAULT NULL,
    p_is_partner_managed BOOLEAN DEFAULT FALSE,
    p_partner_name VARCHAR(255) DEFAULT NULL,
    p_field_staff_id UUID DEFAULT NULL,
    p_champion_teachers JSONB DEFAULT '[]'::jsonb,
    p_assistant_champion_teachers JSONB DEFAULT '[]'::jsonb
) RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    v_school_id UUID;
    v_champion_teacher JSONB;
    v_assistant_teacher JSONB;
    v_champion_count INTEGER;
BEGIN
    -- Validate required fields
    IF p_name IS NULL OR trim(p_name) = '' THEN
        RAISE EXCEPTION 'School name is required';
    END IF;
    
    IF p_school_type IS NULL THEN
        RAISE EXCEPTION 'School type is required';
    END IF;
    
    IF p_division_id IS NULL THEN
        RAISE EXCEPTION 'Division/District is required';
    END IF;
    
    IF p_student_count IS NULL OR p_student_count < 0 THEN
        RAISE EXCEPTION 'Student count is required and must be non-negative';
    END IF;
    
    IF p_champion_teacher_count IS NULL OR p_champion_teacher_count < 1 THEN
        RAISE EXCEPTION 'Champion teacher count is required and must be at least 1';
    END IF;
    
    -- Validate champion teachers requirement (at least 1 required)
    v_champion_count := jsonb_array_length(p_champion_teachers);
    IF v_champion_count = 0 THEN
        RAISE EXCEPTION 'At least one champion teacher is required';
    END IF;
    
    -- Validate that at least one champion teacher has a name
    IF NOT EXISTS (
        SELECT 1 FROM jsonb_array_elements(p_champion_teachers) AS elem
        WHERE elem->>'name' IS NOT NULL AND trim(elem->>'name') != ''
    ) THEN
        RAISE EXCEPTION 'At least one champion teacher must have a name';
    END IF;
    
    -- Insert school record
    INSERT INTO schools (
        name, code, school_type, school_category, division_id,
        student_count, champion_teacher_count,
        contact_phone, email,
        head_teacher_name, head_teacher_phone, head_teacher_email,
        deputy_head_teacher_name, deputy_head_teacher_phone, deputy_head_teacher_email,
        date_joined_ilead, ownership_type, location_coordinates,
        is_partner_managed, partner_name, field_staff_id,
        created_by
    ) VALUES (
        trim(p_name),
        CASE WHEN p_code IS NOT NULL AND trim(p_code) != '' THEN trim(p_code) ELSE NULL END,
        p_school_type,
        p_school_category,
        p_division_id,
        p_student_count,
        p_champion_teacher_count,
        CASE WHEN p_contact_phone IS NOT NULL AND trim(p_contact_phone) != '' THEN trim(p_contact_phone) ELSE NULL END,
        CASE WHEN p_email IS NOT NULL AND trim(p_email) != '' THEN trim(p_email) ELSE NULL END,
        CASE WHEN p_head_teacher_name IS NOT NULL AND trim(p_head_teacher_name) != '' THEN trim(p_head_teacher_name) ELSE NULL END,
        CASE WHEN p_head_teacher_phone IS NOT NULL AND trim(p_head_teacher_phone) != '' THEN trim(p_head_teacher_phone) ELSE NULL END,
        CASE WHEN p_head_teacher_email IS NOT NULL AND trim(p_head_teacher_email) != '' THEN trim(p_head_teacher_email) ELSE NULL END,
        CASE WHEN p_deputy_head_teacher_name IS NOT NULL AND trim(p_deputy_head_teacher_name) != '' THEN trim(p_deputy_head_teacher_name) ELSE NULL END,
        CASE WHEN p_deputy_head_teacher_phone IS NOT NULL AND trim(p_deputy_head_teacher_phone) != '' THEN trim(p_deputy_head_teacher_phone) ELSE NULL END,
        CASE WHEN p_deputy_head_teacher_email IS NOT NULL AND trim(p_deputy_head_teacher_email) != '' THEN trim(p_deputy_head_teacher_email) ELSE NULL END,
        p_date_joined_ilead,
        CASE WHEN p_ownership_type IS NOT NULL AND trim(p_ownership_type) != '' THEN trim(p_ownership_type) ELSE NULL END,
        p_location_coordinates,
        p_is_partner_managed,
        CASE WHEN p_partner_name IS NOT NULL AND trim(p_partner_name) != '' THEN trim(p_partner_name) ELSE NULL END,
        p_field_staff_id,
        auth.uid()
    ) RETURNING id INTO v_school_id;
    
    -- Insert champion teachers
    FOR v_champion_teacher IN SELECT * FROM jsonb_array_elements(p_champion_teachers)
    LOOP
        IF v_champion_teacher->>'name' IS NOT NULL AND trim(v_champion_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'champion',
                v_champion_teacher->>'name',
                v_champion_teacher->>'phone',
                CASE WHEN v_champion_teacher->>'email' IS NOT NULL AND trim(v_champion_teacher->>'email') != '' THEN v_champion_teacher->>'email' ELSE NULL END
            );
        END IF;
    END LOOP;
    
    -- Insert assistant champion teachers (optional)
    FOR v_assistant_teacher IN SELECT * FROM jsonb_array_elements(p_assistant_champion_teachers)
    LOOP
        IF v_assistant_teacher->>'name' IS NOT NULL AND trim(v_assistant_teacher->>'name') != '' THEN
            INSERT INTO school_champion_teachers (
                school_id, contact_type, name, phone, email
            ) VALUES (
                v_school_id, 'assistant_champion',
                v_assistant_teacher->>'name',
                v_assistant_teacher->>'phone',
                CASE WHEN v_assistant_teacher->>'email' IS NOT NULL AND trim(v_assistant_teacher->>'email') != '' THEN v_assistant_teacher->>'email' ELSE NULL END
            );
        END IF;
    END LOOP;
    
    RETURN v_school_id;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION add_school_enhanced TO authenticated;

-- Add comment
COMMENT ON FUNCTION add_school_enhanced IS 'Enhanced function to add a school with all required and optional fields, including champion teachers';
