
import React from 'react';
import { School } from 'lucide-react';
import SchoolManagement from '../SchoolManagement';
import { useAuth } from '@/hooks/useAuth';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

const SecondarySchools = () => {
  const { profile } = useAuth();

  return (
    <PageLayout>
      <PageHeader
        title="Secondary Schools"
        description="View and manage secondary schools (S1-S6)"
        icon={School}
      />

      <ContentCard noPadding>
        <SchoolManagement currentUser={profile} schoolTypeFilter="secondary" />
      </ContentCard>
    </PageLayout>
  );
};

export default SecondarySchools;
