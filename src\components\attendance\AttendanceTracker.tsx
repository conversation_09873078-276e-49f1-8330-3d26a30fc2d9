import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Users, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertCircle,
  UserCheck,
  UserX,
  Timer,
  Save,
  RotateCcw
} from 'lucide-react';
import { useSessionAttendance, useRecordStudentAttendance, useBulkRecordAttendance } from '@/hooks/attendance/useStudentAttendance';
import { Database } from '@/integrations/supabase/types';
import { PageLayout, PageHeader, ContentCard } from '@/components/layout';

type AttendanceStatus = Database['public']['Enums']['attendance_status'];

interface AttendanceTrackerProps {
  sessionId: string;
  sessionName: string;
  sessionDate: string;
  sessionType: string;
  roundTablesCount?: number;
  studentsPerTable?: number;
}

interface StudentAttendanceState {
  student_id: string;
  attendance_status: AttendanceStatus;
  check_in_time?: string;
  late_minutes?: number;
  table_number?: number;
  participation_score?: number;
  behavior_notes?: string;
  absence_reason?: string;
}

const AttendanceTracker: React.FC<AttendanceTrackerProps> = ({
  sessionId,
  sessionName,
  sessionDate,
  sessionType,
  roundTablesCount = 0,
  studentsPerTable = 8,
}) => {
  const [attendanceState, setAttendanceState] = useState<Record<string, StudentAttendanceState>>({});
  const [bulkAction, setBulkAction] = useState<AttendanceStatus | ''>('');
  const [selectedStudents, setSelectedStudents] = useState<Set<string>>(new Set());

  const { data: sessionAttendance, isLoading } = useSessionAttendance(sessionId);
  const recordAttendance = useRecordStudentAttendance();
  const bulkRecordAttendance = useBulkRecordAttendance();

  // Initialize attendance state from existing data
  React.useEffect(() => {
    if (sessionAttendance) {
      const initialState: Record<string, StudentAttendanceState> = {};
      sessionAttendance.forEach(attendance => {
        if (attendance.student) {
          initialState[attendance.student.id] = {
            student_id: attendance.student.id,
            attendance_status: attendance.attendance_status,
            check_in_time: attendance.check_in_time || undefined,
            late_minutes: attendance.late_minutes || 0,
            table_number: attendance.table_number || undefined,
            participation_score: attendance.participation_score || undefined,
            behavior_notes: attendance.behavior_notes || '',
            absence_reason: attendance.absence_reason || '',
          };
        }
      });
      setAttendanceState(initialState);
    }
  }, [sessionAttendance]);

  const updateStudentAttendance = (studentId: string, updates: Partial<StudentAttendanceState>) => {
    setAttendanceState(prev => ({
      ...prev,
      [studentId]: {
        ...prev[studentId],
        student_id: studentId,
        ...updates,
      },
    }));
  };

  const handleBulkAction = () => {
    if (!bulkAction || selectedStudents.size === 0) return;

    const updates: Record<string, StudentAttendanceState> = {};
    selectedStudents.forEach(studentId => {
      updates[studentId] = {
        ...attendanceState[studentId],
        student_id: studentId,
        attendance_status: bulkAction,
        check_in_time: bulkAction === 'present' ? new Date().toISOString() : undefined,
      };
    });

    setAttendanceState(prev => ({ ...prev, ...updates }));
    setSelectedStudents(new Set());
    setBulkAction('');
  };

  const handleSaveAttendance = async () => {
    const attendanceRecords = Object.values(attendanceState).filter(record => record.attendance_status);
    
    if (attendanceRecords.length === 0) return;

    try {
      await bulkRecordAttendance.mutateAsync({
        session_id: sessionId,
        attendance_records: attendanceRecords,
      });
    } catch (error) {
      console.error('Failed to save attendance:', error);
    }
  };

  const getStatusIcon = (status: AttendanceStatus) => {
    switch (status) {
      case 'present':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'absent':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'late':
        return <Timer className="h-4 w-4 text-yellow-600" />;
      case 'excused':
        return <AlertCircle className="h-4 w-4 text-blue-600" />;
      default:
        return <Users className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusColor = (status: AttendanceStatus) => {
    switch (status) {
      case 'present':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'absent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'late':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'excused':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (isLoading) {
    return (
      <PageLayout>
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
        </div>
      </PageLayout>
    );
  }

  const students = sessionAttendance?.map(a => a.student).filter(Boolean) || [];
  const attendanceStats = {
    total: students.length,
    present: Object.values(attendanceState).filter(a => a.attendance_status === 'present').length,
    absent: Object.values(attendanceState).filter(a => a.attendance_status === 'absent').length,
    late: Object.values(attendanceState).filter(a => a.attendance_status === 'late').length,
    excused: Object.values(attendanceState).filter(a => a.attendance_status === 'excused').length,
  };

  return (
    <PageLayout>
      <PageHeader
        title={`Attendance: ${sessionName}`}
        description={`${sessionType} session on ${new Date(sessionDate).toLocaleDateString()}`}
      />

      {/* Session Summary */}
      <ContentCard>
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{attendanceStats.total}</div>
            <div className="text-sm text-gray-600">Total Students</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{attendanceStats.present}</div>
            <div className="text-sm text-gray-600">Present</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{attendanceStats.absent}</div>
            <div className="text-sm text-gray-600">Absent</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{attendanceStats.late}</div>
            <div className="text-sm text-gray-600">Late</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{attendanceStats.excused}</div>
            <div className="text-sm text-gray-600">Excused</div>
          </div>
        </div>

        {/* Bulk Actions */}
        <div className="flex items-center gap-4 mb-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Bulk Action:</span>
            <Select value={bulkAction} onValueChange={(value: AttendanceStatus | '') => setBulkAction(value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="present">Present</SelectItem>
                <SelectItem value="absent">Absent</SelectItem>
                <SelectItem value="late">Late</SelectItem>
                <SelectItem value="excused">Excused</SelectItem>
              </SelectContent>
            </Select>
            <Button 
              onClick={handleBulkAction}
              disabled={!bulkAction || selectedStudents.size === 0}
              size="sm"
            >
              Apply to {selectedStudents.size} selected
            </Button>
          </div>
          
          <div className="ml-auto flex gap-2">
            <Button 
              onClick={() => setAttendanceState({})}
              variant="outline"
              size="sm"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button 
              onClick={handleSaveAttendance}
              disabled={Object.keys(attendanceState).length === 0}
              size="sm"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Attendance
            </Button>
          </div>
        </div>
      </ContentCard>

      {/* Student Attendance List */}
      <ContentCard>
        <CardHeader>
          <CardTitle>Student Attendance</CardTitle>
          <CardDescription>
            Mark attendance for each student in this session
            {roundTablesCount > 0 && ` (${roundTablesCount} tables, ${studentsPerTable} students per table)`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {students.map((student) => {
              if (!student) return null;

              const studentAttendance = attendanceState[student.id] || {
                student_id: student.id,
                attendance_status: 'present' as AttendanceStatus
              };

              return (
                <div key={student.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={selectedStudents.has(student.id)}
                        onChange={(e) => {
                          const newSelected = new Set(selectedStudents);
                          if (e.target.checked) {
                            newSelected.add(student.id);
                          } else {
                            newSelected.delete(student.id);
                          }
                          setSelectedStudents(newSelected);
                        }}
                        className="rounded"
                      />
                      <div>
                        <div className="font-medium">
                          {student.first_name} {student.last_name}
                        </div>
                        <div className="text-sm text-gray-600">
                          {student.student_number} • Grade {student.grade_level} • {student.gender}
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      {getStatusIcon(studentAttendance.attendance_status)}
                      <Badge className={getStatusColor(studentAttendance.attendance_status)}>
                        {studentAttendance.attendance_status}
                      </Badge>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {/* Attendance Status */}
                    <div>
                      <label className="block text-sm font-medium mb-1">Status</label>
                      <Select
                        value={studentAttendance.attendance_status}
                        onValueChange={(value: AttendanceStatus) =>
                          updateStudentAttendance(student.id, { attendance_status: value })
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="present">Present</SelectItem>
                          <SelectItem value="absent">Absent</SelectItem>
                          <SelectItem value="late">Late</SelectItem>
                          <SelectItem value="excused">Excused</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Late Minutes (if late) */}
                    {studentAttendance.attendance_status === 'late' && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Late (minutes)</label>
                        <Input
                          type="number"
                          min="0"
                          value={studentAttendance.late_minutes || 0}
                          onChange={(e) =>
                            updateStudentAttendance(student.id, {
                              late_minutes: parseInt(e.target.value) || 0
                            })
                          }
                        />
                      </div>
                    )}

                    {/* Table Number (for leadership programs) */}
                    {roundTablesCount > 0 && studentAttendance.attendance_status === 'present' && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Table Number</label>
                        <Select
                          value={studentAttendance.table_number?.toString() || ''}
                          onValueChange={(value) =>
                            updateStudentAttendance(student.id, {
                              table_number: value ? parseInt(value) : undefined
                            })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select table" />
                          </SelectTrigger>
                          <SelectContent>
                            {Array.from({ length: roundTablesCount }, (_, i) => (
                              <SelectItem key={i + 1} value={(i + 1).toString()}>
                                Table {i + 1}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}

                    {/* Participation Score */}
                    {studentAttendance.attendance_status === 'present' && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Participation (1-5)</label>
                        <Select
                          value={studentAttendance.participation_score?.toString() || ''}
                          onValueChange={(value) =>
                            updateStudentAttendance(student.id, {
                              participation_score: value ? parseInt(value) : undefined
                            })
                          }
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Rate" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">1 - Poor</SelectItem>
                            <SelectItem value="2">2 - Below Average</SelectItem>
                            <SelectItem value="3">3 - Average</SelectItem>
                            <SelectItem value="4">4 - Good</SelectItem>
                            <SelectItem value="5">5 - Excellent</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                  </div>

                  {/* Notes and Reason */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                    {/* Behavior Notes */}
                    {studentAttendance.attendance_status === 'present' && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Behavior Notes</label>
                        <Textarea
                          placeholder="Optional behavior observations..."
                          value={studentAttendance.behavior_notes || ''}
                          onChange={(e) =>
                            updateStudentAttendance(student.id, { behavior_notes: e.target.value })
                          }
                          rows={2}
                        />
                      </div>
                    )}

                    {/* Absence Reason */}
                    {studentAttendance.attendance_status === 'absent' && (
                      <div>
                        <label className="block text-sm font-medium mb-1">Absence Reason</label>
                        <Textarea
                          placeholder="Reason for absence..."
                          value={studentAttendance.absence_reason || ''}
                          onChange={(e) =>
                            updateStudentAttendance(student.id, { absence_reason: e.target.value })
                          }
                          rows={2}
                        />
                      </div>
                    )}
                  </div>
                </div>
              );
            })}
          </div>
        </CardContent>
      </ContentCard>
    </PageLayout>
  );
};

export default AttendanceTracker;
